from typing import Optional, Dict, Any, List
from datetime import datetime, timezone, timedelta
from beanie import PydanticObjectId
from models.trade_record import TradeRecord, TradeStatus, TradeType
from models.signal import Signal  # 确保导入 Signal
from models.trade_score_log import TradeScoreLog # 确保导入 TradeScoreLog
from models.kol_wallet import KOLWallet # 确保导入 KOLWallet, 尽管聚合中不直接用，但明确上下文

class TradeRecordDAO:
    async def save(self, trade_record: TradeRecord) -> TradeRecord:
        """保存或更新单个交易记录实例"""
        await trade_record.save()
        return trade_record

    async def insert_one_from_dict(self, data: Dict[str, Any]) -> TradeRecord:
        """从字典创建并插入一条新的交易记录"""
        trade_record = TradeRecord(**data)
        await trade_record.insert()
        return trade_record

    async def insert_many_from_dicts(self, records_data: List[Dict[str, Any]]) -> List[TradeRecord]:
        """从字典列表创建并插入多条新的交易记录"""
        if not records_data:
            return []
        trade_records = [TradeRecord(**data) for data in records_data]
        inserted_records = await TradeRecord.insert_many(trade_records)
        return trade_records

    async def get_by_id(self, record_id: PydanticObjectId) -> Optional[TradeRecord]:
        """通过ID获取交易记录"""
        return await TradeRecord.get(record_id)

    async def find_by_signal_id(self, signal_id: PydanticObjectId) -> List[TradeRecord]:
        """根据信号ID查找所有相关的交易记录"""
        return await TradeRecord.find(TradeRecord.signal_id == signal_id).to_list()
    
    async def find_successful_trade_for_signal(
        self, 
        signal_id: PydanticObjectId, 
        trade_type: TradeType
    ) -> Optional[TradeRecord]:
        """
        查找与给定信号ID和交易类型关联的最新成功的交易记录。
        主要用于在卖出时查找对应的成功买入记录。
        """
        return await TradeRecord.find(
            TradeRecord.signal_id == signal_id,
            TradeRecord.trade_type == trade_type,
            TradeRecord.status == TradeStatus.SUCCESS
        ).sort("-created_at").first_or_none()

    async def update_record_by_id(self, record_id: PydanticObjectId, updates: Dict[str, Any]) -> Optional[TradeRecord]:
        """使用字典中的数据更新指定的交易记录的字段"""
        trade_record = await TradeRecord.get(record_id)
        if trade_record:
            updated_fields = False
            for key, value in updates.items():
                if hasattr(trade_record, key):
                    setattr(trade_record, key, value)
                    updated_fields = True
            if updated_fields:
                await trade_record.save()
            return trade_record
        return None
    
    # === 新增：交易记录验证相关方法 ===
    
    async def find_pending_verification_records(
        self, 
        limit: int = 50
    ) -> List[TradeRecord]:
        """
        查找需要验证的交易记录
        - 成功状态的交易记录
        - 有tx_hash
        - 验证状态为空或pending（移除时间限制，查找所有历史记录）
        """
        return await TradeRecord.find(
            TradeRecord.status == TradeStatus.SUCCESS,
            TradeRecord.tx_hash != None,
            {
                "$or": [
                    {"verification_status": None},
                    {"verification_status": "pending"}
                ]
            }
        ).limit(limit).to_list()
    
    async def update_verification_result(
        self, 
        record_id: str, 
        update_data: Dict[str, Any]
    ) -> bool:
        """
        更新交易记录的验证结果
        
        Args:
            record_id: 记录ID（字符串格式）
            update_data: 更新数据字典
            
        Returns:
            bool: 更新是否成功
        """
        try:
            # 转换字符串ID为PydanticObjectId
            object_id = PydanticObjectId(record_id)
            
            # 使用现有的update_record_by_id方法
            updated_record = await self.update_record_by_id(object_id, update_data)
            
            return updated_record is not None
            
        except Exception as e:
            # 记录错误但不抛出异常
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"更新验证结果失败，record_id: {record_id}, error: {str(e)}")
            return False
    
    async def increment_verification_attempts(
        self, 
        record_id: PydanticObjectId
    ) -> Optional[TradeRecord]:
        """
        增加验证尝试次数
        """
        trade_record = await TradeRecord.get(record_id)
        if trade_record:
            trade_record.verification_attempts += 1
            trade_record.last_verification_attempt = datetime.now(timezone.utc)
            await trade_record.save()
            return trade_record
        return None
    
    async def get_verification_statistics(self, hours_back: int = 24) -> Dict[str, int]:
        """
        获取验证统计信息
        """
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours_back)
        
        # 总的成功交易记录数
        total_success = await TradeRecord.find(
            TradeRecord.status == TradeStatus.SUCCESS,
            TradeRecord.tx_hash != None,
            TradeRecord.created_at >= cutoff_time
        ).count()
        
        # 已验证的记录数
        verified = await TradeRecord.find(
            TradeRecord.status == TradeStatus.SUCCESS,
            TradeRecord.tx_hash != None,
            TradeRecord.created_at >= cutoff_time,
            TradeRecord.verification_status == "verified"
        ).count()
        
        # 验证失败的记录数
        failed = await TradeRecord.find(
            TradeRecord.status == TradeStatus.SUCCESS,
            TradeRecord.tx_hash != None,
            TradeRecord.created_at >= cutoff_time,
            TradeRecord.verification_status == "failed"
        ).count()
        
        # 待验证的记录数
        pending = await TradeRecord.find(
            TradeRecord.status == TradeStatus.SUCCESS,
            TradeRecord.tx_hash != None,
            TradeRecord.created_at >= cutoff_time,
            {
                "$or": [
                    {"verification_status": None},
                    {"verification_status": "pending"}
                ]
            }
        ).count()
        
        return {
            "total_success_records": total_success,
            "verified": verified,
            "failed": failed,
            "pending": pending,
            "skipped": total_success - verified - failed - pending
        }
    
    # === 新增：延迟监控相关查询方法 ===
    
    async def find_unprocessed_trades(self, limit: int = 50) -> List[TradeRecord]:
        """
        查询未处理的交易记录
        
        只返回状态为'success'且在延迟记录表中不存在对应记录的交易
        遵循需求文档FR002和7.1节算法要求
        
        Args:
            limit: 返回记录数量限制，默认50条
            
        Returns:
            List[TradeRecord]: 未处理的交易记录列表
        """
        from models.trading_delay_record import TradingDelayRecord
        
        # 1. 使用聚合查询获取所有已处理的交易记录ID
        pipeline = [
            {"$group": {"_id": "$trade_record_id"}}
        ]
        processed_records = await TradingDelayRecord.aggregate(pipeline).to_list()
        processed_trade_ids = [record["_id"] for record in processed_records]
        
        # 2. 查询状态为成功且不在已处理列表中的交易记录
        query = {
            "status": TradeStatus.SUCCESS,
            "_id": {"$nin": processed_trade_ids}
        }
        
        unprocessed_trades = await TradeRecord.find(query).limit(limit).to_list()
        
        return unprocessed_trades
    
    # === 新增：统计功能相关查询方法 ===
    
    async def find_verified_trade_records(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        strategies: Optional[List[str]] = None,
        tokens: Optional[List[str]] = None,
        limit: Optional[int] = None
    ) -> List[TradeRecord]:
        """
        查询已验证的交易记录，用于统计分析
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            strategies: 策略名称列表
            tokens: Token地址列表
            limit: 限制返回数量
            
        Returns:
            List[TradeRecord]: 符合条件的交易记录列表
        """
        # 构建基础查询条件
        query_conditions = [
            TradeRecord.status == TradeStatus.SUCCESS,
            TradeRecord.verification_status == "verified"
        ]
        
        # 添加时间范围过滤
        if start_date:
            query_conditions.append(TradeRecord.created_at >= start_date)
        if end_date:
            query_conditions.append(TradeRecord.created_at <= end_date)
        
        # 构建完整的查询条件字典
        query_dict = {}
        
        # 基础条件
        query_dict["status"] = "success"
        query_dict["verification_status"] = "verified"
        
        # 时间范围条件
        if start_date or end_date:
            date_filter = {}
            if start_date:
                date_filter["$gte"] = start_date
            if end_date:
                date_filter["$lte"] = end_date
            query_dict["created_at"] = date_filter
        
        # 策略过滤
        if strategies:
            query_dict["strategy_name"] = {"$in": strategies}
        
        # Token过滤（检查输入或输出Token）
        if tokens:
            query_dict["$or"] = [
                {"token_in_address": {"$in": tokens}},
                {"token_out_address": {"$in": tokens}}
            ]
        
        # 执行查询
        query = TradeRecord.find(query_dict).sort("+created_at")
        
        if limit:
            query = query.limit(limit)
        
        return await query.to_list()
    
    async def find_by_query(
        self,
        query: Dict[str, Any],
        sort: Optional[List[tuple]] = None,
        limit: Optional[int] = None
    ) -> List[TradeRecord]:
        """
        通用查询方法
        
        Args:
            query: MongoDB查询条件
            sort: 排序条件，格式为 [("field", 1/-1), ...]
            limit: 限制返回数量
            
        Returns:
            List[TradeRecord]: 查询结果
        """
        find_query = TradeRecord.find(query)
        
        if sort:
            # 转换排序格式
            sort_conditions = []
            for field, direction in sort:
                if direction == 1:
                    sort_conditions.append(f"+{field}")
                else:
                    sort_conditions.append(f"-{field}")
            find_query = find_query.sort(*sort_conditions)
        
        if limit:
            find_query = find_query.limit(limit)
        
        return await find_query.to_list()
    
    async def count_by_query(self, query: Dict[str, Any]) -> int:
        """
        按查询条件统计记录数量
        
        Args:
            query: MongoDB查询条件
            
        Returns:
            int: 记录数量
        """
        return await TradeRecord.find(query).count()
    
    async def aggregate_trade_statistics(
        self,
        pipeline: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        执行聚合查询进行统计分析
        
        Args:
            pipeline: MongoDB聚合管道
            
        Returns:
            List[Dict[str, Any]]: 聚合结果
        """
        return await TradeRecord.aggregate(pipeline).to_list()
    
    async def get_unique_strategies(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[str]:
        """
        获取唯一的策略名称列表
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[str]: 策略名称列表
        """
        pipeline = []
        
        # 添加时间范围过滤
        match_conditions = {
            "status": "success",
            "verification_status": "verified"
        }
        
        if start_date or end_date:
            date_filter = {}
            if start_date:
                date_filter["$gte"] = start_date
            if end_date:
                date_filter["$lte"] = end_date
            match_conditions["created_at"] = date_filter
        
        pipeline.append({"$match": match_conditions})
        pipeline.append({"$group": {"_id": "$strategy_name"}})
        pipeline.append({"$sort": {"_id": 1}})
        
        result = await self.aggregate_trade_statistics(pipeline)
        return [item["_id"] for item in result if item["_id"]]
    
    async def get_unique_tokens(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[str]:
        """
        获取唯一的Token地址列表
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[str]: Token地址列表
        """
        pipeline = []
        
        # 添加时间范围过滤
        match_conditions = {
            "status": "success",
            "verification_status": "verified"
        }
        
        if start_date or end_date:
            date_filter = {}
            if start_date:
                date_filter["$gte"] = start_date
            if end_date:
                date_filter["$lte"] = end_date
            match_conditions["created_at"] = date_filter
        
        pipeline.append({"$match": match_conditions})
        
        # 获取所有输入和输出Token
        pipeline.extend([
            {
                "$project": {
                    "tokens": ["$token_in_address", "$token_out_address"]
                }
            },
            {"$unwind": "$tokens"},
            {"$group": {"_id": "$tokens"}},
            {"$sort": {"_id": 1}}
        ])
        
        result = await self.aggregate_trade_statistics(pipeline)
        return [item["_id"] for item in result if item["_id"]]
    
    # === 新增：实际金额更新相关方法（用于修复盈利计算Bug）===
    
    async def find_trades_missing_actual_amounts(
        self,
        trade_type: TradeType,
        missing_field: str,
        limit: int = 50
    ) -> List[TradeRecord]:
        """
        查找缺少实际金额字段的交易记录
        
        Args:
            trade_type: 交易类型（BUY 或 SELL）
            missing_field: 缺少的字段名（token_in_actual_amount 或 token_out_actual_amount）
            limit: 限制返回数量
            
        Returns:
            List[TradeRecord]: 缺少实际金额的交易记录列表
        """
        query_conditions = {
            "status": "success",  # 只查找成功的交易
            "trade_type": trade_type.value,
            "tx_hash": {"$ne": None, "$exists": True},  # 必须有交易哈希
            "wallet_address": {"$ne": None, "$exists": True},  # 必须有钱包地址
            "$or": [
                {missing_field: None},
                {missing_field: {"$exists": False}}
            ]
        }
        
        return await TradeRecord.find(query_conditions).limit(limit).to_list()
    
    async def update_trade_record(
        self,
        record_id: str,
        update_data: Dict[str, Any]
    ) -> bool:
        """
        更新交易记录的状态，例如，用于标记记录已被处理或归档
        """
        try:
            object_id = PydanticObjectId(record_id)
            trade_record = await TradeRecord.get(object_id)
            if trade_record:
                await trade_record.update({"$set": update_data})
                return True
            return False
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"更新交易记录失败 (ID: {record_id}): {str(e)}")
            return False

    async def get_pending_kol_trade_combinations_for_scoring(
        self,
        limit: int = 50,
        max_lookback_days: int = 90 # 限制信号回溯时间，避免扫描过旧数据
    ) -> List[Dict[str, Any]]:
        """
        使用聚合查询获取待打分的 (KOL, 买入交易, 卖出交易, 策略) 组合。
        该查询会排除已在 TradeScoreLog 中存在的组合。
        输出格式: [{"buy_trade": TradeRecord, "sell_trade": TradeRecord, "kol_wallet_address": str, "strategy_name": str}, ...]
        """
        lookback_cutoff_time = datetime.now(timezone.utc) - timedelta(days=max_lookback_days)

        pipeline = [
            # 1. 从 'signals' 集合开始，筛选出最近的、作为平仓依据的 "卖出" 信号
            {
                "$match": {
                    "signal_type": "kol_sell", # 修复：使用生产环境的实际字段值 kol_sell
                    "buy_signal_ref_id": {"$exists": True, "$ne": None}, # 必须有关联的买入信号
                    "hit_kol_wallets": {"$exists": True, "$ne": []}, # 必须命中KOL
                    "created_at": {"$gte": lookback_cutoff_time} # 限制回溯时间
                }
            },
            # 2. 查找与此卖出信号关联的成功的 "卖出" 交易记录
            {
                "$lookup": {
                    "from": TradeRecord.Settings.name, # "trade_records"
                    "localField": "_id",
                    "foreignField": "signal_id",
                    "as": "sell_trades",
                    "pipeline": [
                        {"$match": {"status": TradeStatus.SUCCESS.value, "trade_type": TradeType.SELL.value}}
                    ]
                }
            },
            {"$unwind": "$sell_trades"}, # 每个卖出信号应该只对应一个成功的卖出交易（如果有的话）
            
            # 3. 查找与原始 "买入" 信号关联的成功的 "买入" 交易记录
            # 我们需要先获取原始买入信号的信息
            {
                "$lookup": {
                    "from": Signal.Settings.name, # "signals"
                    "localField": "buy_signal_ref_id",
                    "foreignField": "_id",
                    "as": "original_buy_signal_info"
                }
            },
            {"$unwind": "$original_buy_signal_info"}, # buy_signal_ref_id 应该是唯一的

            {
                "$lookup": {
                    "from": TradeRecord.Settings.name, # "trade_records"
                    "localField": "buy_signal_ref_id", # 用原始买入信号ID关联
                    "foreignField": "signal_id",
                    "as": "buy_trades",
                    "pipeline": [
                        {"$match": {"status": TradeStatus.SUCCESS.value, "trade_type": TradeType.BUY.value}}
                    ]
                }
            },
            {"$unwind": "$buy_trades"}, # 每个原始买入信号应该只对应一个成功的买入交易

            # 4. 确保买入和卖出交易的策略名称一致 (KOL打分是基于同一策略下的买卖对)
            # 并且卖出交易的 token_in_address 应该等于 买入交易的 token_out_address (交易的是同一种代币)
            {
                "$match": {
                    "$expr": {
                        "$and": [
                            {"$eq": ["$buy_trades.strategy_name", "$sell_trades.strategy_name"]},
                            {"$eq": ["$buy_trades.token_out_address", "$sell_trades.token_in_address"]},
                            # 可选：进一步验证时间顺序，买入应早于卖出 (通常由信号逻辑保证)
                            {"$lt": ["$buy_trades.created_at", "$sell_trades.created_at"]}
                        ]
                    }
                }
            },

            # 5. 展开KOL钱包地址，为每个KOL生成一条记录
            # 注意：KOL信息从触发卖出的信号中获取（即平仓信号）
            # 技术方案提到从买入信号和卖出信号的KOL取并集。
            # 如果要严格按此方案，需要在前面也lookup买入信号的hit_kol_wallets，然后union。
            # 此处简化为使用平仓信号的KOL，通常已足够。如果需要更复杂，可以调整。
            # 为简单起见，我们先用 sell_signal 的 hit_kol_wallets
            # TODO: 根据方案细化，可能需要合并 buy_signal_info.hit_kol_wallets 和 self.hit_kol_wallets
            {"$unwind": "$hit_kol_wallets"},

            # 6. 构造检查 TradeScoreLog 是否存在的字段
            {
                "$addFields": {
                    "log_check_buy_trade_id": "$buy_trades._id",
                    "log_check_sell_trade_id": "$sell_trades._id",
                    "log_check_kol_wallet_address": "$hit_kol_wallets", # 这是KOL钱包地址字符串
                    "log_check_strategy_name": "$buy_trades.strategy_name" # 买卖策略名已验证一致
                }
            },
            
            # 7. 检查 TradeScoreLog 中是否已存在该组合的打分记录
            {
                "$lookup": {
                    "from": TradeScoreLog.Settings.name, # "trade_score_logs"
                    "let": {
                        "buy_id_str": {"$toString": "$log_check_buy_trade_id"}, # TradeScoreLog存的是str ID
                        "sell_id_str": {"$toString": "$log_check_sell_trade_id"},
                        "kol_addr": "$log_check_kol_wallet_address",
                        "strategy": "$log_check_strategy_name"
                    },
                    "pipeline": [
                        {
                            "$match": {
                                "$expr": {
                                    "$and": [
                                        {"$eq": ["$buy_trade_record_id", "$$buy_id_str"]},
                                        {"$eq": ["$sell_trade_record_id", "$$sell_id_str"]},
                                        {"$eq": ["$kol_wallet_address", "$$kol_addr"]},
                                        {"$eq": ["$strategy_name", "$$strategy"]}
                                    ]
                                }
                            }
                        },
                        {"$limit": 1} # 只需要知道是否存在
                    ],
                    "as": "existing_score_logs"
                }
            },

            # 8. 只保留那些没有打分记录的组合
            {"$match": {"existing_score_logs": {"$size": 0}}},
            
            # 9. 投影最终输出的字段
            {
                "$project": {
                    "_id": 0, # 不输出聚合产生的_id
                    "buy_trade": "$buy_trades",
                    "sell_trade": "$sell_trades",
                    "kol_wallet_address": "$hit_kol_wallets", # 这是字符串地址
                    "strategy_name": "$buy_trades.strategy_name" 
                    # 可以考虑添加原始信号ID等信息用于调试
                    # "original_sell_signal_id": "$_id",
                    # "original_buy_signal_id": "$buy_signal_ref_id",
                }
            },
            # 10. 限制返回数量
            {"$limit": limit}
        ]
        
        try:
            # 直接使用 TradeRecord 类执行聚合，因为起始点是 Signal，但我们最终需要 TradeRecord 结构
            # 或者，如果聚合不直接返回 TradeRecord 实例，这里应该用 Signal.aggregate()
            # 鉴于我们是从 Signal 开始，但最终希望获得 TradeRecord 实体，
            # 更合适的做法是使用 Signal 模型来发起聚合，或者使用底层的 motor_asyncio 客户端。
            # Beanie 的聚合可能期望 $match 在模型自身的字段上。
            # 为简单和直接，我们这里假设有一个全局的DB实例或能获取到原始集合。
            # 假设 Signal 模型可以调用 .aggregate()
            # result = await Signal.aggregate(pipeline).to_list(length=None) # 获取所有匹配项，由$limit控制
            
            # 由于Beanie的聚合主要针对当前模型，而这个聚合横跨多个模型，
            # 使用底层 motor 客户端或者一个通用的聚合执行器可能更合适。
            # 这里我们用 TradeRecord.aggregate，并确保 TradeRecord 相关的 $lookup 是正确的。
            # 最开始的 $match 是在 Signal 上，所以严格来说，应该从 Signal.collection.aggregate 开始
            
            # 修正：使用 Signal 模型来运行聚合，因为初始 $match 是针对 Signal 集合的
            # from models.signal import Signal # 已经在顶部导入
            raw_results = await Signal.get_motor_collection().aggregate(pipeline).to_list(length=limit)

            # 将结果中的 "buy_trade" 和 "sell_trade" 字典转换为 TradeRecord 对象
            # (如果 Beanie 的聚合投影不能直接返回模型实例)
            processed_results = []
            for res in raw_results:
                # Beanie Pydantic模型可以从字典初始化
                res["buy_trade"] = TradeRecord(**res["buy_trade"])
                res["sell_trade"] = TradeRecord(**res["sell_trade"])
                processed_results.append(res)
            
            return processed_results
            
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"获取待打分KOL交易组合失败: {str(e)}")
            return []

    # === 结束：新增KOL打分聚合查询 ===