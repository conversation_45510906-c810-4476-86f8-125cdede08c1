from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from models.kol_wallet_activity import KOLWalletActivity
from .base_dao import BaseDAO
from pymongo import UpdateOne, DESCENDING
import traceback

class KOLWalletActivityDAO(BaseDAO[KOLWalletActivity]):
    """KOL钱包活动 DAO"""
    
    def __init__(self):
        super().__init__(KOLWalletActivity)
    
    async def find_by_wallet(
        self, 
        wallet_address: str,
        skip: int = 0,
        limit: int = 10
    ) -> List[KOLWalletActivity]:
        """根据钱包地址查找活动"""
        return await self.find_many(
            {"wallet": wallet_address},
            skip=skip,
            limit=limit,
            sort=[("timestamp", DESCENDING)]
        )
    
    async def find_by_tx_hash(self, tx_hash: str) -> Optional[KOLWalletActivity]:
        """根据交易哈希查找活动"""
        return await self.find_one({"tx_hash": tx_hash})
    
    async def find_by_event_type(
        self,
        wallet_address: str,
        event_type: str,
        skip: int = 0,
        limit: int = 10
    ) -> List[KOLWalletActivity]:
        """根据事件类型查找活动"""
        return await self.find_many(
            {"wallet": wallet_address, "event_type": event_type},
            skip=skip,
            limit=limit,
            sort=[("timestamp", DESCENDING)]
        )
    
    async def find_by_token(
        self,
        wallet_address: str,
        token_address: str,
        skip: int = 0,
        limit: int = 10
    ) -> List[KOLWalletActivity]:
        """根据代币地址查找活动"""
        return await self.find_many(
            {"wallet": wallet_address, "token.address": token_address},
            skip=skip,
            limit=limit,
            sort=[("timestamp", DESCENDING)]
        )
    
    async def find_recent_activities(
        self,
        wallet_address: str,
        days: int = 7,
        skip: int = 0,
        limit: int = 10
    ) -> List[KOLWalletActivity]:
        """查找最近的活动"""
        cutoff_time = int((datetime.utcnow() - timedelta(days=days)).timestamp())
        return await self.find_many(
            {"wallet": wallet_address, "timestamp": {"$gte": cutoff_time}},
            skip=skip,
            limit=limit,
            sort=[("timestamp", DESCENDING)]
        )
    
    async def save_activity(self, activity: KOLWalletActivity) -> KOLWalletActivity:
        """保存活动"""
        return await self.insert_one(activity)
    
    async def save_activities(self, activities: List[KOLWalletActivity]) -> int:
        """批量保存活动"""
        if not activities:
            return 0
        return await self.insert_many(activities)
    
    async def get_last_activity(self, wallet_address: str) -> Optional[KOLWalletActivity]:
        """获取指定钱包地址的最新活动记录 (按timestamp降序)。"""
        # 使用 self.model.find_one 来确保返回的是KOLWalletActivity实例
        return await self.model.find_one(
            {"wallet": wallet_address},
            sort=[("timestamp", DESCENDING)]
        )
    
    async def upsert_activity(self, activity_data: Dict[str, Any]) -> bool:
        """更新或插入活动数据"""
        tx_hash = activity_data.get("tx_hash")
        if not tx_hash:
            self.logger.error("交易哈希为空，无法更新活动")
            return False
            
        # 更新时间
        activity_data["updated_at"] = datetime.utcnow()
        
        # 检查是否存在
        existing = await self.find_by_tx_hash(tx_hash)
        if not existing:
            activity_data["created_at"] = datetime.utcnow()
        
        # 使用原子更新操作
        return await self.update_one(
            {"tx_hash": tx_hash},
            activity_data,
            upsert=True
        )
    
    async def upsert_activities(self, activities_data: List[Dict[str, Any]]) -> int:
        """批量更新或插入活动数据"""
        if not activities_data:
            return 0
            
        bulk_operations = []
        current_time_for_created_at = datetime.utcnow() # 用于 $setOnInsert
        
        for activity_data in activities_data:
            tx_hash = activity_data.get("tx_hash")
            wallet = activity_data.get("wallet")

            # activity_data 是由 KOLWalletActivity(**item_dict).model_dump() 产生的
            # 我们需要从中排除掉模型 default_factory 可能生成的 created_at 和 updated_at, 
            # 因为这些将由 MongoDB 操作符精确控制。
            set_data = {k: v for k, v in activity_data.items() if k not in ['created_at', 'updated_at', '_id', 'id']}
            
            update_operation = {
                "$set": set_data,
                "$setOnInsert": {"created_at": current_time_for_created_at}, # 只在插入时设置
                "$currentDate": {"updated_at": True} # 在插入或更新时都设置为当前数据库时间
            }
            
            bulk_operations.append(
                UpdateOne(
                    {"tx_hash": tx_hash, "wallet": wallet},
                    update_operation,
                    upsert=True
                )
            )
        
        # 执行批量更新
        try:
            result = await self.collection.bulk_write(bulk_operations)
            self.logger.info(f"批量更新完成: 修改 {result.modified_count} 条, 插入 {result.upserted_count} 条, 匹配 {result.matched_count} 条")
            # 返回实际发生写入操作的文档数 (插入的 + 真实被修改的)
            # 如果 upserted_count > 0, matched_count 可能是0 (对于新插入的)
            # 如果 modified_count > 0, matched_count 应该也 > 0
            # result.modified_count 只包含更新的，不含 upsert 的。
            # upserted_ids 列表包含了新插入文档的 _id。
            return len(result.upserted_ids) + result.modified_count
        except Exception as e:
            self.logger.error(f"批量更新活动数据失败: {str(e)}\n{traceback.format_exc()}")
            return 0
    
    async def count_by_wallet(self, wallet_address: str) -> int:
        """统计钱包活动数量"""
        return await self.count({"wallet": wallet_address})
    
    async def count_by_event_type(self, wallet_address: str, event_type: str) -> int:
        """统计特定事件类型的活动数量"""
        return await self.count({"wallet": wallet_address, "event_type": event_type})
    
    async def delete_by_tx_hash(self, tx_hash: str) -> bool:
        """根据交易哈希删除活动"""
        return await self.delete_one({"tx_hash": tx_hash})
    
    async def delete_by_wallet(self, wallet_address: str) -> int:
        """删除钱包的所有活动"""
        return await self.delete_many({"wallet": wallet_address}) 
    
    async def aggregate(self, pipeline: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """聚合查询"""
        return await self.collection.aggregate(pipeline).to_list()
    
    async def find_by_time_range(
        self,
        start_time: int,
        end_time: int,
        limit: int = 100,
        skip: int = 0,
        sort: List = None
    ) -> List[Dict[str, Any]]:
        """根据时间范围查询活动记录
        
        Args:
            start_time: 开始时间戳
            end_time: 结束时间戳
            limit: 限制返回的记录数
            skip: 跳过的记录数
            sort: 排序方式，默认为None
            
        Returns:
            List[Dict[str, Any]]: 活动记录列表
        """
        query = {"timestamp": {"$gte": start_time, "$lte": end_time}}
        
        # 使用原生的MongoDB集合查询，避免Beanie的模型验证
        cursor = self.collection.find(
            query,
            skip=skip,
            limit=limit
        )
        
        # 设置排序
        if sort:
            cursor = cursor.sort(sort)
        elif sort is None:
            # 默认按时间戳升序，然后按 _id 升序，确保确定性
            cursor = cursor.sort([("timestamp", 1), ("_id", 1)]) # 使用 1 代表升序
        
        # 将MongoDB文档转换为字典
        documents = await cursor.to_list(length=limit)
        
        # 确保关键字段存在且有默认值
        for doc in documents:
            # 处理_id字段（MongoDB特有）
            if '_id' in doc:
                doc['id'] = str(doc.pop('_id'))
                
            # 确保cost_usd和price_usd不为None
            if doc.get('cost_usd') is None:
                doc['cost_usd'] = "0"
            if doc.get('price_usd') is None:
                doc['price_usd'] = "0"
        
        return documents
    
    async def find_activities_in_time_range(
        self,
        wallet_addresses: List[str],
        start_timestamp: int,
        end_timestamp: int,
        event_types: List[str] = None,
        token_address: str = None,
        min_cost_usd: Optional[float] = None,  # 新增：最小交易金额过滤
        sort_by_timestamp: bool = True,
        limit: int = 100,
        skip: int = 0
    ) -> List[Dict[str, Any]]:
        """
        高性能的KOL活动查询方法 - 在数据库层面进行过滤
        
        这是为延迟计算优化的方法，利用数据库索引提高查询性能
        支持按交易金额过滤，确保只返回符合条件的KOL活动
        
        Args:
            wallet_addresses: KOL钱包地址列表
            start_timestamp: 开始时间戳
            end_timestamp: 结束时间戳
            event_types: 事件类型过滤，如['buy', 'sell']
            token_address: 代币地址过滤（可选）
            min_cost_usd: 最小交易金额过滤（可选），只返回cost_usd >= 该值的活动
            sort_by_timestamp: 是否按时间戳排序
            limit: 返回记录数限制
            skip: 跳过记录数
            
        Returns:
            List[Dict[str, Any]]: 过滤后的活动记录列表
        """
        # 构建查询条件 - 在数据库层面过滤，提高性能
        query = {
            "wallet": {"$in": wallet_addresses},
            "timestamp": {"$gte": start_timestamp, "$lte": end_timestamp}
        }
        
        # 添加事件类型过滤
        if event_types:
            query["event_type"] = {"$in": event_types}
        
        # 添加代币地址过滤
        if token_address:
            query["token_address"] = token_address
        
        # 新增：添加交易金额过滤
        if min_cost_usd is not None:
            # 处理cost_usd字段的字符串到数字转换
            # 使用MongoDB的$expr和$toDouble操作符进行类型转换和比较
            query["$expr"] = {
                "$and": [
                    {"$ne": ["$cost_usd", None]},  # cost_usd不为空
                    {"$ne": ["$cost_usd", ""]},    # cost_usd不为空字符串
                    {"$gte": [{"$toDouble": "$cost_usd"}, min_cost_usd]}  # 转换为数字后比较
                ]
            }
        
        # 构建查询
        cursor = self.collection.find(query, skip=skip, limit=limit)
        
        # 设置排序 - 优化查询性能
        if sort_by_timestamp:
            # 按时间戳降序排序，获取最新的活动
            cursor = cursor.sort([("timestamp", -1), ("_id", -1)])
        
        # 执行查询
        documents = await cursor.to_list(length=limit)
        
        # 处理返回数据
        for doc in documents:
            if '_id' in doc:
                doc['id'] = str(doc.pop('_id'))
            
            # 确保必要字段存在
            if doc.get('cost_usd') is None:
                doc['cost_usd'] = "0"
            if doc.get('price_usd') is None:
                doc['price_usd'] = "0"
        
        return documents
    