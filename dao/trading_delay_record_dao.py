"""
TradingDelayRecordDAO - 交易延迟记录数据访问对象

基于技术实现方案 @trading_delay_monitor_dev_plan_ai.md
实现交易延迟记录的CRUD操作和业务查询方法

主要功能：
- 基础CRUD操作：保存、查询延迟记录
- 统计分析功能：获取延迟统计信息  
- 分析查询功能：查找用于分析的延迟记录
- 信号抑制功能：查询相同策略和代币的最近记录

测试覆盖：TC-301~TC-309 (9个测试用例)
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from beanie import PydanticObjectId

from models.trading_delay_record import DelayStatus, TradingDelayRecord


class TradingDelayRecordDAO:
    """
    交易延迟记录数据访问对象
    
    提供对交易延迟记录的完整数据访问功能，包括：
    - CRUD操作：创建、查询、更新、删除延迟记录
    - 统计分析：延迟数据的聚合统计
    - 业务查询：支持信号抑制检查等业务逻辑
    
    所有方法都是异步的，适配Beanie ODM的异步特性。
    """
    
    async def save(self, record: TradingDelayRecord) -> TradingDelayRecord:
        """
        保存延迟记录到数据库
        
        Args:
            record: 要保存的延迟记录实例
            
        Returns:
            TradingDelayRecord: 保存后的记录（包含数据库生成的ID等字段）
            
        Raises:
            ValidationError: 当记录数据验证失败时
            DuplicateKeyError: 当违反唯一性约束时
            
        对应测试用例: TC-301
        """
        await record.save()
        return record
    
    async def find_by_trade_record_id(
        self, 
        trade_record_id: PydanticObjectId
    ) -> Optional[TradingDelayRecord]:
        """
        根据交易记录ID查找对应的延迟记录
        
        Args:
            trade_record_id: 交易记录的唯一标识符
            
        Returns:
            Optional[TradingDelayRecord]: 找到的延迟记录，如果不存在则返回None
            
        对应测试用例: TC-302, TC-303
        """
        # 使用Beanie ODM的字段比较操作，更类型安全和优雅
        return await TradingDelayRecord.find_one(
            TradingDelayRecord.trade_record_id == trade_record_id
        )
    
    async def get_delay_statistics(
        self, 
        start_time: datetime, 
        end_time: datetime
    ) -> List[Dict[str, Any]]:
        """
        获取指定时间范围内的延迟统计信息
        
        使用MongoDB聚合管道计算各种延迟状态的统计数据。
        
        Args:
            start_time: 统计开始时间（包含）
            end_time: 统计结束时间（包含）
            
        Returns:
            List[Dict[str, Any]]: 统计结果列表，每个元素包含：
                - _id: 延迟状态（DelayStatus枚举值）
                - count: 该状态的记录数量
                - avg_delay: 平均延迟时间（秒）
                - max_delay: 最大延迟时间（秒）
                - min_delay: 最小延迟时间（秒）
                
        对应测试用例: TC-304
        """
        pipeline = [
            {
                "$match": {
                    "created_at": {
                        "$gte": start_time,
                        "$lte": end_time
                    }
                }
            },
            {
                "$group": {
                    "_id": "$delay_status",
                    "count": {"$sum": 1},
                    "avg_delay": {"$avg": "$delay_seconds"},
                    "max_delay": {"$max": "$delay_seconds"},
                    "min_delay": {"$min": "$delay_seconds"}
                }
            }
        ]
        
        return await TradingDelayRecord.aggregate(pipeline).to_list()
    
    async def find_delays_for_analysis(
        self,
        start_time: datetime,
        end_time: datetime,
        limit: int = 1000
    ) -> List[TradingDelayRecord]:
        """
        查找用于分析的延迟记录
        
        返回指定时间范围内状态为CALCULATED的延迟记录，
        按创建时间倒序排列，用于数据分析和报告生成。
        
        Args:
            start_time: 查询开始时间（包含）
            end_time: 查询结束时间（包含）
            limit: 返回记录数量限制，默认1000条
            
        Returns:
            List[TradingDelayRecord]: 延迟记录列表，按创建时间降序排列
            
        对应测试用例: TC-305
        """
        # 使用Beanie ODM的字段比较操作，更类型安全和优雅
        return await TradingDelayRecord.find(
            TradingDelayRecord.created_at >= start_time,
            TradingDelayRecord.created_at <= end_time,
            TradingDelayRecord.delay_status == DelayStatus.CALCULATED
        ).sort(-TradingDelayRecord.created_at).limit(limit).to_list()
    
    async def find_recent_records_by_strategy_token(
        self,
        strategy_name: str,
        token_address: str,
        before_timestamp: datetime,
        limit: int = 10
    ) -> List[TradingDelayRecord]:
        """
        查询相同策略和代币的最近延迟记录
        
        用于信号抑制检查，查询在指定时间戳之前的最近记录。
        这个方法支持FR009需求中的信号抑制逻辑，通过检查历史记录
        来判断是否需要抑制当前信号的延迟计算。
        
        Args:
            strategy_name: 策略名称，必须完全匹配
            token_address: 代币地址，必须完全匹配  
            before_timestamp: 时间戳上限，使用严格小于比较（不包含该时间点）
            limit: 返回记录数限制，默认10条
            
        Returns:
            List[TradingDelayRecord]: 延迟记录列表，按创建时间降序排列
            
        Note:
            - 查询条件使用严格小于（<）而不是小于等于（<=）
            - 结果按创建时间降序排列，最新的记录在前
            - 主要用于信号抑制检查中的历史记录查询
            
        对应测试用例: TC-306, TC-307, TC-308, TC-309
        遵循需求文档FR009要求
        """
        # 使用Beanie ODM的字段比较操作，更类型安全和优雅
        return await TradingDelayRecord.find(
            TradingDelayRecord.strategy_name == strategy_name,
            TradingDelayRecord.token_address == token_address,
            TradingDelayRecord.created_at < before_timestamp
        ).sort(-TradingDelayRecord.created_at).limit(limit).to_list() 