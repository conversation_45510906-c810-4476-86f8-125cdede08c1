import unittest
from unittest.mock import patch, AsyncMock, MagicMock, call
from datetime import datetime, timezone
from typing import Optional

# Import the functions to be tested
from workflows.monitor_kol_activity.handler import (
    filter_target_tokens,
    validate,
    send_message_to_channel,
    _execute_kol_buy_strategy,
    extract_strategy_trading_overrides
)
from models.config import KolActivityConfig, SingleKolStrategyConfig, ApplicationConfig
from models.trade_record import TradeStatus as ModelTradeStatus
from models.signal import Signal
from models.trade_record import TradeRecord
from models.trade_execution import TradeExecutionResult, TradeStatus

class TestMonitorKolActivityHandler(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        from zoneinfo import ZoneInfo
        self.eastern_zone = ZoneInfo("Asia/Shanghai")
        self.mock_current_time_dt_eastern = datetime(2024, 8, 15, 12, 0, 0, tzinfo=self.eastern_zone)

    def _create_mock_strategy_config(self, name, min_amount, is_active=True, kol_account_min_count=2, auto_trade_enabled=True, kol_min_winrate: Optional[float] = None, **kwargs):
        base_config = {
            'strategy_name': name,
            'transaction_min_amount': min_amount,
            'is_active': is_active,
            'kol_account_min_count': kol_account_min_count,
            'auto_trade_enabled': auto_trade_enabled,
            'kol_min_winrate': kol_min_winrate,
            'transaction_lookback_hours': 24,
            'token_mint_lookback_hours': 72,
            'kol_account_min_txs': 5,
            'kol_account_max_txs': 1000,
            'sell_strategy_hours': 24,
            'sell_kol_ratio': 0.5,
            'same_token_notification_interval': 60,
            'wallet_private_key_env_var': "FAKE_PK_ENV_VAR",
            'wallet_address': "FAKE_WALLET_ADDR",
            'buy_amount_sol': 0.01,
            'buy_slippage_percentage': 1.0,
            'buy_priority_fee_sol': 0.00005,
        }
        base_config.update(kwargs)
        return SingleKolStrategyConfig(**base_config)

    @patch('workflows.monitor_kol_activity.handler.ConfigDAO')
    @patch('workflows.monitor_kol_activity.handler._execute_kol_buy_strategy', new_callable=AsyncMock)
    @patch('time.time', return_value=**********)
    async def test_filter_target_tokens_multi_strategy(self, mock_time, mock_execute_strategy, mock_config_dao_cls):
        mock_config_dao_instance = mock_config_dao_cls.return_value
        strategy1_config = self._create_mock_strategy_config("Aggro", 500)
        strategy2_config = self._create_mock_strategy_config("Conserve", 2000)
        strategy3_config = self._create_mock_strategy_config("Inactive", 100, is_active=False)
        mock_kol_activity_config_data = KolActivityConfig(buy_strategies=[strategy1_config, strategy2_config, strategy3_config])
        mock_config_dao_instance.get_config = AsyncMock(return_value=MagicMock(data=mock_kol_activity_config_data))
        
        token_data_s1_t1 = {'address': 'token_s1_t1', 'hit_kol_wallets': ['k1','k2']}
        token_data_s2_t1 = {'address': 'token_s2_t1', 'hit_kol_wallets': ['k3','k4']}
        token_data_s2_t2 = {'address': 'token_s2_t2', 'hit_kol_wallets': ['k5','k6']}
        
        async def execute_strategy_side_effect(strategy_params: SingleKolStrategyConfig):
            if strategy_params.strategy_name == "Aggro": return [token_data_s1_t1]
            elif strategy_params.strategy_name == "Conserve": return [token_data_s2_t1, token_data_s2_t2]
            return []
        mock_execute_strategy.side_effect = execute_strategy_side_effect
        
        result = await filter_target_tokens()
        
        self.assertEqual(len(result), 3)
        self.assertEqual(mock_execute_strategy.call_count, 2)
        passed_strategy_configs = [c[0][0] for c in mock_execute_strategy.call_args_list]
        self.assertIn(strategy1_config, passed_strategy_configs)
        self.assertIn(strategy2_config, passed_strategy_configs)

    @patch('workflows.monitor_kol_activity.handler.TelegramUserDAO')
    @patch('workflows.monitor_kol_activity.handler.TokenMessageSendHistoryDAO')
    @patch('workflows.monitor_kol_activity.handler.SignalDAO')
    @patch('workflows.monitor_kol_activity.handler.TradeRecordDAO')
    @patch('workflows.monitor_kol_activity.handler.ConfigDAO')
    @patch('workflows.monitor_kol_activity.handler.get_auto_trade_manager', new_callable=AsyncMock)
    @patch('workflows.monitor_kol_activity.handler.TelegramMessageSender')
    @patch('workflows.monitor_kol_activity.handler.get_current_time_dt')
    @patch('workflows.monitor_kol_activity.handler.Signal')
    @patch('workflows.monitor_kol_activity.handler.TradeRecord')
    @patch('jinja2.Template')
    async def test_send_message_to_channel_auto_trade_disabled_by_global_config(
        self, mock_template_cls, mock_trade_record_cls, mock_signal_cls, mock_get_current_time_dt,
        mock_sender_cls, mock_get_auto_trade_manager,
        mock_config_dao_cls, mock_trade_record_dao_cls, mock_signal_dao_cls,
        mock_history_dao_cls, mock_user_dao_cls
    ):
        mock_get_current_time_dt.return_value = self.mock_current_time_dt_eastern
        mock_user_dao_instance = mock_user_dao_cls.return_value
        mock_user_dao_instance.get_all_users = AsyncMock(return_value=[{'chat_id': 'test_chat'}])

        mock_signal_dao_instance = mock_signal_dao_cls.return_value
        mock_signal_dao_instance.collection.find_one = AsyncMock(return_value=None)

        mock_signal_instance = MagicMock()
        mock_signal_instance.save = AsyncMock()
        mock_signal_instance.id = "mock_signal_id"
        mock_signal_cls.return_value = mock_signal_instance

        # Mock the message sender
        mock_sender_instance = mock_sender_cls.return_value
        mock_sender_instance.send_message_to_user = AsyncMock(return_value=True)

        # Mock the history DAO
        mock_history_dao_instance = mock_history_dao_cls.return_value
        mock_history_dao_instance.insert_one = AsyncMock()

        # Mock the template
        mock_template_instance = mock_template_cls.return_value
        mock_template_instance.render = MagicMock(return_value="Test message")

        # Mock AutoTradeManager to return disabled
        mock_auto_trade_manager = mock_get_auto_trade_manager.return_value
        mock_auto_trade_manager.config_manager.is_enabled = AsyncMock(return_value=False)

        strategy_config = self._create_mock_strategy_config("Test", 500)
        item = {
            'token_data': {'address': 'addr1', 'name': 'Test Token', 'symbol': 'TT', 'market_cap': 10000},
            'strategy_config_snapshot': strategy_config.model_dump(),
            'hit_kol_wallets': ['k1']
        }

        await send_message_to_channel([item])

        # 验证调用了 get_auto_trade_manager 但是没有执行交易（因为全局配置禁用了自动交易）
        mock_get_auto_trade_manager.assert_called_once()
        mock_auto_trade_manager.config_manager.is_enabled.assert_called_once()
        # 验证没有调用 execute_trade
        mock_auto_trade_manager.execute_trade.assert_not_called()

class TestExecuteKolBuyStrategy(unittest.IsolatedAsyncioTestCase):

    def _create_mock_strategy_config(self, name, kol_min_winrate: Optional[float], **kwargs):
        base_config = {
            'strategy_name': name,
            'kol_min_winrate': kol_min_winrate,
            'transaction_lookback_hours': 24,
            'transaction_min_amount': 100,
            'kol_account_min_count': 1,
            'token_mint_lookback_hours': 48,
            'kol_account_min_txs': 10,
            'kol_account_max_txs': 500,
            'sell_kol_ratio': 0.5,
            'sell_strategy_hours': 24,
            'is_active': True,
        }
        base_config.update(kwargs)
        return SingleKolStrategyConfig(**base_config)

    @patch('workflows.monitor_kol_activity.handler.get_current_time_dt', return_value=datetime(2025, 6, 6, 12, 0, 0, tzinfo=timezone.utc))
    @patch('workflows.monitor_kol_activity.handler.KOLWalletActivityDAO')
    @patch('workflows.monitor_kol_activity.handler.TokenDAO')
    @patch('workflows.monitor_kol_activity.handler.check_kol_sell_ratio', new_callable=AsyncMock)
    async def test_execute_kol_buy_strategy_with_winrate_filter(self, mock_sell_check, mock_token_dao_cls, mock_activity_dao_cls, mock_time):
        mock_activity_dao_instance = mock_activity_dao_cls.return_value
        mock_activity_dao_instance.aggregate = AsyncMock(return_value=[
            {'_id': 'token1', 'kol_wallets': [{'wallet_address': 'kol1'}]}
        ])
        mock_token_dao_instance = mock_token_dao_cls.return_value
        mock_token_dao_instance.find_by_addresses = AsyncMock(return_value=[
             MagicMock(address='token1', first_mint_time=datetime(2025, 6, 6, 11, 0, 0, tzinfo=timezone.utc))
        ])
        mock_sell_check.return_value = (False, 0.0, 0)

        strategy_params = self._create_mock_strategy_config("WinrateTest", kol_min_winrate=0.6)
        
        await _execute_kol_buy_strategy(strategy_params)

        mock_activity_dao_instance.aggregate.assert_called_once()
        pipeline = mock_activity_dao_instance.aggregate.call_args[0][0]
        
        self.assertTrue(any('gmgn_wallet_stats' in stage.get('$lookup', {}).get('from', '') for stage in pipeline))
        winrate_filter_stage = next((stage for stage in pipeline if '$addFields' in stage and 'kol_wallets' in stage['$addFields']), None)
        self.assertIn({'$gt': ['$$enriched_kol.winrate', 0.6]}, winrate_filter_stage['$addFields']['kol_wallets']['$filter']['cond']['$and'])

    @patch('workflows.monitor_kol_activity.handler.get_current_time_dt', return_value=datetime(2025, 6, 6, 12, 0, 0, tzinfo=timezone.utc))
    @patch('workflows.monitor_kol_activity.handler.KOLWalletActivityDAO')
    @patch('workflows.monitor_kol_activity.handler.TokenDAO')
    @patch('workflows.monitor_kol_activity.handler.check_kol_sell_ratio', new_callable=AsyncMock)
    async def test_execute_kol_buy_strategy_without_winrate_filter(self, mock_sell_check, mock_token_dao_cls, mock_activity_dao_cls, mock_time):
        mock_activity_dao_instance = mock_activity_dao_cls.return_value
        mock_activity_dao_instance.aggregate = AsyncMock(return_value=[])
        strategy_params = self._create_mock_strategy_config("NoWinrateTest", kol_min_winrate=None)
        
        await _execute_kol_buy_strategy(strategy_params)

        mock_activity_dao_instance.aggregate.assert_called_once()
        pipeline = mock_activity_dao_instance.aggregate.call_args[0][0]

        self.assertFalse(any('gmgn_wallet_stats' in stage.get('$lookup', {}).get('from', '') for stage in pipeline))
        self.assertTrue(any("'records': {'$push': '$$ROOT'}" in str(stage) for stage in pipeline))

    @patch('workflows.monitor_kol_activity.handler.get_current_time_dt', return_value=datetime(2025, 6, 6, 12, 0, 0, tzinfo=timezone.utc))
    @patch('workflows.monitor_kol_activity.handler.KOLWalletActivityDAO')
    @patch('workflows.monitor_kol_activity.handler.TokenDAO')
    @patch('workflows.monitor_kol_activity.handler.check_kol_sell_ratio', new_callable=AsyncMock)
    async def test_execute_kol_buy_strategy_with_zero_winrate(self, mock_sell_check, mock_token_dao_cls, mock_activity_dao_cls, mock_time):
        mock_activity_dao_instance = mock_activity_dao_cls.return_value
        mock_activity_dao_instance.aggregate = AsyncMock(return_value=[])
        strategy_params = self._create_mock_strategy_config("ZeroWinrateTest", kol_min_winrate=0)
        
        await _execute_kol_buy_strategy(strategy_params)
        
        pipeline = mock_activity_dao_instance.aggregate.call_args[0][0]
        self.assertFalse(any('gmgn_wallet_stats' in stage.get('$lookup', {}).get('from', '') for stage in pipeline))

class TestStrategyParameterExtraction(unittest.IsolatedAsyncioTestCase):
    """
    测试策略参数提取功能的测试类
    用于验证Bug修复：策略交易参数提取逻辑不完整
    """

    def _create_complete_strategy_config(self) -> SingleKolStrategyConfig:
        """
        创建包含所有策略级别交易参数的完整配置
        """
        return SingleKolStrategyConfig(
            strategy_name="complete_strategy",
            transaction_min_amount=500,
            is_active=True,
            kol_account_min_count=2,
            transaction_lookback_hours=24,
            token_mint_lookback_hours=72,
            kol_account_min_txs=5,
            kol_account_max_txs=1000,
            sell_strategy_hours=24,
            sell_kol_ratio=0.5,
            same_token_notification_interval=60,
            wallet_private_key_env_var="TEST_PK_ENV_VAR",
            wallet_address="TEST_WALLET_ADDR",
            # 基础交易参数（5个）
            buy_amount_sol=0.05,
            buy_slippage_percentage=2.0,
            buy_priority_fee_sol=0.0001,
            sell_slippage_percentage=3.0,
            sell_priority_fee_sol=0.0002,
            # 滑点递增重试覆盖配置（9个）
            strategy_enable_slippage_retry=True,
            strategy_slippage_increment_percentage=0.5,
            strategy_max_slippage_percentage=10.0,
            strategy_enable_buy_slippage_retry=True,
            strategy_buy_slippage_increment_percentage=0.3,
            strategy_max_buy_slippage_percentage=8.0,
            strategy_enable_sell_slippage_retry=True,
            strategy_sell_slippage_increment_percentage=0.4,
            strategy_max_sell_slippage_percentage=12.0,
            # 重试间隔覆盖配置（6个）
            strategy_retry_delay_seconds=2.0,
            strategy_retry_delay_strategy="exponential",
            strategy_max_retry_delay_seconds=30.0,
            strategy_slippage_error_delay_seconds=5.0,
            strategy_buy_retry_delay_seconds=1.5,
            strategy_sell_retry_delay_seconds=2.5
        )

    def test_current_incomplete_parameter_extraction(self):
        """
        测试当前不完整的参数提取逻辑（复现Bug）
        验证当前代码只提取了3个参数，缺失了17个参数
        """
        strategy_config = self._create_complete_strategy_config()
        strategy_snapshot = strategy_config.model_dump()
        
        # 模拟当前handler.py中第464-473行的不完整提取逻辑
        current_incomplete_overrides = {}
        
        if strategy_snapshot.get('buy_amount_sol') is not None:
            current_incomplete_overrides['buy_amount_sol'] = strategy_snapshot['buy_amount_sol']

        if strategy_snapshot.get('buy_slippage_percentage') is not None:
            current_incomplete_overrides['buy_slippage_percentage'] = strategy_snapshot['buy_slippage_percentage']

        if strategy_snapshot.get('buy_priority_fee_sol') is not None:
            current_incomplete_overrides['buy_priority_fee_sol'] = strategy_snapshot['buy_priority_fee_sol']
        
        # 验证Bug：只提取了3个参数
        self.assertEqual(len(current_incomplete_overrides), 3)
        self.assertIn('buy_amount_sol', current_incomplete_overrides)
        self.assertIn('buy_slippage_percentage', current_incomplete_overrides)
        self.assertIn('buy_priority_fee_sol', current_incomplete_overrides)
        
        # 验证缺失的17个参数（使用实际的字段名称）
        expected_missing_params = [
            # 基础交易参数（2个）
            'sell_slippage_percentage', 'sell_priority_fee_sol',
            # 滑点递增重试覆盖配置（9个）
            'strategy_enable_slippage_retry', 'strategy_slippage_increment_percentage', 'strategy_max_slippage_percentage',
            'strategy_enable_buy_slippage_retry', 'strategy_buy_slippage_increment_percentage', 'strategy_max_buy_slippage_percentage',
            'strategy_enable_sell_slippage_retry', 'strategy_sell_slippage_increment_percentage', 'strategy_max_sell_slippage_percentage',
            # 重试间隔覆盖配置（6个）
            'strategy_retry_delay_seconds', 'strategy_retry_delay_strategy', 'strategy_max_retry_delay_seconds',
            'strategy_slippage_error_delay_seconds', 'strategy_buy_retry_delay_seconds', 'strategy_sell_retry_delay_seconds'
        ]
        
        for param in expected_missing_params:
            self.assertNotIn(param, current_incomplete_overrides, 
                           f"参数 {param} 不应该在当前不完整的提取结果中")
        
        # 验证这些参数在原始配置中确实存在且不为None
        for param in expected_missing_params:
            self.assertIsNotNone(strategy_snapshot.get(param), 
                               f"参数 {param} 在原始配置中应该存在且不为None")

    def test_extract_strategy_trading_overrides_complete(self):
        """
        测试新的完整参数提取方法
        验证能够正确提取所有20个策略级别的交易参数
        """
        strategy_config = self._create_complete_strategy_config()
        
        # 调用新的参数提取方法
        extracted_overrides = extract_strategy_trading_overrides(strategy_config)
        
        # 验证提取了所有20个参数
        expected_params = [
            # 基础交易参数（5个）
            'buy_amount_sol', 'buy_slippage_percentage', 'buy_priority_fee_sol',
            'sell_slippage_percentage', 'sell_priority_fee_sol',
            # 滑点递增重试覆盖配置（9个）
            'strategy_enable_slippage_retry', 'strategy_slippage_increment_percentage', 'strategy_max_slippage_percentage',
            'strategy_enable_buy_slippage_retry', 'strategy_buy_slippage_increment_percentage', 'strategy_max_buy_slippage_percentage',
            'strategy_enable_sell_slippage_retry', 'strategy_sell_slippage_increment_percentage', 'strategy_max_sell_slippage_percentage',
            # 重试间隔覆盖配置（6个）
            'strategy_retry_delay_seconds', 'strategy_retry_delay_strategy', 'strategy_max_retry_delay_seconds',
            'strategy_slippage_error_delay_seconds', 'strategy_buy_retry_delay_seconds', 'strategy_sell_retry_delay_seconds'
        ]
        
        self.assertEqual(len(extracted_overrides), 20)
        
        for param in expected_params:
            self.assertIn(param, extracted_overrides, f"参数 {param} 应该被提取")
            self.assertIsNotNone(extracted_overrides[param], f"参数 {param} 的值不应该为None")
        
        # 验证具体的值是否正确
        self.assertEqual(extracted_overrides['buy_amount_sol'], 0.05)
        self.assertEqual(extracted_overrides['buy_slippage_percentage'], 2.0)
        self.assertEqual(extracted_overrides['buy_priority_fee_sol'], 0.0001)
        self.assertEqual(extracted_overrides['sell_slippage_percentage'], 3.0)
        self.assertEqual(extracted_overrides['sell_priority_fee_sol'], 0.0002)
        self.assertEqual(extracted_overrides['strategy_enable_slippage_retry'], True)
        self.assertEqual(extracted_overrides['strategy_slippage_increment_percentage'], 0.5)
        self.assertEqual(extracted_overrides['strategy_retry_delay_seconds'], 2.0)
        self.assertEqual(extracted_overrides['strategy_retry_delay_strategy'], "exponential")

    def test_extract_strategy_trading_overrides_partial_config(self):
        """
        测试新的参数提取方法处理部分配置的情况
        验证只提取非None的参数
        """
        partial_config = SingleKolStrategyConfig(
            strategy_name="partial_strategy",
            transaction_min_amount=500,
            is_active=True,
            kol_account_min_count=2,
            transaction_lookback_hours=24,
            token_mint_lookback_hours=72,
            kol_account_min_txs=5,
            kol_account_max_txs=1000,
            sell_strategy_hours=24,
            sell_kol_ratio=0.5,
            same_token_notification_interval=60,
            wallet_private_key_env_var="TEST_PK_ENV_VAR",
            wallet_address="TEST_WALLET_ADDR",
            # 只设置部分参数
            buy_amount_sol=0.02,
            buy_slippage_percentage=1.5,
            strategy_enable_slippage_retry=True,
            strategy_retry_delay_seconds=3.0
            # 其他参数保持默认值None
        )
        
        extracted_overrides = extract_strategy_trading_overrides(partial_config)
        
        # 验证只提取了非None的参数
        expected_non_none_params = ['buy_amount_sol', 'buy_slippage_percentage', 'strategy_enable_slippage_retry', 'strategy_retry_delay_seconds']
        self.assertEqual(len(extracted_overrides), len(expected_non_none_params))
        
        for param in expected_non_none_params:
            self.assertIn(param, extracted_overrides)
        
        # 验证具体值
        self.assertEqual(extracted_overrides['buy_amount_sol'], 0.02)
        self.assertEqual(extracted_overrides['buy_slippage_percentage'], 1.5)
        self.assertEqual(extracted_overrides['strategy_enable_slippage_retry'], True)
        self.assertEqual(extracted_overrides['strategy_retry_delay_seconds'], 3.0)

    def test_extract_strategy_trading_overrides_empty_config(self):
        """
        测试新的参数提取方法处理空配置的情况
        验证当所有交易参数都为None时返回空字典
        """
        empty_config = SingleKolStrategyConfig(
            strategy_name="empty_strategy",
            transaction_min_amount=500,
            is_active=True,
            kol_account_min_count=2,
            transaction_lookback_hours=24,
            token_mint_lookback_hours=72,
            kol_account_min_txs=5,
            kol_account_max_txs=1000,
            sell_strategy_hours=24,
            sell_kol_ratio=0.5,
            same_token_notification_interval=60,
            wallet_private_key_env_var="TEST_PK_ENV_VAR",
            wallet_address="TEST_WALLET_ADDR"
            # 所有交易参数保持默认值None
        )
        
        extracted_overrides = extract_strategy_trading_overrides(empty_config)
        
        # 验证返回空字典
        self.assertEqual(len(extracted_overrides), 0)
        self.assertEqual(extracted_overrides, {})

    def test_extract_strategy_trading_overrides_with_serialized_dict(self):
        """
        测试函数能够处理从model_dump()产生的字典，包含额外字段和序列化值
        这个测试验证了修复bug后的健壮性
        """
        # 模拟从model_dump()产生的字典，包含可能导致重构失败的字段
        serialized_strategy_dict = {
            'strategy_name': 'test_strategy',
            'buy_amount_sol': 0.1,
            'buy_slippage_percentage': 5.0,
            'strategy_enable_slippage_retry': True,
            'strategy_max_slippage_percentage': 15.0,
            
            # 可能导致重构失败的额外字段
            '_internal_field': 'some_value',
            'computed_field': datetime.now().isoformat(),  # 序列化的datetime
            'unknown_field': {'nested': 'data'},
            'null_field': None,
            
            # 模拟Pydantic的内部字段
            '__fields_set__': {'strategy_name', 'buy_amount_sol'},
            '__dict__': {},
        }
        
        # 调用修复后的函数 - 应该能够正常处理而不抛出异常
        result = extract_strategy_trading_overrides(serialized_strategy_dict)
        
        # 验证结果只包含有效的交易参数
        expected_result = {
            'buy_amount_sol': 0.1,
            'buy_slippage_percentage': 5.0,
            'strategy_enable_slippage_retry': True,
            'strategy_max_slippage_percentage': 15.0
        }
        
        assert result == expected_result
        assert len(result) == 4
        assert '_internal_field' not in result
        assert 'computed_field' not in result
        assert 'unknown_field' not in result
        assert '__fields_set__' not in result

    def test_extract_strategy_trading_overrides_dict_vs_object_consistency(self):
        """
        测试函数使用字典和对象参数产生一致的结果
        """
        from models.config import SingleKolStrategyConfig
        
        # 创建一个策略配置对象
        strategy_config = SingleKolStrategyConfig(
            strategy_name='consistency_test',
            transaction_lookback_hours=1,
            transaction_min_amount=100,
            kol_account_min_count=2,
            kol_account_min_txs=5,
            kol_account_max_txs=1000,
            token_mint_lookback_hours=24,
            same_token_notification_interval=60,
            sell_kol_ratio=0.5,
            is_active=True,
            buy_amount_sol=0.2,
            buy_slippage_percentage=3.0,
            strategy_enable_slippage_retry=True,
            strategy_max_slippage_percentage=12.0
        )
        
        # 从对象提取
        result_from_object = extract_strategy_trading_overrides(strategy_config)
        
        # 从字典提取
        strategy_dict = strategy_config.model_dump()
        result_from_dict = extract_strategy_trading_overrides(strategy_dict)
        
        # 两个结果应该完全一致
        assert result_from_object == result_from_dict
        assert len(result_from_object) == len(result_from_dict)
        
        # 验证包含预期的字段
        expected_fields = {
            'buy_amount_sol': 0.2,
            'buy_slippage_percentage': 3.0,
            'strategy_enable_slippage_retry': True,
            'strategy_max_slippage_percentage': 12.0
        }
        assert result_from_object == expected_fields
        assert result_from_dict == expected_fields

if __name__ == '__main__':
    unittest.main()