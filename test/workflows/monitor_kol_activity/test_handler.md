# KOL活动监控处理逻辑单元测试
创建日期：2025-05-12
更新日期：2025-06-16
测试方法：自动化测试
测试级别：单元测试

## 测试用例
| 用例方法 | 用例标题 | 前置条件 | 输入 | 预期输出 | 实际输出 | 状态 |
| --- | --- | --- | --- | --- | --- | --- |
| `test_filter_target_tokens_multi_strategy` | 测试多策略场景下筛选目标代币 | `ConfigDAO` 配置了多个买入策略 (部分激活，部分未激活)，`_execute_kol_buy_strategy` 对不同策略返回不同代币列表 | - | 返回所有激活策略找到的代币列表，并附带各自的策略快照，未激活策略不执行 | 匹配预期 | 通过 |
| `test_filter_target_tokens_backward_compatibility_single_strategy` | 测试旧版单策略配置的兼容性 | `ConfigDAO` 配置了一个旧版的、直接为 `SingleKolStrategyConfig` 类型的 `kol_activity` 配置 | - | 正确解析旧版配置，并返回该策略找到的代币列表及策略快照 | 匹配预期 | 通过 |
| `test_validate` | 测试基本数据验证逻辑 | - | 包含任意字典的列表或空列表 | `True` | `True` | 通过 |
| `test_send_message_to_channel_send_new_multi_strategy` | 测试多策略场景下发送新消息到频道 (无自动交易) | 多个用户，`TelegramMessageSender` 发送成功，`SignalDAO` 无近期信号, 自动交易相关配置为关闭或不完整 | 包含多个由不同策略触发的代币数据和策略快照列表 (auto_trade_enabled=False) | 成功发送消息到所有用户，创建对应的信号记录，无自动交易执行 | 匹配预期 | 通过 |
| `test_send_message_to_channel_send_fail_multi_strategy` | 测试多策略场景下消息发送失败处理 (无自动交易) | 单个用户，`TelegramMessageSender` 发送失败，`SignalDAO` 无近期信号, 自动交易相关配置为关闭或不完整 | 单个代币数据和策略快照 (auto_trade_enabled=False) | 创建新信号，尝试向所有用户发送消息失败，记录发送失败历史，创建SKIPPED的TradeRecord | 匹配预期 | 通过 |
| `test_timestamp_timezone_consistency` | 测试`handler.py`中时间戳是否一致使用东八区时间 | `get_current_time_dt` 返回东八区时间，`datetime.utcnow` 返回UTC时间 | 包含一个由测试策略触发的代币数据和策略快照 | `Signal`的`trigger_timestamp`和消息发送历史的`created_at`均使用东八区时间 | 匹配预期 | 通过 |
| `test_multi_strategy_different_notification_intervals` | 测试多策略场景下同一代币不同通知间隔 | 同一个代币同时配置两个不同策略，分别设置不同的通知间隔，策略1有近期Signal，策略2无近期Signal | 包含两个不同策略但指向同一代币的数据列表 | 策略1因近期Signal存在而被跳过；策略2创建新信号并尝试向所有用户发送消息 | 匹配预期 | 通过 |
| `test_execute_kol_buy_strategy_skips_if_sell_condition_met` | 测试买入时若同时满足卖出条件则跳过 | `_execute_kol_buy_strategy` 被调用，`check_kol_sell_ratio` mock返回 `True` (卖出条件满足) | `SingleKolStrategyConfig` 实例 | 返回空的代币列表 | 匹配预期 | 通过 |
| `test_execute_kol_buy_strategy_proceeds_if_sell_condition_not_met` | 测试买入时若卖出条件未满足则继续 | `_execute_kol_buy_strategy` 被调用，`check_kol_sell_ratio` mock返回 `False` (卖出条件未满足) | `SingleKolStrategyConfig` 实例 | 返回包含符合条件代币的列表 | 匹配预期 | 通过 |
| `test_execute_kol_buy_strategy_no_hit_kols_skips_sell_check` | 测试当KOL购买聚合结果中没有有效KOL钱包(`hit_kol_wallets`为空)时，应跳过卖出条件检查，且不生成买入信号 | `_execute_kol_buy_strategy` 被调用，聚合结果配置为使 `hit_kol_wallets` 为空 | `SingleKolStrategyConfig` 实例 | 返回空的代币列表，`check_kol_sell_ratio` 未被调用 | 匹配预期 | 通过 |
| `test_send_message_to_channel_amount_formatting` | 测试交易金额格式化显示 | AutoTradeManager配置完整, 交易成功 | 单个代币数据和策略快照 (含完整交易配置) | 创建PENDING后SUCCESS的`TradeRecord`, 关联到`Signal`, 用户消息含正确格式化的金额信息 | 匹配预期 | 通过 |
| `test_send_message_to_channel_auto_trade_buy_fail_and_admin_notification` | 测试自动买入交易失败 | AutoTradeManager返回失败结果, 管理员ID已配置 | 单个代币数据和策略快照 | 创建PENDING后FAILED的`TradeRecord`, 用户消息含失败详情, **管理员通知由AutoTradeManager内部处理** | 匹配预期 | 通过 |
| `test_send_message_to_channel_auto_trade_skipped_config_incomplete` | 测试因配置不完整导致自动交易跳过 | AutoTradeManager返回SKIPPED状态 | 单个代币数据和策略快照 | 创建SKIPPED的`TradeRecord` (原因:配置不完整), 用户消息提示跳过 | 匹配预期 | 通过 |
| `test_send_message_to_channel_auto_trade_disabled_by_strategy` | 测试因策略配置禁用导致自动交易跳过 | 钱包地址为None (配置不完整) | 单个代币数据和策略快照 | 直接创建SKIPPED的`TradeRecord` (原因:策略禁用), 用户消息提示跳过 | 匹配预期 | 通过 |
| `test_send_message_to_channel_auto_trade_unhandled_exception` | 测试自动交易过程中发生未捕获的异常 | AutoTradeManager.execute_trade 抛出 `ValueError`, 管理员ID已配置 | 单个代币数据和策略快照 | 创建PENDING后FAILED的`TradeRecord` (原因: 未捕获异常), 用户消息含失败详情, **管理员通知由AutoTradeManager内部处理** | 匹配预期 | 通过 |
| `test_send_message_to_channel_signal_creation_failure` | 测试创建信号记录失败 | `SignalDAO.insert_signals` 返回 `None` 或空列表 | 单个代币数据和策略快照 (auto_trade_enabled=false) | `insert_signals` 被调用后, 后续的交易记录、消息发送、历史记录均不执行 | 待测试 | 待执行 |
| `test_send_message_to_channel_template_render_failure` | 测试Telegram消息模板渲染失败 | `Template.render` 抛出异常 | 单个代币数据和策略快照 (auto_trade_enabled=false) | 尝试渲染模板失败后，发送回退格式的普通消息给用户，历史记录照常 | 匹配预期 | 通过 |
| `test_current_incomplete_parameter_extraction` | 测试当前不完整的参数提取逻辑（复现Bug） | 创建包含所有20个策略级别交易参数的完整配置 | 完整的策略配置对象 | 当前逻辑只提取3个参数，缺失17个参数 | 匹配预期 | 通过 |
| `test_extract_strategy_trading_overrides_complete` | 测试新的完整参数提取方法 | 创建包含所有20个策略级别交易参数的完整配置 | 完整的策略配置对象 | 正确提取所有20个非None的策略级别交易参数 | 匹配预期 | 通过 |
| `test_extract_strategy_trading_overrides_partial_config` | 测试新的参数提取方法处理部分配置 | 创建只包含部分策略级别交易参数的配置 | 部分配置的策略对象 | 只提取非None的参数，忽略None值参数 | 匹配预期 | 通过 |
| `test_extract_strategy_trading_overrides_empty_config` | 测试新的参数提取方法处理空配置 | 创建不包含任何策略级别交易参数的基础配置 | 基础配置的策略对象 | 返回空字典，不提取任何参数 | 匹配预期 | 通过 |

## 测试覆盖范围
- ✅ 多策略配置处理
- ✅ 向后兼容性
- ✅ 数据验证
- ✅ 消息发送逻辑
- ✅ 自动交易集成
- ✅ KOL胜率过滤
- ✅ 策略参数提取（Bug修复验证）

## 最近更新
- 2025-06-16: 新增策略参数提取相关测试用例，验证Bug修复效果 