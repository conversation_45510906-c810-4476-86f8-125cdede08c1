# -*- coding: utf-8 -*-
"""
ProcessNode单次执行后停止问题的测试用例

测试目标：
1. 复现ProcessNode只执行一次就停止的Bug
2. 验证消息管理器和本地队列的状态
3. 测试修复效果
"""

import asyncio
import logging
import pytest
import pytest_asyncio
import time
from unittest.mock import Mock, AsyncMock, patch
from typing import List, Any

from utils.workflows.nodes.process_node import ProcessNode
from utils.workflows.message_queue.message_queue import KafkaQueue, Message
from utils.workflows.message_queue.message_manager import KafkaMessageManager
from utils.workflows.parallel_control import ParallelControl


@pytest.mark.asyncio
class TestProcessNodeSingleExecution:
    """测试ProcessNode单次执行后停止的问题"""

    @pytest.fixture
    def mock_kafka_queue(self):
        """创建模拟的Kafka队列"""
        queue = Mock(spec=KafkaQueue)
        queue.name = "test_queue"
        queue.get_last_offset = AsyncMock(return_value=0)
        queue.pull = AsyncMock(return_value=[])
        return queue

    @pytest.fixture
    def mock_parallel_control(self):
        """创建模拟的并行控制器"""
        control = Mock(spec=ParallelControl)
        control.acquire_resource = AsyncMock()
        control.release_resource = AsyncMock()
        return control

    @pytest.fixture
    def test_process_node(self, mock_kafka_queue, mock_parallel_control):
        """创建测试用的ProcessNode"""
        
        class TestProcessNode(ProcessNode):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, **kwargs)
                self.processed_messages = []
                self.process_call_count = 0
            
            async def process_item(self, item: Any):
                """模拟处理逻辑"""
                self.processed_messages.append(item)
                yield f"processed_{item}"
        
        node = TestProcessNode(
            name="TestProcessNode",
            input_queue=mock_kafka_queue
        )
        node.parallel_control = mock_parallel_control
        
        # 设置消息管理器
        node.message_manager = KafkaMessageManager(
            kafka_queue=mock_kafka_queue,
            parallel_control=mock_parallel_control,
            kafka_receive_count=10,
            kafka_receive_timeout=1000,
            local_queue_length=100,
            interval_to_ack=100,
            interval_to_put_message=100
        )
        
        return node

    async def test_process_node_single_execution_bug_reproduction(self, test_process_node, mock_kafka_queue):
        """测试ProcessNode的超时机制和连续处理能力（修复后应该通过）
        
        修复前的Bug场景：
        1. Kafka队列有多条消息积压
        2. 本地队列为空时get_message()无限阻塞
        3. ProcessNode的wrap_process()循环被卡住，无法处理更多消息
        
        修复后的预期行为：
        1. process()方法每次处理一条消息是正确的设计
        2. 当本地队列为空时，receive_message()应该超时返回None
        3. ProcessNode应该能够连续处理所有可用消息
        """
        
        # 模拟Kafka队列有大量积压消息
        mock_messages = [
            Message(data=f"message_{i}", metadata={"message_id": str(i)})
            for i in range(5)
        ]
        
        # 模拟第一次拉取成功，后续拉取为空
        mock_kafka_queue.pull.side_effect = [
            mock_messages,  # 第一次拉取成功
            [],             # 后续拉取为空
            [],
            []
        ]
        
        # 启动消息管理器
        coroutines = await test_process_node.message_manager.start()
        
        # 启动消息拉取协程
        pull_task = asyncio.create_task(coroutines[0])  # put_messages_to_local
        ack_task = asyncio.create_task(coroutines[1])   # ack_messages
        
        # 等待消息被拉取到本地队列
        await asyncio.sleep(0.2)
        
        # 验证本地队列有消息
        local_queue_size = test_process_node.message_manager.local_queue.qsize()
        print(f"本地队列大小: {local_queue_size}")
        
        # 连续执行ProcessNode处理，模拟wrap_process()的行为
        process_count = 0
        max_attempts = 10  # 最多尝试10次
        
        try:
            for attempt in range(max_attempts):
                # 执行一次处理
                await test_process_node.process()
                
                # 检查是否处理了消息
                current_processed = len(test_process_node.processed_messages)
                if current_processed > process_count:
                    process_count = current_processed
                    print(f"完成第 {process_count} 次处理")
                else:
                    # 没有处理新消息，可能本地队列为空
                    print(f"第 {attempt + 1} 次尝试未处理新消息，本地队列可能为空")
                    if test_process_node.message_manager.local_queue.empty():
                        print("本地队列确实为空，停止处理")
                        break
                
        except Exception as e:
            print(f"处理过程中发生异常: {e}")
        
        finally:
            # 清理任务
            pull_task.cancel()
            ack_task.cancel()
            try:
                await pull_task
            except asyncio.CancelledError:
                pass
            try:
                await ack_task
            except asyncio.CancelledError:
                pass
        
        # 验证结果
        print(f"总共处理了 {process_count} 条消息")
        print(f"处理的消息内容: {test_process_node.processed_messages}")
        
        # 修复后应该能处理所有5条消息
        assert process_count >= 1, "ProcessNode应该至少处理一条消息"
        assert process_count == 5, f"应该处理所有5条消息，实际处理了 {process_count} 条"

    async def test_message_manager_local_queue_status(self, test_process_node, mock_kafka_queue):
        """测试消息管理器和本地队列的状态"""
        
        # 模拟消息
        mock_messages = [
            Message(data=f"message_{i}", metadata={"message_id": str(i)})
            for i in range(3)
        ]
        
        mock_kafka_queue.pull.return_value = mock_messages
        
        # 启动消息管理器
        coroutines = await test_process_node.message_manager.start()
        pull_task = asyncio.create_task(coroutines[0])
        ack_task = asyncio.create_task(coroutines[1])
        
        try:
            # 等待消息被拉取
            await asyncio.sleep(0.2)
            
            # 检查本地队列状态
            local_queue = test_process_node.message_manager.local_queue
            queue_size = local_queue.qsize()
            
            print(f"本地队列大小: {queue_size}")
            print(f"本地队列是否为空: {local_queue.empty()}")
            print(f"本地队列是否已满: {local_queue.full()}")
            
            # 验证消息管理器状态
            assert queue_size > 0, "本地队列应该有消息"
            assert not local_queue.empty(), "本地队列不应该为空"
            
            # 测试消息获取
            message = await test_process_node.message_manager.get_message()
            assert message is not None, "应该能够获取到消息"
            
            print(f"获取到消息: {message.data}")
            
        finally:
            pull_task.cancel()
            ack_task.cancel()
            try:
                await pull_task
            except asyncio.CancelledError:
                pass
            try:
                await ack_task
            except asyncio.CancelledError:
                pass

    async def test_receive_message_blocking_behavior(self, test_process_node):
        """测试receive_message的阻塞行为"""
        
        # 测试在本地队列为空时的行为
        local_queue = test_process_node.message_manager.local_queue
        assert local_queue.empty(), "本地队列应该为空"
        
        # 测试receive_message是否会阻塞
        start_time = time.time()
        timeout = 2.0
        
        try:
            # 使用超时来测试是否会无限阻塞
            message = await asyncio.wait_for(
                test_process_node.receive_message(),
                timeout=timeout
            )
            # 如果能获取到消息，说明没有阻塞
            print(f"获取到消息: {message}")
        except asyncio.TimeoutError:
            elapsed = time.time() - start_time
            print(f"receive_message在 {elapsed:.2f} 秒后超时，说明发生了阻塞")
            # 这是预期的行为，因为队列为空时应该阻塞
        except Exception as e:
            print(f"receive_message发生异常: {e}")

    async def test_process_node_continuous_execution(self, test_process_node, mock_kafka_queue):
        """测试ProcessNode的连续执行能力（修复后的预期行为）"""
        
        # 模拟持续的消息流
        message_batches = [
            [Message(data=f"batch1_msg_{i}", metadata={"message_id": str(100 + i)}) for i in range(2)],
            [Message(data=f"batch2_msg_{i}", metadata={"message_id": str(200 + i)}) for i in range(2)],
            []  # 最后返回空列表
        ]
        
        mock_kafka_queue.pull.side_effect = message_batches
        
        # 启动消息管理器
        coroutines = await test_process_node.message_manager.start()
        pull_task = asyncio.create_task(coroutines[0])
        ack_task = asyncio.create_task(coroutines[1])
        
        try:
            # 等待所有消息被拉取
            await asyncio.sleep(0.5)
            
            # 连续执行处理
            process_count = 0
            max_processes = 4  # 预期处理4条消息
            
            for _ in range(max_processes):
                if test_process_node.message_manager.local_queue.empty():
                    print(f"本地队列为空，停止处理。已处理 {process_count} 条消息")
                    break
                
                await test_process_node.process()
                process_count += 1
                print(f"完成第 {process_count} 次处理")
            
            # 验证处理结果
            print(f"总共处理了 {process_count} 条消息")
            print(f"处理的消息: {test_process_node.processed_messages}")
            
            # 修复后应该能处理所有消息
            assert process_count == 4, f"应该处理4条消息，实际处理了 {process_count} 条"
            
        finally:
            pull_task.cancel()
            ack_task.cancel()
            try:
                await pull_task
            except asyncio.CancelledError:
                pass
            try:
                await ack_task
            except asyncio.CancelledError:
                pass


class LogCapture:
    """日志捕获工具"""
    
    def __init__(self, logger_name: str, level: int = logging.WARNING):
        self.logger_name = logger_name
        self.level = level
        self.captured_logs = []
        self.handler = None
        self.logger = None
    
    def start(self):
        """开始捕获日志"""
        self.logger = logging.getLogger(self.logger_name)
        self.handler = logging.Handler()
        self.handler.setLevel(self.level)
        self.handler.emit = self._capture_log
        self.logger.addHandler(self.handler)
    
    def stop(self):
        """停止捕获日志"""
        if self.logger and self.handler:
            self.logger.removeHandler(self.handler)
    
    def _capture_log(self, record):
        """捕获日志记录"""
        self.captured_logs.append(record)
    
    def has_warning(self, message: str) -> bool:
        """检查是否包含指定的警告消息"""
        for record in self.captured_logs:
            if message in record.getMessage():
                return True
        return False
    
    def get_logs(self) -> List[str]:
        """获取捕获的日志消息"""
        return [record.getMessage() for record in self.captured_logs]


if __name__ == "__main__":
    # 运行测试
    import sys
    import os
    
    # 添加项目根目录到路径
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    
    # 设置日志级别
    logging.basicConfig(level=logging.INFO)
    
    # 运行测试
    pytest.main([__file__, "-v", "-s"])