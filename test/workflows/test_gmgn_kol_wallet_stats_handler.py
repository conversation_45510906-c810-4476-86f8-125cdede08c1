"""
GMGN KOL钱包统计数据工作流处理器测试
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from workflows.gmgn_kol_wallet_stats.handler import (
    generate_wallet_addresses,
    process_wallet_stats,
    validate_wallet_data
)


class TestGmgnKolWalletStatsHandler:
    """GMGN KOL钱包统计数据工作流处理器测试类"""
    
    def setup_method(self):
        """测试前置设置"""
        self.test_wallet_address = "7BgBvyjrZX1YKz4oh9mjb8ZScatkkwb8DzFx4sxXEP5P"
        
        # 测试用的API数据
        self.test_api_data = {
            "buy": "10",
            "sell": "5", 
            "pnl": "150.75",
            "winrate": "0.8",
            "balance": "2.5",
            "risk": {
                "token_active": 3,
                "token_honeypot": 0,
                "token_honeypot_ratio": 0.0
            }
        }
    
    @pytest.mark.asyncio
    async def test_generate_wallet_addresses_success(self):
        """测试成功生成钱包地址和时间窗口组合"""
        with patch('workflows.gmgn_kol_wallet_stats.handler.gmgn_wallet_stats_dao') as mock_dao:
            # 设置每个period返回不同的钱包地址
            def mock_find_wallets(limit, hours_threshold, period):
                if period == "all":
                    return ["wallet1", "wallet2"]
                elif period == "30d":
                    return ["wallet1", "wallet3"]
                elif period == "7d":
                    return ["wallet1", "wallet3"]
                elif period == "1d":
                    return ["wallet2", "wallet4"]
                return []
            
            mock_dao.find_wallets_need_stats_update = AsyncMock(side_effect=mock_find_wallets)
            
            # 执行测试
            result = await generate_wallet_addresses()
            
            # 验证结果
            assert isinstance(result, list)
            assert len(result) == 8  # 2 + 2 + 2 + 2 = 8个任务（all, 30d, 7d, 1d各2个）
            
            # 验证任务格式
            for task in result:
                assert isinstance(task, dict)
                assert "wallet_address" in task
                assert "period" in task
                assert task["period"] in ["all", "30d", "7d", "1d"]
            
            # 验证DAO调用次数
            assert mock_dao.find_wallets_need_stats_update.call_count == 4
    
    @pytest.mark.asyncio
    async def test_process_wallet_stats_success(self):
        """测试成功处理钱包统计数据"""
        task_data = {
            "wallet_address": self.test_wallet_address,
            "period": "7d"
        }
        
        # Mock爬虫
        mock_stats = MagicMock()
        mock_stats.raw_data = self.test_api_data
        
        with patch('workflows.gmgn_kol_wallet_stats.handler.GmgnWalletStatsSpider') as mock_spider_class:
            mock_spider = AsyncMock()
            mock_spider.get_wallet_stats = AsyncMock(return_value=mock_stats)
            mock_spider.close = AsyncMock()
            mock_spider_class.return_value = mock_spider
            
            result = await process_wallet_stats(task_data)
            
            # 验证结果
            assert result is not None
            assert result["wallet_address"] == self.test_wallet_address
            assert result["period"] == "7d"
            assert result["stats_data"] == self.test_api_data
            
            # 验证爬虫调用
            mock_spider.get_wallet_stats.assert_called_once_with(self.test_wallet_address, period="7d")
            mock_spider.close.assert_called_once()
    
    def test_validate_wallet_data_success(self):
        """测试有效数据验证"""
        valid_data = {
            "wallet_address": self.test_wallet_address,
            "period": "7d",
            "stats_data": self.test_api_data
        }
        
        result = validate_wallet_data(valid_data)
        assert result is True
    
    def test_validate_wallet_data_invalid_period(self):
        """测试无效period的验证"""
        invalid_data = {
            "wallet_address": self.test_wallet_address,
            "period": "invalid_period",
            "stats_data": self.test_api_data
        }
        
        result = validate_wallet_data(invalid_data)
        assert result is False 