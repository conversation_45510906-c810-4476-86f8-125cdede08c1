#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略选择器集成测试

测试策略选择器在 DailyBacktestVerificationRunner 中的集成情况
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import date, timedelta

from workflows.daily_backtest_verification.components.verification_runner import (
    DailyBacktestVerificationRunner,
    VerificationResult,
    VerificationStatus,
    StrategyVerificationResult
)
from workflows.daily_backtest_verification.components.strategy_selector import (
    StrategySelector,
    MissingStrategyError
)


class TestStrategyIntegration:
    """测试策略选择器在验证运行器中的集成"""
    
    def setup_method(self):
        """设置测试环境"""
        self.runner = DailyBacktestVerificationRunner()
        
    @pytest.mark.asyncio
    async def test_validate_strategy_configuration_success(self):
        """测试策略配置验证成功"""
        # 准备测试数据
        config_data = {
            'strategies': {
                'kol_strategy_v1': {'enabled': True}
            }
        }
        
        # 执行验证
        results = await self.runner._validate_strategy_configuration(config_data, 'kol_strategy_v1')
        
        # 验证结果
        assert len(results) == 1
        assert results[0].success
        assert results[0].strategy_name == 'kol_strategy_v1'
    
    @pytest.mark.asyncio
    async def test_validate_strategy_configuration_custom_strategy(self):
        """测试自定义策略名称的处理"""
        # 准备测试数据
        config_data = {
            'strategies': {
                'custom_strategy': {'enabled': True}
            }
        }
        
        # 执行验证，现在应该成功（自动生成默认配置）
        results = await self.runner._validate_strategy_configuration(config_data, 'custom_strategy')
        # 验证结果
        assert len(results) == 1
        assert results[0].success
        assert results[0].strategy_name == 'custom_strategy'
    
    @pytest.mark.asyncio
    async def test_validate_strategy_configuration_missing_strategy(self):
        """测试缺少策略名称的处理"""
        # 准备测试数据
        config_data = {
            'strategies': {}
        }
        
        # 执行验证并检查异常
        with pytest.raises(MissingStrategyError):
            await self.runner._validate_strategy_configuration(config_data, None)
    
    def test_extract_strategy_name_from_config_success(self):
        """测试从配置中成功提取策略名称"""
        # 准备测试数据
        config_data = {
            'strategy': 'kol_strategy_v2',
            'other_config': 'value'
        }
        
        # 执行提取
        strategy_name = self.runner._extract_strategy_name_from_config(config_data)
        
        # 验证结果
        assert strategy_name == 'kol_strategy_v2'
    
    def test_extract_strategy_name_from_config_missing(self):
        """测试配置中缺少策略名称"""
        # 准备测试数据
        config_data = {
            'other_config': 'value'
        }
        
        # 执行提取并检查异常
        with pytest.raises(MissingStrategyError, match="配置中缺少策略名称"):
            self.runner._extract_strategy_name_from_config(config_data)
    
    def test_extract_strategy_name_from_config_empty_strategy(self):
        """测试配置中策略名称为空"""
        # 准备测试数据
        config_data = {
            'strategy': '',
            'other_config': 'value'
        }
        
        # 执行提取并检查异常
        with pytest.raises(MissingStrategyError, match="配置中缺少策略名称"):
            self.runner._extract_strategy_name_from_config(config_data)
    
    def test_extract_strategy_name_from_config_non_dict(self):
        """测试非字典格式的配置数据"""
        # 准备测试数据
        config_data = "not a dict"
        
        # 执行提取并检查异常
        with pytest.raises(MissingStrategyError, match="无法从配置中提取策略名称"):
            self.runner._extract_strategy_name_from_config(config_data)
    
    @pytest.mark.asyncio
    @patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._load_configurations')
    @patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._validate_strategy_configuration')
    @patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._execute_backtest_with_validation')
    @patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._read_backtest_result_with_validation')
    @patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._generate_and_send_report')
    async def test_run_verification_with_strategy_validation(
        self, 
        mock_generate_report,
        mock_read_result,
        mock_execute_backtest,
        mock_validate_strategy,
        mock_load_config
    ):
        """测试完整的验证流程包含策略验证"""
        # 设置模拟返回值
        config_data = {'strategy': 'kol_strategy_v1'}
        mock_load_config.return_value = (config_data, '/tmp/config.json', ['123456'])
        
        # 创建成功的策略验证结果
        successful_strategy_result = StrategyVerificationResult(
            strategy_name="kol_strategy_v1",
            success=True,
            error_message=None
        )
        mock_validate_strategy.return_value = [successful_strategy_result]
        
        mock_backtest_result = Mock()
        mock_backtest_result.result_file_path = '/tmp/result.json'
        mock_backtest_result.execution_time = 10.5
        mock_execute_backtest.return_value = mock_backtest_result
        
        mock_read_result.return_value = {'trades': []}
        
        mock_verification_result = VerificationResult(
            status=VerificationStatus.SUCCESS,
            date=date.today(),
            report_text="验证成功"
        )
        mock_generate_report.return_value = mock_verification_result
        
        # 执行验证
        result = await self.runner.run_verification()
        
        # 验证调用顺序和结果
        mock_load_config.assert_called_once()
        mock_execute_backtest.assert_called_once()
        mock_read_result.assert_called_once()
        mock_generate_report.assert_called_once()
        
        assert result.status == VerificationStatus.SUCCESS
    
    @pytest.mark.asyncio
    @patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._load_configurations')
    async def test_run_verification_strategy_validation_failure(
        self, 
        mock_load_config
    ):
        """测试策略验证失败时的处理"""
        # 设置模拟返回值 - 无效策略
        config_data = {'strategy': 'invalid_strategy'}
        mock_load_config.return_value = (config_data, '/tmp/config.json', ['123456'])
        
        # 执行验证
        result = await self.runner.run_verification()
        
        # 验证结果 - 现在自定义策略应该成功，不再抛出错误
        # 如果有其他错误，应该是配置相关的错误，而不是策略验证错误
        if result.status == VerificationStatus.ERROR:
            # 如果有错误，应该不是策略相关的错误
            assert result.error_message is not None
            assert "无效的策略名称" not in result.error_message
            assert "InvalidStrategyError" not in result.error_message


class TestStrategySelector:
    """测试策略选择器的初始化和基本功能"""
    
    def test_strategy_selector_initialization(self):
        """测试策略选择器在运行器中的初始化"""
        runner = DailyBacktestVerificationRunner()
        
        # 验证策略选择器已正确初始化
        assert hasattr(runner, 'strategy_selector')
        assert isinstance(runner.strategy_selector, StrategySelector)
    
    def test_strategy_selector_available_strategies(self):
        """测试策略选择器可用策略列表"""
        runner = DailyBacktestVerificationRunner()
        
        # 获取可用策略
        available_strategies = runner.strategy_selector.get_available_strategies()
        
        # 验证包含预期的策略
        expected_strategies = [
            'kol_strategy_v1',
            'kol_strategy_v2',
            'kol_strategy_v3',
            'kol_strategy_v4'
        ]
        
        for strategy in expected_strategies:
            assert strategy in available_strategies