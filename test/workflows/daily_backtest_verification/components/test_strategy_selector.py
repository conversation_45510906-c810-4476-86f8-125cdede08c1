#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略选择器测试用例

测试策略选择器的核心功能：
- 策略名称验证
- 异常处理
- 单策略模式强制指定策略名称

技术实现方案：@daily_backtest_verification_dev_plan_ai.md
测试用例设计：@daily_backtest_verification_test_cases_ai.md
"""

import pytest
from unittest.mock import Mock, patch
from workflows.daily_backtest_verification.components.strategy_selector import (
    StrategySelector,
    MissingStrategyError
)


class TestStrategySelector:
    """策略选择器测试类"""
    
    def setup_method(self):
        """测试前置设置"""
        self.selector = StrategySelector()
    
    def test_init_strategy_selector(self):
        """测试策略选择器初始化"""
        # 验证初始化成功
        assert self.selector is not None
        assert hasattr(self.selector, 'validate_strategy_name')
        assert hasattr(self.selector, 'get_strategy_config')
    
    def test_validate_strategy_name_valid_strategies(self):
        """测试有效策略名称验证"""
        valid_strategies = [
            'kol_strategy_v1',
            'kol_strategy_v2', 
            'kol_strategy_v3',
            'kol_strategy_v4',
            'kol_strategy_v5'
        ]
        
        for strategy in valid_strategies:
            # 应该不抛出异常
            self.selector.validate_strategy_name(strategy)
    
    def test_validate_strategy_name_custom_strategies(self):
        """测试自定义策略名称验证"""
        custom_strategies = [
            'invalid_strategy',
            'unknown_strategy',
            'kol_strategy_v6',  # 不存在的版本
            'random_name',
            '胜率高',
            '稳健投资'
        ]
        
        for strategy in custom_strategies:
            # 现在应该接受任何非空策略名称，不抛出异常
            self.selector.validate_strategy_name(strategy)
    
    def test_validate_strategy_name_none_input(self):
        """测试None输入的策略名称验证"""
        with pytest.raises(MissingStrategyError) as exc_info:
            self.selector.validate_strategy_name(None)
        assert "策略名称不能为空" in str(exc_info.value)
    
    def test_validate_strategy_name_empty_string(self):
        """测试空字符串输入的策略名称验证"""
        with pytest.raises(MissingStrategyError) as exc_info:
            self.selector.validate_strategy_name("")
        assert "策略名称不能为空" in str(exc_info.value)
    
    def test_validate_strategy_name_whitespace_only(self):
        """测试仅包含空白字符的策略名称验证"""
        whitespace_inputs = ["   ", "\t", "\n", "\r\n"]
        
        for whitespace in whitespace_inputs:
            with pytest.raises(MissingStrategyError) as exc_info:
                self.selector.validate_strategy_name(whitespace)
            assert "策略名称不能为空" in str(exc_info.value)
    
    def test_get_strategy_config_valid_strategy(self):
        """测试获取有效策略配置"""
        strategy_name = 'kol_strategy_v1'
        config = self.selector.get_strategy_config(strategy_name)
        
        # 验证配置结构
        assert isinstance(config, dict)
        assert 'strategy_name' in config
        assert config['strategy_name'] == strategy_name
        assert 'description' in config
        assert 'parameters' in config
    
    def test_get_strategy_config_custom_strategy(self):
        """测试获取自定义策略配置"""
        custom_strategy = 'invalid_strategy'
        config = self.selector.get_strategy_config(custom_strategy)
        
        # 验证自动生成的默认配置
        assert isinstance(config, dict)
        assert config['strategy_name'] == custom_strategy
        assert 'description' in config
        assert 'parameters' in config
        assert isinstance(config['parameters'], dict)
    
    def test_get_strategy_config_none_input(self):
        """测试获取None策略配置"""
        with pytest.raises(MissingStrategyError):
            self.selector.get_strategy_config(None)
    
    def test_strategy_selector_integration(self):
        """测试策略选择器集成功能"""
        # 测试完整的策略选择流程
        strategy_name = 'kol_strategy_v2'
        
        # 1. 验证策略名称
        self.selector.validate_strategy_name(strategy_name)
        
        # 2. 获取策略配置
        config = self.selector.get_strategy_config(strategy_name)
        
        # 3. 验证配置完整性
        assert config['strategy_name'] == strategy_name
        assert len(config['description']) > 0
        assert isinstance(config['parameters'], dict)
    
    def test_error_messages_contain_helpful_info(self):
        """测试错误消息包含有用信息"""
        # 测试MissingStrategyError消息
        with pytest.raises(MissingStrategyError) as exc_info:
            self.selector.validate_strategy_name(None)
        
        error_msg = str(exc_info.value)
        assert '不能为空' in error_msg or 'cannot be empty' in error_msg.lower()
        
        # 测试空字符串
        with pytest.raises(MissingStrategyError) as exc_info:
            self.selector.validate_strategy_name('')
        
        error_msg = str(exc_info.value)
        assert '不能为空' in error_msg or 'cannot be empty' in error_msg.lower()


class TestStrategyExceptions:
    """策略异常类测试"""
    
    def test_missing_strategy_error_inheritance(self):
        """测试MissingStrategyError继承关系"""
        error = MissingStrategyError("test message")
        assert isinstance(error, Exception)
        assert str(error) == "test message"