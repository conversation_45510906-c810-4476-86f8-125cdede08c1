import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import date, datetime
from pathlib import Path
from dataclasses import dataclass
from typing import Dict, Any, List

# 这些导入会失败，因为我们还没有实现VerificationRunner
from workflows.daily_backtest_verification.components.verification_runner import (
    DailyBacktestVerificationRunner,
    VerificationResult,
    VerificationError,
    StrategyVerificationResult
)


class TestDailyBacktestVerificationRunner:
    """测试每日回测校验运行器"""

    @pytest.fixture
    def runner(self):
        """创建测试用的校验运行器实例"""
        return DailyBacktestVerificationRunner()

    @pytest.fixture
    def mock_config(self):
        """模拟配置数据"""
        return {
            'backtest_script_path': '/path/to/backtest_script.py',
            'config_template_path': '/path/to/config_template.yaml',
            'output_directory': '/path/to/output',
            'admin_chat_ids': ['123456789', '987654321'],
            'backtest_params': {
                'start_date': '2025-01-10',
                'end_date': '2025-01-11'
            }
        }

    @pytest.fixture
    def mock_backtest_result(self):
        """模拟回测执行结果"""
        from workflows.daily_backtest_verification.components.backtest_executor import BacktestResult
        return BacktestResult(
            success=True,
            output='回测执行成功',
            error='',
            execution_time=120.5,
            result_file_path='/path/to/output/result.json'
        )

    @pytest.fixture
    def mock_result_data(self):
        """模拟回测结果数据"""
        from workflows.daily_backtest_verification.components.backtest_result_reader import BacktestResultData, BacktestTradeInfo
        from datetime import datetime
        
        trades = [
            BacktestTradeInfo(
                symbol='BTC',
                timestamp=datetime.fromisoformat('2025-01-11T10:00:00+00:00'),
                action='buy',
                amount=1.0,
                price=45000.0
            ),
            BacktestTradeInfo(
                symbol='ETH',
                timestamp=datetime.fromisoformat('2025-01-11T11:00:00+00:00'),
                action='sell',
                amount=10.0,
                price=3200.0
            )
        ]
        
        return BacktestResultData(
            trades=trades,
            total_trades=15,
            start_time=datetime.fromisoformat('2025-01-11T10:00:00+00:00'),
            end_time=datetime.fromisoformat('2025-01-11T12:00:00+00:00')
        )

    @pytest.fixture
    def mock_verification_result(self):
        """模拟校验结果"""
        return VerificationResult(
            status='success',
            date=date(2025, 1, 11),
            total_trades=15,
            total_signals=15,
            matched_trades=15,
            execution_time=120.5,
            report_text="✅ 校验通过\n日期: 2025-01-11\n回测交易数: 15\n实际信号数: 15",
            error_message=None
        )

    @pytest.mark.asyncio
    async def test_run_verification_success(self, runner, mock_config, mock_backtest_result, mock_result_data, mock_verification_result):
        """测试成功的校验流程"""
        target_date = date(2025, 1, 11)
        
        # 创建成功的策略验证结果
        successful_strategy_result = StrategyVerificationResult(
            strategy_name="test_strategy",
            success=True,
            error_message=None
        )
        
        with patch.object(runner, '_load_configurations', return_value=(mock_config, '/tmp/temp_config.yaml', mock_config['admin_chat_ids'])) as mock_load_config, \
             patch.object(runner, '_validate_strategy_configuration', return_value=[successful_strategy_result]) as mock_validate, \
             patch.object(runner, '_execute_backtest_with_validation', return_value=mock_backtest_result) as mock_execute, \
             patch.object(runner, '_read_backtest_result_with_validation', return_value=mock_result_data) as mock_read, \
             patch.object(runner, '_generate_and_send_report', return_value=mock_verification_result) as mock_generate:
            
            result = await runner.run_verification(target_date)
            
            # 验证各个组件的方法被正确调用
            mock_load_config.assert_called_once()
            mock_validate.assert_called_once_with(mock_config, None)
            mock_execute.assert_called_once_with('/tmp/temp_config.yaml', mock_config['admin_chat_ids'])
            mock_read.assert_called_once_with(mock_backtest_result.result_file_path, mock_config['admin_chat_ids'])
            mock_generate.assert_called_once_with(mock_result_data, target_date, mock_backtest_result.execution_time, mock_config['admin_chat_ids'], [successful_strategy_result])
            
            # 验证返回结果
            assert result.status == 'success'
            assert result.date == target_date
            assert result.total_trades == 15
            assert result.total_signals == 15
            assert result.matched_trades == 15
            assert result.execution_time == 120.5
            assert result.error_message is None
            assert "✅ 校验通过" in result.report_text

    @pytest.mark.asyncio
    async def test_run_verification_default_date(self, runner):
        """测试使用默认日期（昨天）"""
        from datetime import timedelta
        expected_date = date.today() - timedelta(days=1)
        
        mock_result = VerificationResult(
            status='success',
            date=expected_date,
            total_trades=0,
            total_signals=0,
            matched_trades=0,
            execution_time=60.0,
            report_text="测试报告",
            error_message=None
        )
        
        with patch.object(runner, '_load_configurations', return_value=({}, '/tmp/temp.yaml', [])), \
             patch.object(runner, '_validate_strategy_configuration', return_value=None), \
             patch.object(runner, '_execute_backtest_with_validation') as mock_execute, \
             patch.object(runner, '_read_backtest_result_with_validation', return_value={}), \
             patch.object(runner, '_generate_and_send_report', return_value=mock_result):
            
            mock_execute.return_value = MagicMock(success=True, result_file_path='/tmp/result.json', execution_time=60.0)
            
            # 不传入日期参数
            result = await runner.run_verification()
            
            # 验证结果中使用了昨天的日期
            assert result.date == expected_date

    @pytest.mark.asyncio
    async def test_run_verification_config_load_failure(self, runner):
        """测试配置加载失败"""
        target_date = date(2025, 1, 11)
        
        with patch.object(runner, '_load_configurations', side_effect=Exception("配置文件不存在")) as mock_load, \
             patch.object(runner, '_handle_verification_exception') as mock_handle_exception:
            
            error_result = VerificationResult(
                status='error',
                date=target_date,
                total_trades=0,
                total_signals=0,
                matched_trades=0,
                execution_time=0,
                report_text="❌ 失败: 配置文件不存在",
                error_message="配置文件不存在"
            )
            mock_handle_exception.return_value = error_result
            
            result = await runner.run_verification(target_date)
            
            # 验证配置加载被调用
            mock_load.assert_called_once()
            
            # 验证异常处理被调用
            mock_handle_exception.assert_called_once()
            
            # 验证返回错误结果
            assert result.status == 'error'
            assert result.date == target_date
            assert "配置文件不存在" in result.error_message
            assert "❌ 失败" in result.report_text

    @pytest.mark.asyncio
    async def test_run_verification_backtest_execution_failure(self, runner, mock_config):
        """测试回测执行失败"""
        target_date = date(2025, 1, 11)
        
        # 创建成功的策略验证结果
        successful_strategy_result = StrategyVerificationResult(
            strategy_name="test_strategy",
            success=True,
            error_message=None
        )
        
        with patch.object(runner, '_load_configurations', return_value=(mock_config, '/tmp/temp_config.yaml', mock_config['admin_chat_ids'])), \
             patch.object(runner, '_validate_strategy_configuration', return_value=[successful_strategy_result]), \
             patch.object(runner, '_execute_backtest_with_validation', side_effect=Exception("回测执行失败")) as mock_execute, \
             patch.object(runner, '_handle_verification_exception') as mock_handle_exception:
            
            error_result = VerificationResult(
                status='error',
                date=target_date,
                total_trades=0,
                total_signals=0,
                matched_trades=0,
                execution_time=0,
                report_text="❌ 失败: 回测执行失败",
                error_message="回测执行失败"
            )
            mock_handle_exception.return_value = error_result
            
            result = await runner.run_verification(target_date)
            
            # 验证回测执行被调用
            mock_execute.assert_called_once()
            
            # 验证异常处理被调用
            mock_handle_exception.assert_called_once()
            
            # 验证返回错误结果
            assert result.status == 'error'
            assert result.date == target_date
            assert "回测执行失败" in result.error_message

    @pytest.mark.asyncio
    async def test_run_verification_result_read_failure(self, runner, mock_config, mock_backtest_result):
        """测试结果读取失败"""
        target_date = date(2025, 1, 11)
        
        # 创建成功的策略验证结果
        successful_strategy_result = StrategyVerificationResult(
            strategy_name="test_strategy",
            success=True,
            error_message=None
        )
        
        with patch.object(runner, '_load_configurations', return_value=(mock_config, '/tmp/temp_config.yaml', mock_config['admin_chat_ids'])), \
             patch.object(runner, '_validate_strategy_configuration', return_value=[successful_strategy_result]), \
             patch.object(runner, '_execute_backtest_with_validation', return_value=mock_backtest_result) as mock_execute, \
             patch.object(runner, '_read_backtest_result_with_validation', side_effect=Exception("结果读取失败")) as mock_read, \
             patch.object(runner, '_handle_verification_exception') as mock_handle_exception:
            
            error_result = VerificationResult(
                status='error',
                date=target_date,
                total_trades=0,
                total_signals=0,
                matched_trades=0,
                execution_time=0,
                report_text="❌ 失败: 结果文件损坏",
                error_message="结果文件损坏"
            )
            mock_handle_exception.return_value = error_result
            
            result = await runner.run_verification(target_date)
            
            # 验证结果读取被调用
            mock_read.assert_called_once()
            
            # 验证异常处理被调用
            mock_handle_exception.assert_called_once()
            
            # 验证返回错误结果
            assert result.status == 'error'
            assert "结果文件损坏" in result.error_message

    @pytest.mark.asyncio
    async def test_run_verification_mismatch_scenario(self, runner, mock_config, mock_backtest_result):
        """测试数据不匹配场景"""
        target_date = date(2025, 1, 11)
        mismatch_data = {
            'total_trades': 12,  # 不匹配
            'total_signals': 15,  # 不匹配
            'matched_trades': 10,  # 不匹配
            'trades': []
        }
        
        mismatch_result = VerificationResult(
            status='mismatch',
            date=target_date,
            total_trades=12,
            total_signals=15,
            matched_trades=10,
            execution_time=120.5,
            report_text="⚠️ 不匹配: 5个信号未生成交易",
            error_message=None
        )
        
        # 创建成功的策略验证结果
        successful_strategy_result = StrategyVerificationResult(
            strategy_name="test_strategy",
            success=True,
            error_message=None
        )
        
        with patch.object(runner, '_load_configurations', return_value=(mock_config, '/tmp/temp_config.yaml', mock_config['admin_chat_ids'])), \
             patch.object(runner, '_validate_strategy_configuration', return_value=[successful_strategy_result]), \
             patch.object(runner, '_execute_backtest_with_validation', return_value=mock_backtest_result), \
             patch.object(runner, '_read_backtest_result_with_validation', return_value=mismatch_data), \
             patch.object(runner, '_generate_and_send_report', return_value=mismatch_result):
            
            result = await runner.run_verification(target_date)
            
            # 验证结果状态为不匹配
            assert result.status == 'mismatch'
            assert result.total_trades == 12
            assert result.total_signals == 15
            assert result.matched_trades == 10
            assert "⚠️ 不匹配" in result.report_text
            assert "5个信号未生成交易" in result.report_text

    @pytest.mark.asyncio
    async def test_run_verification_notification_failure(self, runner, mock_config, mock_backtest_result, mock_result_data):
        """测试通知发送失败"""
        target_date = date(2025, 1, 11)
        
        success_result = VerificationResult(
            status='success',
            date=target_date,
            total_trades=10,
            total_signals=10,
            matched_trades=10,
            execution_time=120.5,
            report_text="✅ 校验通过",
            error_message=None
        )
        
        with patch.object(runner, '_load_configurations', return_value=(mock_config, '/tmp/temp_config.yaml', mock_config['admin_chat_ids'])), \
             patch.object(runner, '_validate_strategy_configuration', return_value=None), \
             patch.object(runner, '_execute_backtest_with_validation', return_value=mock_backtest_result), \
             patch.object(runner, '_read_backtest_result_with_validation', return_value=mock_result_data), \
             patch.object(runner, '_generate_and_send_report', side_effect=Exception("Telegram发送失败")), \
             patch.object(runner, '_handle_verification_exception', return_value=success_result):
            
            # 执行校验（不应该因为通知失败而失败）
            result = await runner.run_verification(target_date)
            
            # 验证主要校验仍然成功
            assert result.status == 'success'
            assert result.date == target_date

    @pytest.mark.asyncio
    async def test_cleanup_temp_files(self, runner):
        """测试临时文件清理"""
        # 创建临时文件
        temp_files = ['/tmp/test_file1.yaml', '/tmp/test_file2.json']
        
        with patch('pathlib.Path.exists', return_value=True) as mock_exists, \
             patch('pathlib.Path.unlink') as mock_unlink:
            
            runner._cleanup_temp_files(temp_files)
            
            # 验证文件删除被调用
            assert mock_unlink.call_count == len(temp_files)

    def test_generate_verification_report_success(self, runner):
        """测试生成成功校验报告"""
        backtest_result = {
            'total_trades': 10,
            'total_signals': 10,
            'matched_trades': 10
        }
        target_date = date(2025, 1, 11)
        execution_time = 90.5
        
        result = runner._generate_verification_report(backtest_result, target_date, execution_time, [])
        
        assert result.status == 'success'
        assert result.date == target_date
        assert result.total_trades == 10
        assert result.total_signals == 10
        assert result.matched_trades == 10
        assert result.execution_time == 90.5
        assert "✅ 通过" in result.report_text

    def test_generate_verification_report_mismatch(self, runner):
        """测试生成不匹配校验报告"""
        backtest_result = {
            'total_trades': 8,
            'total_signals': 10,
            'matched_trades': 7
        }
        target_date = date(2025, 1, 11)
        execution_time = 85.0
        
        result = runner._generate_verification_report(backtest_result, target_date, execution_time, [])
        
        assert result.status == 'mismatch'
        assert "⚠️ 不匹配" in result.report_text
        assert "3个信号未生成交易" in result.report_text

    def test_format_report_text_success(self, runner, mock_verification_result):
        """测试格式化成功报告文本"""
        mock_verification_result.status = 'success'
        
        report_text = runner._format_report_text(mock_verification_result)
        
        assert "📊 每日回测校验报告" in report_text
        assert "✅ 通过" in report_text
        assert "2025-01-11" in report_text
        assert "回测交易数: 15" in report_text
        assert "实际信号数: 15" in report_text

    def test_format_report_text_mismatch(self, runner):
        """测试格式化不匹配报告文本"""
        result = VerificationResult(
            status='mismatch',
            date=date(2025, 1, 11),
            total_trades=8,
            total_signals=10,
            matched_trades=7,
            execution_time=85.0,
            report_text="",
            error_message=None
        )
        
        report_text = runner._format_report_text(result)
        
        assert "⚠️ 不匹配" in report_text
        assert "⚠️ 匹配差异: 3个信号未生成交易" in report_text

    def test_create_error_result(self, runner):
        """测试创建错误结果"""
        target_date = date(2025, 1, 11)
        error_message = "测试错误信息"
        
        result = runner._create_error_result(target_date, error_message)
        
        assert result.status == 'error'
        assert result.date == target_date
        assert result.error_message == error_message
        assert "❌ 失败" in result.report_text
        assert error_message in result.report_text

    @pytest.mark.asyncio
    async def test_send_verification_notification_success(self, runner):
        """测试发送校验通知成功"""
        mock_result = MagicMock()
        mock_result.report_text = "测试报告"
        admin_chat_ids = ['123', '456']
        
        with patch.object(runner.sender, 'send_verification_report', return_value=True) as mock_send:
            await runner._send_verification_notification(mock_result, admin_chat_ids)
            
            mock_send.assert_called_once_with("测试报告", admin_chat_ids)

    @pytest.mark.asyncio
    async def test_send_verification_notification_failure(self, runner):
        """测试发送校验通知失败"""
        mock_result = MagicMock()
        mock_result.report_text = "测试报告"
        admin_chat_ids = ['123', '456']
        
        with patch.object(runner.sender, 'send_verification_report', side_effect=Exception("发送失败")):
            # 不应该抛出异常
            await runner._send_verification_notification(mock_result, admin_chat_ids)

    @pytest.mark.asyncio
    async def test_send_error_notification(self, runner):
        """测试发送错误通知"""
        error_message = "测试错误"
        admin_chat_ids = ['123', '456']
        
        with patch.object(runner.sender, 'send_error_notification', return_value=None) as mock_send:
            await runner._send_error_notification(error_message, admin_chat_ids)
            
            mock_send.assert_called_once_with(error_message, admin_chat_ids)

    @pytest.mark.asyncio
    async def test_send_error_notification_exception(self, runner):
        """测试发送错误通知异常"""
        error_message = "测试错误"
        admin_chat_ids = ['123', '456']
        
        with patch.object(runner.sender, 'send_error_notification', side_effect=Exception("通知发送异常")):
            # 不应该抛出异常
            await runner._send_error_notification(error_message, admin_chat_ids)