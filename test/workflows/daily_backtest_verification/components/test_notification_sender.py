import pytest
from unittest.mock import AsyncMock, Mock, patch
from datetime import datetime, date
from typing import List

from workflows.daily_backtest_verification.components.notification_sender import (
    NotificationSender
)


class TestNotificationSender:
    """NotificationSender 测试类"""

    @pytest.fixture
    def mock_telegram_sender(self):
        """Mock TelegramMessageSender"""
        with patch('workflows.daily_backtest_verification.components.notification_sender.TelegramMessageSender') as mock_class:
            mock_instance = AsyncMock()
            mock_class.return_value = mock_instance
            yield mock_instance

    @pytest.fixture
    def notification_sender(self, mock_telegram_sender):
        """创建 NotificationSender 实例（使用Mock的TelegramMessageSender）"""
        return NotificationSender()

    @pytest.fixture
    def sample_report_text(self):
        """示例报告文本"""
        return """📊 每日回测校验报告
日期: 2025-01-12
状态: ✅ 通过
回测交易数: 5
实际信号数: 5
匹配交易数: 5
执行时间: 120.5秒"""

    @pytest.fixture
    def sample_admin_chat_ids(self):
        """示例管理员Chat ID列表"""
        return ["123456789", "987654321"]

    @pytest.fixture
    def sample_error_message(self):
        """示例错误消息"""
        return "每日回测校验失败\n日期: 2025-01-12\n错误: 配置文件不存在"

    @pytest.mark.asyncio
    async def test_send_verification_report_success(self, notification_sender, mock_telegram_sender, 
                                                   sample_report_text, sample_admin_chat_ids):
        """测试成功发送校验报告"""
        # Given
        mock_telegram_sender.send_message_to_user.return_value = True
        
        # When
        result = await notification_sender.send_verification_report(
            sample_report_text, sample_admin_chat_ids
        )
        
        # Then
        assert result is True
        assert mock_telegram_sender.send_message_to_user.call_count == 2
        
        # 验证发送给每个管理员
        calls = mock_telegram_sender.send_message_to_user.call_args_list
        for i, chat_id in enumerate(sample_admin_chat_ids):
            call_args = calls[i]
            assert call_args[1]['user_id'] == chat_id
            assert call_args[1]['message'] == sample_report_text

    @pytest.mark.asyncio
    async def test_send_verification_report_partial_failure(self, notification_sender, mock_telegram_sender,
                                                           sample_report_text, sample_admin_chat_ids):
        """测试部分发送失败"""
        # Given: 第一个Chat ID发送成功，第二个失败
        mock_telegram_sender.send_message_to_user.side_effect = [True, False]
        
        # When
        result = await notification_sender.send_verification_report(
            sample_report_text, sample_admin_chat_ids
        )
        
        # Then
        assert result is False
        assert mock_telegram_sender.send_message_to_user.call_count == 2

    @pytest.mark.asyncio
    async def test_send_verification_report_all_failure(self, notification_sender, mock_telegram_sender,
                                                       sample_report_text, sample_admin_chat_ids):
        """测试全部发送失败"""
        # Given
        mock_telegram_sender.send_message_to_user.return_value = False
        
        # When
        result = await notification_sender.send_verification_report(
            sample_report_text, sample_admin_chat_ids
        )
        
        # Then
        assert result is False
        assert mock_telegram_sender.send_message_to_user.call_count == 2

    @pytest.mark.asyncio
    async def test_send_verification_report_empty_chat_ids(self, notification_sender, mock_telegram_sender,
                                                          sample_report_text):
        """测试空的Chat ID列表"""
        # Given
        empty_chat_ids = []
        
        # When
        result = await notification_sender.send_verification_report(
            sample_report_text, empty_chat_ids
        )
        
        # Then
        assert result is True  # 空列表应该被认为是成功
        assert mock_telegram_sender.send_message_to_user.call_count == 0

    @pytest.mark.asyncio
    async def test_send_verification_report_telegram_exception(self, notification_sender, mock_telegram_sender,
                                                              sample_report_text, sample_admin_chat_ids):
        """测试Telegram API异常"""
        # Given
        mock_telegram_sender.send_message_to_user.side_effect = Exception("Telegram API Error")
        
        # When
        result = await notification_sender.send_verification_report(
            sample_report_text, sample_admin_chat_ids
        )
        
        # Then
        assert result is False
        # 第一次异常后应该停止发送
        assert mock_telegram_sender.send_message_to_user.call_count == 1

    @pytest.mark.asyncio
    async def test_send_error_notification_success(self, notification_sender, mock_telegram_sender,
                                                  sample_error_message, sample_admin_chat_ids):
        """测试成功发送错误通知"""
        # Given
        mock_telegram_sender.send_message_to_user.return_value = True
        
        # When
        result = await notification_sender.send_error_notification(
            sample_error_message, sample_admin_chat_ids
        )
        
        # Then
        assert result is True
        assert mock_telegram_sender.send_message_to_user.call_count == 2
        
        # 验证错误消息格式
        calls = mock_telegram_sender.send_message_to_user.call_args_list
        for call_args in calls:
            message = call_args[1]['message']
            assert "🚨 系统错误" in message
            assert sample_error_message in message

    @pytest.mark.asyncio
    async def test_send_error_notification_failure(self, notification_sender, mock_telegram_sender,
                                                  sample_error_message, sample_admin_chat_ids):
        """测试错误通知发送失败"""
        # Given
        mock_telegram_sender.send_message_to_user.return_value = False
        
        # When
        result = await notification_sender.send_error_notification(
            sample_error_message, sample_admin_chat_ids
        )
        
        # Then
        assert result is False
        assert mock_telegram_sender.send_message_to_user.call_count == 2

    @pytest.mark.asyncio
    async def test_send_error_notification_exception(self, notification_sender, mock_telegram_sender,
                                                    sample_error_message, sample_admin_chat_ids):
        """测试错误通知异常处理"""
        # Given
        mock_telegram_sender.send_message_to_user.side_effect = Exception("Network Error")
        
        # When
        result = await notification_sender.send_error_notification(
            sample_error_message, sample_admin_chat_ids
        )
        
        # Then
        assert result is False
        assert mock_telegram_sender.send_message_to_user.call_count == 1

    @pytest.mark.asyncio
    async def test_send_verification_report_message_format(self, notification_sender, mock_telegram_sender,
                                                          sample_admin_chat_ids):
        """测试校验报告消息格式"""
        # Given
        report_text = "Test Report"
        mock_telegram_sender.send_message_to_user.return_value = True
        
        # When
        await notification_sender.send_verification_report(report_text, sample_admin_chat_ids)
        
        # Then
        call_args = mock_telegram_sender.send_message_to_user.call_args_list[0]
        message = call_args[1]['message']
        assert message == report_text  # 校验报告应该直接发送，不添加额外格式

    @pytest.mark.asyncio
    async def test_send_error_notification_message_format(self, notification_sender, mock_telegram_sender,
                                                         sample_admin_chat_ids):
        """测试错误通知消息格式"""
        # Given
        error_message = "测试错误"
        mock_telegram_sender.send_message_to_user.return_value = True
        expected_format = f"🚨 系统错误\n\n{error_message}"
        
        # When
        await notification_sender.send_error_notification(error_message, sample_admin_chat_ids)
        
        # Then
        call_args = mock_telegram_sender.send_message_to_user.call_args_list[0]
        message = call_args[1]['message']
        assert message == expected_format

    def test_notification_sender_initialization(self, notification_sender):
        """测试NotificationSender初始化"""
        # Then
        assert notification_sender is not None
        assert hasattr(notification_sender, 'telegram_sender')
        assert hasattr(notification_sender, 'logger')

    @pytest.mark.asyncio
    async def test_send_verification_report_with_special_characters(self, notification_sender, mock_telegram_sender,
                                                                   sample_admin_chat_ids):
        """测试发送包含特殊字符的报告"""
        # Given
        special_report = "报告包含特殊字符: 🚀 💰 📈 ⚠️"
        mock_telegram_sender.send_message_to_user.return_value = True
        
        # When
        result = await notification_sender.send_verification_report(special_report, sample_admin_chat_ids)
        
        # Then
        assert result is True
        call_args = mock_telegram_sender.send_message_to_user.call_args_list[0]
        message = call_args[1]['message']
        assert message == special_report 