"""
配置读取器测试用例

测试 ConfigReader 组件的功能：
- 读取 kol_activity 配置
- 读取管理员 Telegram 配置  
- 创建和清理临时文件
"""

import pytest
import asyncio
import tempfile
import json
import os
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
from typing import Dict, Any, List

# 导入待测试的类（目前还不存在，会导致测试失败）
try:
    from workflows.daily_backtest_verification.components.config_reader import (
        ConfigReader, 
        ConfigNotFoundError, 
        ConfigInvalidError
    )
except ImportError:
    # 在红阶段，这些类还不存在，先用 placeholder
    class ConfigReader:
        pass
    
    class ConfigNotFoundError(Exception):
        pass
    
    class ConfigInvalidError(Exception):
        pass


class TestConfigReader:
    """配置读取器测试类"""
    
    @pytest.fixture
    def config_reader(self):
        """创建配置读取器实例"""
        return ConfigReader()
    
    @pytest.fixture
    def mock_kol_activity_config(self):
        """模拟的 kol_activity 配置数据"""
        return {
            "type": "kol_activity",
            "data": {
                "buy_strategies": [
                    {
                        "strategy_name": "胜率高",
                        "transaction_lookback_hours": 6,
                        "transaction_min_amount": 500.0,
                        "kol_account_min_count": 6,
                        "token_mint_lookback_hours": 72,
                        "kol_account_min_txs": 10,
                        "kol_account_max_txs": 500,
                        "sell_strategy_hours": 12,
                        "sell_kol_ratio": 0.5,
                        "same_token_notification_interval": 60
                    },
                    {
                        "strategy_name": "test_strategy",
                        "transaction_lookback_hours": 24,
                        "transaction_min_amount": 1000.0,
                        "kol_account_min_count": 3,
                        "sell_strategy_hours": 24,
                        "sell_kol_ratio": 0.5
                    }
                ]
            }
        }
    
    @pytest.fixture
    def mock_admin_config(self):
        """模拟的管理员配置数据"""
        return {
            "type": "application_config",
            "data": {
                "admin_telegram_chat_ids": ["*********", "*********"]
            }
        }

    # === 测试用例 3.1.1: 正常策略配置读取 ===
    @pytest.mark.asyncio
    async def test_read_strategy_config_success(self, config_reader, mock_kol_activity_config):
        """测试成功读取指定策略配置"""
        # Given: 数据库中存在有效的 kol_activity 配置
        with patch.object(config_reader, 'config_dao') as mock_dao:
            # 创建一个模拟的 Config 对象
            mock_config = MagicMock()
            mock_config.data = mock_kol_activity_config["data"]
            # 使用 AsyncMock 处理异步方法
            mock_dao.get_config = AsyncMock(return_value=mock_config)

            # When: 调用 read_strategy_config() 读取"胜率高"策略
            config_data, temp_file_path = await config_reader.read_strategy_config("胜率高", "2025-06-07")

            # Then: 返回正确的回测配置数据
            assert config_data is not None
            assert temp_file_path is not None
            assert os.path.exists(temp_file_path)

            # And: 配置数据包含正确的字段
            expected_fields = [
                'transaction_lookback_hours', 'transaction_min_amount', 'kol_account_min_count',
                'sell_strategy_hours', 'sell_kol_ratio', 'backtest_start_time', 'backtest_end_time'
            ]
            for field in expected_fields:
                assert field in config_data

            # And: 策略特定的配置正确
            assert config_data['sell_strategy_hours'] == 12
            assert config_data['kol_account_min_count'] == 6
            assert config_data['transaction_min_amount'] == 500.0

            # 清理临时文件
            config_reader.cleanup_temp_file(temp_file_path)

    # === 测试用例 3.1.2: 策略不存在 ===
    @pytest.mark.asyncio
    async def test_read_strategy_config_strategy_not_found(self, config_reader, mock_kol_activity_config):
        """测试指定策略不存在的情况"""
        # Given: 数据库中存在配置但不包含指定策略
        with patch.object(config_reader, 'config_dao') as mock_dao:
            mock_config = MagicMock()
            mock_config.data = mock_kol_activity_config["data"]
            mock_dao.get_config = AsyncMock(return_value=mock_config)

            # When & Then: 调用 read_strategy_config() 查找不存在的策略应该抛出异常
            with pytest.raises(ConfigNotFoundError, match="策略 '不存在的策略' 不存在"):
                await config_reader.read_strategy_config("不存在的策略", "2025-06-07")

    # === 测试用例 3.1.3: 配置不存在 ===
    @pytest.mark.asyncio
    async def test_read_strategy_config_config_not_found(self, config_reader):
        """测试配置不存在的情况"""
        # Given: 数据库中不存在 kol_activity 配置
        with patch.object(config_reader, 'config_dao') as mock_dao:
            mock_dao.get_config = AsyncMock(return_value=None)

            # When & Then: 调用 read_strategy_config() 应该抛出异常
            with pytest.raises(ConfigNotFoundError, match="kol_activity 配置不存在"):
                await config_reader.read_strategy_config("胜率高", "2025-06-07")

    # === 测试用例 3.1.4: 配置格式无效 ===
    @pytest.mark.asyncio
    async def test_read_strategy_config_invalid_format(self, config_reader):
        """测试配置格式无效的情况"""
        # Given: 数据库中存在格式错误的配置
        invalid_config = {
            "type": "kol_activity",
            "data": "invalid_format"  # 应该是字典，不是字符串
        }

        with patch.object(config_reader, 'config_dao') as mock_dao:
            # 创建一个模拟的 Config 对象
            mock_config = MagicMock()
            mock_config.data = invalid_config["data"]
            mock_dao.get_config = AsyncMock(return_value=mock_config)

            # When & Then: 调用 read_strategy_config() 应该抛出异常
            with pytest.raises(ConfigInvalidError, match="配置格式无效"):
                await config_reader.read_strategy_config("胜率高", "2025-06-07")

    # === 测试用例 3.1.5: 管理员配置读取 ===
    @pytest.mark.asyncio
    async def test_read_admin_telegram_config(self, config_reader, mock_admin_config):
        """测试读取管理员 Telegram 配置"""
        # Given: 数据库中存在 application_config 配置
        with patch.object(config_reader, 'config_dao') as mock_dao:
            # 创建一个模拟的 Config 对象
            mock_config = MagicMock()
            mock_config.data = mock_admin_config["data"]
            mock_dao.get_config = AsyncMock(return_value=mock_config)
            
            # When: 调用 read_admin_telegram_config()
            chat_ids = await config_reader.read_admin_telegram_config()
            
            # Then: 返回正确的 Chat ID 列表
            assert chat_ids == ["*********", "*********"]

    # === 测试用例 3.1.6: 临时文件创建和清理 ===
    def test_create_and_cleanup_temp_file(self, config_reader):
        """测试临时文件创建和清理"""
        # Given: 有效的配置数据
        test_data = {"test": "data", "number": 123}
        
        # When: 创建临时文件并写入数据
        temp_file_path = config_reader.create_temp_config_file(test_data)
        
        # Then: 文件被正确创建且内容正确
        assert temp_file_path is not None
        assert os.path.exists(temp_file_path)
        
        with open(temp_file_path, 'r', encoding='utf-8') as f:
            file_content = json.load(f)
        assert file_content == test_data
        
        # When: 调用清理方法
        config_reader.cleanup_temp_file(temp_file_path)
        
        # Then: 文件被正确删除
        assert not os.path.exists(temp_file_path)

    # === 边界测试：空配置数据 ===
    @pytest.mark.asyncio
    async def test_read_strategy_config_empty_data(self, config_reader):
        """测试空配置数据的情况"""
        # Given: 配置存在但 buy_strategies 字段为空
        empty_config = {
            "type": "kol_activity",
            "data": {
                "buy_strategies": []
            }
        }

        with patch.object(config_reader, 'config_dao') as mock_dao:
            # 创建一个模拟的 Config 对象
            mock_config = MagicMock()
            mock_config.data = empty_config["data"]
            mock_dao.get_config = AsyncMock(return_value=mock_config)

            # When & Then: 应该抛出策略不存在异常
            with pytest.raises(ConfigNotFoundError, match="策略 '胜率高' 不存在"):
                await config_reader.read_strategy_config("胜率高", "2025-06-07")

    # === 边界测试：管理员配置不存在 ===
    @pytest.mark.asyncio
    async def test_read_admin_telegram_config_not_found(self, config_reader):
        """测试管理员配置不存在的情况"""
        # Given: 数据库中不存在 application_config 配置
        with patch.object(config_reader, 'config_dao') as mock_dao:
            mock_dao.get_config = AsyncMock(return_value=None)
            
            # When: 调用 read_admin_telegram_config()
            chat_ids = await config_reader.read_admin_telegram_config()
            
            # Then: 返回空列表
            assert chat_ids == []

    # === 异常测试：临时文件创建失败 ===
    def test_create_temp_file_permission_error(self, config_reader):
        """测试临时文件创建权限错误"""
        # Given: 模拟文件系统权限错误
        test_data = {"test": "data"}
        
        with patch('tempfile.NamedTemporaryFile', side_effect=PermissionError("权限不足")):
            # When & Then: 应该抛出适当的异常
            with pytest.raises(Exception, match="临时文件创建失败"):
                config_reader.create_temp_config_file(test_data)

    # === 异常测试：清理不存在的文件 ===
    def test_cleanup_nonexistent_file(self, config_reader):
        """测试清理不存在的文件"""
        # Given: 不存在的文件路径
        nonexistent_path = "/tmp/nonexistent_file_12345.json"

        # When & Then: 清理操作应该静默处理，不抛出异常
        config_reader.cleanup_temp_file(nonexistent_path)  # 应该不抛出异常

    # === 测试用例 3.1.7: 回测配置生成 ===
    def test_generate_backtest_config(self, config_reader):
        """测试回测配置生成"""
        # Given: 策略配置数据
        strategy_config = {
            "transaction_lookback_hours": 6,
            "transaction_min_amount": 500,
            "kol_account_min_count": 6,
            "token_mint_lookback_hours": 72,
            "kol_account_min_txs": 10,
            "kol_account_max_txs": 500,
            "sell_strategy_hours": 12,
            "sell_kol_ratio": 0.5,
            "same_token_notification_interval": 60
        }

        # When: 生成回测配置
        backtest_config = config_reader._generate_backtest_config(strategy_config, "2025-06-07")

        # Then: 配置包含所有必要字段
        expected_fields = [
            'transaction_lookback_hours', 'transaction_min_amount', 'kol_account_min_count',
            'token_mint_lookback_hours', 'kol_account_min_txs', 'kol_account_max_txs',
            'backtest_start_time', 'backtest_end_time', 'sell_strategy_hours',
            'sell_kol_ratio', 'same_token_notification_interval_minutes', 'processing_interval'
        ]
        for field in expected_fields:
            assert field in backtest_config

        # And: 时间戳正确设置
        assert backtest_config['backtest_start_time'] == **********  # 2025-06-07 00:00:00 UTC
        assert backtest_config['backtest_end_time'] == **********   # 2025-06-07 23:59:59 UTC

        # And: 策略参数正确传递
        assert backtest_config['sell_strategy_hours'] == 12
        assert backtest_config['kol_account_min_count'] == 6

    # === 测试用例 3.1.8: 多策略配置读取 ===
    @pytest.mark.asyncio
    async def test_read_multi_strategy_config_success(self, config_reader, mock_kol_activity_config):
        """测试成功读取多策略配置"""
        # Given: 数据库中存在有效的 kol_activity 配置
        with patch.object(config_reader, 'config_dao') as mock_dao:
            # 创建一个模拟的 Config 对象
            mock_config = MagicMock()
            mock_config.data = mock_kol_activity_config["data"]
            # 使用 AsyncMock 处理异步方法
            mock_dao.get_config = AsyncMock(return_value=mock_config)

            # When: 调用 read_multi_strategy_config() 读取多个策略
            strategies = ["胜率高", "test_strategy"]
            config_data, temp_file_path = await config_reader.read_multi_strategy_config(strategies, "2025-06-07")

            # Then: 返回正确的回测配置数据
            assert config_data is not None
            assert temp_file_path is not None
            assert os.path.exists(temp_file_path)

            # And: 配置数据包含目标策略列表
            assert 'target_strategies' in config_data
            assert config_data['target_strategies'] == strategies

            # And: 配置数据包含正确的字段
            expected_fields = [
                'transaction_lookback_hours', 'transaction_min_amount', 'kol_account_min_count',
                'sell_strategy_hours', 'sell_kol_ratio', 'backtest_start_time', 'backtest_end_time'
            ]
            for field in expected_fields:
                assert field in config_data

            # 清理临时文件
            config_reader.cleanup_temp_file(temp_file_path)

    # === 测试用例 3.1.9: 多策略配置读取 - 策略不存在 ===
    @pytest.mark.asyncio
    async def test_read_multi_strategy_config_strategy_not_found(self, config_reader, mock_kol_activity_config):
        """测试多策略配置读取时部分策略不存在的情况"""
        # Given: 数据库中存在配置但不包含某些指定策略
        with patch.object(config_reader, 'config_dao') as mock_dao:
            mock_config = MagicMock()
            mock_config.data = mock_kol_activity_config["data"]
            mock_dao.get_config = AsyncMock(return_value=mock_config)

            # When & Then: 调用 read_multi_strategy_config() 查找包含不存在策略的列表应该抛出异常
            strategies = ["胜率高", "不存在的策略"]
            with pytest.raises(ConfigNotFoundError, match="策略不存在: 不存在的策略"):
                await config_reader.read_multi_strategy_config(strategies, "2025-06-07")

    # === 测试用例 3.1.10: 获取所有已启用策略 ===
    @pytest.mark.asyncio
    async def test_get_all_enabled_strategies_success(self, config_reader, mock_kol_activity_config):
        """测试成功获取所有已启用策略"""
        # Given: 数据库中存在有效的 kol_activity 配置
        with patch.object(config_reader, 'config_dao') as mock_dao:
            mock_config = MagicMock()
            mock_config.data = mock_kol_activity_config["data"]
            mock_dao.get_config = AsyncMock(return_value=mock_config)

            # When: 调用 get_all_enabled_strategies()
            strategies = await config_reader.get_all_enabled_strategies()

            # Then: 返回正确的策略列表
            assert isinstance(strategies, list)
            assert len(strategies) == 2
            assert "胜率高" in strategies
            assert "test_strategy" in strategies


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"]) 