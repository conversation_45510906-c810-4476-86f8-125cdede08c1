"""
配置读取器测试用例

测试 ConfigReader 组件的功能：
- 读取 kol_activity 配置
- 读取管理员 Telegram 配置  
- 创建和清理临时文件
"""

import pytest
import asyncio
import tempfile
import json
import os
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
from typing import Dict, Any, List

# 导入待测试的类（目前还不存在，会导致测试失败）
try:
    from workflows.daily_backtest_verification.components.config_reader import (
        ConfigReader, 
        ConfigNotFoundError, 
        ConfigInvalidError
    )
except ImportError:
    # 在红阶段，这些类还不存在，先用 placeholder
    class ConfigReader:
        pass
    
    class ConfigNotFoundError(Exception):
        pass
    
    class ConfigInvalidError(Exception):
        pass


class TestConfigReader:
    """配置读取器测试类"""
    
    @pytest.fixture
    def config_reader(self):
        """创建配置读取器实例"""
        return ConfigReader()
    
    @pytest.fixture
    def mock_kol_activity_config(self):
        """模拟的 kol_activity 配置数据"""
        return {
            "type": "kol_activity",
            "data": {
                "buy_strategies": [{
                    "strategy_name": "test_strategy",
                    "transaction_lookback_hours": 24,
                    "transaction_min_amount": 1000.0,
                    "kol_account_min_count": 3,
                    "sell_strategy_hours": 24,
                    "sell_kol_ratio": 0.5
                }]
            }
        }
    
    @pytest.fixture
    def mock_admin_config(self):
        """模拟的管理员配置数据"""
        return {
            "type": "application_config",
            "data": {
                "admin_telegram_chat_ids": ["*********", "*********"]
            }
        }

    # === 测试用例 3.1.1: 正常配置读取 ===
    @pytest.mark.asyncio
    async def test_read_kol_activity_config_success(self, config_reader, mock_kol_activity_config):
        """测试成功读取 kol_activity 配置"""
        # Given: 数据库中存在有效的 kol_activity 配置
        with patch.object(config_reader, 'config_dao') as mock_dao:
            # 创建一个模拟的 Config 对象
            mock_config = MagicMock()
            mock_config.data = mock_kol_activity_config["data"]
            # 使用 AsyncMock 处理异步方法
            mock_dao.get_config = AsyncMock(return_value=mock_config)
            
            # When: 调用 read_kol_activity_config()
            config_data, temp_file_path = await config_reader.read_kol_activity_config()
            
            # Then: 返回配置数据和临时文件路径
            assert config_data == mock_kol_activity_config["data"]
            assert temp_file_path is not None
            assert os.path.exists(temp_file_path)
            
            # And: 临时文件内容正确
            with open(temp_file_path, 'r', encoding='utf-8') as f:
                file_content = json.load(f)
            assert file_content == mock_kol_activity_config["data"]
            
            # 清理临时文件
            config_reader.cleanup_temp_file(temp_file_path)

    # === 测试用例 3.1.2: 配置不存在 ===
    @pytest.mark.asyncio
    async def test_read_kol_activity_config_not_found(self, config_reader):
        """测试配置不存在的情况"""
        # Given: 数据库中不存在 kol_activity 配置
        with patch.object(config_reader, 'config_dao') as mock_dao:
            mock_dao.get_config = AsyncMock(return_value=None)
            
            # When & Then: 调用 read_kol_activity_config() 应该抛出异常
            with pytest.raises(ConfigNotFoundError, match="kol_activity 配置不存在"):
                await config_reader.read_kol_activity_config()

    # === 测试用例 3.1.3: 配置格式无效 ===
    @pytest.mark.asyncio
    async def test_read_kol_activity_config_invalid_format(self, config_reader):
        """测试配置格式无效的情况"""
        # Given: 数据库中存在格式错误的配置
        invalid_config = {
            "type": "kol_activity",
            "data": "invalid_format"  # 应该是字典，不是字符串
        }
        
        with patch.object(config_reader, 'config_dao') as mock_dao:
            # 创建一个模拟的 Config 对象
            mock_config = MagicMock()
            mock_config.data = invalid_config["data"]
            mock_dao.get_config = AsyncMock(return_value=mock_config)
            
            # When & Then: 调用 read_kol_activity_config() 应该抛出异常
            with pytest.raises(ConfigInvalidError, match="配置格式无效"):
                await config_reader.read_kol_activity_config()

    # === 测试用例 3.1.4: 管理员配置读取 ===
    @pytest.mark.asyncio
    async def test_read_admin_telegram_config(self, config_reader, mock_admin_config):
        """测试读取管理员 Telegram 配置"""
        # Given: 数据库中存在 application_config 配置
        with patch.object(config_reader, 'config_dao') as mock_dao:
            # 创建一个模拟的 Config 对象
            mock_config = MagicMock()
            mock_config.data = mock_admin_config["data"]
            mock_dao.get_config = AsyncMock(return_value=mock_config)
            
            # When: 调用 read_admin_telegram_config()
            chat_ids = await config_reader.read_admin_telegram_config()
            
            # Then: 返回正确的 Chat ID 列表
            assert chat_ids == ["*********", "*********"]

    # === 测试用例 3.1.5: 临时文件创建和清理 ===
    def test_create_and_cleanup_temp_file(self, config_reader):
        """测试临时文件创建和清理"""
        # Given: 有效的配置数据
        test_data = {"test": "data", "number": 123}
        
        # When: 创建临时文件并写入数据
        temp_file_path = config_reader.create_temp_config_file(test_data)
        
        # Then: 文件被正确创建且内容正确
        assert temp_file_path is not None
        assert os.path.exists(temp_file_path)
        
        with open(temp_file_path, 'r', encoding='utf-8') as f:
            file_content = json.load(f)
        assert file_content == test_data
        
        # When: 调用清理方法
        config_reader.cleanup_temp_file(temp_file_path)
        
        # Then: 文件被正确删除
        assert not os.path.exists(temp_file_path)

    # === 边界测试：空配置数据 ===
    @pytest.mark.asyncio
    async def test_read_kol_activity_config_empty_data(self, config_reader):
        """测试空配置数据的情况"""
        # Given: 配置存在但 data 字段为空
        empty_config = {
            "type": "kol_activity",
            "data": {}
        }
        
        with patch.object(config_reader, 'config_dao') as mock_dao:
            # 创建一个模拟的 Config 对象
            mock_config = MagicMock()
            mock_config.data = empty_config["data"]
            mock_dao.get_config = AsyncMock(return_value=mock_config)
            
            # When & Then: 应该能正常处理空数据
            config_data, temp_file_path = await config_reader.read_kol_activity_config()
            assert config_data == {}
            assert temp_file_path is not None
            
            # 清理
            config_reader.cleanup_temp_file(temp_file_path)

    # === 边界测试：管理员配置不存在 ===
    @pytest.mark.asyncio
    async def test_read_admin_telegram_config_not_found(self, config_reader):
        """测试管理员配置不存在的情况"""
        # Given: 数据库中不存在 application_config 配置
        with patch.object(config_reader, 'config_dao') as mock_dao:
            mock_dao.get_config = AsyncMock(return_value=None)
            
            # When: 调用 read_admin_telegram_config()
            chat_ids = await config_reader.read_admin_telegram_config()
            
            # Then: 返回空列表
            assert chat_ids == []

    # === 异常测试：临时文件创建失败 ===
    def test_create_temp_file_permission_error(self, config_reader):
        """测试临时文件创建权限错误"""
        # Given: 模拟文件系统权限错误
        test_data = {"test": "data"}
        
        with patch('tempfile.NamedTemporaryFile', side_effect=PermissionError("权限不足")):
            # When & Then: 应该抛出适当的异常
            with pytest.raises(Exception, match="临时文件创建失败"):
                config_reader.create_temp_config_file(test_data)

    # === 异常测试：清理不存在的文件 ===
    def test_cleanup_nonexistent_file(self, config_reader):
        """测试清理不存在的文件"""
        # Given: 不存在的文件路径
        nonexistent_path = "/tmp/nonexistent_file_12345.json"
        
        # When & Then: 清理操作应该静默处理，不抛出异常
        config_reader.cleanup_temp_file(nonexistent_path)  # 应该不抛出异常


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"]) 