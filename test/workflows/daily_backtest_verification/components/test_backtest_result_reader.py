"""
回测结果读取器测试用例

测试 BacktestResultReader 组件的功能：
- 读取和解析回测结果文件（result.json）
- 提取交易信息（trades数组）
- 处理各种异常情况和边界条件
- 数据格式验证和转换
"""

import pytest
import tempfile
import json
import os
from datetime import datetime
from typing import Dict, Any, List, Optional
from unittest.mock import patch, mock_open

# 导入待测试的类（目前还不存在，会导致测试失败）
try:
    from workflows.daily_backtest_verification.components.backtest_result_reader import (
        BacktestResultReader,
        BacktestTradeInfo,
        BacktestResultData,
        ResultFileNotFoundError,
        ResultFileInvalidError
    )
except ImportError:
    # 在红阶段，这些类还不存在，先用 placeholder
    from dataclasses import dataclass
    from typing import Optional
    
    @dataclass
    class BacktestTradeInfo:
        symbol: str
        timestamp: datetime
        action: str
        amount: float
        price: float
    
    @dataclass 
    class BacktestResultData:
        trades: List[BacktestTradeInfo]
        total_trades: int
        start_time: datetime
        end_time: datetime
        
    class BacktestResultReader:
        pass
    
    class ResultFileNotFoundError(Exception):
        pass
    
    class ResultFileInvalidError(Exception):
        pass


class TestBacktestResultReader:
    """回测结果读取器测试类"""
    
    @pytest.fixture
    def result_reader(self):
        """创建回测结果读取器实例"""
        return BacktestResultReader()
    
    @pytest.fixture
    def valid_result_data(self):
        """有效的回测结果数据 - 使用实际的回测结果格式"""
        return {
            "trades": [
                {
                    "token_address": "39zSVsSHFqNhARbVh6n8ZF78nCmhV3gSg8D39xhBNe73",
                    "buy_timestamp": 1749248379.0,
                    "sell_timestamp": 1749248492.0,
                    "buy_price": 0.0018967156071861165,
                    "sell_price": 0.0021387515,
                    "quantity": 100.0,
                    "buy_reason": "kol_signal",
                    "sell_reason": "kol_ratio"
                },
                {
                    "token_address": "7BgBvyjrZX1YKz4oh9mjb8ZScatkkwb8DzFx6LnRpump",
                    "buy_timestamp": 1749250000.0,
                    "sell_timestamp": 1749253600.0,
                    "buy_price": 0.001234,
                    "sell_price": 0.001456,
                    "quantity": 200.0,
                    "buy_reason": "kol_signal",
                    "sell_reason": "time_limit"
                }
            ],
            "statistics": {
                "total_trades": 2,
                "backtest_start_time": "2025-06-07T00:00:00Z",
                "backtest_end_time": "2025-06-07T23:59:59Z",
                "profit": 150.0
            }
        }
    
    @pytest.fixture
    def temp_result_file(self, valid_result_data):
        """创建临时回测结果文件"""
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(valid_result_data, temp_file, indent=2)
        temp_file.close()
        
        yield temp_file.name
        
        # 清理临时文件
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)

    # === 测试用例 3.3.1: 成功读取回测结果文件 ===
    @pytest.mark.asyncio
    async def test_read_result_file_success(self, result_reader, temp_result_file):
        """测试成功读取回测结果文件"""
        # When: 读取有效的结果文件
        result = await result_reader.read_result_file(temp_result_file)

        # Then: 返回正确的结果数据
        assert isinstance(result, BacktestResultData)
        # 2个完整交易转换为4个交易记录（每个完整交易包含买入和卖出）
        assert len(result.trades) == 4
        assert result.total_trades == 2  # 原始完整交易数量

        # And: 交易信息正确解析
        first_trade = result.trades[0]  # 第一个买入记录
        assert isinstance(first_trade, BacktestTradeInfo)
        assert first_trade.symbol == "39zSVsSHFqNhARbVh6n8ZF78nCmhV3gSg8D39xhBNe73"
        assert first_trade.action == "buy"
        assert first_trade.amount == 100.0
        assert first_trade.price == 0.0018967156071861165
        assert isinstance(first_trade.timestamp, datetime)

        # And: 第二个记录是对应的卖出记录
        second_trade = result.trades[1]  # 第一个卖出记录
        assert second_trade.symbol == "39zSVsSHFqNhARbVh6n8ZF78nCmhV3gSg8D39xhBNe73"
        assert second_trade.action == "sell"
        assert second_trade.amount == 100.0
        assert second_trade.price == 0.0021387515

    # === 测试用例 3.3.2: 结果文件不存在 ===
    @pytest.mark.asyncio
    async def test_read_result_file_not_found(self, result_reader):
        """测试结果文件不存在的情况"""
        # Given: 不存在的文件路径
        nonexistent_file = "/path/to/nonexistent/result.json"
        
        # When & Then: 应该抛出文件不存在异常
        with pytest.raises(ResultFileNotFoundError, match="回测结果文件不存在"):
            await result_reader.read_result_file(nonexistent_file)

    # === 测试用例 3.3.3: 结果文件格式无效 ===
    @pytest.mark.asyncio
    async def test_read_result_file_invalid_json(self, result_reader):
        """测试无效JSON格式的结果文件"""
        # Given: 无效JSON格式的文件
        invalid_json_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        invalid_json_file.write("{ invalid json content")
        invalid_json_file.close()
        
        try:
            # When & Then: 应该抛出格式无效异常
            with pytest.raises(ResultFileInvalidError, match="回测结果文件格式无效"):
                await result_reader.read_result_file(invalid_json_file.name)
        finally:
            os.unlink(invalid_json_file.name)

    # === 测试用例 3.3.4: 缺少必要字段 ===
    @pytest.mark.asyncio
    async def test_read_result_file_missing_fields(self, result_reader):
        """测试缺少必要字段的结果文件"""
        # Given: 缺少trades字段的数据
        incomplete_data = {
            "statistics": {
                "total_trades": 0,
                "backtest_start_time": "2025-01-27T10:00:00Z",
                "backtest_end_time": "2025-01-27T10:00:00Z"
            }
        }
        
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(incomplete_data, temp_file)
        temp_file.close()
        
        try:
            # When & Then: 应该抛出格式无效异常
            with pytest.raises(ResultFileInvalidError, match="缺少必要字段"):
                await result_reader.read_result_file(temp_file.name)
        finally:
            os.unlink(temp_file.name)

    # === 测试用例 3.3.5: 交易数据格式验证 ===
    @pytest.mark.asyncio
    async def test_validate_trade_data(self, result_reader):
        """测试交易数据格式验证"""
        # Given: 无效的交易数据 - 使用新的回测结果格式
        invalid_trade_data = {
            "trades": [
                {
                    "token_address": "invalid_address",
                    "buy_timestamp": "invalid_timestamp",  # 无效时间格式
                    "sell_timestamp": 1749248492.0,
                    "buy_price": 0.001,
                    "sell_price": 0.002,
                    "quantity": "invalid_quantity"  # 无效数量格式
                }
            ],
            "statistics": {
                "total_trades": 1,
                "backtest_start_time": "2025-06-07T00:00:00Z",
                "backtest_end_time": "2025-06-07T23:59:59Z"
            }
        }

        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(invalid_trade_data, temp_file)
        temp_file.close()

        try:
            # When & Then: 应该抛出数据验证异常
            with pytest.raises(ResultFileInvalidError, match="交易数据格式无效"):
                await result_reader.read_result_file(temp_file.name)
        finally:
            os.unlink(temp_file.name)

    # === 测试用例 3.3.6: 提取交易信息 ===
    def test_extract_trades_info(self, result_reader, valid_result_data):
        """测试提取交易信息"""
        # When: 提取交易信息
        trades = result_reader._extract_trades_info(valid_result_data["trades"])

        # Then: 正确提取所有交易（2个完整交易转换为4个交易记录）
        assert len(trades) == 4
        assert all(isinstance(trade, BacktestTradeInfo) for trade in trades)

        # And: 第一个完整交易的买入记录正确
        first_buy_trade = trades[0]
        assert first_buy_trade.symbol == "39zSVsSHFqNhARbVh6n8ZF78nCmhV3gSg8D39xhBNe73"
        assert first_buy_trade.action == "buy"
        assert first_buy_trade.amount == 100.0
        assert first_buy_trade.price == 0.0018967156071861165

        # And: 第一个完整交易的卖出记录正确
        first_sell_trade = trades[1]
        assert first_sell_trade.symbol == "39zSVsSHFqNhARbVh6n8ZF78nCmhV3gSg8D39xhBNe73"
        assert first_sell_trade.action == "sell"
        assert first_sell_trade.amount == 100.0
        assert first_sell_trade.price == 0.0021387515

    # === 测试用例 3.3.7: 时间戳解析 ===
    def test_parse_timestamp(self, result_reader):
        """测试时间戳解析"""
        # Given: 不同格式的时间戳（包括Unix时间戳）
        unix_timestamp = 1749248379.0
        iso_timestamp = "2025-01-27T10:00:00Z"
        iso_with_ms = "2025-01-27T10:00:00.123Z"

        # When: 解析时间戳
        dt1 = result_reader._parse_timestamp(unix_timestamp)
        dt2 = result_reader._parse_timestamp(iso_timestamp)
        dt3 = result_reader._parse_timestamp(iso_with_ms)

        # Then: 正确解析为datetime对象
        assert isinstance(dt1, datetime)
        assert isinstance(dt2, datetime)
        assert isinstance(dt3, datetime)

    # === 测试用例 3.3.8: 数据类型转换 ===
    def test_convert_data_types(self, result_reader):
        """测试数据类型转换"""
        # Given: 完整交易数据（新格式）
        trade_data = {
            "token_address": "7BgBvyjrZX1YKz4oh9mjb8ZScatkkwb8DzFx6LnRpump",
            "buy_timestamp": 1749250000.0,
            "sell_timestamp": 1749253600.0,
            "buy_price": 0.001234,
            "sell_price": 0.001456,
            "quantity": 200.0,
            "buy_reason": "kol_signal",
            "sell_reason": "time_limit"
        }

        # When: 转换数据类型（返回买入和卖出两个记录）
        buy_trade, sell_trade = result_reader._parse_trade_data(trade_data)

        # Then: 买入记录正确转换
        assert isinstance(buy_trade.amount, float)
        assert isinstance(buy_trade.price, float)
        assert buy_trade.amount == 200.0
        assert buy_trade.price == 0.001234
        assert buy_trade.action == "buy"

        # And: 卖出记录正确转换
        assert isinstance(sell_trade.amount, float)
        assert isinstance(sell_trade.price, float)
        assert sell_trade.amount == 200.0
        assert sell_trade.price == 0.001456
        assert sell_trade.action == "sell"

    # === 边界测试：空交易列表 ===
    @pytest.mark.asyncio
    async def test_read_result_file_empty_trades(self, result_reader):
        """测试空交易列表"""
        # Given: 空交易列表的数据
        empty_trades_data = {
            "trades": [],
            "statistics": {
                "total_trades": 0,
                "backtest_start_time": "2025-01-27T10:00:00Z",
                "backtest_end_time": "2025-01-27T10:00:00Z"
            }
        }
        
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(empty_trades_data, temp_file)
        temp_file.close()
        
        try:
            # When: 读取空交易列表
            result = await result_reader.read_result_file(temp_file.name)
            
            # Then: 正确处理空列表
            assert len(result.trades) == 0
            assert result.total_trades == 0
        finally:
            os.unlink(temp_file.name)

    # === 边界测试：大文件处理 ===
    @pytest.mark.asyncio
    async def test_read_large_result_file(self, result_reader):
        """测试大文件处理"""
        # Given: 大量交易数据（使用新的完整交易格式）
        large_trades = []
        for i in range(500):  # 500个完整交易
            large_trades.append({
                "token_address": f"TOKEN{i}Address123456789012345678901234567890",
                "buy_timestamp": 1749248379.0 + i,
                "sell_timestamp": 1749248379.0 + i + 3600,  # 1小时后卖出
                "buy_price": 0.001 * (i + 1),
                "sell_price": 0.0011 * (i + 1),
                "quantity": float(i + 1),
                "buy_reason": "kol_signal",
                "sell_reason": "time_limit"
            })

        large_data = {
            "trades": large_trades,
            "statistics": {
                "total_trades": 500,
                "backtest_start_time": "2025-06-07T00:00:00Z",
                "backtest_end_time": "2025-06-07T23:59:59Z"
            }
        }

        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(large_data, temp_file)
        temp_file.close()

        try:
            # When: 读取大文件
            result = await result_reader.read_result_file(temp_file.name)

            # Then: 正确处理大量数据（500个完整交易转换为1000个交易记录）
            assert len(result.trades) == 1000
            assert result.total_trades == 500  # 原始完整交易数量
        finally:
            os.unlink(temp_file.name)

    # === 性能测试：文件读取性能 ===
    @pytest.mark.asyncio
    async def test_file_reading_performance(self, result_reader, temp_result_file):
        """测试文件读取性能"""
        import time
        
        # Given: 计时开始
        start_time = time.time()
        
        # When: 读取文件
        result = await result_reader.read_result_file(temp_result_file)
        
        # Then: 读取时间应该在合理范围内（<1秒）
        execution_time = time.time() - start_time
        assert execution_time < 1.0
        assert result is not None

    # === 测试用例 3.3.9: 新回测结果格式验证 ===
    @pytest.mark.asyncio
    async def test_new_backtest_result_format_validation(self, result_reader):
        """测试新的回测结果格式验证"""
        # Given: 缺少必要字段的新格式数据
        invalid_new_format_data = {
            "trades": [
                {
                    "token_address": "39zSVsSHFqNhARbVh6n8ZF78nCmhV3gSg8D39xhBNe73",
                    "buy_timestamp": 1749248379.0,
                    # 缺少 sell_timestamp
                    "buy_price": 0.001,
                    "sell_price": 0.002,
                    "quantity": 100.0
                }
            ],
            "statistics": {
                "total_trades": 1,
                "backtest_start_time": "2025-06-07T00:00:00Z",
                "backtest_end_time": "2025-06-07T23:59:59Z"
            }
        }

        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(invalid_new_format_data, temp_file)
        temp_file.close()

        try:
            # When & Then: 应该抛出格式验证异常
            with pytest.raises(ResultFileInvalidError, match="交易数据格式无效"):
                await result_reader.read_result_file(temp_file.name)
        finally:
            os.unlink(temp_file.name)

    # === 测试用例 3.3.10: 时间戳逻辑验证 ===
    @pytest.mark.asyncio
    async def test_timestamp_logic_validation(self, result_reader):
        """测试时间戳逻辑验证（卖出时间必须晚于买入时间）"""
        # Given: 卖出时间早于买入时间的数据
        invalid_timestamp_data = {
            "trades": [
                {
                    "token_address": "39zSVsSHFqNhARbVh6n8ZF78nCmhV3gSg8D39xhBNe73",
                    "buy_timestamp": 1749248492.0,   # 买入时间
                    "sell_timestamp": 1749248379.0,  # 卖出时间早于买入时间
                    "buy_price": 0.001,
                    "sell_price": 0.002,
                    "quantity": 100.0
                }
            ],
            "statistics": {
                "total_trades": 1,
                "backtest_start_time": "2025-06-07T00:00:00Z",
                "backtest_end_time": "2025-06-07T23:59:59Z"
            }
        }

        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(invalid_timestamp_data, temp_file)
        temp_file.close()

        try:
            # When & Then: 应该抛出时间戳逻辑验证异常
            with pytest.raises(ResultFileInvalidError, match="交易数据格式无效"):
                await result_reader.read_result_file(temp_file.name)
        finally:
            os.unlink(temp_file.name)