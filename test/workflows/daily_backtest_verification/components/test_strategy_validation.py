# -*- coding: utf-8 -*-
"""
策略验证测试用例

测试策略名称限制和验证逻辑：
1. 策略名称在config中不存在时报错
2. 策略名称在config中存在但未启用时报错
3. 多策略模式下的验证逻辑
4. 报告生成逻辑
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import date

from workflows.daily_backtest_verification.components.verification_runner import (
    DailyBacktestVerificationRunner,
    VerificationResult,
    VerificationStatus,
    StrategyVerificationResult
)
from workflows.daily_backtest_verification.components.strategy_selector import (
    StrategySelector,
    MissingStrategyError,
    StrategyNotFoundError,
    StrategyDisabledError
)


class TestStrategyValidation:
    """策略验证测试类"""
    
    def setup_method(self):
        """测试前置设置"""
        self.selector = StrategySelector()
        self.runner = DailyBacktestVerificationRunner()
    
    def test_validate_strategy_in_config_not_found(self):
        """测试策略在配置中不存在的情况"""
        config_data = {
            'strategies': {
                'kol_strategy_v1': {'enabled': True},
                'kol_strategy_v2': {'enabled': False}
            }
        }
        
        # 测试不存在的策略
        with pytest.raises(StrategyNotFoundError) as exc_info:
            self.selector.validate_strategy_in_config('non_existent_strategy', config_data)
        
        assert "non_existent_strategy" in str(exc_info.value)
        assert "在配置中不存在" in str(exc_info.value)
    
    def test_validate_strategy_in_config_disabled(self):
        """测试策略存在但未启用的情况"""
        config_data = {
            'strategies': {
                'kol_strategy_v1': {'enabled': True},
                'kol_strategy_v2': {'enabled': False}
            }
        }
        
        # 测试未启用的策略
        with pytest.raises(StrategyDisabledError) as exc_info:
            self.selector.validate_strategy_in_config('kol_strategy_v2', config_data)
        
        assert "kol_strategy_v2" in str(exc_info.value)
        assert "存在但未启用" in str(exc_info.value)
    
    def test_validate_strategy_in_config_enabled(self):
        """测试策略存在且已启用的情况"""
        config_data = {
            'strategies': {
                'kol_strategy_v1': {'enabled': True},
                'kol_strategy_v2': {'enabled': False}
            }
        }
        
        # 测试已启用的策略，应该不抛出异常
        self.selector.validate_strategy_in_config('kol_strategy_v1', config_data)
    
    def test_validate_strategy_in_config_missing_strategies_section(self):
        """测试配置中缺少strategies部分的情况"""
        config_data = {
            'other_config': 'value'
        }
        
        # 测试缺少strategies部分
        with pytest.raises(StrategyNotFoundError) as exc_info:
            self.selector.validate_strategy_in_config('any_strategy', config_data)
        
        assert "any_strategy" in str(exc_info.value)
        assert "在配置中不存在" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_validate_single_strategy_not_found(self):
        """测试单个策略验证 - 策略不存在"""
        config_data = {
            'strategies': {
                'kol_strategy_v1': {'enabled': True}
            }
        }
        
        result = await self.runner._validate_single_strategy(config_data, 'non_existent_strategy')
        
        assert isinstance(result, StrategyVerificationResult)
        assert result.strategy_name == 'non_existent_strategy'
        assert result.success is False
        assert "在配置中不存在" in result.error_message
    
    @pytest.mark.asyncio
    async def test_validate_single_strategy_disabled(self):
        """测试单个策略验证 - 策略未启用"""
        config_data = {
            'strategies': {
                'kol_strategy_v1': {'enabled': True},
                'kol_strategy_v2': {'enabled': False}
            }
        }
        
        result = await self.runner._validate_single_strategy(config_data, 'kol_strategy_v2')
        
        assert isinstance(result, StrategyVerificationResult)
        assert result.strategy_name == 'kol_strategy_v2'
        assert result.success is False
        assert "存在但未启用" in result.error_message
    
    @pytest.mark.asyncio
    async def test_validate_single_strategy_success(self):
        """测试单个策略验证 - 成功"""
        config_data = {
            'strategies': {
                'kol_strategy_v1': {'enabled': True},
                'kol_strategy_v2': {'enabled': False}
            }
        }
        
        result = await self.runner._validate_single_strategy(config_data, 'kol_strategy_v1')
        
        assert isinstance(result, StrategyVerificationResult)
        assert result.strategy_name == 'kol_strategy_v1'
        assert result.success is True
        assert result.error_message is None
    
    @pytest.mark.asyncio
    async def test_multi_strategy_validation_all_success(self):
        """测试多策略验证 - 全部成功"""
        config_data = {
            'strategies': {
                'kol_strategy_v1': {'enabled': True},
                'kol_strategy_v2': {'enabled': True},
                'kol_strategy_v3': {'enabled': False}  # 未启用，不会被验证
            }
        }
        
        results = await self.runner._validate_strategy_configuration(config_data, None)
        
        assert len(results) == 2  # 只验证启用的策略
        assert all(r.success for r in results)
        strategy_names = [r.strategy_name for r in results]
        assert 'kol_strategy_v1' in strategy_names
        assert 'kol_strategy_v2' in strategy_names
        assert 'kol_strategy_v3' not in strategy_names
    
    @pytest.mark.asyncio
    async def test_multi_strategy_validation_partial_success(self):
        """测试多策略验证 - 部分成功"""
        config_data = {
            'strategies': {
                'kol_strategy_v1': {'enabled': True},
                'invalid_strategy': {'enabled': True}  # 这个策略不存在，会失败
            }
        }
        
        # 模拟invalid_strategy验证失败
        with patch.object(self.runner, '_validate_single_strategy') as mock_validate:
            mock_validate.side_effect = [
                StrategyVerificationResult('kol_strategy_v1', True),
                StrategyVerificationResult('invalid_strategy', False, '策略验证失败')
            ]
            
            results = await self.runner._validate_strategy_configuration(config_data, None)
            
            assert len(results) == 2
            successful = [r for r in results if r.success]
            failed = [r for r in results if not r.success]
            
            assert len(successful) == 1
            assert len(failed) == 1
            assert successful[0].strategy_name == 'kol_strategy_v1'
            assert failed[0].strategy_name == 'invalid_strategy'
    
    @pytest.mark.asyncio
    async def test_multi_strategy_validation_all_failed(self):
        """测试多策略验证 - 全部失败"""
        config_data = {
            'strategies': {
                'invalid_strategy1': {'enabled': True},
                'invalid_strategy2': {'enabled': True}
            }
        }
        
        # 模拟所有策略验证失败
        with patch.object(self.runner, '_validate_single_strategy') as mock_validate:
            mock_validate.side_effect = [
                StrategyVerificationResult('invalid_strategy1', False, '策略1验证失败'),
                StrategyVerificationResult('invalid_strategy2', False, '策略2验证失败')
            ]
            
            # _validate_strategy_configuration 不再抛出异常，而是返回结果列表
            results = await self.runner._validate_strategy_configuration(config_data, None)
            
            # 验证所有策略都失败
            assert len(results) == 2
            assert all(not r.success for r in results)
            assert results[0].strategy_name == 'invalid_strategy1'
            assert results[1].strategy_name == 'invalid_strategy2'
    
    @pytest.mark.asyncio
    async def test_multi_strategy_validation_no_enabled_strategies(self):
        """测试多策略验证 - 没有启用的策略"""
        config_data = {
            'strategies': {
                'kol_strategy_v1': {'enabled': False},
                'kol_strategy_v2': {'enabled': False}
            }
        }
        
        with pytest.raises(MissingStrategyError) as exc_info:
            await self.runner._validate_strategy_configuration(config_data, None)
        
        assert "配置中没有启用的策略" in str(exc_info.value)
    
    def test_get_enabled_strategies_from_config(self):
        """测试从配置中获取启用的策略"""
        config_data = {
            'strategies': {
                'kol_strategy_v1': {'enabled': True},
                'kol_strategy_v2': {'enabled': False},
                'kol_strategy_v3': {'enabled': True},
                'kol_strategy_v4': {'enabled': False}
            }
        }
        
        enabled_strategies = self.selector.get_enabled_strategies_from_config(config_data)
        
        assert len(enabled_strategies) == 2
        assert 'kol_strategy_v1' in enabled_strategies
        assert 'kol_strategy_v3' in enabled_strategies
        assert 'kol_strategy_v2' not in enabled_strategies
        assert 'kol_strategy_v4' not in enabled_strategies
    
    def test_get_enabled_strategies_from_config_empty(self):
        """测试从配置中获取启用的策略 - 空配置"""
        config_data = {
            'strategies': {
                'kol_strategy_v1': {'enabled': False},
                'kol_strategy_v2': {'enabled': False}
            }
        }
        
        enabled_strategies = self.selector.get_enabled_strategies_from_config(config_data)
        
        assert len(enabled_strategies) == 0
    
    def test_get_enabled_strategies_from_config_missing_strategies(self):
        """测试从配置中获取启用的策略 - 缺少strategies部分"""
        config_data = {
            'other_config': 'value'
        }
        
        enabled_strategies = self.selector.get_enabled_strategies_from_config(config_data)
        
        assert len(enabled_strategies) == 0


class TestStrategyValidationIntegration:
    """策略验证集成测试"""
    
    def setup_method(self):
        """测试前置设置"""
        self.runner = DailyBacktestVerificationRunner()
    
    @pytest.mark.asyncio
    @patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._load_configurations')
    @patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._execute_backtest_with_validation')
    @patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._read_backtest_result_with_validation')
    @patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._generate_and_send_report')
    async def test_run_verification_strategy_not_found(
        self,
        mock_generate_report,
        mock_read_result,
        mock_execute_backtest,
        mock_load_config
    ):
        """测试完整验证流程 - 策略不存在"""
        # 设置配置数据，包含不存在的策略
        config_data = {
            'strategies': {
                'kol_strategy_v1': {'enabled': True}
            }
        }
        mock_load_config.return_value = (config_data, '/tmp/config.yaml', ['123456'])
        
        # 执行验证，指定不存在的策略
        result = await self.runner.run_verification(strategy_name='non_existent_strategy')
        
        # 验证结果
        assert result.status == VerificationStatus.ERROR
        assert "在配置中不存在" in result.error_message
        
        # 验证没有执行后续步骤
        mock_execute_backtest.assert_not_called()
        mock_read_result.assert_not_called()
        mock_generate_report.assert_not_called()
    
    @pytest.mark.asyncio
    @patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._load_configurations')
    @patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._execute_backtest_with_validation')
    @patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._read_backtest_result_with_validation')
    @patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._generate_and_send_report')
    async def test_run_verification_strategy_disabled(
        self,
        mock_generate_report,
        mock_read_result,
        mock_execute_backtest,
        mock_load_config
    ):
        """测试完整验证流程 - 策略未启用"""
        # 设置配置数据，包含未启用的策略
        config_data = {
            'strategies': {
                'kol_strategy_v1': {'enabled': True},
                'kol_strategy_v2': {'enabled': False}
            }
        }
        mock_load_config.return_value = (config_data, '/tmp/config.yaml', ['123456'])
        
        # 执行验证，指定未启用的策略
        result = await self.runner.run_verification(strategy_name='kol_strategy_v2')
        
        # 验证结果
        assert result.status == VerificationStatus.ERROR
        assert "存在但未启用" in result.error_message
        
        # 验证没有执行后续步骤
        mock_execute_backtest.assert_not_called()
        mock_read_result.assert_not_called()
        mock_generate_report.assert_not_called()