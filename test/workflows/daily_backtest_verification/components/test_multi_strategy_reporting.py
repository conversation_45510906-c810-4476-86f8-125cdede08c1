# -*- coding: utf-8 -*-
"""
多策略报告生成测试用例

测试多策略验证模式下的报告生成逻辑：
1. 部分策略成功时的报告生成
2. 所有策略失败时的警告处理
3. 报告中包含失败策略的详细信息
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import date

from workflows.daily_backtest_verification.components.verification_runner import (
    DailyBacktestVerificationRunner,
    VerificationResult,
    VerificationStatus,
    StrategyVerificationResult
)
from workflows.daily_backtest_verification.components.strategy_selector import (
    StrategySelector,
    MissingStrategyError,
    StrategyNotFoundError,
    StrategyDisabledError
)


class TestMultiStrategyReporting:
    """多策略报告生成测试类"""
    
    def setup_method(self):
        """测试前置设置"""
        self.runner = DailyBacktestVerificationRunner()
        self.target_date = date(2024, 1, 15)
        self.execution_time = 45.2
    
    def test_format_report_text_multi_strategy_partial_success(self):
        """测试多策略部分成功的报告格式"""
        # 创建策略验证结果
        strategy_results = [
            StrategyVerificationResult('kol_strategy_v1', True),
            StrategyVerificationResult('kol_strategy_v2', True),
            StrategyVerificationResult('invalid_strategy', False, '策略在配置中不存在'),
            StrategyVerificationResult('disabled_strategy', False, '策略存在但未启用')
        ]
        
        # 创建验证结果 - 数据完全匹配
        result = VerificationResult(
            status=VerificationStatus.SUCCESS,
            date=self.target_date,
            total_trades=100,
            total_signals=100,
            matched_trades=100,
            execution_time=self.execution_time,
            strategy_results=strategy_results,
            failed_strategies=['invalid_strategy', 'disabled_strategy']
        )
        
        # 生成报告文本
        report_text = self.runner._format_report_text(result)
        
        # 验证报告内容
        assert "每日回测校验报告" in report_text
        assert "2024-01-15" in report_text
        assert "回测交易数: 100" in report_text
        assert "实际信号数: 100" in report_text
        assert "匹配交易数: 100" in report_text
        assert "执行时间: 45.2秒" in report_text
        
        # 验证策略执行情况
        assert "策略执行情况:" in report_text
        assert "成功策略 (2):" in report_text
        assert "kol_strategy_v1" in report_text
        assert "kol_strategy_v2" in report_text
        assert "失败策略 (2):" in report_text
        assert "invalid_strategy: 策略在配置中不存在" in report_text
        assert "disabled_strategy: 策略存在但未启用" in report_text
    
    def test_format_report_text_multi_strategy_all_failed(self):
        """测试多策略全部失败的报告格式"""
        # 创建策略验证结果
        strategy_results = [
            StrategyVerificationResult('invalid_strategy1', False, '策略在配置中不存在'),
            StrategyVerificationResult('invalid_strategy2', False, '策略存在但未启用'),
            StrategyVerificationResult('invalid_strategy3', False, '策略配置错误')
        ]
        
        # 创建错误结果
        result = VerificationResult(
            status=VerificationStatus.ERROR,
            date=self.target_date,
            error_message="所有策略验证都失败",
            strategy_results=strategy_results,
            failed_strategies=['invalid_strategy1', 'invalid_strategy2', 'invalid_strategy3']
        )
        
        # 生成报告文本
        report_text = self.runner._format_report_text(result)
        
        # 验证报告内容
        assert "每日回测校验报告" in report_text
        assert "2024-01-15" in report_text
        assert "策略执行情况:" in report_text
        assert "失败策略 (3):" in report_text
        assert "invalid_strategy1: 策略在配置中不存在" in report_text
        assert "invalid_strategy2: 策略存在但未启用" in report_text
        assert "invalid_strategy3: 策略配置错误" in report_text
        assert "错误信息: 所有策略验证都失败" in report_text
        
        # 不应该有成功策略部分
        assert "成功策略" not in report_text
    
    def test_format_report_text_single_strategy_success(self):
        """测试单策略成功的报告格式"""
        # 创建策略验证结果
        strategy_results = [
            StrategyVerificationResult('kol_strategy_v1', True)
        ]
        
        # 创建验证结果
        result = VerificationResult(
            status=VerificationStatus.SUCCESS,
            date=self.target_date,
            total_trades=50,
            total_signals=50,
            matched_trades=50,
            execution_time=self.execution_time,
            strategy_results=strategy_results
        )
        
        # 生成报告文本
        report_text = self.runner._format_report_text(result)
        
        # 验证报告内容
        assert "每日回测校验报告" in report_text
        assert "策略: kol_strategy_v1" in report_text
        
        # 单策略模式不应该显示策略执行情况列表
        assert "策略执行情况:" not in report_text
        assert "成功策略" not in report_text
        assert "失败策略" not in report_text
    
    def test_format_report_text_single_strategy_failed(self):
        """测试单策略失败的报告格式"""
        # 创建策略验证结果
        strategy_results = [
            StrategyVerificationResult('invalid_strategy', False, '策略在配置中不存在')
        ]
        
        # 创建错误结果
        result = VerificationResult(
            status=VerificationStatus.ERROR,
            date=self.target_date,
            error_message="策略验证失败",
            strategy_results=strategy_results,
            failed_strategies=['invalid_strategy']
        )
        
        # 生成报告文本
        report_text = self.runner._format_report_text(result)
        
        # 验证报告内容
        assert "每日回测校验报告" in report_text
        assert "策略: invalid_strategy" in report_text
        assert "策略验证失败: 策略在配置中不存在" in report_text
        assert "错误信息: 策略验证失败" in report_text
    
    @pytest.mark.asyncio
    @patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._load_configurations')
    @patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._execute_backtest_with_validation')
    @patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._read_backtest_result_with_validation')
    @patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._send_verification_notification')
    async def test_run_verification_multi_strategy_partial_success(
        self,
        mock_send_notification,
        mock_read_result,
        mock_execute_backtest,
        mock_load_config
    ):
        """测试多策略部分成功的完整验证流程"""
        # 设置配置数据
        config_data = {
            'strategies': {
                'kol_strategy_v1': {'enabled': True},
                'kol_strategy_v2': {'enabled': True},
                'invalid_strategy': {'enabled': True}  # 这个策略不存在，会失败
            }
        }
        mock_load_config.return_value = (config_data, '/tmp/config.yaml', ['123456'])
        
        # 模拟回测执行成功
        mock_backtest_result = Mock()
        mock_backtest_result.success = True
        mock_backtest_result.result_file_path = '/tmp/result.json'
        mock_execute_backtest.return_value = mock_backtest_result
        
        # 模拟结果读取成功 - 数据完全匹配
        mock_result_data = {
            'total_trades': 100,
            'total_signals': 100,
            'matched_trades': 100
        }
        mock_read_result.return_value = mock_result_data
        
        # 不需要模拟_validate_single_strategy，让它使用真实的策略验证逻辑
        # 但是需要模拟StrategySelector的方法来控制验证结果
        with patch.object(self.runner.strategy_selector, 'validate_strategy_name') as mock_validate_name, \
             patch.object(self.runner.strategy_selector, 'validate_strategy_in_config') as mock_validate_config:
            
            # 模拟策略名称验证都通过
            mock_validate_name.return_value = None
            
            # 模拟策略配置验证：前两个成功，第三个失败
            def side_effect_config(strategy_name, config_data):
                if strategy_name == 'invalid_strategy':
                    from workflows.daily_backtest_verification.components.strategy_selector import StrategyNotFoundError
                    raise StrategyNotFoundError(f"策略 '{strategy_name}' 在配置中不存在")
                # 其他策略验证通过
                return None
            
            mock_validate_config.side_effect = side_effect_config
            
            # 执行验证（多策略模式）
            result = await self.runner.run_verification()
            
            # 验证结果状态
            assert result.status == VerificationStatus.SUCCESS  # 有成功的策略，所以整体成功
            assert len(result.strategy_results) == 3
            
            # 验证成功和失败的策略
            successful_strategies = [r for r in result.strategy_results if r.success]
            failed_strategies = [r for r in result.strategy_results if not r.success]
            
            assert len(successful_strategies) == 2
            assert len(failed_strategies) == 1
            assert failed_strategies[0].strategy_name == 'invalid_strategy'
            
            # 验证报告包含失败策略信息
            assert "失败策略 (1):" in result.report_text
            assert "invalid_strategy: 策略 'invalid_strategy' 在配置中不存在" in result.report_text
            
            # 验证发送了通知
            mock_send_notification.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._load_configurations')
    @patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._send_error_notification')
    async def test_run_verification_multi_strategy_all_failed(
        self,
        mock_send_notification,
        mock_load_config
    ):
        """测试多策略全部失败的完整验证流程"""
        # 设置配置数据
        config_data = {
            'strategies': {
                'invalid_strategy1': {'enabled': True},
                'invalid_strategy2': {'enabled': True}
            }
        }
        mock_load_config.return_value = (config_data, '/tmp/config.yaml', ['123456'])
        
        # 模拟所有策略验证失败
        with patch.object(self.runner.strategy_selector, 'validate_strategy_name') as mock_validate_name, \
             patch.object(self.runner.strategy_selector, 'validate_strategy_in_config') as mock_validate_config:
            
            # 模拟策略名称验证都通过
            mock_validate_name.return_value = None
            
            # 模拟策略配置验证：所有策略都失败
            def side_effect_config(strategy_name, config_data):
                if strategy_name == 'invalid_strategy1':
                    from workflows.daily_backtest_verification.components.strategy_selector import StrategyNotFoundError
                    raise StrategyNotFoundError(f"策略 '{strategy_name}' 在配置中不存在")
                elif strategy_name == 'invalid_strategy2':
                    from workflows.daily_backtest_verification.components.strategy_selector import StrategyDisabledError
                    raise StrategyDisabledError(f"策略 '{strategy_name}' 存在但未启用")
                return None
            
            mock_validate_config.side_effect = side_effect_config
            
            # 执行验证（多策略模式）
            result = await self.runner.run_verification()
            
            # 验证结果状态
            assert result.status == VerificationStatus.ERROR  # 所有策略都失败
            assert "所有策略验证都失败" in result.error_message
            
            # 验证策略结果
            assert len(result.strategy_results) == 2
            assert all(not r.success for r in result.strategy_results)
            
            # 验证报告包含所有失败策略信息
            assert "失败策略 (2):" in result.report_text
            assert "invalid_strategy1: 策略 'invalid_strategy1' 在配置中不存在" in result.report_text
            assert "invalid_strategy2: 策略 'invalid_strategy2' 存在但未启用" in result.report_text
            
            # 验证发送了通知（错误通知）
            mock_send_notification.assert_called_once()
    
    def test_create_error_result_with_strategy_results(self):
        """测试创建包含策略结果的错误结果"""
        strategy_results = [
            StrategyVerificationResult('strategy1', False, '错误1'),
            StrategyVerificationResult('strategy2', False, '错误2')
        ]
        
        result = self.runner._create_error_result(
            self.target_date,
            "测试错误信息",
            strategy_results
        )
        
        assert result.status == VerificationStatus.ERROR
        assert result.error_message == "测试错误信息"
        assert len(result.strategy_results) == 2
        assert result.failed_strategies == ['strategy1', 'strategy2']
        assert "错误信息: 测试错误信息" in result.report_text