"""
工作流流量控制输出队列警告问题测试

测试并验证工作流节点流量控制配置的警告问题修复
"""

import pytest
import logging
import io
from typing import List

from utils.workflows.workflow_config import WorkflowConfigParser
from utils.workflows.nodes import InputNode, ProcessNode


class LogCapture:
    """日志捕获工具，用于测试日志输出"""
    
    def __init__(self, logger_name: str, level: int = logging.WARNING):
        """初始化日志捕获器
        
        Args:
            logger_name: 要捕获的logger名称
            level: 要捕获的日志级别
        """
        self.logger_name = logger_name
        self.level = level
        self.log_stream = io.StringIO()
        self.handler = logging.StreamHandler(self.log_stream)
        self.handler.setLevel(level)
        self.logger = logging.getLogger(logger_name)
        self.original_level = self.logger.level
        self.original_handlers = self.logger.handlers.copy()
    
    def start(self):
        """开始捕获日志"""
        self.logger.setLevel(self.level)
        self.logger.addHandler(self.handler)
    
    def stop(self):
        """停止捕获日志"""
        self.logger.removeHandler(self.handler)
        self.logger.setLevel(self.original_level)
        # 恢复原始handlers
        self.logger.handlers = self.original_handlers
    
    def get_logs(self) -> str:
        """获取捕获的日志内容"""
        return self.log_stream.getvalue()
    
    def has_warning(self, warning_text: str) -> bool:
        """检查是否包含特定警告"""
        return warning_text in self.get_logs()


@pytest.fixture
def test_workflow_config():
    """提供测试用的工作流配置"""
    return {
        "name": "测试流量控制警告工作流",
        "description": "用于测试流量控制警告问题的工作流",
        "nodes": [
            {
                "name": "TestInputNode",
                "node_type": "input",
                "interval": 1,
                "generate_data": "workflows.gmgn_kol_wallet_activity.handler.generate_data",
                "flow_control": {
                    "max_pending_messages": 20,
                    "check_interval": 1,
                    "enable_flow_control": True
                }
            },
            {
                "name": "TestProcessNode",
                "node_type": "process",
                "depend_ons": ["TestInputNode"],
                "concurrency": 2,
                "interval": 1,
                "process_item": "workflows.gmgn_kol_wallet_activity.handler.process_kol_wallet_activity",
                "flow_control": {
                    "max_pending_messages": 15,
                    "check_interval": 2,
                    "enable_flow_control": True
                }
            },
            {
                "name": "TestStorageNode",
                "node_type": "storage",
                "depend_ons": ["TestProcessNode"],
                "batch_size": 1000,
                "store_data": "workflows.gmgn_kol_wallet_activity.handler.store_data",
                "validate": "workflows.gmgn_kol_wallet_activity.handler.validate"
            }
        ]
    }


class TestFlowControlWarningBug:
    """测试流量控制警告Bug的修复"""
    
    def test_flow_control_warning_reproduced(self, test_workflow_config):
        """测试Bug修复后不再产生警告
        
        修复前这个测试会发现警告，修复后应该没有警告
        """
        parser = WorkflowConfigParser()
        
        # 为输入节点和处理节点设置日志捕获
        input_capture = LogCapture("TestInputNode", logging.WARNING)
        process_capture = LogCapture("TestProcessNode", logging.WARNING)
        
        input_capture.start()
        process_capture.start()
        
        try:
            # 创建工作流
            workflow = parser.create_workflow_from_config(test_workflow_config)
            
            # 检查是否产生了警告
            expected_warning = "启用了流量控制，但没有配置输出队列"
            
            input_has_warning = input_capture.has_warning(expected_warning)
            process_has_warning = process_capture.has_warning(expected_warning)
            
            # 修复后应该没有警告
            assert not input_has_warning and not process_has_warning, (
                f"修复后不应该有流量控制警告。"
                f"输入节点日志: {input_capture.get_logs()}\n"
                f"处理节点日志: {process_capture.get_logs()}"
            )
            
        finally:
            input_capture.stop()
            process_capture.stop()
    
    def test_flow_control_works_after_connection(self, test_workflow_config):
        """测试流量控制在节点连接后能正常工作
        
        验证即使有警告，流量控制功能本身仍然正常
        """
        parser = WorkflowConfigParser()
        workflow = parser.create_workflow_from_config(test_workflow_config)
        
        # 检查节点是否正确连接并配置了输出队列
        input_node = workflow.nodes.get("TestInputNode")
        process_node = workflow.nodes.get("TestProcessNode")
        
        assert input_node is not None, "输入节点应该存在"
        assert process_node is not None, "处理节点应该存在"
        
        # 检查输入节点的输出队列是否配置
        assert len(input_node.output_queues) > 0, "输入节点应该有输出队列"
        
        # 检查处理节点的输入队列是否配置
        assert process_node.input_queue is not None, "处理节点应该有输入队列"
        
        # 检查流量控制参数是否正确设置
        assert input_node.max_pending_messages == 20, "输入节点流量控制参数应该正确"
        assert input_node.check_interval == 1, "输入节点检查间隔应该正确"
        assert input_node.enable_flow_control is True, "输入节点应该启用流量控制"
        
        assert process_node.max_pending_messages == 15, "处理节点流量控制参数应该正确"
        assert process_node.check_interval == 2, "处理节点检查间隔应该正确"
        assert process_node.enable_flow_control is True, "处理节点应该启用流量控制"
    
    def test_no_flow_control_warning_after_fix(self, test_workflow_config):
        """测试修复后不再产生流量控制警告
        
        这个测试在修复后应该通过，在修复前可能失败
        """
        parser = WorkflowConfigParser()
        
        # 为所有可能的节点设置日志捕获
        input_capture = LogCapture("TestInputNode", logging.WARNING)
        process_capture = LogCapture("TestProcessNode", logging.WARNING)
        
        input_capture.start()
        process_capture.start()
        
        try:
            # 创建工作流
            workflow = parser.create_workflow_from_config(test_workflow_config)
            
            # 检查不应该产生警告
            warning_text = "启用了流量控制，但没有配置输出队列"
            
            input_has_warning = input_capture.has_warning(warning_text)
            process_has_warning = process_capture.has_warning(warning_text)
            
            # 修复后应该没有警告
            assert not input_has_warning, (
                f"修复后输入节点不应该有流量控制警告。"
                f"日志内容: {input_capture.get_logs()}"
            )
            assert not process_has_warning, (
                f"修复后处理节点不应该有流量控制警告。"
                f"日志内容: {process_capture.get_logs()}"
            )
            
            # 验证流量控制器确实被正确设置
            input_node = workflow.nodes.get("TestInputNode")
            process_node = workflow.nodes.get("TestProcessNode")
            
            # 检查流量控制器是否存在且配置正确
            assert hasattr(input_node, 'flow_controller'), "输入节点应该有流量控制器"
            assert hasattr(process_node, 'flow_controller'), "处理节点应该有流量控制器"
            
            # 如果流量控制器存在，检查其配置
            if input_node.flow_controller:
                assert input_node.flow_controller.max_pending_messages == 20
                assert input_node.flow_controller.enable_flow_control is True
                
            if process_node.flow_controller:
                assert process_node.flow_controller.max_pending_messages == 15
                assert process_node.flow_controller.enable_flow_control is True
        
        finally:
            input_capture.stop()
            process_capture.stop()
    
    def test_workflow_config_parsing_integrity(self, test_workflow_config):
        """测试工作流配置解析的完整性
        
        确保修复不会破坏配置解析的其他功能
        """
        parser = WorkflowConfigParser()
        workflow = parser.create_workflow_from_config(test_workflow_config)
        
        # 检查工作流基本信息
        assert workflow.name == "测试流量控制警告工作流"
        assert len(workflow.nodes) == 3
        
        # 检查节点类型
        input_node = workflow.nodes.get("TestInputNode")
        process_node = workflow.nodes.get("TestProcessNode")
        storage_node = workflow.nodes.get("TestStorageNode")
        
        assert isinstance(input_node, InputNode), "TestInputNode应该是InputNode类型"
        assert isinstance(process_node, ProcessNode), "TestProcessNode应该是ProcessNode类型"
        
        # 检查节点连接关系
        connections = workflow.get_node_connections()
        assert len(connections) == 2, "应该有2个连接关系"
        
        # 验证连接关系正确
        connection_pairs = [(conn[0], conn[1]) for conn in connections]
        assert ("TestInputNode", "TestProcessNode") in connection_pairs
        assert ("TestProcessNode", "TestStorageNode") in connection_pairs


class TestFlowControlConfigurationEdgeCases:
    """测试流量控制配置的边界情况"""
    
    def test_input_node_without_flow_control(self):
        """测试没有配置flow_control的输入节点不产生警告"""
        config = {
            "name": "测试无流量控制工作流",
            "nodes": [
                {
                    "name": "NoFlowControlInputNode",
                    "node_type": "input",
                    "generate_data": "workflows.gmgn_kol_wallet_activity.handler.generate_data"
                }
            ]
        }
        
        parser = WorkflowConfigParser()
        capture = LogCapture("NoFlowControlInputNode", logging.WARNING)
        capture.start()
        
        try:
            workflow = parser.create_workflow_from_config(config)
            
            # 没有配置flow_control的节点不应该有警告
            warning_text = "启用了流量控制，但没有配置输出队列"
            assert not capture.has_warning(warning_text), (
                f"没有配置flow_control的节点不应该有警告。日志: {capture.get_logs()}"
            )
        finally:
            capture.stop()
    
    def test_flow_control_disabled(self):
        """测试显式禁用流量控制的节点不产生警告"""
        config = {
            "name": "测试禁用流量控制工作流",
            "nodes": [
                {
                    "name": "DisabledFlowControlNode",
                    "node_type": "input",
                    "generate_data": "workflows.gmgn_kol_wallet_activity.handler.generate_data",
                    "flow_control": {
                        "enable_flow_control": False,
                        "max_pending_messages": 10
                    }
                }
            ]
        }
        
        parser = WorkflowConfigParser()
        capture = LogCapture("DisabledFlowControlNode", logging.WARNING)
        capture.start()
        
        try:
            workflow = parser.create_workflow_from_config(config)
            
            # 禁用flow_control的节点不应该有警告
            warning_text = "启用了流量控制，但没有配置输出队列"
            assert not capture.has_warning(warning_text), (
                f"禁用flow_control的节点不应该有警告。日志: {capture.get_logs()}"
            )
        finally:
            capture.stop() 