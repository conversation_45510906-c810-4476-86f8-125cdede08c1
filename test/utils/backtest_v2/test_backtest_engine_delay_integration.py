"""回测引擎V2延迟模拟集成测试

验证延迟模拟功能是否正确集成到回测引擎V2中
"""

import unittest
from unittest.mock import Mock, patch, AsyncMock
import logging
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

from utils.backtest_v2.backtest_engine import BacktestEngineV2
from utils.backtest_v2.config_manager import BacktestConfigV2, DelaySimulationConfig

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestBacktestEngineDelayIntegration(unittest.IsolatedAsyncioTestCase):
    """回测引擎V2延迟模拟集成测试类"""
    
    def setUp(self):
        """测试前置条件"""
        # 创建启用延迟模拟的配置
        self.delay_config = DelaySimulationConfig(
            enabled=True,
            strategy="fixed",
            fixed_delay_seconds=25.69,
            max_delay_seconds=300.0
        )
        
        self.config_with_delay = BacktestConfigV2(
            backtest_start_time=1672531200,
            backtest_end_time=1675209600,
            delay_simulation=self.delay_config
        )
        
        # 创建禁用延迟模拟的配置
        self.config_without_delay = BacktestConfigV2(
            backtest_start_time=1672531200,
            backtest_end_time=1675209600,
            delay_simulation=DelaySimulationConfig(enabled=False)
        )
    
    def test_engine_initialization_with_delay_enabled(self):
        """测试启用延迟模拟时回测引擎的初始化"""
        engine = BacktestEngineV2(self.config_with_delay)
        
        # 验证延迟模拟器已初始化
        self.assertIsNotNone(engine.delay_simulator)
        self.assertEqual(engine.config.delay_simulation.enabled, True)
        self.assertEqual(engine.config.delay_simulation.fixed_delay_seconds, 25.69)
    
    def test_engine_initialization_with_delay_disabled(self):
        """测试禁用延迟模拟时回测引擎的初始化"""
        engine = BacktestEngineV2(self.config_without_delay)
        
        # 验证延迟模拟器未初始化
        self.assertIsNone(engine.delay_simulator)
        self.assertEqual(engine.config.delay_simulation.enabled, False)
    
    def test_apply_delay_simulation_to_buy_signals(self):
        """测试买入信号延迟模拟应用方法的基本功能"""
        engine = BacktestEngineV2(self.config_with_delay)
        
        # 模拟买入信号 - 使用正确的时间戳字段名
        buy_signals = [
            {
                'signal_timestamp': 1672531200,  # 2023-01-01 00:00:00
                'token_address': 'test_token_1',
                'avg_price_usd': 0.001
            },
            {
                'signal_timestamp': 1672531260,  # 2023-01-01 00:01:00  
                'token_address': 'test_token_2', 
                'avg_price_usd': 0.002
            }
        ]
        
        # 应用延迟模拟
        delayed_signals = engine._apply_delay_simulation(buy_signals)
        
        # 验证结果
        self.assertEqual(len(delayed_signals), 2)
        
        # 验证延迟模拟统计信息已被记录
        self.assertTrue(hasattr(engine, 'delay_simulation_stats'))
        stats = engine.delay_simulation_stats
        self.assertIsInstance(stats, dict)
        self.assertEqual(stats['total_signals'], 2)
        
        # 验证信号已被处理（无论成功或失败）
        self.assertEqual(stats['success_count'] + stats['failure_count'], 2)
    
    def test_apply_delay_simulation_to_sell_signals(self):
        """测试卖出信号延迟模拟应用方法的基本功能"""
        engine = BacktestEngineV2(self.config_with_delay)
        
        # 模拟卖出信号
        sell_signals = [
            {
                'sell_timestamp': 1672531800,  # 2023-01-01 00:10:00
                'token_address': 'test_token_1',
                'avg_price_usd': 0.0015,
                'sell_reason': 'profit_target'
            },
            {
                'sell_timestamp': 1672532100,  # 2023-01-01 00:15:00
                'token_address': 'test_token_2',
                'avg_price_usd': 0.0025,
                'sell_reason': 'stop_loss'
            }
        ]
        
        # 应用延迟模拟到卖出信号
        delayed_sell_signals = engine._apply_delay_simulation_to_sell_signals(sell_signals)
        
        # 验证结果
        self.assertEqual(len(delayed_sell_signals), 2)
        
        # 验证每个卖出信号都被处理
        for i, delayed_signal in enumerate(delayed_sell_signals):
            original_signal = sell_signals[i]
            
            # 验证原始字段保持不变
            self.assertEqual(delayed_signal['token_address'], original_signal['token_address'])
            self.assertEqual(delayed_signal['avg_price_usd'], original_signal['avg_price_usd'])
            self.assertEqual(delayed_signal['sell_reason'], original_signal['sell_reason'])
            
            # 如果延迟应用成功，应该有延迟相关字段
            if 'original_sell_timestamp' in delayed_signal:
                self.assertEqual(delayed_signal['original_sell_timestamp'], original_signal['sell_timestamp'])
                self.assertIn('sell_delay_applied_seconds', delayed_signal)
                # 验证延迟后的时间戳大于原始时间戳
                self.assertGreater(delayed_signal['sell_timestamp'], original_signal['sell_timestamp'])
    
    def test_apply_delay_simulation_with_disabled_simulator(self):
        """测试延迟模拟器禁用时的行为"""
        engine = BacktestEngineV2(self.config_without_delay)
        
        buy_signals = [
            {
                'signal_timestamp': 1672531200,
                'token_address': 'test_token_1',
                'avg_price_usd': 0.001
            }
        ]
        
        sell_signals = [
            {
                'sell_timestamp': 1672531800,
                'token_address': 'test_token_1',
                'avg_price_usd': 0.0015,
                'sell_reason': 'profit_target'
            }
        ]
        
        # 应用延迟模拟（应该直接返回原信号）
        delayed_buy_signals = engine._apply_delay_simulation(buy_signals)
        delayed_sell_signals = engine._apply_delay_simulation_to_sell_signals(sell_signals)
        
        # 验证信号未被修改
        self.assertEqual(len(delayed_buy_signals), 1)
        self.assertEqual(delayed_buy_signals[0], buy_signals[0])
        
        self.assertEqual(len(delayed_sell_signals), 1)
        self.assertEqual(delayed_sell_signals[0], sell_signals[0])
    
    def test_buy_sell_signal_timestamp_matching_with_delay(self):
        """
        测试延迟应用后买卖信号的时间戳匹配逻辑
        验证买入信号延迟后，卖出信号仍能正确匹配
        """
        # Arrange
        engine = BacktestEngineV2(self.config_with_delay)
        
        # 模拟买入信号（延迟前）
        original_timestamp = datetime(2025, 1, 1, 10, 0, 0)
        buy_signal = {
            'signal_timestamp': original_timestamp,
            'token_address': '0x123...abc',
            'signal_type': 'buy',
            'confidence': 0.8,
            'price_usd': 1.0
        }
        
        # 模拟卖出信号（基于原始买入时间戳）
        original_sell_timestamp = original_timestamp + timedelta(minutes=30)
        sell_signal = {
            'signal_timestamp': original_sell_timestamp,
            'sell_timestamp': original_sell_timestamp,  # 添加延迟模拟器期望的字段
            'token_address': '0x123...abc',
            'signal_type': 'sell',
            'buy_signal_timestamp': original_timestamp,  # 关键：基于原始时间戳
            'confidence': 0.7,
            'price_usd': 1.2
        }
        
        # Act - 应用延迟到买入信号
        delayed_buy_result = engine._apply_delay_simulation([buy_signal])
        
        # 验证买入信号延迟应用
        self.assertEqual(len(delayed_buy_result), 1)
        delayed_buy_signal = delayed_buy_result[0]
        
        # 验证原始时间戳被保存
        self.assertIn('original_signal_timestamp', delayed_buy_signal)
        self.assertEqual(delayed_buy_signal['original_signal_timestamp'], original_timestamp)
        
        # 验证延迟后时间戳（应该比原始时间戳大）
        self.assertGreater(delayed_buy_signal['signal_timestamp'], original_timestamp)
        
        # Act - 应用延迟到卖出信号
        delayed_sell_result = engine._apply_delay_simulation_to_sell_signals([sell_signal])
        
        # 验证卖出信号延迟应用
        self.assertEqual(len(delayed_sell_result), 1)
        delayed_sell_signal = delayed_sell_result[0]
        
        # 验证卖出信号的buy_signal_timestamp保持不变（用于匹配）
        self.assertEqual(delayed_sell_signal['buy_signal_timestamp'], original_timestamp)
        
        # 验证卖出信号本身的时间戳被延迟（应该比原始卖出时间戳大）
        original_sell_timestamp_float = original_sell_timestamp.timestamp()
        self.assertGreater(delayed_sell_signal['sell_timestamp'], original_sell_timestamp_float)

    def test_delay_simulation_error_handling(self):
        """
        测试延迟模拟的错误处理机制
        验证当延迟计算失败时的处理逻辑
        """
        # Arrange - 使用有效的配置
        engine = BacktestEngineV2(self.config_with_delay)
        
        # 创建一个会导致延迟计算错误的信号（缺少必要字段）
        invalid_signal = {
            'token_address': '0x123...abc',
            'signal_type': 'buy',
            'confidence': 0.8,
            'price_usd': 1.0
            # 故意缺少 signal_timestamp 字段
        }
        
        valid_signal_timestamp = int(datetime(2025, 1, 1, 10, 0, 0).timestamp())
        valid_signal = {
            'signal_timestamp': valid_signal_timestamp,
            'token_address': '0x456...def',
            'signal_type': 'buy',
            'confidence': 0.8,
            'price_usd': 1.0
        }
        
        test_signals = [invalid_signal, valid_signal]
        
        # Act - 应用延迟模拟
        result_signals = engine._apply_delay_simulation(test_signals)
        
        # Assert - 即使有错误，处理应该继续
        self.assertEqual(len(result_signals), 2)
        
        # 第一个信号应该保持原样（因为延迟应用失败）
        self.assertEqual(result_signals[0], invalid_signal)
        
        # 第二个信号应该被正确延迟
        self.assertIn('original_signal_timestamp', result_signals[1])
        self.assertEqual(result_signals[1]['original_signal_timestamp'], valid_signal_timestamp)
        
        # 验证延迟后的时间戳大于原始时间戳
        self.assertGreater(result_signals[1]['signal_timestamp'], valid_signal_timestamp)
        
        # 验证延迟统计信息记录了失败
        if hasattr(engine, 'delay_simulation_stats'):
            stats = engine.delay_simulation_stats
            self.assertGreater(stats['failure_count'], 0)  # 应该有失败记录

    def test_delay_simulation_statistics_collection(self):
        """
        测试延迟模拟统计信息的收集
        验证延迟应用后统计信息的准确性
        """
        # Arrange
        engine = BacktestEngineV2(self.config_with_delay)
        
        # 多个测试信号
        signals = [
            {
                'signal_timestamp': datetime(2025, 1, 1, 10, 0, 0),
                'token_address': '0x123...abc',
                'signal_type': 'buy',
                'confidence': 0.8,
                'price_usd': 1.0
            },
            {
                'signal_timestamp': datetime(2025, 1, 1, 11, 0, 0),
                'token_address': '0x456...def',
                'signal_type': 'sell',
                'confidence': 0.7,
                'price_usd': 1.2
            }
        ]
        
        # 保存原始时间戳用于比较
        original_timestamps = [signal['signal_timestamp'] for signal in signals]
        
        # Act
        delayed_signals = engine._apply_delay_simulation(signals)
        
        # Assert
        self.assertEqual(len(delayed_signals), 2)
        
        # 验证每个信号都被正确延迟
        for i, delayed_signal in enumerate(delayed_signals):
            original_timestamp = original_timestamps[i]
            
            # 验证延迟应用：延迟后的时间戳应该大于原始时间戳
            self.assertGreater(delayed_signal['signal_timestamp'], original_timestamp)
            
            # 验证原始时间戳被正确保存
            self.assertEqual(delayed_signal['original_signal_timestamp'], original_timestamp)
            
            # 验证延迟时间在合理范围内（配置的固定延迟是25.69秒）
            delay_seconds = (delayed_signal['signal_timestamp'] - original_timestamp).total_seconds()
            self.assertAlmostEqual(delay_seconds, 25.69, places=2)

    def test_delay_simulation_with_mixed_signal_types(self):
        """
        测试混合信号类型的延迟模拟
        验证买入和卖出信号都能正确应用延迟
        """
        # Arrange
        engine = BacktestEngineV2(self.config_with_delay)
        
        # 混合信号类型
        mixed_signals = [
            {
                'signal_timestamp': datetime(2025, 1, 1, 10, 0, 0),
                'token_address': '0x123...abc',
                'signal_type': 'buy',
                'confidence': 0.8,
                'price_usd': 1.0
            },
            {
                'signal_timestamp': datetime(2025, 1, 1, 10, 30, 0),
                'token_address': '0x123...abc',
                'signal_type': 'sell',
                'buy_signal_timestamp': datetime(2025, 1, 1, 10, 0, 0),
                'confidence': 0.7,
                'price_usd': 1.2
            },
            {
                'signal_timestamp': datetime(2025, 1, 1, 11, 0, 0),
                'token_address': '0x456...def',
                'signal_type': 'buy',
                'confidence': 0.9,
                'price_usd': 0.8
            }
        ]
        
        # 保存原始时间戳用于比较
        original_timestamps = [signal['signal_timestamp'] for signal in mixed_signals]
        
        # Act
        delayed_signals = engine._apply_delay_simulation(mixed_signals)
        
        # Assert
        self.assertEqual(len(delayed_signals), 3)
        
        # 验证每种信号类型都被正确处理
        for i, delayed_signal in enumerate(delayed_signals):
            original_timestamp = original_timestamps[i]
            
            # 验证延迟应用：延迟后的时间戳应该大于原始时间戳
            self.assertGreater(delayed_signal['signal_timestamp'], original_timestamp)
            
            # 验证原始时间戳被正确保存
            self.assertEqual(delayed_signal['original_signal_timestamp'], original_timestamp)
            
            # 验证信号类型保持不变
            self.assertEqual(delayed_signal['signal_type'], mixed_signals[i]['signal_type'])
            
            # 对于卖出信号，验证buy_signal_timestamp保持不变
            if mixed_signals[i]['signal_type'] == 'sell':
                self.assertEqual(
                    delayed_signal['buy_signal_timestamp'], 
                    mixed_signals[i]['buy_signal_timestamp']
                )
            
            # 验证延迟时间在合理范围内（配置的固定延迟是25.69秒）
            delay_seconds = (delayed_signal['signal_timestamp'] - original_timestamp).total_seconds()
            self.assertAlmostEqual(delay_seconds, 25.69, places=2)

    @patch.object(BacktestEngineV2, '_analyze_signals_concurrent')
    async def test_delay_simulation_in_full_backtest_flow(self, mock_analyze_signals):
        """测试延迟模拟在完整回测流程中的集成"""
        engine = BacktestEngineV2(self.config_with_delay)
        
        # Mock所有依赖项
        with patch.object(engine, 'data_query') as mock_data_query, \
             patch.object(engine, 'sell_strategy') as mock_sell_strategy:
            
            mock_data_query.build_buy_data_aggregation_pipeline = AsyncMock(return_value=[])
            mock_data_query.execute_aggregation_query = AsyncMock(return_value={})
            mock_data_query.get_token_info = AsyncMock(return_value={})
            mock_data_query.filter_new_token_records = AsyncMock(return_value={})
            
            mock_sell_strategy.preload_sell_data_for_tokens = AsyncMock()
            mock_sell_strategy.determine_sell_signals = AsyncMock(return_value=[])
            mock_sell_strategy.sell_data_cache = {}
            
            # Mock信号分析结果
            mock_buy_signals = [
                {
                    'signal_timestamp': 1672531200,
                    'token_address': 'test_token_1',
                    'avg_price_usd': 0.001,
                    'total_volume_usd': 1000,
                    'trigger_kol_count': 5
                }
            ]
            mock_analyze_signals.return_value = mock_buy_signals
            
            # Mock结果分析器
            with patch.object(engine, 'result_analyzer') as mock_result_analyzer:
                mock_result_analyzer.analyze.return_value = {
                    'total_trades': 1,
                    'win_rate': 0.75,
                    'total_return': 0.6271
                }
                
                # 执行回测
                results = await engine.run_backtest()
                
                # 验证延迟模拟统计信息被包含在结果中
                self.assertIn('delay_simulation', results)
                self.assertEqual(results['delay_simulation']['enabled'], True)
                self.assertIn('statistics', results['delay_simulation'])

    def test_delay_simulation_statistics_tracking(self):
        """测试延迟模拟统计信息的跟踪功能"""
        engine = BacktestEngineV2(self.config_with_delay)
        
        # 准备测试信号
        buy_signals = [
            {
                'signal_timestamp': 1672531200,
                'token_address': 'test_token_1',
                'avg_price_usd': 0.001
            },
            {
                'signal_timestamp': 1672531260,
                'token_address': 'test_token_2',
                'avg_price_usd': 0.002
            }
        ]
        
        # 应用延迟模拟
        engine._apply_delay_simulation(buy_signals)
        
        # 验证统计信息
        self.assertTrue(hasattr(engine, 'delay_simulation_stats'))
        stats = engine.delay_simulation_stats
        
        # 验证统计字段
        required_stats_fields = [
            'total_signals', 'success_count', 'failure_count',
            'total_delay_applied', 'min_delay', 'max_delay'
        ]
        
        for field in required_stats_fields:
            self.assertIn(field, stats)
        
        # 验证统计数值合理性
        self.assertEqual(stats['total_signals'], 2)
        self.assertEqual(stats['success_count'] + stats['failure_count'], 2)
        self.assertGreaterEqual(stats['total_delay_applied'], 0)

    def test_delay_simulation_with_trade_generation(self):
        """
        测试延迟模拟后是否能正常生成交易
        验证买卖信号匹配和交易计算的完整流程
        """
        # Arrange
        engine = BacktestEngineV2(self.config_with_delay)
        
        # 模拟原始时间戳
        original_timestamp = 1672531200  # 2023-01-01 00:00:00
        
        # 模拟买入信号（延迟前）
        buy_signal = {
            'token_address': '0x123...abc',
            'signal_timestamp': original_timestamp,
            'original_signal_timestamp': original_timestamp,  # 模拟延迟应用后的状态
            'avg_price_usd': 1.0,
            'total_volume_usd': 100.0,
            'trigger_kol_count': 5
        }
        
        # 应用延迟到买入信号
        delayed_buy_signals = engine._apply_delay_simulation([buy_signal])
        self.assertEqual(len(delayed_buy_signals), 1)
        
        delayed_buy_signal = delayed_buy_signals[0]
        
        # 验证买入信号延迟应用
        self.assertEqual(delayed_buy_signal['original_signal_timestamp'], original_timestamp)
        self.assertGreater(delayed_buy_signal['signal_timestamp'], original_timestamp)
        
        # 模拟卖出信号（基于原始买入时间戳）
        original_sell_timestamp = original_timestamp + 30 * 60  # 买入后30分钟
        sell_signal = {
            'signal_timestamp': original_sell_timestamp,
            'sell_timestamp': original_sell_timestamp,  # 添加延迟模拟器期望的字段
            'token_address': '0x123...abc',
            'signal_type': 'sell',
            'buy_signal_timestamp': original_timestamp,  # 关键：基于原始时间戳
            'confidence': 0.7,
            'price_usd': 1.2
        }
        
        # 应用延迟到卖出信号
        delayed_sell_signals = engine._apply_delay_simulation_to_sell_signals([sell_signal])
        self.assertEqual(len(delayed_sell_signals), 1)
        
        delayed_sell_signal = delayed_sell_signals[0]
        
        # 验证卖出信号延迟应用
        self.assertEqual(delayed_sell_signal['buy_signal_timestamp'], original_timestamp)  # 匹配时间戳保持不变
        self.assertGreater(delayed_sell_signal['sell_timestamp'], original_sell_timestamp)  # 卖出时间被延迟
        
        # Act - 计算交易
        trades = engine._calculate_trades(delayed_buy_signals, delayed_sell_signals)
        
        # Assert - 验证交易生成
        self.assertEqual(len(trades), 1, "应该生成1笔交易")
        
        trade = trades[0]
        self.assertEqual(trade['token_address'], '0x123...abc')
        self.assertEqual(trade['buy_timestamp'], delayed_buy_signal['signal_timestamp'])
        self.assertEqual(trade['sell_timestamp'], delayed_sell_signal['sell_timestamp'])
        self.assertGreater(trade['holding_hours'], 0, "持有时间应该大于0")
        
        # 验证延迟信息记录
        self.assertEqual(trade['original_buy_timestamp'], original_timestamp)
        self.assertGreater(trade['buy_delay_applied_seconds'], 0)
        self.assertGreater(trade['sell_delay_applied_seconds'], 0)
        
        print(f"✅ 交易生成成功:")
        print(f"   买入时间: {trade['buy_timestamp']} (延迟: {trade['buy_delay_applied_seconds']}s)")
        print(f"   卖出时间: {trade['sell_timestamp']} (延迟: {trade['sell_delay_applied_seconds']}s)")
        print(f"   持有时间: {trade['holding_hours']:.2f} 小时")

    def test_delay_simulation_time_logic_validation(self):
        """
        测试延迟模拟的时间逻辑验证
        验证当延迟导致时间逻辑错误时的处理
        """
        # Arrange
        engine = BacktestEngineV2(self.config_with_delay)
        
        original_timestamp = 1672531200
        
        # 创建一个买入信号，延迟后时间戳
        buy_signal = {
            'token_address': '0x123...abc',
            'signal_timestamp': original_timestamp + 30,  # 延迟后的买入时间
            'original_signal_timestamp': original_timestamp,
            'avg_price_usd': 1.0,
            'total_volume_usd': 100.0
        }
        
        # 创建一个卖出信号，延迟后时间戳小于买入时间戳（异常情况）
        sell_signal = {
            'token_address': '0x123...abc',
            'buy_signal_timestamp': original_timestamp,
            'sell_timestamp': original_timestamp + 20,  # 延迟后的卖出时间早于买入时间
            'avg_price_usd': 1.2,
            'sell_reason': 'timeout'
        }
        
        # Act
        trades = engine._calculate_trades([buy_signal], [sell_signal])
        
        # Assert - 应该没有生成交易（因为时间逻辑错误）
        self.assertEqual(len(trades), 0, "时间逻辑错误时不应该生成交易")
        
        print("✅ 时间逻辑验证正常工作，异常情况被正确过滤")

    async def test_trade_generation_with_delay_signal_matching(self):
        """测试延迟信号的买卖匹配逻辑"""
        # 创建延迟模拟配置
        delay_config = DelaySimulationConfig(
            enabled=True,
            strategy='fixed',
            fixed_delay_seconds=20,
            max_delay_seconds=30
        )
        
        engine = BacktestEngineV2(self.config_with_delay)
        engine.config.delay_simulation = delay_config
        
        # 创建买入信号（有延迟）
        original_timestamp = int(datetime(2024, 6, 15, 10, 0, 0).timestamp())
        delayed_timestamp = original_timestamp + 20  # 延迟20秒
        
        buy_signal = {
            'signal_timestamp': delayed_timestamp,  # 延迟后的时间戳
            'original_signal_timestamp': original_timestamp,  # 原始时间戳
            'token_address': 'test_token_123',
            'kol_wallets': ['wallet1', 'wallet2'],
            'avg_price_usd': 1.0
        }
        
        # 模拟卖出策略
        sell_strategy = engine.sell_strategy
        
        # 测试timeout卖出策略
        timeout_sell_signal = await sell_strategy._timeout_sell_strategy(buy_signal)
        
        self.assertIsNotNone(timeout_sell_signal)
        if timeout_sell_signal is None:
            self.fail("timeout_sell_signal should not be None")
            return
            
        # 关键检查：buy_signal_timestamp应该使用原始时间戳
        self.assertEqual(timeout_sell_signal['buy_signal_timestamp'], original_timestamp)
        self.assertEqual(timeout_sell_signal['token_address'], 'test_token_123')
        
        # 验证买卖信号匹配逻辑
        buy_signals = [buy_signal]
        sell_signals = [timeout_sell_signal]  # 确保不是None
        
        # 调用交易计算方法
        trades = engine._calculate_trades(buy_signals, sell_signals)
        
        # 关键验证：应该成功匹配并生成1笔交易
        self.assertEqual(len(trades), 1, "应该成功匹配并生成1笔交易")
        
        trade = trades[0]
        self.assertEqual(trade['token_address'], 'test_token_123')
        self.assertEqual(trade['buy_timestamp'], delayed_timestamp)  # 买入时间应该是延迟后的时间
        self.assertEqual(trade['sell_timestamp'], timeout_sell_signal['sell_timestamp'])
        
        logger.info(f"成功生成交易: {trade}")

    async def test_signal_matching_without_delay(self):
        """测试无延迟情况下的信号匹配（作为对照组）"""
        engine = BacktestEngineV2(self.config_with_delay)
        
        # 创建无延迟的买入信号
        timestamp = int(datetime(2024, 6, 15, 10, 0, 0).timestamp())
        
        buy_signal = {
            'signal_timestamp': timestamp,
            'token_address': 'test_token_456',
            'kol_wallets': ['wallet1', 'wallet2'],
            'avg_price_usd': 1.0
        }
        
        # 模拟卖出策略
        sell_strategy = engine.sell_strategy
        timeout_sell_signal = await sell_strategy._timeout_sell_strategy(buy_signal)
        
        self.assertIsNotNone(timeout_sell_signal)
        if timeout_sell_signal is None:
            self.fail("timeout_sell_signal should not be None")
            return
            
        # 无延迟情况下，buy_signal_timestamp应该等于signal_timestamp
        self.assertEqual(timeout_sell_signal['buy_signal_timestamp'], timestamp)
        
        # 验证匹配
        buy_signals = [buy_signal]
        sell_signals = [timeout_sell_signal]  # 确保不是None
        trades = engine._calculate_trades(buy_signals, sell_signals)
        
        self.assertEqual(len(trades), 1, "无延迟情况下也应该成功匹配")
        
        trade = trades[0]
        self.assertEqual(trade['buy_timestamp'], timestamp)
        
        logger.info(f"无延迟交易生成成功: {trade}")


if __name__ == '__main__':
    unittest.main() 