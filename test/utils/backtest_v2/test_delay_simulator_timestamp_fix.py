"""
Unix时间戳0处理修复测试

测试DelaySimulator在处理Unix时间戳为0时的行为，确保：
1. Unix时间戳0（1970-01-01 00:00:00 UTC）被正确识别为有效时间戳
2. 不会错误地回退到NEW_TIMESTAMP_FIELD
3. 延迟计算能够正常工作

创建日期：2025-01-17
测试级别：单元测试
Bug修复：修复timestamp为0时被错误当作falsy值处理的问题
"""

import pytest
from datetime import datetime
from unittest.mock import Mock
from utils.backtest_v2.delay_simulator import DelaySimulator, DelayResult, InvalidSignalError


class TestDelaySimulatorTimestampZeroFix:
    """测试Unix时间戳为0的处理修复"""
    
    def setup_method(self):
        """设置测试环境"""
        # 创建模拟延迟策略
        self.mock_strategy = Mock()
        self.mock_strategy.calculate_delay.return_value = 30.0
        self.mock_strategy.get_strategy_name.return_value = "test_strategy"
        self.mock_strategy.name = "test_strategy"
        
        self.simulator = DelaySimulator(self.mock_strategy)
    
    def test_unix_timestamp_zero_legacy_field(self):
        """测试使用传统字段名且时间戳为0的情况"""
        # Unix时间戳0 = 1970-01-01 00:00:00 UTC
        signal = {
            'timestamp': 0,  # LEGACY_TIMESTAMP_FIELD
            'token_address': '0x123'
        }
        
        result = self.simulator.apply_delay_to_signal(signal)
        
        # 验证结果
        assert result is not None
        assert isinstance(result, DelayResult)
        assert result.delay_seconds == 30.0
        assert result.original_timestamp == datetime.fromtimestamp(0)
        assert result.strategy_used == "test_strategy"
        
        # 验证策略被正确调用
        self.mock_strategy.calculate_delay.assert_called_once()
        call_args = self.mock_strategy.calculate_delay.call_args[0][0]
        assert call_args == datetime.fromtimestamp(0)
    
    def test_unix_timestamp_zero_with_new_field_present(self):
        """测试当传统字段为0且新字段也存在时，优先使用传统字段"""
        signal = {
            'timestamp': 0,  # LEGACY_TIMESTAMP_FIELD
            'signal_timestamp': datetime.fromtimestamp(1000),  # NEW_TIMESTAMP_FIELD
            'token_address': '0x123'
        }
        
        result = self.simulator.apply_delay_to_signal(signal)
        
        # 验证使用的是传统字段的时间戳0，而不是新字段的时间戳1000
        assert result is not None
        assert result.original_timestamp == datetime.fromtimestamp(0)
        
        # 验证策略被传入的是时间戳0对应的datetime对象
        call_args = self.mock_strategy.calculate_delay.call_args[0][0]
        assert call_args == datetime.fromtimestamp(0)
    
    def test_unix_timestamp_zero_validation(self):
        """测试时间戳0的验证逻辑"""
        signal = {
            'timestamp': 0,
            'token_address': '0x123'
        }
        
        # 这应该不会抛出异常，因为0是有效的Unix时间戳
        try:
            self.simulator._validate_signal_format(signal)
        except InvalidSignalError:
            pytest.fail("时间戳0应该被视为有效时间戳")
    
    def test_no_legacy_field_fallback_to_new_field(self):
        """测试当传统字段不存在时，正确回退到新字段"""
        signal = {
            'signal_timestamp': datetime.fromtimestamp(0),  # NEW_TIMESTAMP_FIELD
            'token_address': '0x123'
        }
        
        result = self.simulator.apply_delay_to_signal(signal)
        
        assert result is not None
        assert result.original_timestamp == datetime.fromtimestamp(0)
    
    def test_both_fields_missing_raises_error(self):
        """测试当两个时间戳字段都不存在时返回None（因为异常被捕获）"""
        signal = {
            'token_address': '0x123'
            # 没有任何时间戳字段
        }
        
        result = self.simulator.apply_delay_to_signal(signal)
        
        # apply_delay_to_signal捕获异常并返回None
        assert result is None
        
        # 验证失败统计信息被更新
        assert self.simulator.statistics.failed_signals > 0
    
    def test_legacy_field_none_fallback_to_new_field(self):
        """测试当传统字段为None时回退到新字段"""
        signal = {
            'timestamp': None,  # LEGACY_TIMESTAMP_FIELD为None
            'signal_timestamp': datetime.fromtimestamp(1000),  # NEW_TIMESTAMP_FIELD
            'token_address': '0x123'
        }
        
        result = self.simulator.apply_delay_to_signal(signal)
        
        assert result is not None
        assert result.original_timestamp == datetime.fromtimestamp(1000)
    
    def test_compare_zero_vs_positive_timestamp(self):
        """对比测试：验证时间戳0和正数时间戳的处理一致性"""
        # 测试时间戳0
        signal_zero = {
            'timestamp': 0,
            'token_address': '0x123'
        }
        
        # 测试正数时间戳
        signal_positive = {
            'timestamp': 1000,
            'token_address': '0x123'
        }
        
        result_zero = self.simulator.apply_delay_to_signal(signal_zero)
        result_positive = self.simulator.apply_delay_to_signal(signal_positive)
        
        # 两个结果都应该成功
        assert result_zero is not None
        assert result_positive is not None
        
        # 延迟时间应该相同（因为使用同一个模拟策略）
        assert result_zero.delay_seconds == result_positive.delay_seconds
        
        # 时间戳应该不同
        assert result_zero.original_timestamp != result_positive.original_timestamp
        assert result_zero.original_timestamp == datetime.fromtimestamp(0)
        assert result_positive.original_timestamp == datetime.fromtimestamp(1000)
    
    def test_statistics_with_zero_timestamp(self):
        """测试时间戳0的统计信息记录"""
        signal = {
            'timestamp': 0,
            'token_address': '0x123'
        }
        
        # 记录处理前的统计信息
        initial_processed = self.simulator.statistics.total_signals_processed
        
        result = self.simulator.apply_delay_to_signal(signal)
        
        # 验证统计信息更新
        assert result is not None
        assert self.simulator.statistics.total_signals_processed == initial_processed + 1
        assert self.simulator.statistics.successful_delays == initial_processed + 1
        assert self.simulator.statistics.failed_delays == 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 