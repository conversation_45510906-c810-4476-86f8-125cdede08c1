"""
配置管理延迟扩展测试模块

测试ConfigManagerV2的延迟模拟配置扩展功能
对应测试用例设计: @backtest_v2_delay_simulation_test_cases_ai.md TC010-TC012
"""

import pytest
import unittest
from unittest.mock import Mock, patch
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import json
import yaml

# 注意：这些导入在产品代码实现前会失败 - 这是TDD的【红】阶段
try:
    from utils.backtest_v2.config_manager import ConfigManagerV2, BacktestConfigV2
    from utils.backtest_v2.delay_strategy import DelayStrategy, FixedDelayStrategy
except ImportError:
    # TDD红阶段：产品代码尚未实现
    ConfigManagerV2 = None
    BacktestConfigV2 = None
    DelayStrategy = None
    FixedDelayStrategy = None


class TestConfigManagerDelayExtension(unittest.IsolatedAsyncioTestCase):
    """配置管理延迟扩展核心功能测试
    
    对应测试用例: TC010 - 配置管理延迟扩展测试
    """
    
    def setUp(self):
        """测试前置设置"""
        self.valid_delay_config = {
            "enabled": True,
            "strategy": "fixed",
            "fixed_delay_seconds": 30.0,
            "max_delay_seconds": 300.0
        }
        
        self.base_config = {
            "backtest_period": {
                "start_date": "2025-01-01",
                "end_date": "2025-01-31"
            },
            "trading_params": {
                "initial_balance": 10000.0
            },
            "delay_simulation": self.valid_delay_config
        }
    
    def test_config_manager_delay_config_parsing(self):
        """TC010.1: 验证延迟配置解析"""
        if ConfigManagerV2 is None:
            pytest.skip("产品代码尚未实现 - TDD红阶段")
        
        config_manager = ConfigManagerV2()
        
        # 解析包含延迟配置的配置
        config = config_manager.load_config_from_dict(self.base_config)
        
        # 验证延迟配置被正确解析
        self.assertTrue(hasattr(config, 'delay_simulation'))
        self.assertTrue(config.delay_simulation.enabled)
        self.assertEqual(config.delay_simulation.strategy, "fixed")
        self.assertEqual(config.delay_simulation.fixed_delay_seconds, 30.0)
        self.assertEqual(config.delay_simulation.max_delay_seconds, 300.0)
    
    def test_config_manager_delay_config_defaults(self):
        """TC010.2: 验证延迟配置默认值"""
        if ConfigManagerV2 is None:
            pytest.skip("产品代码尚未实现 - TDD红阶段")
        
        # 不包含延迟配置的基础配置
        base_config_without_delay = {
            "backtest_period": {
                "start_date": "2025-01-01",
                "end_date": "2025-01-31"
            },
            "trading_params": {
                "initial_balance": 10000.0
            }
        }
        
        config_manager = ConfigManagerV2()
        config = config_manager.load_config_from_dict(base_config_without_delay)
        
        # 验证默认延迟配置
        self.assertTrue(hasattr(config, 'delay_simulation'))
        self.assertFalse(config.delay_simulation.enabled)  # 默认禁用
        self.assertEqual(config.delay_simulation.strategy, "fixed")  # 默认策略
        self.assertEqual(config.delay_simulation.fixed_delay_seconds, 0.0)  # 默认零延迟
        self.assertEqual(config.delay_simulation.max_delay_seconds, 300.0)  # 默认最大延迟
    
    def test_config_manager_delay_config_validation_success(self):
        """TC010.3: 验证延迟配置验证成功案例"""
        if ConfigManagerV2 is None:
            pytest.skip("产品代码尚未实现 - TDD红阶段")
        
        valid_configs = [
            # 基本固定延迟
            {**self.base_config, "delay_simulation": {
                "enabled": True,
                "strategy": "fixed",
                "fixed_delay_seconds": 45.0
            }},
            # 禁用延迟
            {**self.base_config, "delay_simulation": {
                "enabled": False,
                "strategy": "fixed",
                "fixed_delay_seconds": 0.0
            }},
            # 最大延迟边界
            {**self.base_config, "delay_simulation": {
                "enabled": True,
                "strategy": "fixed",
                "fixed_delay_seconds": 300.0,
                "max_delay_seconds": 300.0
            }}
        ]
        
        config_manager = ConfigManagerV2()
        
        for valid_config in valid_configs:
            # 应该不抛出异常
            config = config_manager.load_config_from_dict(valid_config)
            self.assertIsNotNone(config)
    
    def test_config_manager_delay_config_validation_failures(self):
        """TC010.4: 验证延迟配置验证失败案例"""
        if ConfigManagerV2 is None:
            pytest.skip("产品代码尚未实现 - TDD红阶段")
        
        from utils.backtest_v2.config_manager import ConfigValidationError
        
        invalid_configs = [
            # 负延迟
            {**self.base_config, "delay_simulation": {
                "enabled": True,
                "strategy": "fixed",
                "fixed_delay_seconds": -1.0
            }},
            # 延迟超过最大值
            {**self.base_config, "delay_simulation": {
                "enabled": True,
                "strategy": "fixed",
                "fixed_delay_seconds": 500.0,
                "max_delay_seconds": 300.0
            }},
            # 无效策略
            {**self.base_config, "delay_simulation": {
                "enabled": True,
                "strategy": "invalid_strategy",
                "fixed_delay_seconds": 30.0
            }},
            # 负最大延迟
            {**self.base_config, "delay_simulation": {
                "enabled": True,
                "strategy": "fixed",
                "fixed_delay_seconds": 30.0,
                "max_delay_seconds": -1.0
            }}
        ]
        
        config_manager = ConfigManagerV2()
        
        for invalid_config in invalid_configs:
            with self.assertRaises((ConfigValidationError, ValueError, TypeError)):
                config_manager.load_config_from_dict(invalid_config)
    
    def test_config_manager_delay_strategy_creation(self):
        """TC010.5: 验证延迟策略创建"""
        if ConfigManagerV2 is None or FixedDelayStrategy is None:
            pytest.skip("产品代码尚未实现 - TDD红阶段")
        
        config_manager = ConfigManagerV2()
        config = config_manager.load_config_from_dict(self.base_config)
        
        # 创建延迟策略
        strategy = config_manager.create_delay_strategy(config.delay_simulation)
        
        self.assertIsInstance(strategy, FixedDelayStrategy)
        self.assertEqual(strategy.get_strategy_name(), "fixed")
    
    def test_config_manager_delay_disabled_strategy_creation(self):
        """TC010.6: 验证延迟禁用时的策略创建"""
        if ConfigManagerV2 is None:
            pytest.skip("产品代码尚未实现 - TDD红阶段")
        
        disabled_config = {
            **self.base_config,
            "delay_simulation": {
                "enabled": False,
                "strategy": "fixed",
                "fixed_delay_seconds": 30.0
            }
        }
        
        config_manager = ConfigManagerV2()
        config = config_manager.load_config_from_dict(disabled_config)
        
        # 延迟禁用时应该返回None或零延迟策略
        strategy = config_manager.create_delay_strategy(config.delay_simulation)
        
        if strategy is not None:
            # 如果返回策略，应该是零延迟
            self.assertEqual(strategy.delay_seconds, 0.0)


class TestBacktestConfigV2DelayExtension(unittest.IsolatedAsyncioTestCase):
    """回测配置V2延迟扩展测试
    
    对应测试用例: TC011 - 回测配置延迟扩展测试
    """
    
    def test_backtest_config_v2_delay_simulation_attribute(self):
        """TC011.1: 验证BacktestConfigV2延迟模拟属性"""
        if BacktestConfigV2 is None:
            pytest.skip("产品代码尚未实现 - TDD红阶段")
        
        # 测试延迟模拟属性存在
        config = BacktestConfigV2()
        
        self.assertTrue(hasattr(config, 'delay_simulation'))
        self.assertIsNotNone(config.delay_simulation)
    
    def test_backtest_config_v2_delay_simulation_dataclass(self):
        """TC011.2: 验证延迟模拟配置数据类"""
        if BacktestConfigV2 is None:
            pytest.skip("产品代码尚未实现 - TDD红阶段")
        
        config = BacktestConfigV2()
        delay_config = config.delay_simulation
        
        # 验证延迟配置属性
        expected_attributes = [
            'enabled', 'strategy', 'fixed_delay_seconds', 'max_delay_seconds'
        ]
        
        for attr in expected_attributes:
            self.assertTrue(hasattr(delay_config, attr), f"Missing attribute: {attr}")
    
    def test_backtest_config_v2_delay_simulation_defaults(self):
        """TC011.3: 验证延迟模拟默认值"""
        if BacktestConfigV2 is None:
            pytest.skip("产品代码尚未实现 - TDD红阶段")
        
        config = BacktestConfigV2()
        delay_config = config.delay_simulation
        
        # 验证默认值
        self.assertFalse(delay_config.enabled)  # 默认禁用
        self.assertEqual(delay_config.strategy, "fixed")
        self.assertEqual(delay_config.fixed_delay_seconds, 0.0)
        self.assertEqual(delay_config.max_delay_seconds, 300.0)
    
    def test_backtest_config_v2_delay_simulation_custom_values(self):
        """TC011.4: 验证延迟模拟自定义值"""
        if BacktestConfigV2 is None:
            pytest.skip("产品代码尚未实现 - TDD红阶段")
        
        # 自定义延迟配置
        custom_delay_config = {
            'enabled': True,
            'strategy': 'fixed',
            'fixed_delay_seconds': 45.0,
            'max_delay_seconds': 600.0
        }
        
        config = BacktestConfigV2()
        
        # 设置自定义值
        for key, value in custom_delay_config.items():
            setattr(config.delay_simulation, key, value)
        
        # 验证自定义值
        self.assertTrue(config.delay_simulation.enabled)
        self.assertEqual(config.delay_simulation.strategy, "fixed")
        self.assertEqual(config.delay_simulation.fixed_delay_seconds, 45.0)
        self.assertEqual(config.delay_simulation.max_delay_seconds, 600.0)


class TestConfigManagerDelayIntegration(unittest.IsolatedAsyncioTestCase):
    """配置管理延迟集成测试
    
    对应测试用例: TC012 - 配置管理延迟集成测试
    """
    
    def setUp(self):
        """测试前置设置"""
        self.yaml_config_content = """
backtest_start_time: **********
backtest_end_time: **********
transaction_min_amount: 500.0
kol_account_min_count: 3

delay_simulation:
  enabled: true
  strategy: "fixed"
  fixed_delay_seconds: 30.0
  max_delay_seconds: 300.0
"""
        
        self.json_config_content = {
            "backtest_start_time": **********,
            "backtest_end_time": **********,
            "transaction_min_amount": 500.0,
            "kol_account_min_count": 3,
            "delay_simulation": {
                "enabled": True,
                "strategy": "fixed",
                "fixed_delay_seconds": 45.0,
                "max_delay_seconds": 300.0
            }
        }
    
    @patch('builtins.open')
    @patch('utils.backtest_v2.config_manager.yaml.safe_load')  # 使用完整路径patch
    def test_config_manager_yaml_file_loading(self, mock_yaml_load, mock_open):
        """TC012.1: 验证YAML文件延迟配置加载"""
        if ConfigManagerV2 is None:
            pytest.skip("产品代码尚未实现 - TDD红阶段")
        
        # 直接构造测试数据，避免Mock冲突
        yaml_data = {
            "backtest_start_time": **********,
            "backtest_end_time": **********,
            "transaction_min_amount": 500.0,
            "kol_account_min_count": 3,
            "delay_simulation": {
                "enabled": True,
                "strategy": "fixed",
                "fixed_delay_seconds": 30.0,
                "max_delay_seconds": 300.0
            }
        }
        
        mock_yaml_load.return_value = yaml_data
        
        config_manager = ConfigManagerV2()
        
        config = config_manager.load_config_from_file("test_config.yaml")
        
        # 验证延迟配置加载
        self.assertTrue(config.delay_simulation.enabled)
        self.assertEqual(config.delay_simulation.strategy, "fixed")
        self.assertEqual(config.delay_simulation.fixed_delay_seconds, 30.0)
    
    @patch('builtins.open')
    @patch('json.load')
    def test_config_manager_json_file_loading(self, mock_json_load, mock_open):
        """TC012.2: 验证JSON文件延迟配置加载"""
        if ConfigManagerV2 is None:
            pytest.skip("产品代码尚未实现 - TDD红阶段")
        
        # Mock JSON loading
        mock_json_load.return_value = self.json_config_content
        
        config_manager = ConfigManagerV2()
        config = config_manager.load_config_from_file("test_config.json")
        
        # 验证延迟配置加载
        self.assertTrue(config.delay_simulation.enabled)
        self.assertEqual(config.delay_simulation.strategy, "fixed")
        self.assertEqual(config.delay_simulation.fixed_delay_seconds, 45.0)
    
    def test_config_manager_command_line_override(self):
        """TC012.3: 验证命令行参数覆盖延迟配置"""
        if ConfigManagerV2 is None:
            pytest.skip("产品代码尚未实现 - TDD红阶段")
        
        base_config = {
            "backtest_start_time": **********,
            "backtest_end_time": **********,
            "transaction_min_amount": 500.0,
            "kol_account_min_count": 3,
            "delay_simulation": {
                "enabled": False,
                "fixed_delay_seconds": 30.0
            }
        }
        
        # 命令行覆盖参数
        cli_overrides = {
            "delay_simulation.enabled": True,
            "delay_simulation.fixed_delay_seconds": 60.0
        }
        
        config_manager = ConfigManagerV2()
        config = config_manager.load_config_from_dict(base_config)
        
        # 应用命令行覆盖
        config = config_manager.apply_cli_overrides(config, cli_overrides)
        
        # 验证覆盖生效
        self.assertTrue(config.delay_simulation.enabled)
        self.assertEqual(config.delay_simulation.fixed_delay_seconds, 60.0)
    
    def test_config_manager_config_validation_integration(self):
        """TC012.4: 验证配置验证集成"""
        if ConfigManagerV2 is None:
            pytest.skip("产品代码尚未实现 - TDD红阶段")
        
        # 完整的配置验证流程
        valid_config = {
            "backtest_start_time": **********,
            "backtest_end_time": **********,
            "transaction_min_amount": 500.0,
            "kol_account_min_count": 3,
            "delay_simulation": {
                "enabled": True,
                "strategy": "fixed",
                "fixed_delay_seconds": 30.0,
                "max_delay_seconds": 300.0
            }
        }
        
        config_manager = ConfigManagerV2()
        
        # 加载和验证配置
        config = config_manager.load_config_from_dict(valid_config)
        validation_result = config_manager.validate_config(config)
        
        # 验证通过
        self.assertTrue(validation_result.is_valid)
        self.assertEqual(len(validation_result.errors), 0)
    
    def test_config_manager_backwards_compatibility(self):
        """TC012.5: 验证向后兼容性"""
        if ConfigManagerV2 is None:
            pytest.skip("产品代码尚未实现 - TDD红阶段")
        
        # 旧版本配置（不包含延迟模拟）
        legacy_config = {
            "backtest_start_time": **********,
            "backtest_end_time": **********,
            "transaction_min_amount": 500.0,
            "kol_account_min_count": 3
        }
        
        config_manager = ConfigManagerV2()
        config = config_manager.load_config_from_dict(legacy_config)
        
        # 应该有默认的延迟配置
        self.assertTrue(hasattr(config, 'delay_simulation'))
        self.assertFalse(config.delay_simulation.enabled)  # 默认禁用，保持兼容性
    
    def test_config_manager_environment_variable_override(self):
        """TC012.6: 验证环境变量覆盖延迟配置"""
        if ConfigManagerV2 is None:
            pytest.skip("产品代码尚未实现 - TDD红阶段")
        
        import os
        
        # 设置环境变量
        env_vars = {
            'BACKTEST_DELAY_SIMULATION_ENABLED': 'true',
            'BACKTEST_DELAY_SIMULATION_FIXED_DELAY_SECONDS': '45.0'
        }
        
        with patch.dict(os.environ, env_vars):
            config_manager = ConfigManagerV2()
            
            base_config = {
                "backtest_start_time": **********,
                "backtest_end_time": **********,
                "transaction_min_amount": 500.0,
                "kol_account_min_count": 3,
                "delay_simulation": {
                    "enabled": False,
                    "fixed_delay_seconds": 30.0
                }
            }
            
            config = config_manager.load_config_from_dict(base_config)
            config = config_manager.apply_environment_overrides(config)
            
            # 验证环境变量覆盖
            self.assertTrue(config.delay_simulation.enabled)
            self.assertEqual(config.delay_simulation.fixed_delay_seconds, 45.0)


if __name__ == '__main__':
    unittest.main() 