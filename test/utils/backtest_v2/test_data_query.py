"""
数据查询组件单元测试

测试DataQueryV2类的各项功能
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from utils.backtest_v2.data_query import DataQuery
from utils.backtest_v2.config_manager import BacktestConfigV2


class TestDataQuery:
    """数据查询组件测试"""
    
    @pytest.fixture
    def sample_config(self):
        """创建测试配置"""
        return BacktestConfigV2(
            backtest_start_time=**********,  # 2022-01-01
            backtest_end_time=**********,    # 2022-01-02
            transaction_min_amount=100,
            kol_account_min_txs=5,
            kol_account_max_txs=1000,
            kol_account_min_count=3,
            token_mint_lookback_hours=24,
            transaction_lookback_hours=1,
            fixed_trade_amount=100.0
        )
    
    @pytest.fixture
    def data_query(self, sample_config):
        """创建数据查询实例"""
        return DataQuery(sample_config)
    
    def test_initialization(self, data_query, sample_config):
        """测试初始化"""
        assert data_query.config == sample_config
        assert data_query.activity_dao is not None
        assert data_query.token_dao is not None
        assert data_query.transaction_min_amount == sample_config.transaction_min_amount
        assert data_query.kol_account_min_count == sample_config.kol_account_min_count
    
    @pytest.mark.asyncio
    async def test_build_aggregation_pipeline(self, data_query):
        """测试聚合管道构建"""
        start_time = **********
        end_time = **********

        pipeline = await data_query.build_buy_data_aggregation_pipeline(start_time, end_time)

        # 验证管道结构
        assert isinstance(pipeline, list)
        assert len(pipeline) > 0

        # 验证第一个阶段是$match
        first_stage = pipeline[0]
        assert "$match" in first_stage

        # 验证时间范围过滤
        match_stage = first_stage["$match"]
        assert "timestamp" in match_stage
        assert "$gte" in match_stage["timestamp"]
        assert "$lte" in match_stage["timestamp"]
        assert match_stage["timestamp"]["$gte"] == start_time
        assert match_stage["timestamp"]["$lte"] == end_time

        # 验证事件类型过滤
        assert "event_type" in match_stage
        assert match_stage["event_type"] == "buy"
    
    @pytest.mark.asyncio
    async def test_build_aggregation_pipeline_with_custom_params(self, sample_config):
        """测试自定义参数的聚合管道构建"""
        # 修改配置参数
        sample_config.transaction_min_amount = 500
        sample_config.kol_account_min_txs = 10
        sample_config.kol_account_max_txs = 500
        sample_config.kol_min_winrate = 0.6

        data_query = DataQuery(sample_config)
        pipeline = await data_query.build_buy_data_aggregation_pipeline(**********, **********)

        # 验证参数被正确应用
        assert data_query.transaction_min_amount == 500
        assert data_query.kol_account_min_txs == 10
        assert data_query.kol_account_max_txs == 500
        assert data_query.kol_min_winrate == 0.6
    
    def test_validate_query_result_edge_cases(self, data_query):
        """测试查询结果验证的边界情况"""
        # 测试空字典
        assert data_query.validate_query_result({}) == True

        # 测试None输入
        with pytest.raises(ValueError):
            data_query.validate_query_result(None)

        # 测试非字典输入
        with pytest.raises(ValueError):
            data_query.validate_query_result([])

        # 测试记录字段缺失
        invalid_record_result = {
            "addr1": {
                "records": [
                    {"timestamp": **********, "wallet": "kol1"}  # 缺少cost_usd和event_type
                ],
                "kol_wallets": [],
                "kol_wallets_count": 0
            }
        }

        with pytest.raises(ValueError):
            data_query.validate_query_result(invalid_record_result)
    
    @pytest.mark.asyncio
    async def test_filter_new_token_records(self, data_query):
        """测试新代币记录过滤"""
        # 准备测试数据
        token_data_map = {
            "addr1": {
                "records": [
                    {"timestamp": **********, "wallet": "kol1", "cost_usd": 500},
                    {"timestamp": 1640995400, "wallet": "kol2", "cost_usd": 600},
                    {"timestamp": 1641000000, "wallet": "kol3", "cost_usd": 400}  # 超出范围
                ],
                "kol_wallets": [
                    {"wallet_address": "kol1"}, {"wallet_address": "kol2"}, {"wallet_address": "kol3"}
                ],
                "kol_wallets_count": 3
            }
        }

        token_info_map = {
            "addr1": {
                "address": "addr1",
                "first_mint_time": **********,  # mint时间
                "name": "Token1",
                "symbol": "TK1"
            }
        }

        # 执行过滤
        filtered_results = await data_query.filter_new_token_records(token_data_map, token_info_map)

        # 验证过滤结果
        assert "addr1" in filtered_results
        filtered_records = filtered_results["addr1"]["records"]

        # 应该只保留在lookback时间内的记录
        # lookback_hours = 24, 所以********** + 24*3600 = **********
        # **********和1640995400都在范围内，1641000000也在范围内
        assert len(filtered_records) == 3
    
    @pytest.mark.asyncio
    async def test_get_token_info(self, data_query):
        """测试获取token信息"""
        token_addresses = ["addr1", "addr2"]

        # Mock token_dao的查询结果
        mock_tokens = [
            {
                "address": "addr1",
                "first_mint_time": **********,
                "name": "Token1",
                "symbol": "TK1"
            }
        ]

        with patch.object(data_query.token_dao, 'find_by_addresses') as mock_find:
            mock_find.return_value = mock_tokens

            # Mock TokenInfo获取缺失token
            with patch('utils.backtest_v2.data_query.TokenInfo') as mock_token_info_class:
                mock_token_info = AsyncMock()
                mock_token_info.get_token_info.return_value = {
                    "first_mint_time": **********,
                    "name": "Token2",
                    "symbol": "TK2"
                }
                mock_token_info_class.return_value = mock_token_info

                result = await data_query.get_token_info(token_addresses)

        # 验证结果
        assert isinstance(result, dict)
        assert "addr1" in result
        assert result["addr1"]["name"] == "Token1"
    
    def test_validate_query_result(self, data_query):
        """测试查询结果验证"""
        # 有效结果
        valid_result = {
            "addr1": {
                "records": [
                    {"timestamp": **********, "wallet": "kol1", "cost_usd": 500, "event_type": "buy"}
                ],
                "kol_wallets": [{"wallet_address": "kol1"}],
                "kol_wallets_count": 1
            }
        }

        # 无效结果（缺少必要字段）
        invalid_result = {
            "addr1": {
                "records": [],
                # 缺少kol_wallets和kol_wallets_count
            }
        }

        # 验证有效结果
        assert data_query.validate_query_result(valid_result) == True

        # 验证无效结果
        with pytest.raises(ValueError):
            data_query.validate_query_result(invalid_result)
    
    @pytest.mark.asyncio
    async def test_filter_with_missing_token_info(self, data_query):
        """测试缺失token信息的处理"""
        token_data_map = {
            "addr1": {
                "records": [{"timestamp": **********, "wallet": "kol1", "cost_usd": 500}],
                "kol_wallets": [{"wallet_address": "kol1"}],
                "kol_wallets_count": 1
            }
        }

        # 空的token信息映射（模拟缺失信息）
        token_info_map = {}

        # 执行过滤
        filtered_results = await data_query.filter_new_token_records(token_data_map, token_info_map)

        # 验证缺失信息的token被过滤掉
        assert len(filtered_results) == 0

    @pytest.mark.asyncio
    async def test_kol_winrate_filtering_in_pipeline(self, sample_config):
        """测试聚合管道中的KOL胜率过滤功能"""
        # 设置胜率过滤阈值
        sample_config.kol_min_winrate = 0.5

        data_query = DataQuery(sample_config)
        pipeline = await data_query.build_buy_data_aggregation_pipeline(**********, **********)

        # 验证管道包含了胜率过滤逻辑
        # 查找包含gmgn_wallet_stats lookup的阶段
        gmgn_lookup_stage = None
        for stage in pipeline:
            if '$lookup' in stage and stage['$lookup'].get('from') == 'gmgn_wallet_stats':
                gmgn_lookup_stage = stage
                break

        assert gmgn_lookup_stage is not None, "应该包含gmgn_wallet_stats的lookup阶段"
        assert gmgn_lookup_stage['$lookup']['pipeline'][0]['$match']['period'] == 'all'

        # 查找包含胜率过滤的阶段
        winrate_filter_stage = None
        for stage in pipeline:
            if '$addFields' in stage and 'kol_wallets' in stage['$addFields']:
                filter_cond = stage['$addFields']['kol_wallets'].get('$filter', {}).get('cond', {})
                if '$and' in filter_cond:
                    for condition in filter_cond['$and']:
                        if '$gt' in condition and '$$enriched_kol.winrate' in str(condition):
                            winrate_filter_stage = stage
                            break

        assert winrate_filter_stage is not None, "应该包含胜率过滤的阶段"
    
    @pytest.mark.asyncio
    async def test_integration_flow_mock(self, data_query):
        """测试集成流程（使用mock）"""
        # 1. Mock聚合查询 - 确保有足够的KOL数量（至少3个）
        mock_aggregation_results = {
            "addr1": {
                "records": [
                    {"timestamp": **********, "wallet": "kol1", "cost_usd": 500, "event_type": "buy"},
                    {"timestamp": 1640995400, "wallet": "kol2", "cost_usd": 600, "event_type": "buy"},
                    {"timestamp": 1640995500, "wallet": "kol3", "cost_usd": 400, "event_type": "buy"}
                ],
                "kol_wallets": [
                    {"wallet_address": "kol1"},
                    {"wallet_address": "kol2"},
                    {"wallet_address": "kol3"}
                ],
                "kol_wallets_count": 3
            }
        }

        # 2. Mock token信息
        mock_token_info = {
            "addr1": {
                "address": "addr1",
                "first_mint_time": **********,
                "name": "Token1",
                "symbol": "TK1"
            }
        }

        # 3. 测试新代币记录过滤
        filtered_results = await data_query.filter_new_token_records(
            mock_aggregation_results, mock_token_info
        )

        # 验证结果
        assert "addr1" in filtered_results
        assert len(filtered_results["addr1"]["records"]) == 3
        assert filtered_results["addr1"]["kol_wallets_count"] == 3


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
