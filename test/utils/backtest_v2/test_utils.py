"""
工具函数单元测试

测试回测模块V2中的工具函数
"""

import pytest
import tempfile
import os
import json
from datetime import datetime, timedelta

from utils.backtest_v2.config_manager import BacktestConfigV2


class TestUtilityFunctions:
    """工具函数测试"""
    
    @pytest.fixture
    def sample_config(self):
        """创建测试配置"""
        return BacktestConfigV2(
            backtest_start_time=**********,  # 2022-01-01
            backtest_end_time=**********,    # 2022-01-02
            transaction_min_amount=100,
            kol_account_min_txs=5,
            kol_account_max_txs=1000,
            kol_account_min_count=3,
            token_mint_lookback_hours=24,
            transaction_lookback_hours=1,
            sell_strategy_hours=12,
            sell_kol_ratio=0.3,
            fixed_trade_amount=1000.0,
            commission_pct=0.003,
            slippage_pct=0.002
        )
    
    def test_timestamp_conversion(self):
        """测试时间戳转换工具"""
        # 测试时间戳转换为可读格式
        timestamp = **********  # 2022-01-01 00:00:00 UTC
        dt = datetime.fromtimestamp(timestamp)
        
        # 验证转换正确性
        assert dt.year == 2022
        assert dt.month == 1
        assert dt.day == 1
        
        # 测试反向转换
        converted_timestamp = int(dt.timestamp())
        assert abs(converted_timestamp - timestamp) <= 1  # 允许1秒误差
    
    def test_time_range_calculation(self, sample_config):
        """测试时间范围计算"""
        start_time = sample_config.backtest_start_time
        end_time = sample_config.backtest_end_time
        
        # 计算时间差
        duration_seconds = end_time - start_time
        duration_hours = duration_seconds / 3600
        duration_days = duration_seconds / 86400
        
        # 验证计算结果
        assert duration_seconds == 86400  # 1天 = 86400秒
        assert duration_hours == 24       # 1天 = 24小时
        assert duration_days == 1         # 1天
    
    def test_percentage_calculation(self):
        """测试百分比计算工具"""
        # 测试收益率计算
        buy_price = 0.001
        sell_price = 0.0015
        return_rate = (sell_price - buy_price) / buy_price
        
        # 验证计算结果
        assert abs(return_rate - 0.5) < 0.001  # 50%收益率
        
        # 测试百分比格式化
        percentage = return_rate * 100
        assert abs(percentage - 50.0) < 0.1
    
    def test_data_validation_helpers(self):
        """测试数据验证辅助函数"""
        # 测试有效数据
        valid_record = {
            "timestamp": 1640995400,
            "wallet": "test_wallet",
            "cost_usd": 100.0,
            "event_type": "buy"
        }
        
        # 验证必需字段存在
        required_fields = ["timestamp", "wallet", "cost_usd", "event_type"]
        for field in required_fields:
            assert field in valid_record
            assert valid_record[field] is not None
        
        # 验证数据类型
        assert isinstance(valid_record["timestamp"], int)
        assert isinstance(valid_record["wallet"], str)
        assert isinstance(valid_record["cost_usd"], (int, float))
        assert isinstance(valid_record["event_type"], str)
    
    def test_configuration_serialization(self, sample_config):
        """测试配置序列化和反序列化"""
        # 序列化配置
        config_dict = sample_config.__dict__
        
        # 验证序列化结果
        assert isinstance(config_dict, dict)
        assert "backtest_start_time" in config_dict
        assert "backtest_end_time" in config_dict
        assert "fixed_trade_amount" in config_dict
        
        # 测试JSON序列化
        config_json = json.dumps(config_dict, default=str)
        assert isinstance(config_json, str)
        assert len(config_json) > 0
        
        # 测试反序列化
        parsed_config = json.loads(config_json)
        assert isinstance(parsed_config, dict)
        assert parsed_config["fixed_trade_amount"] == sample_config.fixed_trade_amount
    
    def test_file_path_utilities(self):
        """测试文件路径工具函数"""
        # 测试临时目录创建
        with tempfile.TemporaryDirectory() as temp_dir:
            assert os.path.exists(temp_dir)
            assert os.path.isdir(temp_dir)
            
            # 测试文件路径构建
            test_file = os.path.join(temp_dir, "test.json")
            assert temp_dir in test_file
            assert "test.json" in test_file
            
            # 测试文件创建
            with open(test_file, 'w') as f:
                json.dump({"test": "data"}, f)
            
            assert os.path.exists(test_file)
            assert os.path.getsize(test_file) > 0
    
    def test_error_handling_patterns(self):
        """测试错误处理模式"""
        # 测试除零错误处理
        def safe_divide(a, b):
            try:
                return a / b
            except ZeroDivisionError:
                return 0.0
        
        assert safe_divide(10, 2) == 5.0
        assert safe_divide(10, 0) == 0.0
        
        # 测试空值处理
        def safe_get(data, key, default=None):
            try:
                return data[key]
            except (KeyError, TypeError):
                return default
        
        test_data = {"key1": "value1"}
        assert safe_get(test_data, "key1") == "value1"
        assert safe_get(test_data, "key2") is None
        assert safe_get(test_data, "key2", "default") == "default"
        assert safe_get(None, "key1") is None
    
    def test_list_processing_utilities(self):
        """测试列表处理工具函数"""
        # 测试列表去重
        test_list = [1, 2, 2, 3, 3, 3, 4]
        unique_list = list(set(test_list))
        assert len(unique_list) == 4
        assert all(item in unique_list for item in [1, 2, 3, 4])
        
        # 测试列表分组
        test_records = [
            {"type": "A", "value": 1},
            {"type": "B", "value": 2},
            {"type": "A", "value": 3},
            {"type": "B", "value": 4}
        ]
        
        grouped = {}
        for record in test_records:
            record_type = record["type"]
            if record_type not in grouped:
                grouped[record_type] = []
            grouped[record_type].append(record)
        
        assert len(grouped) == 2
        assert len(grouped["A"]) == 2
        assert len(grouped["B"]) == 2
    
    def test_statistical_calculations(self):
        """测试统计计算工具"""
        test_values = [1.0, 2.0, 3.0, 4.0, 5.0]
        
        # 测试平均值
        mean_value = sum(test_values) / len(test_values)
        assert mean_value == 3.0
        
        # 测试最大最小值
        max_value = max(test_values)
        min_value = min(test_values)
        assert max_value == 5.0
        assert min_value == 1.0
        
        # 测试标准差计算
        variance = sum((x - mean_value) ** 2 for x in test_values) / len(test_values)
        std_dev = variance ** 0.5
        assert abs(std_dev - 1.4142135623730951) < 0.001
    
    def test_format_utilities(self):
        """测试格式化工具函数"""
        # 测试数值格式化
        test_number = 1234.5678
        formatted_2_decimal = round(test_number, 2)
        assert formatted_2_decimal == 1234.57
        
        formatted_4_decimal = round(test_number, 4)
        assert formatted_4_decimal == 1234.5678
        
        # 测试百分比格式化
        test_ratio = 0.1234
        percentage_str = f"{test_ratio:.2%}"
        assert percentage_str == "12.34%"
        
        # 测试时间格式化
        test_timestamp = **********
        dt = datetime.fromtimestamp(test_timestamp)
        formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")
        assert "2022-01-01" in formatted_time
    
    def test_data_structure_validation(self):
        """测试数据结构验证"""
        # 测试字典结构验证
        required_keys = ["timestamp", "price", "volume"]
        test_dict = {"timestamp": 123, "price": 1.0, "volume": 100}
        
        # 验证所有必需键存在
        assert all(key in test_dict for key in required_keys)
        
        # 测试列表结构验证
        test_list = [
            {"id": 1, "value": "a"},
            {"id": 2, "value": "b"},
            {"id": 3, "value": "c"}
        ]
        
        # 验证列表中所有元素都有必需字段
        required_fields = ["id", "value"]
        assert all(
            all(field in item for field in required_fields)
            for item in test_list
        )
    
    def test_memory_efficiency_patterns(self):
        """测试内存效率模式"""
        # 测试生成器模式
        def number_generator(n):
            for i in range(n):
                yield i * 2
        
        gen = number_generator(5)
        results = list(gen)
        assert results == [0, 2, 4, 6, 8]
        
        # 测试列表推导式
        squares = [x**2 for x in range(5)]
        assert squares == [0, 1, 4, 9, 16]
        
        # 测试字典推导式
        square_dict = {x: x**2 for x in range(5)}
        assert square_dict == {0: 0, 1: 1, 2: 4, 3: 9, 4: 16}


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
