"""
测试回测引擎V2中时间戳类型不一致问题的修复
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from typing import Dict, Any, List

from utils.backtest_v2.backtest_engine import BacktestEngineV2
from utils.backtest_v2.config_manager import BacktestConfigV2


class TestBacktestEngineV2TimestampFix:
    """测试回测引擎V2时间戳修复"""
    
    def test_normalize_timestamp_datetime(self):
        """测试_normalize_timestamp方法处理datetime对象"""
        dt = datetime(2024, 1, 1, 12, 0, 0)
        expected = dt.timestamp()
        
        result = BacktestEngineV2._normalize_timestamp(dt)
        
        assert isinstance(result, float)
        assert result == expected
    
    def test_normalize_timestamp_int(self):
        """测试_normalize_timestamp方法处理int类型Unix时间戳"""
        timestamp = 1704110400  # 2024-01-01 12:00:00 UTC
        
        result = BacktestEngineV2._normalize_timestamp(timestamp)
        
        assert isinstance(result, float)
        assert result == float(timestamp)
    
    def test_normalize_timestamp_float(self):
        """测试_normalize_timestamp方法处理float类型Unix时间戳"""
        timestamp = 1704110400.123
        
        result = BacktestEngineV2._normalize_timestamp(timestamp)
        
        assert isinstance(result, float)
        assert result == timestamp
    
    def test_normalize_timestamp_none(self):
        """测试_normalize_timestamp方法处理None值"""
        with pytest.raises(ValueError, match="时间戳不能为None"):
            BacktestEngineV2._normalize_timestamp(None)
    
    def test_normalize_timestamp_invalid_type(self):
        """测试_normalize_timestamp方法处理不支持的类型"""
        with pytest.raises(ValueError, match="不支持的时间戳类型"):
            BacktestEngineV2._normalize_timestamp("invalid")
    
    def test_calculate_trades_mixed_timestamp_types(self):
        """测试_calculate_trades方法处理混合时间戳类型"""
        # 创建模拟配置
        config = Mock(spec=BacktestConfigV2)
        config.commission_pct = 0.001
        config.slippage_pct = 0.001
        config.sell_strategy_hours = 24
        # 添加delay_simulation属性
        config.delay_simulation = Mock()
        config.delay_simulation.enabled = False
        
        engine = BacktestEngineV2(config)
        
        # 创建买入信号（datetime时间戳）
        buy_datetime = datetime(2024, 1, 1, 12, 0, 0)
        buy_signals = [{
            'token_address': 'test_token',
            'signal_timestamp': buy_datetime,  # datetime对象
            'original_signal_timestamp': buy_datetime.timestamp(),  # float时间戳
            'avg_price_usd': 1.0,
            'total_volume_usd': 1000.0,
            'trigger_kol_count': 3
        }]
        
        # 创建卖出信号（float时间戳）
        sell_timestamp = buy_datetime.timestamp() + 3600  # 1小时后
        sell_signals = [{
            'token_address': 'test_token',
            'buy_signal_timestamp': buy_datetime.timestamp(),  # 用于匹配
            'sell_timestamp': sell_timestamp,  # float时间戳
            'avg_price_usd': 1.1,
            'sell_reason': 'time_limit'
        }]
        
        # 执行交易计算
        trades = engine._calculate_trades(buy_signals, sell_signals)
        
        # 验证结果
        assert len(trades) == 1
        trade = trades[0]
        
        # 验证时间戳类型和值
        assert isinstance(trade['buy_timestamp'], float)
        assert isinstance(trade['sell_timestamp'], float)
        assert trade['buy_timestamp'] == buy_datetime.timestamp()
        assert trade['sell_timestamp'] == sell_timestamp
        
        # 验证持有时间计算正确（1小时）
        assert trade['holding_hours'] == 1.0
        
        # 验证延迟计算正确（应该为0，因为没有延迟）
        assert trade['buy_delay_applied_seconds'] == 0.0
    
    def test_calculate_trades_with_delay_simulation(self):
        """测试带有延迟模拟的交易计算"""
        # 创建模拟配置
        config = Mock(spec=BacktestConfigV2)
        config.commission_pct = 0.001
        config.slippage_pct = 0.001
        config.sell_strategy_hours = 24
        # 添加delay_simulation属性
        config.delay_simulation = Mock()
        config.delay_simulation.enabled = False
        
        engine = BacktestEngineV2(config)
        
        # 创建带延迟的买入信号
        original_datetime = datetime(2024, 1, 1, 12, 0, 0)
        delayed_datetime = original_datetime + timedelta(seconds=300)  # 延迟5分钟
        
        buy_signals = [{
            'token_address': 'test_token',
            'signal_timestamp': delayed_datetime,  # 延迟后的datetime对象
            'original_signal_timestamp': original_datetime.timestamp(),  # 原始float时间戳
            'avg_price_usd': 1.0,
            'total_volume_usd': 1000.0,
            'trigger_kol_count': 3
        }]
        
        # 创建卖出信号
        sell_timestamp = delayed_datetime.timestamp() + 3600  # 1小时后
        sell_signals = [{
            'token_address': 'test_token',
            'buy_signal_timestamp': original_datetime.timestamp(),  # 用于匹配原始时间戳
            'sell_timestamp': sell_timestamp,  # float时间戳
            'avg_price_usd': 1.1,
            'sell_reason': 'time_limit'
        }]
        
        # 执行交易计算
        trades = engine._calculate_trades(buy_signals, sell_signals)
        
        # 验证结果
        assert len(trades) == 1
        trade = trades[0]
        
        # 验证延迟计算正确（5分钟 = 300秒）
        assert trade['buy_delay_applied_seconds'] == 300.0
        
        # 验证持有时间计算正确（使用延迟后的时间戳）
        expected_holding_hours = 3600.0 / 3600.0  # 1小时
        assert trade['holding_hours'] == expected_holding_hours
    
    def test_calculate_trades_invalid_timestamp_handling(self):
        """测试处理无效时间戳的情况"""
        # 创建模拟配置
        config = Mock(spec=BacktestConfigV2)
        config.commission_pct = 0.001
        config.slippage_pct = 0.001
        config.sell_strategy_hours = 24
        # 添加delay_simulation属性
        config.delay_simulation = Mock()
        config.delay_simulation.enabled = False
        
        engine = BacktestEngineV2(config)
        
        # 创建带有无效时间戳的买入信号
        buy_signals = [{
            'token_address': 'test_token',
            'signal_timestamp': "invalid_timestamp",  # 无效类型
            'avg_price_usd': 1.0,
            'total_volume_usd': 1000.0,
            'trigger_kol_count': 3
        }]
        
        # 创建正常的卖出信号，buy_signal_timestamp需要与买入信号的signal_timestamp匹配
        sell_signals = [{
            'token_address': 'test_token',
            'buy_signal_timestamp': "invalid_timestamp",  # 与买入信号的signal_timestamp匹配
            'sell_timestamp': 1704114000,  # 1小时后
            'avg_price_usd': 1.1,
            'sell_reason': 'time_limit'
        }]
        
        # 执行交易计算，应该跳过无效的交易
        with patch('utils.backtest_v2.backtest_engine.logger') as mock_logger:
            trades = engine._calculate_trades(buy_signals, sell_signals)
        
        # 验证没有生成交易（由于时间戳无效被跳过）
        assert len(trades) == 0
        
        # 验证记录了错误日志
        mock_logger.error.assert_called()
        # 检查是否有包含"时间戳处理错误"的错误调用
        error_calls = mock_logger.error.call_args_list
        assert len(error_calls) > 0
        # 检查错误消息内容
        found_timestamp_error = False
        for call in error_calls:
            call_str = str(call)
            if '时间戳处理错误' in call_str or 'timestamp' in call_str.lower():
                found_timestamp_error = True
                break
        assert found_timestamp_error, f"未找到时间戳处理错误日志，实际调用: {error_calls}"
    
    def test_calculate_trades_negative_holding_time(self):
        """测试处理负的持有时间"""
        # 创建模拟配置
        config = Mock(spec=BacktestConfigV2)
        config.commission_pct = 0.001
        config.slippage_pct = 0.001
        config.sell_strategy_hours = 24
        # 添加delay_simulation属性
        config.delay_simulation = Mock()
        config.delay_simulation.enabled = False
        
        engine = BacktestEngineV2(config)
        
        # 创建买入信号
        buy_timestamp = 1704114000  # 较晚的时间
        buy_signals = [{
            'token_address': 'test_token',
            'signal_timestamp': buy_timestamp,
            'avg_price_usd': 1.0,
            'total_volume_usd': 1000.0,
            'trigger_kol_count': 3
        }]
        
        # 创建卖出信号（时间早于买入时间）
        sell_timestamp = 1704110400  # 较早的时间
        sell_signals = [{
            'token_address': 'test_token',
            'buy_signal_timestamp': buy_timestamp,
            'sell_timestamp': sell_timestamp,
            'avg_price_usd': 1.1,
            'sell_reason': 'time_limit'
        }]
        
        # 执行交易计算
        with patch('utils.backtest_v2.backtest_engine.logger') as mock_logger:
            trades = engine._calculate_trades(buy_signals, sell_signals)
        
        # 验证没有生成交易（由于时间逻辑错误被跳过）
        assert len(trades) == 0
        
        # 验证记录了警告日志
        mock_logger.warning.assert_called()
        warning_calls = [call for call in mock_logger.warning.call_args_list if '时间逻辑错误' in str(call)]
        assert len(warning_calls) > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 