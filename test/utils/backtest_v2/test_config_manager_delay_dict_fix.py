"""测试配置管理器延迟模拟字典转换修复功能

测试用例涵盖：
1. 静默字段丢失问题的修复
2. 不明确验证错误的修复
3. 类型转换和验证的改进
4. 边界情况和错误处理

创建日期：2025-01-17
测试方法：自动化测试
测试级别：单元测试
"""

import pytest
from typing import Dict, Any
from utils.backtest_v2.config_manager import (
    BacktestConfigV2,
    DelaySimulationConfig,
    ConfigValidationError,
    ConfigurationError
)


class TestDelaySimulationDictConversion:
    """测试延迟模拟配置字典转换修复功能"""
    
    def test_unknown_fields_rejection(self):
        """测试未知字段会被明确拒绝而不是静默丢失"""
        config_dict = {
            "delay_simulation": {
                "enabled": True,
                "strategy": "fixed",
                "unknown_field": "should_be_rejected",  # 未知字段
                "another_unknown": 123
            }
        }
        
        with pytest.raises(ConfigValidationError) as exc_info:
            BacktestConfigV2(**config_dict)
        
        error_msg = str(exc_info.value)
        assert "未知字段" in error_msg
        assert "unknown_field" in error_msg
        assert "another_unknown" in error_msg
        assert "支持的字段" in error_msg
    
    def test_clear_field_validation_errors(self):
        """测试字段验证错误信息更加明确"""
        config_dict = {
            "delay_simulation": {
                "enabled": "invalid_bool",  # 无效布尔值
                "strategy": "invalid_strategy",  # 无效策略
                "fixed_delay_seconds": -5.0,  # 负数
                "max_delay_seconds": "not_a_number",  # 无效数值
                "delay_data_time_range": 0  # 无效范围
            }
        }
        
        with pytest.raises(ConfigValidationError) as exc_info:
            BacktestConfigV2(**config_dict)
        
        error_msg = str(exc_info.value)
        # 应该包含具体的字段错误信息
        assert "延迟模拟配置无效" in error_msg
    
    def test_individual_field_validation_errors(self):
        """测试各个字段的具体验证错误"""
        
        # 测试enabled字段
        with pytest.raises(ConfigValidationError) as exc_info:
            BacktestConfigV2(delay_simulation={"enabled": "invalid_bool"})
        assert "enabled" in str(exc_info.value)
        
        # 测试strategy字段
        with pytest.raises(ConfigValidationError) as exc_info:
            BacktestConfigV2(delay_simulation={"strategy": "invalid_strategy"})
        assert "strategy" in str(exc_info.value)
        
        # 测试fixed_delay_seconds字段
        with pytest.raises(ConfigValidationError) as exc_info:
            BacktestConfigV2(delay_simulation={"fixed_delay_seconds": -5.0})
        assert "fixed_delay_seconds" in str(exc_info.value)
        
        # 测试max_delay_seconds字段
        with pytest.raises(ConfigValidationError) as exc_info:
            BacktestConfigV2(delay_simulation={"max_delay_seconds": "not_number"})
        assert "max_delay_seconds" in str(exc_info.value)
        
        # 测试statistical_type字段
        with pytest.raises(ConfigValidationError) as exc_info:
            BacktestConfigV2(delay_simulation={"statistical_type": "invalid_type"})
        assert "statistical_type" in str(exc_info.value)
        
        # 测试delay_data_time_range字段
        with pytest.raises(ConfigValidationError) as exc_info:
            BacktestConfigV2(delay_simulation={"delay_data_time_range": 0})
        assert "delay_data_time_range" in str(exc_info.value)
    
    def test_valid_dict_conversion(self):
        """测试有效字典的正确转换"""
        config_dict = {
            "delay_simulation": {
                "enabled": True,
                "strategy": "fixed",
                "fixed_delay_seconds": 10.0,
                "max_delay_seconds": 300.0,
                "statistical_type": "median",
                "delay_data_time_range": 30
            }
        }
        
        config = BacktestConfigV2(**config_dict)
        
        assert isinstance(config.delay_simulation, DelaySimulationConfig)
        assert config.delay_simulation.enabled is True
        assert config.delay_simulation.strategy == "fixed"
        assert config.delay_simulation.fixed_delay_seconds == 10.0
        assert config.delay_simulation.max_delay_seconds == 300.0
        assert config.delay_simulation.statistical_type == "median"
        assert config.delay_simulation.delay_data_time_range == 30
    
    def test_partial_dict_with_defaults(self):
        """测试部分字典配置会使用默认值"""
        config_dict = {
            "delay_simulation": {
                "enabled": True,
                "fixed_delay_seconds": 5.0
            }
        }
        
        config = BacktestConfigV2(**config_dict)
        
        assert config.delay_simulation.enabled is True
        assert config.delay_simulation.fixed_delay_seconds == 5.0
        # 其他字段应该使用默认值
        assert config.delay_simulation.strategy == "fixed"  # 默认值
        assert config.delay_simulation.max_delay_seconds == 300.0  # 默认值
    
    def test_boolean_conversion_flexibility(self):
        """测试布尔值转换的灵活性"""
        test_cases = [
            ("true", True),
            ("True", True),
            ("1", True),
            ("yes", True),
            ("on", True),
            ("false", False),
            ("False", False),
            ("0", False),
            ("no", False),
            ("off", False),
            (1, True),
            (0, False),
            (1.0, True),
            (0.0, False)
        ]
        
        for input_value, expected in test_cases:
            config_dict = {
                "delay_simulation": {
                    "enabled": input_value
                }
            }
            
            config = BacktestConfigV2(**config_dict)
            assert config.delay_simulation.enabled is expected, f"Failed for input: {input_value}"
    
    def test_numeric_conversion(self):
        """测试数值转换"""
        config_dict = {
            "delay_simulation": {
                "fixed_delay_seconds": "10.5",  # 字符串数值
                "max_delay_seconds": 300,  # 整数
                "delay_data_time_range": "30"  # 字符串整数
            }
        }
        
        with pytest.raises(ConfigValidationError):
            # 字符串数值应该被拒绝，要求明确的数值类型
            BacktestConfigV2(**config_dict)
    
    def test_error_propagation_chain(self):
        """测试错误传播链是否正确"""
        config_dict = {
            "delay_simulation": {
                "strategy": "fixed",
                "fixed_delay_seconds": 100.0,
                "max_delay_seconds": 50.0  # 小于fixed_delay_seconds，违反DelaySimulationConfig验证
            }
        }
        
        with pytest.raises(ConfigValidationError) as exc_info:
            BacktestConfigV2(**config_dict)
        
        error_msg = str(exc_info.value)
        assert "延迟模拟配置" in error_msg
        # 应该传播DelaySimulationConfig的验证错误
        assert "固定延迟时间" in error_msg or "不能超过最大延迟时间" in error_msg
    
    def test_non_dict_delay_simulation_error(self):
        """测试非字典类型的delay_simulation参数"""
        with pytest.raises(ConfigValidationError) as exc_info:
            BacktestConfigV2(delay_simulation="not_a_dict")
        
        error_msg = str(exc_info.value)
        assert "延迟模拟配置必须是DelaySimulationConfig实例或字典" in error_msg
        assert "当前类型" in error_msg
    
    def test_empty_dict_uses_defaults(self):
        """测试空字典使用默认值"""
        config_dict = {
            "delay_simulation": {}
        }
        
        config = BacktestConfigV2(**config_dict)
        
        # 应该创建带有默认值的DelaySimulationConfig
        assert isinstance(config.delay_simulation, DelaySimulationConfig)
        assert config.delay_simulation.enabled is False  # 默认值
        assert config.delay_simulation.strategy == "fixed"  # 默认值
    
    def test_convert_delay_simulation_dict_directly(self):
        """直接测试_convert_delay_simulation_dict方法"""
        
        # 测试有效转换
        delay_dict = {
            "enabled": True,
            "strategy": "fixed",
            "fixed_delay_seconds": 5.0
        }
        
        result = BacktestConfigV2._convert_delay_simulation_dict(delay_dict)
        assert isinstance(result, DelaySimulationConfig)
        assert result.enabled is True
        assert result.strategy == "fixed"
        assert result.fixed_delay_seconds == 5.0
        
        # 测试未知字段错误
        invalid_dict = {
            "enabled": True,
            "unknown_field": "value"
        }
        
        with pytest.raises(ConfigValidationError) as exc_info:
            BacktestConfigV2._convert_delay_simulation_dict(invalid_dict)
        
        assert "未知字段" in str(exc_info.value)
        assert "unknown_field" in str(exc_info.value)
    
    def test_validate_delay_field_directly(self):
        """直接测试_validate_delay_field方法"""
        
        # 测试enabled字段
        assert BacktestConfigV2._validate_delay_field("enabled", True) is True
        assert BacktestConfigV2._validate_delay_field("enabled", "true") is True
        assert BacktestConfigV2._validate_delay_field("enabled", "false") is False
        assert BacktestConfigV2._validate_delay_field("enabled", 1) is True
        assert BacktestConfigV2._validate_delay_field("enabled", 0) is False
        
        with pytest.raises(ValueError):
            BacktestConfigV2._validate_delay_field("enabled", "invalid")
        
        # 测试strategy字段
        assert BacktestConfigV2._validate_delay_field("strategy", "fixed") == "fixed"
        
        with pytest.raises(ValueError) as exc_info:
            BacktestConfigV2._validate_delay_field("strategy", "invalid")
        assert "无效策略" in str(exc_info.value)
        
        # 测试数值字段
        assert BacktestConfigV2._validate_delay_field("fixed_delay_seconds", 10) == 10.0
        assert BacktestConfigV2._validate_delay_field("fixed_delay_seconds", 10.5) == 10.5
        
        with pytest.raises(ValueError):
            BacktestConfigV2._validate_delay_field("fixed_delay_seconds", -5)
        
        with pytest.raises(ValueError):
            BacktestConfigV2._validate_delay_field("fixed_delay_seconds", "not_number")
        
        # 测试整数字段
        assert BacktestConfigV2._validate_delay_field("delay_data_time_range", 30) == 30
        assert BacktestConfigV2._validate_delay_field("delay_data_time_range", 30.5) == 30
        
        with pytest.raises(ValueError):
            BacktestConfigV2._validate_delay_field("delay_data_time_range", 0)
        
        # 测试未知字段
        with pytest.raises(ValueError) as exc_info:
            BacktestConfigV2._validate_delay_field("unknown_field", "value")
        assert "未知字段" in str(exc_info.value)


if __name__ == '__main__':
    pytest.main([__file__, '-v']) 