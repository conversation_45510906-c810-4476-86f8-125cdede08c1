"""
回测系统4个Bug修复的测试用例
对应Bug修复方案: docs/features/0.1.0/backtesting/fixes/BUGFIX_PLAN_Backtest_System_ThreeBugFixes_20250617.md
"""

import pytest
import pandas as pd
import json
import hashlib
from unittest.mock import Mock, patch, MagicMock


class TestBug1HoldingTimeUnit:
    """Bug 1: 持有时间单位错误测试"""
    
    def test_holding_hours_calculation_bug(self):
        """测试当前版本的holding_hours计算bug - 应该存储分钟但字段名为hours"""
        # 模拟买入和卖出信号，时间戳差为3600秒（1小时）
        buy_signal = {'signal_timestamp': 1000000}
        sell_signal = {'sell_timestamp': 1003600}  # 1小时后
        
        # 当前buggy版本的计算方式
        holding_hours_buggy = (sell_signal['sell_timestamp'] - buy_signal['signal_timestamp']) / 60
        
        # 这里应该是60分钟，但字段名叫holding_hours，容易误解
        assert holding_hours_buggy == 60.0  # 实际是分钟，但字段名是hours
        
        # 在result_analyzer中会再次乘以60
        holding_minutes_double_converted = holding_hours_buggy * 60
        assert holding_minutes_double_converted == 3600.0  # 错误：应该是60分钟，但显示为3600分钟
    
    def test_correct_holding_hours_after_fix(self):
        """测试修复后的holding_hours计算 - 应该真正存储小时数据"""
        buy_signal = {'signal_timestamp': 1000000}
        sell_signal = {'sell_timestamp': 1003600}  # 1小时后
        
        # 修复后的正确计算方式
        holding_hours_correct = (sell_signal['sell_timestamp'] - buy_signal['signal_timestamp']) / 3600
        assert holding_hours_correct == 1.0  # 真正的小时数
        
        # 在result_analyzer中转换为分钟
        holding_minutes_correct = holding_hours_correct * 60
        assert holding_minutes_correct == 60.0  # 正确：60分钟


class TestBug2GridModeStatisticsCleanup:
    """Bug 2: 网格模式统计字段清理测试"""
    
    def test_unwanted_statistics_fields_exist(self):
        """测试当前版本包含不需要的统计字段"""
        # 模拟包含不需要字段的统计数据
        mock_stats = {
            'sell_reason_stats_kol_ratio_return_rate_count': 10,
            'sell_reason_stats_kol_ratio_return_rate_mean': 0.15,
            'sell_reason_stats_kol_ratio_holding_hours_mean': 2.5,
            'sell_reason_stats_time_limit_return_rate_count': 8,
            'sell_reason_stats_time_limit_return_rate_mean': 0.12,
            'kol_count_stats_2_return_rate_count': 5,
            'kol_count_stats_2_return_rate_mean': 0.08,
            'total_trades': 25,
            'win_rate': 0.48
        }
        
        # 需要移除的字段
        unwanted_fields = [
            'sell_reason_stats_kol_ratio_return_rate_count',
            'sell_reason_stats_kol_ratio_return_rate_mean', 
            'sell_reason_stats_kol_ratio_holding_hours_mean',
            'sell_reason_stats_time_limit_return_rate_count',
            'sell_reason_stats_time_limit_return_rate_mean',
            'kol_count_stats_2_return_rate_count',
            'kol_count_stats_2_return_rate_mean'
        ]
        
        # 验证这些字段确实存在（复现bug）
        for field in unwanted_fields:
            assert field in mock_stats, f"字段 {field} 应该存在以复现bug"
    
    def test_statistics_fields_removed_after_fix(self):
        """测试修复后移除了不需要的统计字段"""
        # 模拟修复后的统计数据
        cleaned_stats = {
            'total_trades': 25,
            'win_rate': 0.48,
            'total_return_rate': 0.43
        }
        
        # 应该被移除的字段
        unwanted_fields = [
            'sell_reason_stats_kol_ratio_return_rate_count',
            'sell_reason_stats_kol_ratio_return_rate_mean', 
            'sell_reason_stats_kol_ratio_holding_hours_mean',
            'sell_reason_stats_time_limit_return_rate_count',
            'sell_reason_stats_time_limit_return_rate_mean',
            'kol_count_stats_2_return_rate_count',
            'kol_count_stats_2_return_rate_mean'
        ]
        
        # 验证这些字段已被移除
        for field in unwanted_fields:
            assert field not in cleaned_stats, f"字段 {field} 应该被移除"


class TestBug3GridModeTableLayout:
    """Bug 3: 网格模式详细结果表格布局测试"""
    
    def test_current_column_order_bug(self):
        """测试当前版本的列顺序问题"""
        # 模拟当前的列顺序（有问题的版本）
        current_cols = ['组合编号', '详细配置', '胜率', '总收益率', '执行时间(秒)', '详细报告']
        
        # 检查详细报告列在最后（这是问题所在）
        detail_report_index = current_cols.index('详细报告')
        config_index = current_cols.index('详细配置')
        
        # Bug: 详细报告列在统计字段之后，距离详细配置太远
        assert detail_report_index > config_index + 1, "详细报告列应该距离详细配置列较远（复现bug）"
    
    def test_fixed_column_order(self):
        """测试修复后的列顺序"""
        # 模拟修复后的列顺序
        fixed_cols = ['组合编号', '详细配置', '详细报告', '胜率', '总收益率', '执行时间(秒)']
        
        # 检查详细报告列紧跟在详细配置列之后
        detail_report_index = fixed_cols.index('详细报告')
        config_index = fixed_cols.index('详细配置')
        
        # 修复后：详细报告列应该紧跟在详细配置列之后
        assert detail_report_index == config_index + 1, "详细报告列应该紧跟在详细配置列之后"


class TestBug4StrategyHashStability:
    """Bug 4: 策略ID稳定性测试"""
    
    def generate_strategy_hash(self, params_dict):
        """生成策略参数的稳定哈希ID"""
        # 对参数字典按键排序，确保一致性
        sorted_params = dict(sorted(params_dict.items()))
        
        # 转换为JSON字符串（确保数值类型一致）
        params_str = json.dumps(sorted_params, sort_keys=True, separators=(',', ':'))
        
        # 生成SHA256哈希的前8位作为策略ID
        hash_obj = hashlib.sha256(params_str.encode('utf-8'))
        strategy_id = hash_obj.hexdigest()[:8]
        
        return strategy_id
    
    def test_current_unstable_combination_numbers(self):
        """测试当前版本的不稳定组合编号问题"""
        # 模拟两个不同的参数网格，包含相同的策略参数
        grid1_params = [
            {'transaction_lookback_hours': 24, 'kol_account_min_count': 3},
            {'transaction_lookback_hours': 48, 'kol_account_min_count': 5},
        ]
        
        grid2_params = [
            {'transaction_lookback_hours': 48, 'kol_account_min_count': 5},  # 相同策略，但在不同位置
            {'transaction_lookback_hours': 24, 'kol_account_min_count': 3},
            {'transaction_lookback_hours': 12, 'kol_account_min_count': 2},
        ]
        
        # 当前版本：相同策略在不同网格中有不同的组合编号
        strategy1_in_grid1 = 0  # 第一个策略在grid1中是组合0
        strategy1_in_grid2 = 1  # 相同策略在grid2中是组合1
        
        # Bug: 相同策略参数在不同网格中有不同编号
        assert strategy1_in_grid1 != strategy1_in_grid2, "相同策略在不同网格中编号不同（复现bug）"
    
    def test_strategy_hash_consistency(self):
        """测试策略哈希ID的一致性"""
        # 相同的参数字典（不同顺序）
        params1 = {'transaction_lookback_hours': 24, 'kol_account_min_count': 3}
        params2 = {'kol_account_min_count': 3, 'transaction_lookback_hours': 24}
        
        # 生成哈希ID
        hash1 = self.generate_strategy_hash(params1)
        hash2 = self.generate_strategy_hash(params2)
        
        # 应该生成相同的策略ID
        assert hash1 == hash2, f"相同参数应该生成相同ID: {hash1} vs {hash2}"
    
    def test_strategy_id_uniqueness(self):
        """测试不同策略参数生成不同ID"""
        params1 = {'transaction_lookback_hours': 24, 'kol_account_min_count': 3}
        params2 = {'transaction_lookback_hours': 48, 'kol_account_min_count': 5}
        
        hash1 = self.generate_strategy_hash(params1)
        hash2 = self.generate_strategy_hash(params2)
        
        # 不同参数应该生成不同ID
        assert hash1 != hash2, f"不同参数应该生成不同ID: {hash1} vs {hash2}"
    
    def test_cross_grid_strategy_identification(self):
        """测试跨网格回测的策略识别"""
        # 两个不同的参数网格，包含相同的策略组合
        common_strategy = {'transaction_lookback_hours': 24, 'kol_account_min_count': 3}
        
        # 在不同网格中生成相同策略的ID
        hash_grid1 = self.generate_strategy_hash(common_strategy)
        hash_grid2 = self.generate_strategy_hash(common_strategy)
        
        # 相同策略在两个网格中应该产生相同的策略ID
        assert hash_grid1 == hash_grid2, "相同策略在不同网格中应该有相同ID"
    
    def test_config_file_strategy_id_generation(self):
        """测试从配置文件生成策略ID"""
        # 模拟配置文件内容（忽略时间范围参数）
        config_data = {
            'transaction_lookback_hours': 24,
            'kol_account_min_count': 3,
            'start_date': '2025-06-01',  # 时间参数应该被忽略
            'end_date': '2025-06-17'     # 时间参数应该被忽略
        }
        
        # 提取策略相关参数（忽略时间范围）
        strategy_params = {k: v for k, v in config_data.items() 
                          if k not in ['start_date', 'end_date', 'backtest_start_date', 'backtest_end_date']}
        
        strategy_id = self.generate_strategy_hash(strategy_params)
        
        # 验证生成的ID格式正确（8位十六进制字符串）
        assert len(strategy_id) == 8, f"策略ID长度应为8位: {strategy_id}"
        assert all(c in '0123456789abcdef' for c in strategy_id), f"策略ID应为十六进制: {strategy_id}"


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 