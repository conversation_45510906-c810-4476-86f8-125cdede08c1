"""
回测引擎单元测试

测试BacktestEngineV2类的各项功能
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from utils.backtest_v2.backtest_engine import BacktestEngineV2
from utils.backtest_v2.config_manager import BacktestConfigV2


class TestBacktestEngineV2:
    """回测引擎测试"""
    
    @pytest.fixture
    def sample_config(self):
        """创建测试配置"""
        return BacktestConfigV2(
            backtest_start_time=**********,  # 2022-01-01
            backtest_end_time=**********,    # 2022-01-02
            transaction_min_amount=100,
            kol_account_min_txs=5,
            kol_account_max_txs=1000,
            kol_account_min_count=3,
            token_mint_lookback_hours=24,
            transaction_lookback_hours=1,
            sell_strategy_hours=12,
            sell_kol_ratio=0.3,
            fixed_trade_amount=1000.0,
            commission_pct=0.003,
            slippage_pct=0.002
        )
    
    @pytest.fixture
    def backtest_engine(self, sample_config):
        """创建回测引擎实例"""
        return BacktestEngineV2(sample_config)
    
    def test_initialization(self, backtest_engine, sample_config):
        """测试初始化"""
        assert backtest_engine.config == sample_config
        assert backtest_engine.data_query is not None
        assert backtest_engine.sell_strategy is not None
        assert backtest_engine.result_dir is None  # 初始为None

        # 信号分析器和结果分析器在预加载卖出数据后才初始化
        assert backtest_engine.signal_analyzer is None
        assert backtest_engine.result_analyzer is None
    
    def test_configuration_parameters(self, backtest_engine):
        """测试配置参数正确性"""
        # 验证配置对象
        assert backtest_engine.config.fixed_trade_amount == 1000.0
        assert backtest_engine.config.commission_pct == 0.003
        assert backtest_engine.config.slippage_pct == 0.002

        # 验证组件配置一致性
        assert backtest_engine.data_query.config == backtest_engine.config
        assert backtest_engine.sell_strategy.config == backtest_engine.config

        # 信号分析器和结果分析器在初始化时为None
        assert backtest_engine.signal_analyzer is None
        assert backtest_engine.result_analyzer is None
    
    def test_calculate_trades_basic(self, backtest_engine):
        """测试基本的交易计算"""
        # 准备测试数据
        buy_signals = [
            {
                "token_address": "token1",
                "signal_timestamp": 1640995400,
                "avg_price_usd": 0.001,
                "total_volume_usd": 1000.0,
                "trigger_kol_count": 5
            }
        ]

        sell_signals = [
            {
                "token_address": "token1",
                "buy_signal_timestamp": 1640995400,
                "sell_timestamp": 1640995500,
                "avg_price_usd": 0.0015,
                "sell_reason": "kol_ratio"
            }
        ]

        # 计算交易
        trades = backtest_engine._calculate_trades(buy_signals, sell_signals)

        # 验证结果
        assert len(trades) == 1
        trade = trades[0]
        assert trade["token_address"] == "token1"
        assert trade["buy_timestamp"] == 1640995400
        assert trade["sell_timestamp"] == 1640995500
        assert trade["buy_price"] == 0.001
        assert trade["sell_price"] == 0.0015
        assert trade["sell_reason"] == "kol_ratio"
    
    def test_calculate_trades_no_sell_signal(self, backtest_engine):
        """测试没有卖出信号的交易计算"""
        buy_signals = [
            {
                "token_address": "token1",
                "signal_timestamp": 1640995400,
                "avg_price_usd": 0.001,
                "total_volume_usd": 1000.0,
                "trigger_kol_count": 5
            }
        ]

        sell_signals = []  # 没有卖出信号

        # 计算交易
        trades = backtest_engine._calculate_trades(buy_signals, sell_signals)

        # 验证结果 - 没有卖出信号时不应该生成交易
        assert len(trades) == 0

    def test_calculate_trades_return_rate(self, backtest_engine):
        """测试交易收益率计算"""
        buy_signals = [
            {
                "token_address": "token1",
                "signal_timestamp": 1640995400,
                "avg_price_usd": 0.001,
                "total_volume_usd": 1000.0,
                "trigger_kol_count": 5
            }
        ]

        sell_signals = [
            {
                "token_address": "token1",
                "buy_signal_timestamp": 1640995400,
                "sell_timestamp": 1640995500,
                "avg_price_usd": 0.002,  # 价格翻倍
                "sell_reason": "kol_ratio"
            }
        ]

        # 计算交易
        trades = backtest_engine._calculate_trades(buy_signals, sell_signals)

        # 验证收益率计算
        assert len(trades) == 1
        trade = trades[0]
        assert "return_rate" in trade
        assert "profit_usd" in trade

        # 验证收益率考虑了手续费和滑点
        commission = backtest_engine.config.commission_pct
        slippage = backtest_engine.config.slippage_pct
        expected_buy_price = 0.001 * (1 + commission + slippage)
        expected_sell_price = 0.002 * (1 - commission - slippage)
        expected_return_rate = (expected_sell_price - expected_buy_price) / expected_buy_price

        assert abs(trade["return_rate"] - expected_return_rate) < 0.001
    
    def test_calculate_trades_zero_price(self, backtest_engine):
        """测试零价格时的交易计算"""
        buy_signals = [
            {
                "token_address": "token1",
                "signal_timestamp": 1640995400,
                "avg_price_usd": 0.0,  # 零价格
                "total_volume_usd": 1000.0,
                "trigger_kol_count": 5
            }
        ]

        sell_signals = [
            {
                "token_address": "token1",
                "buy_signal_timestamp": 1640995400,
                "sell_timestamp": 1640995500,
                "avg_price_usd": 0.0,  # 零价格
                "sell_reason": "timeout"
            }
        ]

        # 计算交易
        trades = backtest_engine._calculate_trades(buy_signals, sell_signals)

        # 验证结果
        assert len(trades) == 1
        trade = trades[0]
        assert trade["return_rate"] == 0.0
        assert trade["profit_usd"] == 0.0
        assert trade.get("price_data_available") == False
    
    def test_calculate_trades_holding_period(self, backtest_engine):
        """测试持有时间计算"""
        buy_signals = [
            {
                "token_address": "token1",
                "signal_timestamp": 1640995400,
                "avg_price_usd": 0.001,
                "total_volume_usd": 1000.0,
                "trigger_kol_count": 5
            }
        ]

        sell_signals = [
            {
                "token_address": "token1",
                "buy_signal_timestamp": 1640995400,
                "sell_timestamp": 1640995400 + 3600,  # 1小时后
                "avg_price_usd": 0.0015,
                "sell_reason": "kol_ratio"
            }
        ]

        # 计算交易
        trades = backtest_engine._calculate_trades(buy_signals, sell_signals)

        # 验证持有时间
        assert len(trades) == 1
        trade = trades[0]
        assert trade["holding_hours"] == 1.0  # 持有时间应为1小时（以小时为单位）
    
    def test_calculate_trades_multiple_tokens(self, backtest_engine):
        """测试多个token的交易计算"""
        buy_signals = [
            {
                "token_address": "token1",
                "signal_timestamp": 1640995400,
                "avg_price_usd": 0.001,
                "total_volume_usd": 1000.0,
                "trigger_kol_count": 5
            },
            {
                "token_address": "token2",
                "signal_timestamp": 1640995500,
                "avg_price_usd": 0.002,
                "total_volume_usd": 2000.0,
                "trigger_kol_count": 4
            }
        ]

        sell_signals = [
            {
                "token_address": "token1",
                "buy_signal_timestamp": 1640995400,
                "sell_timestamp": 1640995600,
                "avg_price_usd": 0.0015,
                "sell_reason": "kol_ratio"
            },
            {
                "token_address": "token2",
                "buy_signal_timestamp": 1640995500,
                "sell_timestamp": 1640995700,
                "avg_price_usd": 0.0018,
                "sell_reason": "timeout"
            }
        ]

        # 计算交易
        trades = backtest_engine._calculate_trades(buy_signals, sell_signals)

        # 验证结果
        assert len(trades) == 2

        # 验证第一个交易
        trade1 = next(t for t in trades if t["token_address"] == "token1")
        assert trade1["sell_reason"] == "kol_ratio"
        assert trade1["kol_count"] == 5

        # 验证第二个交易
        trade2 = next(t for t in trades if t["token_address"] == "token2")
        assert trade2["sell_reason"] == "timeout"
        assert trade2["kol_count"] == 4
    
    def test_component_initialization_order(self, backtest_engine):
        """测试组件初始化顺序"""
        # 验证初始状态
        assert backtest_engine.data_query is not None
        assert backtest_engine.sell_strategy is not None
        assert backtest_engine.signal_analyzer is None
        assert backtest_engine.result_analyzer is None

        # 模拟预加载卖出数据后的初始化
        from utils.backtest_v2.signal_analyzer import SignalAnalyzer
        from utils.backtest_v2.result_analyzer import ResultAnalyzer

        # 手动初始化信号分析器和结果分析器
        sell_data_cache = {"test": "data"}
        backtest_engine.signal_analyzer = SignalAnalyzer(backtest_engine.config, sell_data_cache)
        backtest_engine.result_analyzer = ResultAnalyzer(backtest_engine.config)

        # 验证初始化后的状态
        assert backtest_engine.signal_analyzer is not None
        assert backtest_engine.result_analyzer is not None
        assert backtest_engine.signal_analyzer.config == backtest_engine.config
        assert backtest_engine.result_analyzer.config == backtest_engine.config
    
    def test_result_directory_setting(self, backtest_engine):
        """测试结果目录设置"""
        # 初始状态
        assert backtest_engine.result_dir is None

        # 设置结果目录
        test_dir = "/tmp/backtest_results"
        backtest_engine.result_dir = test_dir

        # 验证设置
        assert backtest_engine.result_dir == test_dir
    
    def test_configuration_validation(self, backtest_engine):
        """测试配置验证"""
        config = backtest_engine.config

        # 验证时间配置
        assert config.backtest_start_time < config.backtest_end_time
        assert config.backtest_start_time > 0
        assert config.backtest_end_time > 0

        # 验证交易参数
        assert config.transaction_min_amount > 0
        assert config.kol_account_min_txs > 0
        assert config.kol_account_max_txs >= config.kol_account_min_txs
        assert config.kol_account_min_count > 0

        # 验证策略参数
        assert config.sell_strategy_hours > 0
        assert 0 < config.sell_kol_ratio <= 1.0

        # 验证成本参数
        assert 0 <= config.commission_pct <= 1.0
        assert 0 <= config.slippage_pct <= 1.0
        assert config.fixed_trade_amount > 0

    def test_logging_configuration(self, backtest_engine):
        """测试日志配置"""
        # 验证引擎有日志记录能力
        import logging
        logger = logging.getLogger("BacktestEngineV2")
        assert logger is not None

        # 验证组件都有配置对象
        assert backtest_engine.data_query.config is not None
        assert backtest_engine.sell_strategy.config is not None


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
