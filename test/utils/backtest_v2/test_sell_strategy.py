"""
卖出策略组件单元测试

测试SellStrategy类的各项功能
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from utils.backtest_v2.sell_strategy import SellStrategy
from utils.backtest_v2.config_manager import BacktestConfigV2


class TestSellStrategy:
    """卖出策略组件测试"""
    
    @pytest.fixture
    def sample_config(self):
        """创建测试配置"""
        return BacktestConfigV2(
            backtest_start_time=**********,  # 2022-01-01
            backtest_end_time=**********,    # 2022-01-02
            transaction_min_amount=100,
            kol_account_min_txs=5,
            kol_account_max_txs=1000,
            kol_account_min_count=3,
            token_mint_lookback_hours=24,
            transaction_lookback_hours=1,
            sell_strategy_hours=12,
            sell_kol_ratio=0.3,
            fixed_trade_amount=1000.0,
            commission_pct=0.003,
            slippage_pct=0.002
        )
    
    @pytest.fixture
    def sell_strategy(self, sample_config):
        """创建卖出策略实例"""
        return SellStrategy(sample_config)
    
    def test_initialization(self, sell_strategy, sample_config):
        """测试初始化"""
        assert sell_strategy.config == sample_config
        assert sell_strategy.sell_strategy_hours == sample_config.sell_strategy_hours
        assert sell_strategy.sell_kol_ratio == sample_config.sell_kol_ratio
        assert sell_strategy.activity_dao is not None
        assert sell_strategy.price_spider is None  # 延迟初始化
        assert hasattr(sell_strategy, 'sell_data_cache')
        assert isinstance(sell_strategy.sell_data_cache, dict)
    
    def test_configuration_parameters(self, sell_strategy):
        """测试配置参数正确性"""
        assert sell_strategy.sell_strategy_hours == 12
        assert sell_strategy.sell_kol_ratio == 0.3
        assert sell_strategy.config.commission_pct == 0.003
        assert sell_strategy.config.slippage_pct == 0.002
    
    def test_configuration_validation(self, sell_strategy):
        """测试配置验证"""
        # 验证配置参数的有效性
        assert sell_strategy.sell_strategy_hours > 0
        assert 0 < sell_strategy.sell_kol_ratio <= 1.0
        assert sell_strategy.kol_account_min_txs > 0
        assert sell_strategy.kol_account_max_txs >= sell_strategy.kol_account_min_txs

        # 验证配置对象的完整性
        config = sell_strategy.config
        assert hasattr(config, 'sell_strategy_hours')
        assert hasattr(config, 'sell_kol_ratio')
        assert hasattr(config, 'commission_pct')
        assert hasattr(config, 'slippage_pct')

        # 验证数值范围
        assert config.commission_pct >= 0
        assert config.slippage_pct >= 0
    
    def test_sell_data_cache_initialization(self, sell_strategy):
        """测试卖出数据缓存初始化"""
        # 验证初始状态
        assert hasattr(sell_strategy, 'sell_data_cache')
        assert isinstance(sell_strategy.sell_data_cache, dict)

        # 手动设置缓存数据来测试结构
        test_data = {
            "test_token": {
                "records": [
                    {"timestamp": **********, "wallet": "kol1", "cost_usd": 300, "event_type": "sell"}
                ],
                "kol_wallets_count": 1
            }
        }

        sell_strategy.sell_data_cache = test_data

        # 验证数据结构
        assert "test_token" in sell_strategy.sell_data_cache
        token_data = sell_strategy.sell_data_cache["test_token"]
        assert "records" in token_data
        assert "kol_wallets_count" in token_data
    
    @pytest.mark.asyncio
    async def test_kol_ratio_sell_strategy_sufficient(self, sell_strategy):
        """测试KOL比例卖出策略 - 比例足够的情况"""
        # 准备买入信号
        buy_signal = {
            "token_address": "test_token",
            "signal_timestamp": 1640995400,
            "kol_wallets": ["kol1", "kol2", "kol3", "kol4", "kol5"],
            "avg_price_usd": 0.001
        }

        # Mock卖出数据 - 2个KOL卖出，比例为2/5=0.4 > 0.3
        sell_strategy.sell_data_cache = {
            "test_token": {
                "records": [
                    {"timestamp": **********, "wallet": "kol1", "cost_usd": 300},
                    {"timestamp": 1640995600, "wallet": "kol3", "cost_usd": 400},
                    {"timestamp": 1640995550, "wallet": "kol1", "cost_usd": 200}  # 同一KOL多次卖出
                ]
            }
        }

        # Mock价格获取
        with patch.object(sell_strategy, '_get_sell_price', return_value=0.0015):
            sell_signal = await sell_strategy._kol_ratio_sell_strategy(buy_signal)

        # 验证结果
        assert sell_signal is not None
        assert sell_signal["sell_reason"] == "kol_ratio"
        assert sell_signal["sell_timestamp"] == 1640995600  # 第二个KOL卖出的时间戳
        assert sell_signal["kol_sell_ratio"] >= 0.3
    
    @pytest.mark.asyncio
    async def test_kol_ratio_sell_strategy_insufficient(self, sell_strategy):
        """测试KOL比例卖出策略 - 比例不足的情况"""
        # 准备买入信号
        buy_signal = {
            "token_address": "test_token",
            "signal_timestamp": 1640995400,
            "kol_wallets": ["kol1", "kol2", "kol3", "kol4", "kol5"],
            "avg_price_usd": 0.001
        }

        # Mock卖出数据 - 只有1个KOL卖出，比例为1/5=0.2 < 0.3
        sell_strategy.sell_data_cache = {
            "test_token": {
                "records": [
                    {"timestamp": **********, "wallet": "kol1", "cost_usd": 300}
                ]
            }
        }

        sell_signal = await sell_strategy._kol_ratio_sell_strategy(buy_signal)

        # 验证结果 - 比例不足，应该返回None
        assert sell_signal is None
    
    @pytest.mark.asyncio
    async def test_kol_ratio_sell_strategy_no_sell_data(self, sell_strategy):
        """测试没有卖出数据的情况"""
        # 准备买入信号
        buy_signal = {
            "token_address": "test_token",
            "signal_timestamp": 1640995400,
            "kol_wallets": ["kol1", "kol2", "kol3"],
            "avg_price_usd": 0.001
        }

        # 没有卖出数据
        sell_strategy.sell_data_cache = {}

        sell_signal = await sell_strategy._kol_ratio_sell_strategy(buy_signal)

        # 验证结果 - 没有卖出数据，应该返回None
        assert sell_signal is None
    
    @pytest.mark.asyncio
    async def test_kol_ratio_sell_strategy_before_signal_filtered(self, sell_strategy):
        """测试信号时间之前的卖出记录被过滤"""
        # 准备买入信号 - 使用5个KOL，这样1个卖出的比例是1/5=0.2 < 0.3
        buy_signal = {
            "token_address": "test_token",
            "signal_timestamp": 1640995400,
            "kol_wallets": ["kol1", "kol2", "kol3", "kol4", "kol5"],
            "avg_price_usd": 0.001
        }

        # 卖出时间在信号时间之前，应该被过滤
        sell_strategy.sell_data_cache = {
            "test_token": {
                "records": [
                    {"timestamp": 1640995300, "wallet": "kol1", "cost_usd": 300},  # 信号前
                    {"timestamp": **********, "wallet": "kol2", "cost_usd": 400}   # 信号后
                ]
            }
        }

        sell_signal = await sell_strategy._kol_ratio_sell_strategy(buy_signal)

        # 只有1个有效卖出记录，1/5=0.2 < 0.3，应该返回None
        assert sell_signal is None
    
    @pytest.mark.asyncio
    async def test_timeout_sell_strategy(self, sell_strategy):
        """测试超时卖出策略"""
        # 准备买入信号
        buy_signal = {
            "token_address": "test_token",
            "signal_timestamp": 1640995400,
            "kol_wallets": ["kol1", "kol2", "kol3"],
            "avg_price_usd": 0.001
        }

        # Mock价格获取
        with patch.object(sell_strategy, '_get_sell_price', return_value=0.0015):
            sell_signal = await sell_strategy._timeout_sell_strategy(buy_signal)

        # 验证结果
        assert sell_signal is not None
        assert sell_signal["sell_reason"] == "timeout"
        assert sell_signal["token_address"] == "test_token"
        assert sell_signal["buy_signal_timestamp"] == 1640995400
        # 卖出时间应该是买入时间 + 12小时
        expected_sell_time = 1640995400 + 12 * 3600
        assert sell_signal["sell_timestamp"] == expected_sell_time
        assert sell_signal["avg_price_usd"] == 0.0015
    
    @pytest.mark.asyncio
    async def test_ensure_price_spider(self, sell_strategy):
        """测试价格爬虫初始化"""
        # 初始状态应该为None
        assert sell_strategy.price_spider is None
        
        # 调用初始化方法
        await sell_strategy._ensure_price_spider()
        
        # 验证已初始化
        assert sell_strategy.price_spider is not None
        
        # 再次调用应该不重复初始化
        original_spider = sell_strategy.price_spider
        await sell_strategy._ensure_price_spider()
        assert sell_strategy.price_spider is original_spider

    @pytest.mark.asyncio
    async def test_get_sell_price_success(self, sell_strategy):
        """测试成功获取卖出价格"""
        token_address = "test_token"
        sell_timestamp = **********

        # Mock GMGN价格接口返回成功
        mock_price_data = {"close": 0.0015}

        with patch.object(sell_strategy, '_ensure_price_spider') as mock_ensure:
            mock_ensure.return_value = None
            sell_strategy.price_spider = AsyncMock()
            sell_strategy.price_spider.get_price_point_in_time = AsyncMock(return_value=mock_price_data)

            price = await sell_strategy._get_sell_price(token_address, sell_timestamp)

        assert price == 0.0015
        sell_strategy.price_spider.get_price_point_in_time.assert_called_once_with(
            address=token_address,
            timestamp=sell_timestamp,
            resolution="1m"
        )

    @pytest.mark.asyncio
    async def test_get_sell_price_alternative_field(self, sell_strategy):
        """测试使用备用价格字段"""
        token_address = "test_token"
        sell_timestamp = **********

        # Mock GMGN价格接口返回c字段而不是close字段
        mock_price_data = {"c": 0.0012}

        with patch.object(sell_strategy, '_ensure_price_spider') as mock_ensure:
            mock_ensure.return_value = None
            sell_strategy.price_spider = AsyncMock()
            sell_strategy.price_spider.get_price_point_in_time = AsyncMock(return_value=mock_price_data)

            price = await sell_strategy._get_sell_price(token_address, sell_timestamp)

        assert price == 0.0012

    @pytest.mark.asyncio
    async def test_get_sell_price_no_data(self, sell_strategy):
        """测试获取价格失败的情况"""
        token_address = "test_token"
        sell_timestamp = **********

        # Mock GMGN价格接口返回空数据
        with patch.object(sell_strategy, '_ensure_price_spider') as mock_ensure:
            mock_ensure.return_value = None
            sell_strategy.price_spider = AsyncMock()
            sell_strategy.price_spider.get_price_point_in_time = AsyncMock(return_value={})

            price = await sell_strategy._get_sell_price(token_address, sell_timestamp)

        assert price == 0.0

    @pytest.mark.asyncio
    async def test_get_sell_price_exception(self, sell_strategy):
        """测试获取价格时发生异常"""
        token_address = "test_token"
        sell_timestamp = **********

        # Mock GMGN价格接口抛出异常
        with patch.object(sell_strategy, '_ensure_price_spider') as mock_ensure:
            mock_ensure.return_value = None
            sell_strategy.price_spider = AsyncMock()
            sell_strategy.price_spider.get_price_point_in_time = AsyncMock(side_effect=Exception("Network error"))

            price = await sell_strategy._get_sell_price(token_address, sell_timestamp)

        assert price == 0.0

    @pytest.mark.asyncio
    async def test_determine_sell_signals_integration(self, sell_strategy):
        """测试确定卖出信号的集成流程"""
        # 准备买入信号
        buy_signals = [
            {
                "token_address": "test_token1",
                "signal_timestamp": 1640995400,
                "kol_wallets": ["kol1", "kol2", "kol3"],
                "avg_price_usd": 0.001
            },
            {
                "token_address": "test_token2",
                "signal_timestamp": **********,
                "kol_wallets": ["kol4", "kol5", "kol6"],
                "avg_price_usd": 0.002
            }
        ]

        # Mock卖出数据
        sell_strategy.sell_data_cache = {
            "test_token1": {
                "records": [
                    {"timestamp": 1640995600, "wallet": "kol1", "cost_usd": 300},
                    {"timestamp": 1640995700, "wallet": "kol2", "cost_usd": 400}
                ]
            }
        }

        # Mock价格获取
        with patch.object(sell_strategy, '_get_sell_price', return_value=0.0015):
            sell_signals = await sell_strategy.determine_sell_signals(buy_signals)

        # 验证结果
        assert isinstance(sell_signals, list)
        assert len(sell_signals) >= 0  # 可能有0个或多个卖出信号

        # 验证卖出信号格式
        for sell_signal in sell_signals:
            assert "token_address" in sell_signal
            assert "buy_signal_timestamp" in sell_signal
            assert "sell_timestamp" in sell_signal
            assert "sell_reason" in sell_signal
            assert sell_signal["sell_reason"] in ["kol_ratio", "timeout"]

    def test_sell_data_cache_structure(self, sell_strategy):
        """测试卖出数据缓存结构"""
        # 验证初始状态
        assert hasattr(sell_strategy, 'sell_data_cache')
        assert isinstance(sell_strategy.sell_data_cache, dict)

        # 设置测试数据
        sell_strategy.sell_data_cache = {
            "test_token": {
                "records": [
                    {"timestamp": **********, "wallet": "kol1", "cost_usd": 300, "event_type": "sell"}
                ],
                "kol_wallets_count": 1
            }
        }

        # 验证数据结构
        assert "test_token" in sell_strategy.sell_data_cache
        token_data = sell_strategy.sell_data_cache["test_token"]
        assert "records" in token_data
        assert "kol_wallets_count" in token_data

        sell_record = token_data["records"][0]
        assert "timestamp" in sell_record
        assert "wallet" in sell_record
        assert "cost_usd" in sell_record
        assert "event_type" in sell_record


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
