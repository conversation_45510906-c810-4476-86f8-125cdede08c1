"""重试策略测试

测试各种重试策略的功能和行为。
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock

from utils.retry.strategies import (
    ExponentialBackoffStrategy,
    LinearBackoffStrategy,
    RandomDelayStrategy,
    FixedDelayStrategy,
    NoDelayStrategy,
)


class TestExponentialBackoffStrategy:
    """指数退避策略测试"""
    
    def test_init_with_defaults(self):
        """测试默认参数初始化"""
        strategy = ExponentialBackoffStrategy()
        assert strategy.max_retries == 3
        assert strategy.base_delay == 1.0
        assert strategy.multiplier == 2.0
        assert strategy.max_delay == 60.0
        assert strategy.jitter is True
    
    def test_init_with_custom_params(self):
        """测试自定义参数初始化"""
        strategy = ExponentialBackoffStrategy(
            max_retries=5,
            base_delay=0.5,
            multiplier=1.5,
            max_delay=30.0,
            jitter=False
        )
        assert strategy.max_retries == 5
        assert strategy.base_delay == 0.5
        assert strategy.multiplier == 1.5
        assert strategy.max_delay == 30.0
        assert strategy.jitter is False
    
    @pytest.mark.asyncio
    async def test_calculate_delay_without_jitter(self):
        """测试无抖动的延迟计算"""
        strategy = ExponentialBackoffStrategy(
            base_delay=1.0,
            multiplier=2.0,
            jitter=False
        )
        
        # 第1次重试: 1.0 * (2^0) = 1.0
        delay1 = await strategy.calculate_delay(1)
        assert delay1 == 1.0
        
        # 第2次重试: 1.0 * (2^1) = 2.0
        delay2 = await strategy.calculate_delay(2)
        assert delay2 == 2.0
        
        # 第3次重试: 1.0 * (2^2) = 4.0
        delay3 = await strategy.calculate_delay(3)
        assert delay3 == 4.0
    
    @pytest.mark.asyncio
    async def test_calculate_delay_with_max_delay(self):
        """测试最大延迟限制"""
        strategy = ExponentialBackoffStrategy(
            base_delay=1.0,
            multiplier=2.0,
            max_delay=3.0,
            jitter=False
        )
        
        # 第4次重试应该被限制在max_delay
        delay = await strategy.calculate_delay(4)
        assert delay == 3.0
    
    @pytest.mark.asyncio
    async def test_calculate_delay_with_jitter(self):
        """测试有抖动的延迟计算"""
        strategy = ExponentialBackoffStrategy(
            base_delay=2.0,
            multiplier=2.0,
            jitter=True
        )
        
        # 由于有随机抖动，测试多次确保延迟在合理范围内
        delays = []
        for _ in range(10):
            delay = await strategy.calculate_delay(1)
            delays.append(delay)
        
        # 基础延迟是2.0，抖动范围是±25%，所以应该在[1.5, 2.5]范围内
        for delay in delays:
            assert delay >= 0  # 确保延迟不为负
            assert delay <= 2.5 + 0.1  # 允许一些浮点误差
    
    def test_should_retry(self):
        """测试重试判断"""
        strategy = ExponentialBackoffStrategy(max_retries=3)
        
        assert strategy.should_retry(1) is True
        assert strategy.should_retry(2) is True
        assert strategy.should_retry(3) is False  # 第3次已经达到max_retries，不再重试
        assert strategy.should_retry(4) is False
    
    def test_is_retryable_exception(self):
        """测试异常是否可重试"""
        strategy = ExponentialBackoffStrategy()
        
        # 这些异常应该可以重试
        assert strategy.is_retryable_exception(Exception("general error")) is True
        assert strategy.is_retryable_exception(RuntimeError("runtime error")) is True
        assert strategy.is_retryable_exception(ConnectionError("connection error")) is True
        
        # 这些异常不应该重试
        assert strategy.is_retryable_exception(ValueError("value error")) is False
        assert strategy.is_retryable_exception(TypeError("type error")) is False
        assert strategy.is_retryable_exception(KeyboardInterrupt()) is False


class TestLinearBackoffStrategy:
    """线性退避策略测试"""
    
    @pytest.mark.asyncio
    async def test_calculate_delay(self):
        """测试线性延迟计算"""
        strategy = LinearBackoffStrategy(
            base_delay=1.0,
            increment=0.5,
            max_delay=10.0
        )
        
        # 第1次重试: 1.0 + (0.5 * 0) = 1.0
        delay1 = await strategy.calculate_delay(1)
        assert delay1 == 1.0
        
        # 第2次重试: 1.0 + (0.5 * 1) = 1.5
        delay2 = await strategy.calculate_delay(2)
        assert delay2 == 1.5
        
        # 第3次重试: 1.0 + (0.5 * 2) = 2.0
        delay3 = await strategy.calculate_delay(3)
        assert delay3 == 2.0
    
    @pytest.mark.asyncio
    async def test_max_delay_limit(self):
        """测试最大延迟限制"""
        strategy = LinearBackoffStrategy(
            base_delay=1.0,
            increment=2.0,
            max_delay=3.0
        )
        
        # 第2次重试: 1.0 + (2.0 * 1) = 3.0
        delay2 = await strategy.calculate_delay(2)
        assert delay2 == 3.0
        
        # 第3次重试应该被限制在max_delay
        delay3 = await strategy.calculate_delay(3)
        assert delay3 == 3.0


class TestRandomDelayStrategy:
    """随机延迟策略测试"""
    
    def test_init_validation(self):
        """测试初始化参数验证"""
        # 正常情况
        strategy = RandomDelayStrategy(min_delay=0.5, max_delay=2.0)
        assert strategy.min_delay == 0.5
        assert strategy.max_delay == 2.0
        
        # 异常情况：min_delay >= max_delay
        with pytest.raises(ValueError, match="min_delay must be less than max_delay"):
            RandomDelayStrategy(min_delay=2.0, max_delay=1.0)
    
    @pytest.mark.asyncio
    async def test_calculate_delay_range(self):
        """测试随机延迟在指定范围内"""
        strategy = RandomDelayStrategy(min_delay=1.0, max_delay=3.0)
        
        # 测试多次确保延迟在指定范围内
        for _ in range(20):
            delay = await strategy.calculate_delay(1)
            assert 1.0 <= delay <= 3.0


class TestFixedDelayStrategy:
    """固定延迟策略测试"""
    
    @pytest.mark.asyncio
    async def test_calculate_delay(self):
        """测试固定延迟"""
        strategy = FixedDelayStrategy(delay=2.5)
        
        # 无论重试多少次，延迟都应该是固定值
        for attempt in range(1, 6):
            delay = await strategy.calculate_delay(attempt)
            assert delay == 2.5


class TestNoDelayStrategy:
    """无延迟策略测试"""
    
    @pytest.mark.asyncio
    async def test_calculate_delay(self):
        """测试无延迟"""
        strategy = NoDelayStrategy()
        
        # 无论重试多少次，延迟都应该是0
        for attempt in range(1, 6):
            delay = await strategy.calculate_delay(attempt)
            assert delay == 0.0 