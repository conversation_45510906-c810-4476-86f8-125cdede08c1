"""重试系统集成测试

展示如何使用新的重试系统，包括装饰器和管理器。
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, Mock, patch

from utils.retry import (
    RetryManager, 
    ExponentialBackoffStrategy, 
    LinearBackoffStrategy,
    CircuitBreaker,
    async_retry_with_strategy,
    retry_with_strategy
)


class TestNewRetryFeatures:
    """测试新的重试功能"""
    
    @pytest.mark.asyncio
    async def test_exponential_backoff_spider(self):
        """测试在BasicSpider中使用指数退避策略"""
        from test.utils.spiders.test_basic_spider import ConcreteBasicSpider
        
        # 创建使用指数退避策略的spider
        spider = ConcreteBasicSpider(
            max_retries=3, 
            retry_interval=0.1, 
            retry_strategy='exponential'
        )
        
        # 验证策略类型
        assert isinstance(spider.retry_manager.strategy, ExponentialBackoffStrategy)
        assert spider.retry_manager.strategy.base_delay == 0.1
        assert spider.retry_manager.strategy.multiplier == 2.0
    
    @pytest.mark.asyncio 
    @patch('utils.retry.retry_manager.asyncio.sleep', new_callable=AsyncMock)  # Patch the exact location
    async def test_decorator_usage(self, mock_sleep):
        """测试装饰器的使用"""
        call_count = 0
        
        @async_retry_with_strategy(
            strategy=ExponentialBackoffStrategy(max_retries=2, base_delay=0.01),
            enable_circuit_breaker=False
        )
        async def unstable_function():
            nonlocal call_count
            call_count += 1
            if call_count < 2:
                raise Exception("临时失败")
            return "成功"
        
        result = await unstable_function()
        assert result == "成功"
        assert call_count == 2
        # 验证sleep被调用了（用于延迟）
        assert mock_sleep.call_count >= 1
    
    @pytest.mark.asyncio
    @patch('utils.retry.retry_manager.asyncio.sleep', new_callable=AsyncMock)  # Patch the exact location
    async def test_circuit_breaker_integration(self, mock_sleep):
        """测试断路器集成"""
        circuit_breaker = CircuitBreaker(
            failure_threshold=2,
            recovery_timeout=0.1
        )
        
        manager = RetryManager(
            strategy=ExponentialBackoffStrategy(max_retries=1, base_delay=0.01),
            circuit_breaker=circuit_breaker,
            enable_circuit_breaker=True
        )
        
        async def failing_function():
            raise Exception("始终失败")
        
        # 第一次失败
        with pytest.raises(Exception):
            await manager.execute_with_retry(failing_function)
        
        # 第二次失败，应该打开断路器
        with pytest.raises(Exception):
            await manager.execute_with_retry(failing_function)
        
        # 第三次调用应该被断路器直接拒绝
        from utils.retry.circuit_breaker import CircuitBreakerException
        with pytest.raises(CircuitBreakerException):
            await manager.execute_with_retry(failing_function)
    
    @pytest.mark.asyncio
    async def test_different_strategies(self):
        """测试不同策略的延迟计算"""
        strategies = [
            ExponentialBackoffStrategy(base_delay=0.1, jitter=False),
            LinearBackoffStrategy(base_delay=0.1, increment=0.05),
        ]
        
        for strategy in strategies:
            delay1 = await strategy.calculate_delay(1)
            delay2 = await strategy.calculate_delay(2)
            
            # 确保延迟都是正数且第二次比第一次长（或相等）
            assert delay1 > 0
            assert delay2 >= delay1
    
    def test_retry_manager_stats(self):
        """测试重试管理器的统计功能"""
        manager = RetryManager(
            strategy=ExponentialBackoffStrategy(max_retries=2),
            enable_circuit_breaker=False
        )
        
        stats = manager.get_stats()
        assert 'total_executions' in stats
        assert 'total_retries' in stats
        assert 'success_rate' in stats
        assert stats['strategy'] == 'ExponentialBackoffStrategy'
    
    @pytest.mark.asyncio
    @patch('utils.retry.retry_manager.asyncio.sleep', new_callable=AsyncMock)  # Patch the exact location
    async def test_mixed_strategy_usage(self, mock_sleep):
        """测试在一个Spider实例中混合使用不同策略"""
        from test.utils.spiders.test_basic_spider import ConcreteBasicSpider
        
        spider = ConcreteBasicSpider(max_retries=2, retry_interval=0.1)
        
        # Mock失败的响应
        mock_response = Mock()
        mock_response.status_code = 500
        
        # 完全mock所有网络相关组件，避免真实请求
        with patch.object(spider, 'init_sessions') as mock_init, \
             patch.object(spider, 'setup') as mock_setup, \
             patch.object(spider, 'switch_session') as mock_switch:
            
            mock_session = AsyncMock()
            mock_session.request.return_value = mock_response
            spider.session = mock_session
            
            # 确保switch_session不会替换我们的mock_session
            async def mock_switch_session(*args, **kwargs):
                pass
            mock_switch.side_effect = mock_switch_session
            
            # 使用自定义的retry_interval，应该创建临时的管理器
            result = await spider.request_with_retry('GET', 'http://test.com', retry_interval=0.05)
            
            # 应该返回最后一次的响应（实际状态码）
            assert result.status_code == 500
            # 验证至少进行了重试
            assert mock_session.request.call_count >= 1
            # 验证sleep被调用了
            assert mock_sleep.call_count >= 1


class TestAdvancedFeatures:
    """测试高级功能"""
    
    @pytest.mark.asyncio
    @patch('utils.retry.retry_manager.asyncio.sleep', new_callable=AsyncMock)  # Patch the exact location
    async def test_callback_functions(self, mock_sleep):
        """测试回调函数"""
        before_retry_calls = []
        after_retry_calls = []
        
        async def before_retry_callback(attempt, exception, delay):
            before_retry_calls.append((attempt, type(exception).__name__, delay))
        
        async def after_retry_callback(attempt, exception, result):
            after_retry_calls.append((attempt, exception, result))
        
        manager = RetryManager(
            strategy=ExponentialBackoffStrategy(max_retries=2, base_delay=0.01),
            enable_circuit_breaker=False
        )
        
        call_count = 0
        async def test_function():
            nonlocal call_count
            call_count += 1
            if call_count < 2:
                raise RuntimeError("测试异常")  # 使用可重试的异常
            return "成功"
        
        result = await manager.execute_with_retry(
            test_function,
            before_retry=before_retry_callback,
            after_retry=after_retry_callback
        )
        
        assert result == "成功"
        assert len(before_retry_calls) == 1  # 只有一次重试
        assert before_retry_calls[0][1] == "RuntimeError"
        assert len(after_retry_calls) == 1
        assert after_retry_calls[0][2] == "成功"
        # 验证sleep被调用了
        assert mock_sleep.call_count >= 1 