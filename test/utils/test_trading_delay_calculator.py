"""
TradingDelayCalculator 测试模块

本模块测试FR005需求中的延迟计算算法，验证延迟计算器的各种场景
包括正常计算、异常处理、状态标记等功能。

@测试用例设计: docs/features/0.1.0/workflows/trading_delay_monitor_test_cases_ai.md
@技术实现方案: docs/features/0.1.0/workflows/trading_delay_monitor_dev_plan_ai.md
"""

import unittest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone
from typing import List, Optional, Tuple

import pytest
from bson import ObjectId

from utils.trading_delay_calculator import TradingDelayCalculator
from models.trade_record import TradeRecord
from models.trading_delay_record import TradingDelayRecord, DelayStatus


class TestTradingDelayCalculator(unittest.IsolatedAsyncioTestCase):
    """TradingDelayCalculator测试类"""

    def setUp(self):
        """测试前置设置"""
        self.calculator = TradingDelayCalculator()
        
        # 创建测试用的基础时间
        self.base_time = datetime(2025, 6, 19, 10, 30, 0, tzinfo=timezone.utc)
        self.kol_time = datetime(2025, 6, 19, 10, 25, 0, tzinfo=timezone.utc)  # 5分钟前
        
        # 创建测试用的ObjectId
        self.test_signal_id = ObjectId()
        self.test_trade_id = ObjectId()

    def _create_mock_trade_record(self, **overrides) -> MagicMock:
        """创建模拟的交易记录"""
        mock_trade = MagicMock()
        mock_trade.id = overrides.get('id', self.test_trade_id)
        mock_trade.signal_id = overrides.get('signal_id', self.test_signal_id)
        mock_trade.strategy_name = overrides.get('strategy_name', 'test_strategy')
        mock_trade.trade_type = overrides.get('trade_type', 'buy')
        mock_trade.status = overrides.get('status', 'success')
        mock_trade.created_at = overrides.get('created_at', self.base_time)
        mock_trade.token_in_address = overrides.get('token_in_address', 'token_123')
        mock_trade.trade_provider = overrides.get('trade_provider', 'test_provider')
        return mock_trade

    def _create_delay_record_mock(self, delay_status: DelayStatus = DelayStatus.CALCULATED, delay_seconds: float = 300.0):
        """创建模拟的延迟记录"""
        delay_record = MagicMock()
        delay_record.delay_status = delay_status
        delay_record.delay_seconds = delay_seconds
        delay_record.trade_record_id = self.test_trade_id
        delay_record.signal_id = self.test_signal_id
        delay_record.strategy_name = "test_strategy"
        delay_record.token_address = "test_token_address"
        delay_record.trade_type = "buy"
        delay_record.trade_amount = 100.0
        delay_record.hit_kol_wallets = ["wallet1", "wallet2"]
        delay_record.kol_last_trade_timestamp = self.kol_time
        delay_record.trade_execution_timestamp = self.base_time
        
        # Mock save方法为异步
        delay_record.save = AsyncMock()
        
        return delay_record

    async def test_tc_201_normal_delay_calculation(self):
        """
        TC-201: 正常计算交易延迟
        
        验证延迟计算器能够正确计算KOL交易时间与实际交易时间的延迟
        
        @测试用例: TC-201 - 正常计算交易延迟
        """
        # Arrange
        trade_record = self._create_mock_trade_record()
        expected_delay_seconds = 300.0  # 5分钟 = 300秒
        
        # Mock DAO操作 - 重要：要Mock Calculator实例的dao属性
        with patch.object(self.calculator, 'dao') as mock_dao:
            mock_dao.find_by_trade_record_id = AsyncMock(return_value=None)
            
            # Mock KOL解析器 - 直接Mock Calculator实例的kol_resolver属性
            with patch.object(self.calculator, 'kol_resolver') as mock_resolver:
                mock_resolver.resolve_kol_last_trade_time = AsyncMock(return_value=(
                    self.kol_time,  # KOL交易时间
                    ['wallet1', 'wallet2'],  # 命中的钱包
                    None  # 无错误
                ))
                
                # Mock TradingDelayRecord构造函数
                with patch('utils.trading_delay_calculator.TradingDelayRecord') as mock_record_class:
                    mock_delay_record = self._create_delay_record_mock()
                    mock_delay_record.hit_kol_wallets = ['wallet1', 'wallet2']
                    mock_record_class.return_value = mock_delay_record
                    
                    # Act
                    result = await self.calculator._calculate_single_trade_delay(trade_record)
                    
                    # Assert
                    self.assertIsNotNone(result)
                    self.assertEqual(result.delay_status, DelayStatus.CALCULATED)
                    self.assertEqual(result.delay_seconds, expected_delay_seconds)
                    self.assertEqual(result.hit_kol_wallets, ['wallet1', 'wallet2'])
                    
                    # 验证调用参数
                    mock_resolver.resolve_kol_last_trade_time.assert_called_once_with(trade_record)

    async def test_tc_202_missing_signal_id(self):
        """
        TC-202: 处理无signal_id的交易
        
        验证当交易记录缺少signal_id时，正确标记为SIGNAL_MISSING状态
        
        @测试用例: TC-202 - 处理无signal_id的交易
        """
        # Arrange
        trade_record = self._create_mock_trade_record(signal_id=None)
        
        # Mock DAO操作 - 重要：要Mock Calculator实例的dao属性
        with patch.object(self.calculator, 'dao') as mock_dao:
            mock_dao.find_by_trade_record_id = AsyncMock(return_value=None)
            
            # Mock TradingDelayRecord构造函数
            with patch('utils.trading_delay_calculator.TradingDelayRecord') as mock_record_class:
                mock_delay_record = self._create_delay_record_mock(DelayStatus.SIGNAL_MISSING, None)
                mock_record_class.return_value = mock_delay_record
                
                # Act
                result = await self.calculator._calculate_single_trade_delay(trade_record)
                
                # Assert
                self.assertIsNotNone(result)
                self.assertEqual(result.delay_status, DelayStatus.SIGNAL_MISSING)
                self.assertIsNone(result.delay_seconds)

    async def test_tc_203_negative_delay_anomaly(self):
        """
        TC-203: 检测负延迟异常
        
        验证当KOL交易时间晚于实际交易时间时，正确标记为TIMESTAMP_ANOMALY
        
        @测试用例: TC-203 - 检测负延迟异常
        """
        # Arrange
        future_kol_time = datetime(2025, 6, 19, 10, 35, 0, tzinfo=timezone.utc)  # 比交易时间晚5分钟
        trade_record = self._create_mock_trade_record()
        
        # Mock DAO操作 - 重要：要Mock Calculator实例的dao属性
        with patch.object(self.calculator, 'dao') as mock_dao:
            mock_dao.find_by_trade_record_id = AsyncMock(return_value=None)
            
            # Mock KOL解析器 - 直接Mock Calculator实例的kol_resolver属性
            with patch.object(self.calculator, 'kol_resolver') as mock_resolver:
                mock_resolver.resolve_kol_last_trade_time = AsyncMock(return_value=(
                    future_kol_time,  # KOL时间晚于交易时间
                    ['wallet1'],
                    None
                ))
                
                # Mock TradingDelayRecord构造函数
                with patch('utils.trading_delay_calculator.TradingDelayRecord') as mock_record_class:
                    mock_delay_record = self._create_delay_record_mock(
                        DelayStatus.TIMESTAMP_ANOMALY, 
                        -300.0  # 负延迟
                    )
                    mock_delay_record.kol_last_trade_timestamp = future_kol_time
                    mock_record_class.return_value = mock_delay_record
                    
                    # Act
                    result = await self.calculator._calculate_single_trade_delay(trade_record)
                    
                    # Assert
                    self.assertIsNotNone(result)
                    self.assertEqual(result.delay_status, DelayStatus.TIMESTAMP_ANOMALY)
                    self.assertEqual(result.delay_seconds, -300.0)  # 负延迟
                    self.assertEqual(result.kol_last_trade_timestamp, future_kol_time)

    async def test_tc_204_excessive_delay_detection(self):
        """
        TC-204: 检测过长延迟异常
        
        验证当延迟超过10分钟(600秒)时，正确标记为EXCESSIVE_DELAY
        
        @测试用例: TC-204 - 检测过长延迟异常
        """
        # Arrange
        old_kol_time = datetime(2025, 6, 19, 10, 15, 0, tzinfo=timezone.utc)  # 15分钟前
        trade_record = self._create_mock_trade_record()
        expected_delay = 900.0  # 15分钟 = 900秒
        
        # Mock DAO操作 - 重要：要Mock Calculator实例的dao属性
        with patch.object(self.calculator, 'dao') as mock_dao:
            mock_dao.find_by_trade_record_id = AsyncMock(return_value=None)
            
            # Mock KOL解析器 - 直接Mock Calculator实例的kol_resolver属性
            with patch.object(self.calculator, 'kol_resolver') as mock_resolver:
                mock_resolver.resolve_kol_last_trade_time = AsyncMock(return_value=(
                    old_kol_time,  # 15分钟前的KOL时间
                    ['wallet1'],
                    None
                ))
                
                # Mock TradingDelayRecord构造函数
                with patch('utils.trading_delay_calculator.TradingDelayRecord') as mock_record_class:
                    mock_delay_record = self._create_delay_record_mock(
                        DelayStatus.EXCESSIVE_DELAY,
                        expected_delay
                    )
                    mock_delay_record.kol_last_trade_timestamp = old_kol_time
                    mock_record_class.return_value = mock_delay_record
                    
                    # Act
                    result = await self.calculator._calculate_single_trade_delay(trade_record)
                    
                    # Assert
                    self.assertIsNotNone(result)
                    self.assertEqual(result.delay_status, DelayStatus.EXCESSIVE_DELAY)
                    self.assertEqual(result.delay_seconds, expected_delay)
                    self.assertEqual(result.kol_last_trade_timestamp, old_kol_time)

    async def test_tc_205_prevent_duplicate_processing(self):
        """
        TC-205: 防止重复处理交易
        
        验证当延迟记录已存在时，返回None不重复处理
        
        @测试用例: TC-205 - 防止重复处理交易
        """
        # Arrange
        trade_record = self._create_mock_trade_record()
        existing_record = MagicMock()
        existing_record.trade_record_id = self.test_trade_id
        
        # Mock DAO返回已存在的记录 - 直接Mock Calculator实例的dao属性
        with patch.object(self.calculator, 'dao') as mock_dao:
            mock_dao.find_by_trade_record_id = AsyncMock(return_value=existing_record)
            
            # Act
            result = await self.calculator._calculate_single_trade_delay(trade_record)
            
            # Assert
            self.assertIsNone(result)
            
            # 验证DAO查询被调用
            mock_dao.find_by_trade_record_id.assert_called_once_with(trade_record.id)

    async def test_tc_206_batch_processing_multiple_trades(self):
        """
        TC-206: 批量处理多个交易记录
        
        验证批量处理方法能正确处理多个交易记录并返回统计信息
        
        @测试用例: TC-206 - 批量处理多个交易记录
        """
        # Arrange
        trade_records = []
        for i in range(5):
            trade_record = self._create_mock_trade_record(
                id=ObjectId(),
                signal_id=ObjectId(),
                created_at=self.base_time.replace(minute=30+i)
            )
            trade_records.append(trade_record)
        
        # Mock 单个延迟计算方法
        with patch.object(self.calculator, '_calculate_single_trade_delay') as mock_calc:
            # 模拟不同的计算结果
            mock_results = []
            for i, trade in enumerate(trade_records):
                if i < 3:  # 前3个成功
                    mock_result = MagicMock()
                    mock_result.delay_status = DelayStatus.CALCULATED
                    mock_result.delay_seconds = 300.0 + i * 60
                    mock_results.append(mock_result)
                elif i == 3:  # 第4个缺少信号
                    mock_result = MagicMock()
                    mock_result.delay_status = DelayStatus.SIGNAL_MISSING
                    mock_results.append(mock_result)
                else:  # 第5个重复处理
                    mock_results.append(None)
            
            mock_calc.side_effect = mock_results
            
            # Mock DAO保存操作 - 直接Mock Calculator实例的dao属性
            with patch.object(self.calculator, 'dao') as mock_dao:
                mock_dao.save = AsyncMock(return_value=True)
                
                # Act
                statistics = await self.calculator.calculate_batch_delays(trade_records)
                
                # Assert
                self.assertEqual(statistics['total_processed'], 5)
                self.assertEqual(statistics['successful_calculations'], 4)  # 4个有结果（3个成功+1个缺少信号）
                self.assertEqual(statistics['skipped_duplicates'], 1)  # 1个重复
                self.assertEqual(statistics['saved_records'], 4)  # 4个保存到数据库

    async def test_kol_time_resolution_error_handling(self):
        """
        测试KOL时间解析错误的处理
        
        验证当KOL时间解析失败时，正确标记为KOL_ACTIVITY_MISSING
        """
        # Arrange
        trade_record = self._create_mock_trade_record()
        
        # Mock DAO操作 - 重要：要Mock Calculator实例的dao属性
        with patch.object(self.calculator, 'dao') as mock_dao:
            mock_dao.find_by_trade_record_id = AsyncMock(return_value=None)
            
            # Mock KOL解析器返回错误 - 直接Mock Calculator实例的kol_resolver属性
            with patch.object(self.calculator, 'kol_resolver') as mock_resolver:
                mock_resolver.resolve_kol_last_trade_time = AsyncMock(return_value=(
                    None,  # 无KOL时间
                    [],    # 无钱包
                    "signal_not_found"  # 错误信息
                ))
                
                # Mock TradingDelayRecord构造函数
                with patch('utils.trading_delay_calculator.TradingDelayRecord') as mock_record_class:
                    mock_delay_record = self._create_delay_record_mock(
                        DelayStatus.KOL_ACTIVITY_MISSING,
                        None
                    )
                    mock_delay_record.error_message = "signal_not_found"
                    mock_delay_record.kol_last_trade_timestamp = None
                    mock_record_class.return_value = mock_delay_record
                    
                    # Act
                    result = await self.calculator._calculate_single_trade_delay(trade_record)
                    
                    # Assert
                    self.assertIsNotNone(result)
                    self.assertEqual(result.delay_status, DelayStatus.KOL_ACTIVITY_MISSING)
                    self.assertIsNone(result.delay_seconds)
                    self.assertIsNone(result.kol_last_trade_timestamp)
                    self.assertEqual(result.error_message, "signal_not_found")

    async def test_boundary_600_seconds_delay(self):
        """
        测试600秒边界值
        
        验证延迟恰好为600秒时，状态为CALCULATED而非EXCESSIVE_DELAY
        """
        # Arrange
        kol_time_600s = datetime(2025, 6, 19, 10, 20, 0, tzinfo=timezone.utc)  # 恰好10分钟前
        trade_record = self._create_mock_trade_record()
        
        # Mock DAO操作 - 重要：要Mock Calculator实例的dao属性
        with patch.object(self.calculator, 'dao') as mock_dao:
            mock_dao.find_by_trade_record_id = AsyncMock(return_value=None)
            
            # Mock KOL解析器 - 直接Mock Calculator实例的kol_resolver属性
            with patch.object(self.calculator, 'kol_resolver') as mock_resolver:
                mock_resolver.resolve_kol_last_trade_time = AsyncMock(return_value=(
                    kol_time_600s,
                    ['wallet1'],
                    None
                ))
                
                # Mock TradingDelayRecord构造函数
                with patch('utils.trading_delay_calculator.TradingDelayRecord') as mock_record_class:
                    mock_delay_record = self._create_delay_record_mock(
                        DelayStatus.CALCULATED,
                        600.0
                    )
                    mock_record_class.return_value = mock_delay_record
                    
                    # Act
                    result = await self.calculator._calculate_single_trade_delay(trade_record)
                    
                    # Assert
                    self.assertIsNotNone(result)
                    self.assertEqual(result.delay_seconds, 600.0)
                    self.assertEqual(result.delay_status, DelayStatus.CALCULATED)  # 600秒不算过长

    async def test_601_seconds_excessive_delay(self):
        """
        测试601秒边界值
        
        验证延迟为601秒时，状态为EXCESSIVE_DELAY
        """
        # Arrange
        kol_time_601s = datetime(2025, 6, 19, 10, 19, 59, tzinfo=timezone.utc)  # 601秒前
        trade_record = self._create_mock_trade_record()
        
        # Mock DAO操作 - 重要：要Mock Calculator实例的dao属性
        with patch.object(self.calculator, 'dao') as mock_dao:
            mock_dao.find_by_trade_record_id = AsyncMock(return_value=None)
            
            # Mock KOL解析器 - 直接Mock Calculator实例的kol_resolver属性
            with patch.object(self.calculator, 'kol_resolver') as mock_resolver:
                mock_resolver.resolve_kol_last_trade_time = AsyncMock(return_value=(
                    kol_time_601s,
                    ['wallet1'],
                    None
                ))
                
                # Mock TradingDelayRecord构造函数
                with patch('utils.trading_delay_calculator.TradingDelayRecord') as mock_record_class:
                    mock_delay_record = self._create_delay_record_mock(
                        DelayStatus.EXCESSIVE_DELAY,
                        601.0
                    )
                    mock_record_class.return_value = mock_delay_record
                    
                    # Act
                    result = await self.calculator._calculate_single_trade_delay(trade_record)
                    
                    # Assert
                    self.assertIsNotNone(result)
                    self.assertEqual(result.delay_seconds, 601.0)
                    self.assertEqual(result.delay_status, DelayStatus.EXCESSIVE_DELAY)

    async def test_calculate_delays_for_unprocessed_trades_method_exists(self):
        """
        TC-210: 测试calculate_delays_for_unprocessed_trades方法存在性
        
        Bug复现测试：验证TradingDelayCalculator类具有calculate_delays_for_unprocessed_trades方法
        这个测试用于复现工作流调用时的AttributeError错误
        
        前置条件：TradingDelayCalculator实例
        预期结果：方法存在且可调用
        """
        # 验证方法存在
        self.assertTrue(
            hasattr(self.calculator, 'calculate_delays_for_unprocessed_trades'),
            "TradingDelayCalculator缺少calculate_delays_for_unprocessed_trades方法"
        )
        
        # 验证方法可调用
        self.assertTrue(
            callable(getattr(self.calculator, 'calculate_delays_for_unprocessed_trades')),
            "calculate_delays_for_unprocessed_trades不是可调用方法"
        )
    
    async def test_calculate_delays_for_unprocessed_trades_basic_functionality(self):
        """
        TC-211: 测试calculate_delays_for_unprocessed_trades基本功能
        
        Bug复现测试：调用该方法并验证返回统计信息格式
        
        前置条件：TradingDelayCalculator实例
        预期结果：返回包含处理统计信息的字典
        """
        # 调用方法（应该不会抛出AttributeError）
        result = await self.calculator.calculate_delays_for_unprocessed_trades(batch_size=10)
        
        # 验证返回结果格式
        self.assertIsInstance(result, dict, "返回结果应该是字典类型")
        
        # 验证必要的统计字段存在
        expected_keys = ['processed', 'calculated', 'errors', 'skipped']
        for key in expected_keys:
            self.assertIn(key, result, f"返回结果缺少必要字段: {key}")
            self.assertIsInstance(result[key], int, f"字段 {key} 应该是整数类型")


if __name__ == '__main__':
    unittest.main() 