import pytest
import os
from unittest.mock import Mock, AsyncMock, patch

from utils.trading.auto_trade_manager import AutoTradeManager
from utils.trading.solana.okx_trade_service import OkxTradeService
from models.config import TradeChannelConfig, TradingParams


class TestAutoTradeManagerChannelCreation:
    """测试 AutoTradeManager 的渠道创建功能"""
    
    @pytest.fixture
    def mock_trading_params(self):
        """模拟交易参数"""
        return TradingParams(
            default_buy_slippage_percentage=1.0,
            default_sell_slippage_percentage=1.0,
            enable_slippage_retry=True,
            slippage_increment_percentage=0.5,
            max_slippage_percentage=5.0
        )
    
    @pytest.fixture
    def okx_channel_config(self, mock_trading_params):
        """OKX 渠道配置"""
        return TradeChannelConfig(
            channel_type="okx",
            priority=1,
            enabled=True,
            max_retries=3,
            trading_params=mock_trading_params,
            channel_params={
                "api_host": "https://www.okx.com",
                "api_key_env": "TEST_OKX_API_KEY", 
                "api_secret_env": "TEST_OKX_API_SECRET",
                "api_passphrase_env": "TEST_OKX_API_PASSPHRASE",
                "rpc_endpoint": "https://api.mainnet-beta.solana.com"
            }
        )
    
    @pytest.fixture
    def mock_okx_env_vars(self):
        """模拟 OKX 环境变量"""
        env_vars = {
            "TEST_OKX_API_KEY": "test_api_key",
            "TEST_OKX_API_SECRET": "test_api_secret",
            "TEST_OKX_API_PASSPHRASE": "test_passphrase"
        }
        with patch.dict(os.environ, env_vars):
            yield env_vars
    
    @pytest.mark.asyncio
    async def test_create_okx_channel_instance_success(self, okx_channel_config, mock_okx_env_vars):
        """测试成功创建 OKX 渠道实例"""
        manager = AutoTradeManager()
        
        # Mock OkxTradeService 的依赖
        with patch('utils.trading.solana.okx_trade_service.httpx.AsyncClient'), \
             patch('utils.trading.solana.okx_trade_service.AsyncClient'), \
             patch('utils.trading.solana.okx_trade_service.OkxTradeService') as MockOkxTradeService:
            
            # 配置 mock 返回
            mock_instance = Mock(spec=OkxTradeService)
            MockOkxTradeService.return_value = mock_instance
            
            # 测试渠道实例创建
            channel_instance = await manager._create_channel_instance(okx_channel_config)
            
            # 验证结果
            assert channel_instance is not None
            assert channel_instance == mock_instance
            
            # 验证 OkxTradeService 被正确调用
            MockOkxTradeService.assert_called_once_with(channel_config={
                "api_host": "https://www.okx.com",
                "api_key_env": "TEST_OKX_API_KEY",
                "api_secret_env": "TEST_OKX_API_SECRET", 
                "api_passphrase_env": "TEST_OKX_API_PASSPHRASE",
                "rpc_endpoint": "https://api.mainnet-beta.solana.com"
            })
    
    @pytest.mark.asyncio
    async def test_create_okx_channel_instance_missing_env_vars(self, okx_channel_config):
        """测试缺少环境变量时创建 OKX 渠道实例失败"""
        manager = AutoTradeManager()
        
        # 不设置环境变量，应该失败
        channel_instance = await manager._create_channel_instance(okx_channel_config)
        
        # 验证返回 None（创建失败）
        assert channel_instance is None
    
    @pytest.mark.asyncio
    async def test_create_okx_channel_instance_partial_env_vars(self, okx_channel_config):
        """测试部分环境变量缺失时创建 OKX 渠道实例失败"""
        # 只设置部分环境变量
        partial_env_vars = {
            "TEST_OKX_API_KEY": "test_api_key",
            # 缺少 SECRET 和 PASSPHRASE
        }
        
        with patch.dict(os.environ, partial_env_vars, clear=True):
            manager = AutoTradeManager()
            
            channel_instance = await manager._create_channel_instance(okx_channel_config)
            
            # 验证返回 None（创建失败）
            assert channel_instance is None
    
    @pytest.mark.asyncio
    async def test_create_okx_channel_instance_with_default_params(self, mock_okx_env_vars):
        """测试使用默认参数创建 OKX 渠道实例"""
        # 创建最小配置（使用默认值）
        minimal_config = TradeChannelConfig(
            channel_type="okx",
            priority=1,
            enabled=True,
            max_retries=3,
            trading_params=TradingParams(),
            channel_params={}  # 空配置，应该使用默认值
        )
        
        manager = AutoTradeManager()
        
        # Mock OkxTradeService 的依赖 
        with patch('utils.trading.solana.okx_trade_service.httpx.AsyncClient'), \
             patch('utils.trading.solana.okx_trade_service.AsyncClient'), \
             patch('utils.trading.solana.okx_trade_service.OkxTradeService') as MockOkxTradeService:
            
            # 配置 mock 返回
            mock_instance = Mock(spec=OkxTradeService)
            MockOkxTradeService.return_value = mock_instance
            
            # 修改环境变量以匹配默认值
            with patch.dict(os.environ, {
                "OKX_API_KEY": "test_api_key",
                "OKX_API_SECRET": "test_api_secret", 
                "OKX_API_PASSPHRASE": "test_passphrase",
                "SOLANA_RPC_URL": "https://api.mainnet-beta.solana.com"
            }):
                # 测试渠道实例创建
                channel_instance = await manager._create_channel_instance(minimal_config)
                
                # 验证结果
                assert channel_instance is not None
                assert channel_instance == mock_instance
                
                # 验证调用参数使用了默认值
                expected_config = {
                    "api_host": "https://www.okx.com",
                    "api_key_env": "OKX_API_KEY",
                    "api_secret_env": "OKX_API_SECRET",
                    "api_passphrase_env": "OKX_API_PASSPHRASE",
                    "rpc_endpoint": "https://api.mainnet-beta.solana.com"
                }
                MockOkxTradeService.assert_called_once_with(channel_config=expected_config)
    
    @pytest.mark.asyncio
    async def test_create_okx_channel_instance_exception_handling(self, okx_channel_config, mock_okx_env_vars):
        """测试创建 OKX 渠道实例时的异常处理"""
        manager = AutoTradeManager()
        
        # Mock OkxTradeService 抛出异常
        with patch('utils.trading.solana.okx_trade_service.OkxTradeService', side_effect=Exception("创建失败")):
            
            channel_instance = await manager._create_channel_instance(okx_channel_config)
            
            # 验证返回 None（异常被捕获）
            assert channel_instance is None
    
    @pytest.mark.asyncio
    async def test_unsupported_channel_type(self):
        """测试不支持的渠道类型"""
        manager = AutoTradeManager()
        
        # 创建不支持的渠道配置
        unsupported_config = TradeChannelConfig(
            channel_type="unsupported_channel",
            priority=1,
            enabled=True,
            max_retries=3,
            trading_params=TradingParams(),
            channel_params={}
        )
        
        # 测试渠道实例创建
        channel_instance = await manager._create_channel_instance(unsupported_config)
        
        # 验证返回 None
        assert channel_instance is None
    
    def test_okx_channel_in_supported_types(self, okx_channel_config):
        """测试 OKX 渠道类型在支持列表中"""
        # 验证配置中的 channel_type 描述已包含 okx
        assert okx_channel_config.channel_type == "okx"
        
        # 这个测试确保我们的配置模型更新是正确的
        # 在实际的 TradeChannelConfig 模型中，channel_type 的描述应该包含 "okx"


if __name__ == "__main__":
    pytest.main([__file__]) 