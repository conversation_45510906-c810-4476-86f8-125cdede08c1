import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timezone
from beanie import PydanticObjectId

from utils.trading.solana.gmgn_trade_service_v2 import GmgnTradeServiceV2, SOL_MINT_ADDRESS
from utils.trading.solana.trade_interface import TradeType, TradeStatus


class TestGmgnTradeServiceV2DecimalsFix:
    """
    GMGN v2代币小数位Bug修复的单元测试
    
    测试动态获取代币精度信息的功能，确保修复了原始的小数位错误Bug
    """
    
    @pytest.fixture
    def service(self):
        """创建测试用的服务实例"""
        return GmgnTradeServiceV2()
    
    @pytest.fixture
    def mock_strategy_snapshot(self):
        """创建模拟的策略快照"""
        return {
            'sell_slippage_percentage': 2.0,
            'sell_priority_fee_sol': 0.0001,
            'gmgn_v2_anti_mev': False
        }
    
    @pytest.mark.asyncio
    async def test_get_token_decimals_sol_token(self, service):
        """测试场景3: SOL代币固定使用9位小数"""
        decimals = await service._get_token_decimals(SOL_MINT_ADDRESS)
        assert decimals == 9
    
    @pytest.mark.asyncio
    async def test_get_token_decimals_spl_token_success(self, service):
        """测试场景1: SPL代币成功获取小数位"""
        test_token_address = "6xAaY2vJ2nZDCqbhFhPx4L547vyh2gRVn2SwCgEabonk"
        
        # Mock TokenInfo返回6位小数
        mock_token_info = {
            'decimals': 6,
            'name': 'Test Token',
            'symbol': 'TEST'
        }
        
        with patch('utils.spiders.solana.token_info.TokenInfo') as mock_token_info_class:
            mock_instance = AsyncMock()
            mock_instance.get_token_info.return_value = mock_token_info
            mock_token_info_class.return_value = mock_instance
            
            decimals = await service._get_token_decimals(test_token_address)
            assert decimals == 6
            
            # 验证TokenInfo被正确调用
            mock_token_info_class.assert_called_once_with(test_token_address, chain="sol")
            mock_instance.get_token_info.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_token_decimals_cache_mechanism(self, service):
        """测试场景4: 缓存机制验证"""
        test_token_address = "6xAaY2vJ2nZDCqbhFhPx4L547vyh2gRVn2SwCgEabonk"
        
        # Mock TokenInfo返回6位小数
        mock_token_info = {
            'decimals': 6,
            'name': 'Test Token',
            'symbol': 'TEST'
        }
        
        with patch('utils.spiders.solana.token_info.TokenInfo') as mock_token_info_class:
            mock_instance = AsyncMock()
            mock_instance.get_token_info.return_value = mock_token_info
            mock_token_info_class.return_value = mock_instance
            
            # 第一次调用
            decimals1 = await service._get_token_decimals(test_token_address)
            assert decimals1 == 6
            
            # 第二次调用，应该使用缓存
            decimals2 = await service._get_token_decimals(test_token_address)
            assert decimals2 == 6
            
            # TokenInfo只应该被调用一次（第一次调用）
            mock_token_info_class.assert_called_once()
            mock_instance.get_token_info.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_token_decimals_fallback_to_default(self, service):
        """测试场景2: 代币信息获取失败，优雅降级到默认6位小数"""
        test_token_address = "6xAaY2vJ2nZDCqbhFhPx4L547vyh2gRVn2SwCgEabonk"
        
        # Mock TokenInfo抛出异常
        with patch('utils.spiders.solana.token_info.TokenInfo') as mock_token_info_class:
            mock_instance = AsyncMock()
            mock_instance.get_token_info.side_effect = Exception("Network error")
            mock_token_info_class.return_value = mock_instance
            
            decimals = await service._get_token_decimals(test_token_address)
            assert decimals == 6  # 应该使用默认值
    
    @pytest.mark.asyncio
    async def test_get_token_decimals_no_decimals_in_response(self, service):
        """测试获取的代币信息中没有decimals字段的情况"""
        test_token_address = "6xAaY2vJ2nZDCqbhFhPx4L547vyh2gRVn2SwCgEabonk"
        
        # Mock TokenInfo返回没有decimals字段的信息
        mock_token_info = {
            'name': 'Test Token',
            'symbol': 'TEST'
            # 缺少 'decimals' 字段
        }
        
        with patch('utils.spiders.solana.token_info.TokenInfo') as mock_token_info_class:
            mock_instance = AsyncMock()
            mock_instance.get_token_info.return_value = mock_token_info
            mock_token_info_class.return_value = mock_instance
            
            decimals = await service._get_token_decimals(test_token_address)
            assert decimals == 6  # 应该使用默认值
    
    @pytest.mark.asyncio
    async def test_prepare_route_params_async_spl_token(self, service, mock_strategy_snapshot):
        """测试异步路由参数准备方法 - SPL代币"""
        test_token_address = "6xAaY2vJ2nZDCqbhFhPx4L547vyh2gRVn2SwCgEabonk"
        amount = 1241.51066
        
        # Mock _get_token_decimals返回6位小数
        with patch.object(service, '_get_token_decimals', return_value=6) as mock_get_decimals:
            
            params = await service._prepare_route_params_async(
                input_token_address=test_token_address,
                output_token_address=SOL_MINT_ADDRESS,
                amount_input_token=amount,
                wallet_address="test_wallet",
                strategy_snapshot=mock_strategy_snapshot,
                trade_type=TradeType.SELL
            )
            
            # 验证正确调用了_get_token_decimals
            mock_get_decimals.assert_called_once_with(test_token_address)
            
            # 验证计算结果：1241.51066 * 10^6 = 1241510660
            expected_amount = str(int(amount * (10 ** 6)))
            assert params['in_amount'] == expected_amount
            assert params['in_amount'] == "1241510660"
    
    @pytest.mark.asyncio
    async def test_prepare_route_params_async_sol_token(self, service, mock_strategy_snapshot):
        """测试异步路由参数准备方法 - SOL代币"""
        amount = 0.1
        
        # Mock _get_token_decimals (SOL应该返回9)
        with patch.object(service, '_get_token_decimals', return_value=9) as mock_get_decimals:
            
            params = await service._prepare_route_params_async(
                input_token_address=SOL_MINT_ADDRESS,
                output_token_address="6xAaY2vJ2nZDCqbhFhPx4L547vyh2gRVn2SwCgEabonk",
                amount_input_token=amount,
                wallet_address="test_wallet",
                strategy_snapshot=mock_strategy_snapshot,
                trade_type=TradeType.BUY
            )
            
            # 验证调用了_get_token_decimals
            mock_get_decimals.assert_called_once_with(SOL_MINT_ADDRESS)
            
            # 验证计算结果：0.1 * 10^9 = 100000000
            expected_amount = str(int(amount * (10 ** 9)))
            assert params['in_amount'] == expected_amount
            assert params['in_amount'] == "100000000"
    
    @pytest.mark.asyncio
    async def test_decimal_calculation_bug_fix(self, service):
        """核心Bug修复验证：确保SPL代币不再使用错误的9位小数默认值"""
        test_token_address = "6xAaY2vJ2nZDCqbhFhPx4L547vyh2gRVn2SwCgEabonk"
        amount = 1241.51066  # 原始Bug报告中的数量
        
        # Mock TokenInfo返回正确的6位小数
        mock_token_info = {'decimals': 6}
        
        with patch('utils.spiders.solana.token_info.TokenInfo') as mock_token_info_class:
            mock_instance = AsyncMock()
            mock_instance.get_token_info.return_value = mock_token_info
            mock_token_info_class.return_value = mock_instance
            
            params = await service._prepare_route_params_async(
                input_token_address=test_token_address,
                output_token_address=SOL_MINT_ADDRESS,
                amount_input_token=amount,
                wallet_address="test_wallet",
                strategy_snapshot={'sell_slippage_percentage': 2.0},
                trade_type=TradeType.SELL
            )
            
            # 验证使用正确的6位小数计算
            correct_amount = str(int(amount * (10 ** 6)))
            assert params['in_amount'] == correct_amount
            assert params['in_amount'] == "1241510660"
            
            # 确保不是错误的9位小数计算结果
            wrong_amount = str(int(amount * (10 ** 9)))
            assert params['in_amount'] != wrong_amount
            assert params['in_amount'] != "1241510660000"


@pytest.mark.asyncio
async def test_integration_execute_trade_with_decimal_fix():
    """集成测试：验证execute_trade方法使用了修复后的小数位逻辑"""
    service = GmgnTradeServiceV2()
    
    # Mock 所有外部依赖
    with patch.object(service, '_get_token_decimals', return_value=6) as mock_get_decimals, \
         patch.object(service, '_get_swap_route', return_value={
             "code": 0,
             "data": {
                 "quote": {"inAmount": "1241510660", "outAmount": "100000000"},
                 "raw_tx": {"swapTransaction": "fake_tx", "lastValidBlockHeight": 12345}
             }
         }) as mock_get_route, \
         patch.object(service, '_sign_transaction', return_value="signed_tx") as mock_sign, \
         patch.object(service, '_submit_transaction', return_value={
             "code": 0,
             "data": {"hash": "test_tx_hash"}
         }) as mock_submit, \
         patch.object(service, '_monitor_transaction_status', return_value={
             "data": {"success": True, "expired": False, "failed": False}
         }) as mock_monitor:
        
        result = await service.execute_trade(
            trade_type=TradeType.SELL,
            input_token_address="6xAaY2vJ2nZDCqbhFhPx4L547vyh2gRVn2SwCgEabonk",
            output_token_address=SOL_MINT_ADDRESS,
            amount_input_token=1241.51066,
            wallet_private_key_b58="fake_private_key",
            wallet_address="fake_wallet_address",
            strategy_snapshot={'sell_slippage_percentage': 2.0},
            signal_id=PydanticObjectId(),
            trade_record_id=PydanticObjectId()
        )
        
        # 验证_get_token_decimals被调用了
        mock_get_decimals.assert_called_once_with("6xAaY2vJ2nZDCqbhFhPx4L547vyh2gRVn2SwCgEabonk")
        
        # 验证交易成功
        assert result.status == TradeStatus.SUCCESS
        assert result.tx_hash == "test_tx_hash" 