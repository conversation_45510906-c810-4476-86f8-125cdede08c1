import pytest
import asyncio
import os
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timezone
from decimal import Decimal
from beanie import PydanticObjectId
from bson import ObjectId

from utils.trading.solana.okx_trade_service import OkxTradeService, OkxApiException, QuoteResult
from utils.trading.solana.trade_interface import TradeType, TradeStatus, TradeResult


class TestOkxTradeService:
    """OKX 交易服务测试类"""
    
    @pytest.fixture
    def mock_channel_config(self):
        """模拟渠道配置"""
        return {
            "api_host": "https://www.okx.com",
            "api_key_env": "TEST_OKX_API_KEY",
            "api_secret_env": "TEST_OKX_API_SECRET",
            "api_passphrase_env": "TEST_OKX_API_PASSPHRASE",
            "rpc_endpoint": "https://api.mainnet-beta.solana.com"
        }
    
    @pytest.fixture
    def mock_env_vars(self):
        """模拟环境变量"""
        env_vars = {
            "TEST_OKX_API_KEY": "test_api_key",
            "TEST_OKX_API_SECRET": "test_api_secret",
            "TEST_OKX_API_PASSPHRASE": "test_passphrase"
        }
        with patch.dict(os.environ, env_vars):
            yield env_vars
    
    @pytest.fixture
    def mock_strategy_snapshot(self):
        """模拟策略快照"""
        return {
            "strategy_name": "test_strategy",
            "buy_slippage_percentage": 1.0,
            "buy_priority_fee_sol": 0.00005
        }
    
    @pytest.fixture
    def okx_service(self, mock_channel_config, mock_env_vars):
        """创建 OKX 服务实例"""
        with patch('utils.trading.solana.okx_trade_service.httpx.AsyncClient'), \
             patch('utils.trading.solana.okx_trade_service.AsyncClient'):
            service = OkxTradeService(channel_config=mock_channel_config)
            # 手动设置模拟的http_client
            service.http_client = Mock()
            service.http_client.get = AsyncMock()
            service.http_client.aclose = AsyncMock()
            return service
    
    def test_initialization_success(self, mock_channel_config, mock_env_vars):
        """测试成功初始化"""
        with patch('utils.trading.solana.okx_trade_service.httpx.AsyncClient'), \
             patch('utils.trading.solana.okx_trade_service.AsyncClient'):
            service = OkxTradeService(channel_config=mock_channel_config)
            
            assert service.api_key == "test_api_key"
            assert service.api_secret == "test_api_secret"
            assert service.passphrase == "test_passphrase"
            assert service.api_host == "https://www.okx.com"
    
    def test_initialization_missing_env_vars(self, mock_channel_config):
        """测试缺少环境变量时初始化失败"""
        with pytest.raises(ValueError, match="缺少必要的环境变量"):
            OkxTradeService(channel_config=mock_channel_config)
    
    def test_generate_auth_headers(self, okx_service):
        """测试生成认证头"""
        from datetime import datetime, timezone
        import re
        
        headers = okx_service._generate_auth_headers("GET", "/api/v5/test", "")
        
        assert "OK-ACCESS-KEY" in headers
        assert "OK-ACCESS-SIGN" in headers
        assert "OK-ACCESS-TIMESTAMP" in headers
        assert "OK-ACCESS-PASSPHRASE" in headers
        assert headers["OK-ACCESS-KEY"] == "test_api_key"
        
        # 验证时间戳是 ISO 8601 格式
        timestamp = headers["OK-ACCESS-TIMESTAMP"]
        iso_pattern = r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$'
        assert re.match(iso_pattern, timestamp), f"时间戳格式不正确: {timestamp}"
        
        # 验证签名不为空
        assert len(headers["OK-ACCESS-SIGN"]) > 0
        assert headers["OK-ACCESS-PASSPHRASE"] == "test_passphrase"
    
    @pytest.mark.asyncio
    async def test_call_okx_api_success(self, okx_service):
        """测试成功的 API 调用"""
        mock_response = Mock()
        mock_response.json.return_value = {"code": "0", "data": "test_data"}
        mock_response.raise_for_status = Mock()
        
        okx_service.http_client.get = AsyncMock(return_value=mock_response)
        
        result = await okx_service._call_okx_api("GET", "/test")
        
        assert result == {"code": "0", "data": "test_data"}
        okx_service.http_client.get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_call_okx_api_error_response(self, okx_service):
        """测试 API 响应错误"""
        mock_response = Mock()
        mock_response.json.return_value = {"code": "50001", "msg": "API error"}
        mock_response.raise_for_status = Mock()
        
        okx_service.http_client.get = AsyncMock(return_value=mock_response)
        
        with pytest.raises(OkxApiException):
            await okx_service._call_okx_api("GET", "/test")
    
    @pytest.mark.asyncio
    async def test_execute_trade_get_quote_success(self, okx_service, mock_strategy_snapshot):
        """测试获取报价成功"""
        # 修复：匹配实际的OKX API数据结构
        mock_api_response = {
            "code": "0",
            "data": [
                {
                    "tx": {
                        "data": "5uiYAjN7EkP5xv4zrBG5o5Y3F8S6GBdXBLJN8TJHyHjG",  # 使用有效的base58编码
                        "minReceiveAmount": "1000000",
                        "maxSpendAmount": "10000000",
                        "gas": "5000"
                    },
                    "routerResult": {
                        "priceImpactPercentage": "0.5"
                    }
                }
            ]
        }
        
        # 模拟API调用成功
        with patch.object(okx_service, '_call_okx_api', new_callable=AsyncMock, return_value=mock_api_response):
            result = await okx_service.get_quote(
                input_token_address="So11111111111111111111111111111111111111112",
                output_token_address="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                amount_input_token=0.01,
                slippage=1.0,
                wallet_address="test_wallet_address"
            )
            
            assert result.is_success == True
            assert result.api_response == mock_api_response
            assert result.encoded_transaction == "5uiYAjN7EkP5xv4zrBG5o5Y3F8S6GBdXBLJN8TJHyHjG"
            assert result.min_receive_amount == "1000000"
            assert result.max_spend_amount == "10000000"
            assert result.estimated_gas == "5000"
            assert result.price_impact == "0.5"
    
    @pytest.mark.asyncio
    async def test_execute_trade_api_failure(self, okx_service, mock_strategy_snapshot):
        """测试 API 调用失败的交易执行"""
        with patch.object(okx_service, 'get_quote', new_callable=AsyncMock, side_effect=OkxApiException("API error")), \
             patch('utils.trading.solana.okx_trade_service.Keypair') as mock_keypair:
            
            # 模拟密钥对创建成功，这样错误会在 API 调用阶段抛出
            mock_keypair_instance = Mock()
            mock_keypair.from_base58_string.return_value = mock_keypair_instance
            
            trade_record_id = PydanticObjectId(ObjectId())
            signal_id = PydanticObjectId(ObjectId())
            
            result = await okx_service.execute_trade(
                trade_type=TradeType.BUY,
                input_token_address="So11111111111111111111111111111111111111112",
                output_token_address="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                amount_input_token=0.01,
                wallet_private_key_b58="test_private_key",
                wallet_address="test_wallet_address",
                strategy_snapshot=mock_strategy_snapshot,
                signal_id=signal_id,
                trade_record_id=trade_record_id
            )
            
            assert result.status == TradeStatus.FAILED
            assert "OKX API 错误" in result.error_message
    
    def test_is_slippage_related_error(self, okx_service):
        """测试滑点相关错误判断"""
        # 测试正面情况
        assert okx_service.is_slippage_related_error("slippage too high") == True
        assert okx_service.is_slippage_related_error("price impact exceeded") == True
        assert okx_service.is_slippage_related_error("insufficient output amount") == True
        
        # 测试 API 响应中的错误码 - 修复：使用字符串类型的错误码
        provider_response = {"code": "51001"}
        assert okx_service.is_slippage_related_error("test error", provider_response) == True
        
        # 测试负面情况
        assert okx_service.is_slippage_related_error("invalid token") == False
        assert okx_service.is_slippage_related_error("") == False
        assert okx_service.is_slippage_related_error(None) == False
    
    def test_is_non_retryable_error(self, okx_service):
        """测试不可重试错误判断"""
        # 测试 OKX 特有的不可重试错误
        assert okx_service.is_non_retryable_error("api key invalid") == True
        assert okx_service.is_non_retryable_error("signature invalid") == True
        assert okx_service.is_non_retryable_error("token not supported") == True
        
        # 测试 API 响应中的错误码 - 修复：使用字符串类型的错误码
        provider_response = {"code": "50001"}
        assert okx_service.is_non_retryable_error("test error", provider_response) == True
        
        # 测试通用不可重试错误（继承自父类）
        assert okx_service.is_non_retryable_error("insufficient funds") == True
        
        # 测试可重试错误
        assert okx_service.is_non_retryable_error("network timeout") == False
        assert okx_service.is_non_retryable_error("") == False
        assert okx_service.is_non_retryable_error(None) == False
        
    def test_slippage_error_priority_over_non_retryable(self, okx_service):
        """测试滑点错误优先级：即使包含不可重试关键词，滑点错误也应该可重试"""
        # 🔥 重要测试用例：滑点错误优先级
        # 这些错误消息同时包含滑点关键词和不可重试关键词
        mixed_errors = [
            "slippage too high, insufficient funds for gas",  # 滑点 + 资金不足
            "price impact exceeded, unauthorized access",      # 滑点 + 未授权
            "insufficient output amount, token not found",     # 滑点 + 代币未找到
            "minimum received not met, signature invalid"      # 滑点 + 签名无效
        ]
        
        for error_msg in mixed_errors:
            # 应该被识别为滑点错误
            assert okx_service.is_slippage_related_error(error_msg) == True, f"Should be slippage error: {error_msg}"
            # 不应该被识别为不可重试错误（滑点错误优先级更高）
            assert okx_service.is_non_retryable_error(error_msg) == False, f"Should be retryable due to slippage priority: {error_msg}"
    
    @pytest.mark.asyncio
    async def test_close(self, okx_service):
        """测试服务关闭"""
        # http_client已经在fixture中模拟设置了
        okx_service.solana_client.close = AsyncMock()
        
        await okx_service.close()
        
        okx_service.http_client.aclose.assert_called_once()
        okx_service.solana_client.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_wait_for_confirmation_success(self, okx_service):
        """测试交易确认成功"""
        # 使用真实的有效base58编码签名字符串
        test_signature_str = "5uiYAjN7EkP5xv4zrBG5o5Y3F8S6GBdXBLJN8TJHyHjGKLMnoPQrStUvWxYZ"
        
        # 模拟确认成功的响应
        mock_confirm_response = Mock()
        mock_confirm_response.value = [Mock()]
        mock_confirm_response.value[0].confirmation_status = "confirmed"
        mock_confirm_response.value[0].err = None
        
        # 直接patch整个确认方法
        async def mock_confirm_with_retry(*args, **kwargs):
            return mock_confirm_response
            
        # 替换整个方法实现
        okx_service._confirm_transaction_with_retry = mock_confirm_with_retry
        
        # 执行测试
        result = await okx_service._confirm_transaction_with_retry(test_signature_str)
        
        # 验证结果
        assert result is not None
        assert result == mock_confirm_response
    
    @pytest.mark.asyncio
    async def test_wait_for_confirmation_timeout(self, okx_service):
        """测试交易确认超时"""
        # 使用真实的有效base58编码签名字符串
        test_signature_str = "5uiYAjN7EkP5xv4zrBG5o5Y3F8S6GBdXBLJN8TJHyHjGKLMnoPQrStUvWxYZ"
        
        # 直接patch整个确认方法以模拟超时
        async def mock_timeout_with_retry(*args, **kwargs):
            raise asyncio.TimeoutError("Timeout")
            
        # 替换整个方法实现
        okx_service._confirm_transaction_with_retry = mock_timeout_with_retry
        
        # 应该抛出超时异常
        with pytest.raises(asyncio.TimeoutError):
            await okx_service._confirm_transaction_with_retry(test_signature_str, timeout=1, max_retries=1)
    



if __name__ == "__main__":
    pytest.main([__file__]) 