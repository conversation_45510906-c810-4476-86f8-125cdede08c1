# GMGN v2代币小数位Bug修复单元测试

**创建日期**: 2025-06-17  
**更新日期**: 2025-06-17  
**测试方法**: 自动化测试  
**测试级别**: 单元测试  

## 测试概述

本测试套件验证GMGN v2交易服务中代币小数位Bug的修复效果。主要测试动态获取代币精度信息的功能，确保：

1. SPL代币不再使用错误的9位小数默认值
2. 通过TokenInfo类正确获取代币精度信息
3. 缓存机制正常工作
4. 优雅降级到6位小数默认值

## 相关Bug信息

- **Bug ID**: GmgnV2_TokenDecimalsCalculationError
- **修复文档**: `docs/features/0.1.0/自动交易/fixes/BUGFIX_PLAN_GmgnV2_SlippageParameterMismatch_20250617.md`
- **原始问题**: SPL代币使用错误的9位小数计算导致"token balance is less than available balance"错误

## 测试用例

| 用例方法 | 用例概述 | 前置条件 | 输入 | 预期输出 | 实际输出 | 状态 |
| --- | --- | --- | --- | --- | --- | --- |
| test_get_token_decimals_sol_token | SOL代币固定使用9位小数 | 创建服务实例 | SOL地址 | 返回9 | 返回9 | ✅ 通过 |
| test_get_token_decimals_spl_token_success | SPL代币成功获取小数位 | Mock TokenInfo返回6位小数 | SPL代币地址 | 返回6，正确调用TokenInfo | 返回6，TokenInfo被正确调用 | ✅ 通过 |
| test_get_token_decimals_cache_mechanism | 验证缓存机制 | Mock TokenInfo | 相同代币地址调用两次 | 第二次调用使用缓存，TokenInfo只调用一次 | 缓存机制正常工作 | ✅ 通过 |
| test_get_token_decimals_fallback_to_default | 代币信息获取失败的优雅降级 | Mock TokenInfo抛出异常 | SPL代币地址 | 返回默认值6 | 返回默认值6 | ✅ 通过 |
| test_get_token_decimals_no_decimals_in_response | 代币信息缺少decimals字段 | Mock TokenInfo返回无decimals信息 | SPL代币地址 | 返回默认值6 | 返回默认值6 | ✅ 通过 |
| test_prepare_route_params_async_spl_token | 异步路由参数准备-SPL代币 | Mock _get_token_decimals返回6 | SPL代币，1241.51066数量 | in_amount="1241510660" | in_amount="1241510660" | ✅ 通过 |
| test_prepare_route_params_async_sol_token | 异步路由参数准备-SOL代币 | Mock _get_token_decimals返回9 | SOL代币，0.1数量 | in_amount="100000000" | in_amount="100000000" | ✅ 通过 |
| test_decimal_calculation_bug_fix | 核心Bug修复验证 | Mock TokenInfo返回6位小数 | 原始Bug数据1241.51066 | 正确计算"1241510660"，不是错误的"1241510660000" | 正确计算"1241510660"，避免了错误结果 | ✅ 通过 |
| test_integration_execute_trade_with_decimal_fix | 集成测试验证修复效果 | Mock所有外部依赖 | 完整交易参数 | 调用_get_token_decimals，交易成功 | _get_token_decimals被调用，交易成功 | ✅ 通过 |

## 测试重点

### 1. 代币精度获取逻辑
- 验证SOL代币固定返回9位小数
- 验证SPL代币通过TokenInfo动态获取
- 验证多数据源获取顺序：Database -> GMGN -> Solscan

### 2. 缓存机制
- 验证相同代币的重复调用使用缓存
- 验证缓存正确存储和检索

### 3. 错误处理
- 验证TokenInfo获取失败时的优雅降级
- 验证缺少decimals字段时的处理

### 4. Bug修复验证
- 验证原始Bug场景不再出现
- 验证计算结果的正确性
- 对比修复前后的差异

## 运行方式

```bash
# 运行所有GMGN v2修复相关测试
pytest test/utils/trading/solana/test_gmgn_trade_service_v2_fix.py -v

# 运行特定测试
pytest test/utils/trading/solana/test_gmgn_trade_service_v2_fix.py::TestGmgnTradeServiceV2DecimalsFix::test_decimal_calculation_bug_fix -v

# 运行集成测试
pytest test/utils/trading/solana/test_gmgn_trade_service_v2_fix.py::test_integration_execute_trade_with_decimal_fix -v
```

## 测试依赖

- pytest
- pytest-asyncio
- unittest.mock
- beanie (PydanticObjectId)

## 预期测试结果

所有测试用例应该通过，验证：
1. 代币精度动态获取功能正常
2. 缓存机制有效
3. 错误处理健壮
4. 原始Bug已修复 