"""
测试KOL交易时间解析器功能

本测试文件遵循TDD原则，实现TC-101到TC-108测试用例：
- TC-101: 正常解析KOL最后交易时间  
- TC-102: 交易状态不为success
- TC-103: 交易记录中没有signal_id
- TC-104: signal_id在数据库中不存在
- TC-105: 信号中没有KOL钱包地址
- TC-106: 验证时间戳转换准确性
- TC-107: 信号中缺少回溯小时配置
- TC-108: 在时间范围内未找到KOL活动

测试设计对应需求文档中的FR003和FR004。
"""

import pytest
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime, timedelta, timezone

from utils.kol_trade_time_resolver import KOLTradeTimeResolver, ErrorCodes
from models.trade_record import TradeRecord
from models.signal import Signal

class TestKOLTradeTimeResolver:
    """KOL交易时间解析器测试类"""

    @pytest.fixture(autouse=True)
    def setup_method(self):
        """为每个测试方法设置模拟对象"""
        self.resolver = KOLTradeTimeResolver()
        
        # 创建 AsyncMock 实例
        self.mock_signal_dao = AsyncMock()
        self.mock_kol_activity_dao = AsyncMock()
        
        # 将模拟对象注入到解析器实例中
        self.resolver.signal_dao = self.mock_signal_dao
        self.resolver.kol_activity_dao = self.mock_kol_activity_dao

    def get_sample_data(self):
        """提供统一的测试数据样本"""
        signal_time = datetime(2025, 6, 18, 12, 20, 0, tzinfo=timezone.utc)
        
        trade_record = MagicMock(spec=TradeRecord)
        trade_record.id = "trade123"
        trade_record.signal_id = "signal456"
        trade_record.status = "success"
        trade_record.created_at = signal_time + timedelta(minutes=5)

        sample_signal = MagicMock(spec=Signal)
        sample_signal.id = "signal456"
        sample_signal.token_address = "token_address_123"
        sample_signal.hit_kol_wallets = ["wallet1", "wallet2"]
        sample_signal.trigger_timestamp = signal_time
        sample_signal.trigger_conditions = {'transaction_lookback_hours': 2}

        return trade_record, sample_signal

    @pytest.mark.asyncio
    async def test_tc101_resolve_kol_last_trade_time_success(self):
        """TC-101: 正常解析KOL最后交易时间"""
        # Arrange
        trade_record, sample_signal = self.get_sample_data()
        
        gmt8_time = datetime(2025, 6, 18, 18, 20, 0)
        kol_gmt8_timestamp = int(gmt8_time.timestamp())
        
        expected_utc_time = datetime(2025, 6, 18, 10, 20, 0, tzinfo=timezone.utc)

        self.mock_signal_dao.get_signal.return_value = sample_signal
        self.mock_kol_activity_dao.find_activities_in_time_range.return_value = [{
            'timestamp': kol_gmt8_timestamp,
            'token_address': sample_signal.token_address,
            'id': 'activity123'
        }]

        # Act
        kol_time, hit_wallets, error_msg = await self.resolver.resolve_kol_last_trade_time(trade_record)
        
        # Assert
        assert kol_time is not None
        assert abs(kol_time - expected_utc_time) < timedelta(seconds=1)
        assert hit_wallets == ["wallet1", "wallet2"]
        assert error_msg is None
        self.mock_signal_dao.get_signal.assert_called_once_with(trade_record.signal_id)
        self.mock_kol_activity_dao.find_activities_in_time_range.assert_called_once()

    @pytest.mark.asyncio
    async def test_tc102_trade_not_successful(self):
        """TC-102: 交易状态不为success"""
        # Arrange
        trade_record, _ = self.get_sample_data()
        trade_record.status = "failed"
        
        # Act
        kol_time, hit_wallets, error_msg = await self.resolver.resolve_kol_last_trade_time(trade_record)
        
        # Assert
        assert kol_time is None
        assert hit_wallets == []
        assert error_msg == ErrorCodes.TRADE_NOT_SUCCESSFUL
        self.mock_signal_dao.get_signal.assert_not_called()

    @pytest.mark.asyncio
    async def test_tc103_no_signal_id(self):
        """TC-103: 交易记录中没有signal_id"""
        # Arrange
        trade_record, _ = self.get_sample_data()
        trade_record.signal_id = None
        
        # Act
        kol_time, hit_wallets, error_msg = await self.resolver.resolve_kol_last_trade_time(trade_record)
        
        # Assert
        assert kol_time is None
        assert hit_wallets == []
        assert error_msg == ErrorCodes.NO_SIGNAL_ID
        self.mock_signal_dao.get_signal.assert_not_called()

    @pytest.mark.asyncio
    async def test_tc104_signal_not_found(self):
        """TC-104: signal_id在数据库中不存在"""
        # Arrange
        trade_record, _ = self.get_sample_data()
        self.mock_signal_dao.get_signal.return_value = None
        
        # Act
        kol_time, hit_wallets, error_msg = await self.resolver.resolve_kol_last_trade_time(trade_record)
        
        # Assert
        assert kol_time is None
        assert hit_wallets == []
        assert error_msg == ErrorCodes.SIGNAL_NOT_FOUND
        self.mock_signal_dao.get_signal.assert_called_once_with(trade_record.signal_id)

    @pytest.mark.asyncio
    async def test_tc105_empty_kol_wallets(self):
        """TC-105: 信号中没有KOL钱包地址"""
        # Arrange
        trade_record, sample_signal = self.get_sample_data()
        sample_signal.hit_kol_wallets = []
        self.mock_signal_dao.get_signal.return_value = sample_signal
        
        # Act
        kol_time, hit_wallets, error_msg = await self.resolver.resolve_kol_last_trade_time(trade_record)
        
        # Assert
        assert kol_time is None
        assert hit_wallets == []
        assert error_msg == ErrorCodes.NO_KOL_WALLETS
        self.mock_kol_activity_dao.find_activities_in_time_range.assert_not_called()

    @pytest.mark.asyncio
    async def test_tc107_missing_lookback_hours(self):
        """TC-107: 信号中缺少回溯小时配置"""
        # Arrange
        trade_record, sample_signal = self.get_sample_data()
        sample_signal.trigger_conditions = {}
        self.mock_signal_dao.get_signal.return_value = sample_signal
        
        # Act
        kol_time, hit_wallets, error_msg = await self.resolver.resolve_kol_last_trade_time(trade_record)
        
        # Assert
        assert kol_time is None
        assert hit_wallets == ["wallet1", "wallet2"]
        assert error_msg == ErrorCodes.NO_LOOKBACK_HOURS
        self.mock_kol_activity_dao.find_activities_in_time_range.assert_not_called()

    @pytest.mark.asyncio
    async def test_tc108_no_kol_activities(self):
        """TC-108: 在时间范围内未找到KOL活动"""
        # Arrange
        trade_record, sample_signal = self.get_sample_data()
        self.mock_signal_dao.get_signal.return_value = sample_signal
        self.mock_kol_activity_dao.find_activities_in_time_range.return_value = []
        
        # Act
        kol_time, hit_wallets, error_msg = await self.resolver.resolve_kol_last_trade_time(trade_record)
        
        # Assert
        assert kol_time is None
        assert hit_wallets == ["wallet1", "wallet2"]
        assert error_msg == ErrorCodes.NO_KOL_ACTIVITIES_FOUND
        self.mock_kol_activity_dao.find_activities_in_time_range.assert_called_once()

    @pytest.mark.asyncio
    async def test_tc106_timestamp_conversion_accuracy(self):
        """TC-106: 验证时间戳转换准确性 (东八区12:30 -> UTC 04:30)"""
        # Arrange
        trade_record, sample_signal = self.get_sample_data()
        gmt8_time = datetime(2025, 6, 18, 12, 30, 0)
        unix_timestamp = int(gmt8_time.timestamp())
        # 东八区比UTC早8小时, 所以UTC时间是 04:30
        expected_utc_datetime = datetime(2025, 6, 18, 4, 30, 0, tzinfo=timezone.utc)

        self.mock_signal_dao.get_signal.return_value = sample_signal
        self.mock_kol_activity_dao.find_activities_in_time_range.return_value = [{'timestamp': unix_timestamp, 'id': 'activity123'}]
        
        # Act
        kol_time, _, error_msg = await self.resolver.resolve_kol_last_trade_time(trade_record)
        
        # Assert
        assert kol_time is not None
        assert error_msg is None
        assert abs(kol_time - expected_utc_datetime) < timedelta(seconds=1)
        assert kol_time.tzinfo == timezone.utc

    @pytest.mark.asyncio
    async def test_time_range_calculation(self):
        """测试时间范围计算的准确性"""
        # Arrange
        trade_record, sample_signal = self.get_sample_data()
        self.mock_signal_dao.get_signal.return_value = sample_signal
        self.mock_kol_activity_dao.find_activities_in_time_range.return_value = [] # 确保不关心返回值
        
        # Act
        await self.resolver.resolve_kol_last_trade_time(trade_record)
        
        # Assert
        self.mock_kol_activity_dao.find_activities_in_time_range.assert_called_once()
        call_args, call_kwargs = self.mock_kol_activity_dao.find_activities_in_time_range.call_args
        
        # 验证传递给DAO的参数
        trigger_utc = sample_signal.trigger_timestamp
        lookback_hours = sample_signal.trigger_conditions['transaction_lookback_hours']
        
        expected_end_ts_for_query = int((trigger_utc + timedelta(hours=8)).timestamp())
        expected_start_ts_for_query = int((trigger_utc - timedelta(hours=lookback_hours) + timedelta(hours=8)).timestamp())

        assert abs(call_kwargs['end_timestamp'] - expected_end_ts_for_query) <= 1
        assert abs(call_kwargs['start_timestamp'] - expected_start_ts_for_query) <= 1
        assert call_kwargs['wallet_addresses'] == sample_signal.hit_kol_wallets
        assert call_kwargs['limit'] == 1
        assert call_kwargs['sort_by_timestamp'] is True
        assert call_kwargs['event_types'] == ['buy', 'sell']

    # ========================================
    # 新增：交易金额过滤功能测试 (TC-AMT-001 ~ TC-AMT-010)
    # ========================================

    @pytest.mark.asyncio
    async def test_tc_amt_001_basic_amount_filter_verification(self):
        """TC-AMT-001: 基本金额过滤功能验证"""
        # Arrange
        trade_record, sample_signal = self.get_sample_data()
        sample_signal.trigger_conditions = {
            'transaction_lookback_hours': 2,
            'transaction_min_amount': 1000.0
        }
        
        # KOL活动cost_usd="1500.50" 大于最小金额
        kol_activity = {
            'timestamp': int(datetime(2025, 6, 18, 10, 20, 0).timestamp()),
            'cost_usd': "1500.50",
            'id': 'activity123'
        }
        
        self.mock_signal_dao.get_signal.return_value = sample_signal
        self.mock_kol_activity_dao.find_activities_in_time_range.return_value = [kol_activity]

        # Act
        kol_time, hit_wallets, error_msg = await self.resolver.resolve_kol_last_trade_time(trade_record)
        
        # Assert
        assert kol_time is not None
        assert hit_wallets == ["wallet1", "wallet2"]
        assert error_msg is None
        
        # 验证调用时传递了金额过滤参数
        call_args = self.mock_kol_activity_dao.find_activities_in_time_range.call_args
        assert call_args[1]['min_cost_usd'] == 1000.0

    @pytest.mark.asyncio
    async def test_tc_amt_002_insufficient_amount_filter(self):
        """TC-AMT-002: 金额不足过滤测试"""
        # Arrange
        trade_record, sample_signal = self.get_sample_data()
        sample_signal.trigger_conditions = {
            'transaction_lookback_hours': 2,
            'transaction_min_amount': 1000.0
        }
        
        self.mock_signal_dao.get_signal.return_value = sample_signal
        # 模拟DAO返回空列表（所有活动都被金额过滤掉了）
        self.mock_kol_activity_dao.find_activities_in_time_range.return_value = []

        # Act
        kol_time, hit_wallets, error_msg = await self.resolver.resolve_kol_last_trade_time(trade_record)
        
        # Assert
        assert kol_time is None
        assert hit_wallets == ["wallet1", "wallet2"]
        assert error_msg == "no_qualified_kol_activities_found"

    @pytest.mark.asyncio
    async def test_tc_amt_003_amount_boundary_equal(self):
        """TC-AMT-003: 金额边界值测试（等于）"""
        # Arrange
        trade_record, sample_signal = self.get_sample_data()
        sample_signal.trigger_conditions = {
            'transaction_lookback_hours': 2,
            'transaction_min_amount': 1000.0
        }
        
        # KOL活动cost_usd="1000.00" 恰好等于最小金额
        kol_activity = {
            'timestamp': int(datetime(2025, 6, 18, 10, 20, 0).timestamp()),
            'cost_usd': "1000.00",
            'id': 'activity123'
        }
        
        self.mock_signal_dao.get_signal.return_value = sample_signal
        self.mock_kol_activity_dao.find_activities_in_time_range.return_value = [kol_activity]

        # Act
        kol_time, hit_wallets, error_msg = await self.resolver.resolve_kol_last_trade_time(trade_record)
        
        # Assert
        assert kol_time is not None  # 等于阈值应该通过
        assert error_msg is None

    @pytest.mark.asyncio
    async def test_tc_amt_004_amount_boundary_slightly_above(self):
        """TC-AMT-004: 金额边界值测试（略大于）"""
        # Arrange
        trade_record, sample_signal = self.get_sample_data()
        sample_signal.trigger_conditions = {
            'transaction_lookback_hours': 2,
            'transaction_min_amount': 1000.0
        }
        
        # KOL活动cost_usd="1000.01" 略大于最小金额
        kol_activity = {
            'timestamp': int(datetime(2025, 6, 18, 10, 20, 0).timestamp()),
            'cost_usd': "1000.01",
            'id': 'activity123'
        }
        
        self.mock_signal_dao.get_signal.return_value = sample_signal
        self.mock_kol_activity_dao.find_activities_in_time_range.return_value = [kol_activity]

        # Act
        kol_time, hit_wallets, error_msg = await self.resolver.resolve_kol_last_trade_time(trade_record)
        
        # Assert
        assert kol_time is not None  # 大于阈值应该通过
        assert error_msg is None

    @pytest.mark.asyncio
    async def test_tc_amt_005_amount_boundary_slightly_below(self):
        """TC-AMT-005: 金额边界值测试（略小于）"""
        # Arrange
        trade_record, sample_signal = self.get_sample_data()
        sample_signal.trigger_conditions = {
            'transaction_lookback_hours': 2,
            'transaction_min_amount': 1000.0
        }
        
        self.mock_signal_dao.get_signal.return_value = sample_signal
        # 模拟DAO返回空列表（小于阈值的活动被过滤掉了）
        self.mock_kol_activity_dao.find_activities_in_time_range.return_value = []

        # Act
        kol_time, hit_wallets, error_msg = await self.resolver.resolve_kol_last_trade_time(trade_record)
        
        # Assert
        assert kol_time is None  # 小于阈值应该被过滤
        assert error_msg == "no_qualified_kol_activities_found"

    @pytest.mark.asyncio
    async def test_tc_amt_006_missing_min_amount_config(self):
        """TC-AMT-006: 缺少transaction_min_amount配置（向后兼容测试）"""
        # Arrange
        trade_record, sample_signal = self.get_sample_data()
        sample_signal.trigger_conditions = {
            'transaction_lookback_hours': 2
            # 缺少 transaction_min_amount - 应该向后兼容，不进行金额过滤
        }
        
        # 模拟KOL活动数据
        kol_activity = {
            'timestamp': int(datetime(2025, 6, 18, 10, 20, 0).timestamp()),
            'cost_usd': "500.00",  # 任意金额都应该被接受
            'id': 'activity123'
        }
        
        self.mock_signal_dao.get_signal.return_value = sample_signal
        self.mock_kol_activity_dao.find_activities_in_time_range.return_value = [kol_activity]

        # Act
        kol_time, hit_wallets, error_msg = await self.resolver.resolve_kol_last_trade_time(trade_record)
        
        # Assert - 向后兼容：应该正常工作，不进行金额过滤
        assert kol_time is not None  # 应该成功返回时间
        assert hit_wallets == ["wallet1", "wallet2"]
        assert error_msg is None  # 不应该有错误
        
        # 验证调用时没有传递金额过滤参数（向后兼容）
        call_args = self.mock_kol_activity_dao.find_activities_in_time_range.call_args
        assert call_args[1]['min_cost_usd'] is None

    @pytest.mark.asyncio
    async def test_tc_amt_007_explicit_none_min_amount_config(self):
        """TC-AMT-007: 明确设置transaction_min_amount为None（向后兼容测试）"""
        # Arrange
        trade_record, sample_signal = self.get_sample_data()
        sample_signal.trigger_conditions = {
            'transaction_lookback_hours': 2,
            'transaction_min_amount': None  # 明确设置为None
        }
        
        # 模拟KOL活动数据
        kol_activity = {
            'timestamp': int(datetime(2025, 6, 18, 10, 20, 0).timestamp()),
            'cost_usd': "100.00",  # 任意金额都应该被接受
            'id': 'activity123'
        }
        
        self.mock_signal_dao.get_signal.return_value = sample_signal
        self.mock_kol_activity_dao.find_activities_in_time_range.return_value = [kol_activity]

        # Act
        kol_time, hit_wallets, error_msg = await self.resolver.resolve_kol_last_trade_time(trade_record)
        
        # Assert - 向后兼容：应该正常工作，不进行金额过滤
        assert kol_time is not None
        assert hit_wallets == ["wallet1", "wallet2"]
        assert error_msg is None
        
        # 验证调用时没有传递金额过滤参数
        call_args = self.mock_kol_activity_dao.find_activities_in_time_range.call_args
        assert call_args[1]['min_cost_usd'] is None

    @pytest.mark.asyncio
    async def test_tc_amt_009_mixed_amount_filtering(self):
        """TC-AMT-009: 多个KOL活动金额混合过滤"""
        # Arrange
        trade_record, sample_signal = self.get_sample_data()
        sample_signal.trigger_conditions = {
            'transaction_lookback_hours': 2,
            'transaction_min_amount': 1000.0
        }
        
        # 模拟DAO只返回符合金额条件且时间最新的活动
        qualified_activity = {
            'timestamp': int(datetime(2025, 6, 18, 10, 25, 0).timestamp()),  # 最新时间
            'cost_usd': "1500.50",
            'id': 'activity_qualified'
        }
        
        self.mock_signal_dao.get_signal.return_value = sample_signal
        self.mock_kol_activity_dao.find_activities_in_time_range.return_value = [qualified_activity]

        # Act
        kol_time, hit_wallets, error_msg = await self.resolver.resolve_kol_last_trade_time(trade_record)
        
        # Assert
        assert kol_time is not None
        assert error_msg is None
        # 验证返回的是最新的符合条件的活动时间
        expected_time = datetime.fromtimestamp(qualified_activity['timestamp'], tz=timezone.utc)
        assert abs(kol_time - expected_time) < timedelta(seconds=1) 