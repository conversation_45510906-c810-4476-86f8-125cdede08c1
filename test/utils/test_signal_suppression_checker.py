"""
SignalSuppressionChecker单元测试

创建日期：2025-06-19
更新日期：2025-06-19
测试方法：自动化测试
测试级别：单元测试

测试覆盖：TC-SUP-001 到 TC-SUP-009
对应需求：FR009 信号抑制检查功能
"""

import pytest
from datetime import datetime, timezone, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
from bson import ObjectId

from utils.signal_suppression_checker import SignalSuppressionChecker, SuppressionCheckResult
from models.trade_record import TradeRecord
from models.signal import Signal
from models.trading_delay_record import TradingDelayRecord, DelayStatus


class TestSignalSuppressionChecker:
    """SignalSuppressionChecker测试类"""
    
    @pytest.fixture
    def mock_delay_dao(self):
        """Mock TradingDelayRecordDAO"""
        return AsyncMock()
    
    @pytest.fixture
    def mock_signal_dao(self):
        """Mock SignalDAO"""
        return AsyncMock()
    
    @pytest.fixture
    def mock_config_dao(self):
        """Mock ConfigDAO"""
        return AsyncMock()
    
    @pytest.fixture
    def mock_kol_resolver(self):
        """Mock KOLTradeTimeResolver"""
        return AsyncMock()
    
    @pytest.fixture
    def checker(self, mock_delay_dao, mock_signal_dao, mock_config_dao, mock_kol_resolver):
        """创建SignalSuppressionChecker实例"""
        return SignalSuppressionChecker(
            delay_dao=mock_delay_dao,
            signal_dao=mock_signal_dao,
            config_dao=mock_config_dao,
            kol_resolver=mock_kol_resolver
        )
    
    @pytest.fixture
    def sample_trade_record(self):
        """示例交易记录"""
        mock_record = MagicMock()
        mock_record.id = ObjectId()
        mock_record.signal_id = ObjectId()
        mock_record.strategy_name = "test_strategy"
        mock_record.trade_provider = "gmgn"
        mock_record.trade_type = "buy"
        mock_record.status = "success"
        mock_record.token_in_address = "So11111111111111111111111111111111111111112"  # SOL
        mock_record.token_out_address = "0x123456789"  # 目标代币
        mock_record.wallet_address = "wallet123"
        mock_record.created_at = datetime(2025, 6, 19, 10, 30, 0, tzinfo=timezone.utc)
        return mock_record
    
    @pytest.fixture
    def sample_signal(self):
        """示例信号"""
        mock_signal = MagicMock()
        mock_signal.id = ObjectId()
        mock_signal.strategy_name = "test_strategy"
        mock_signal.token_address = "0x123456789"
        mock_signal.trigger_timestamp = datetime(2025, 6, 19, 10, 25, 0, tzinfo=timezone.utc)
        mock_signal.hit_kol_wallets = ["wallet1", "wallet2"]
        mock_signal.trigger_conditions = {
            "transaction_lookback_hours": 2
        }
        return mock_signal
    
    @pytest.fixture
    def sample_strategy_config(self):
        """示例策略配置"""
        return {
            "strategy_name": "test_strategy",
            "same_token_notification_interval": 60  # 60分钟
        }
    
    @pytest.fixture
    def sample_historical_record(self):
        """示例历史延迟记录"""
        mock_record = MagicMock()
        mock_record.id = ObjectId()
        mock_record.trade_record_id = ObjectId()
        mock_record.signal_id = ObjectId()
        mock_record.strategy_name = "test_strategy"
        mock_record.token_address = "0x123456789"
        mock_record.delay_status = DelayStatus.CALCULATED
        mock_record.kol_last_trade_timestamp = datetime(2025, 6, 19, 9, 20, 0, tzinfo=timezone.utc)
        mock_record.created_at = datetime(2025, 6, 19, 9, 25, 0, tzinfo=timezone.utc)
        return mock_record

    @pytest.mark.asyncio
    async def test_tc_sup_001_first_strategy_token_combination(
        self, checker, mock_delay_dao, mock_signal_dao, mock_config_dao, mock_kol_resolver,
        sample_trade_record, sample_signal, sample_strategy_config
    ):
        """
        TC-SUP-001: 首次策略-代币组合检查
        
        验证数据库中无历史延迟记录时，允许延迟计算
        """
        # Arrange
        mock_signal_dao.get_signal.return_value = sample_signal
        mock_config_dao.get_by_type.return_value = MagicMock(
            data=MagicMock(
                buy_strategies=[MagicMock(**sample_strategy_config)]
            )
        )
        mock_kol_resolver.resolve_kol_last_trade_time.return_value = (
            datetime(2025, 6, 19, 10, 20, 0, tzinfo=timezone.utc),
            ["wallet1", "wallet2"],
            None
        )
        mock_delay_dao.find_recent_records_by_strategy_token.return_value = []  # 无历史记录
        
        # Act
        result = await checker.check_signal_suppression(sample_trade_record)
        
        # Assert
        assert isinstance(result, SuppressionCheckResult)
        assert result.is_suppressed is False
        assert result.status == 'no_history'
        assert result.reason == 'first_signal_for_strategy_token_combination'
        assert result.time_interval_minutes is None
        assert result.historical_kol_time is None
        
        # 验证DAO调用
        mock_delay_dao.find_recent_records_by_strategy_token.assert_called_once_with(
            strategy_name="test_strategy",
            token_address="0x123456789",
            before_timestamp=sample_signal.trigger_timestamp,
            limit=1
        )

    @pytest.mark.asyncio
    async def test_tc_sup_002_time_interval_greater_than_threshold(
        self, checker, mock_delay_dao, mock_signal_dao, mock_config_dao, mock_kol_resolver,
        sample_trade_record, sample_signal, sample_strategy_config, sample_historical_record
    ):
        """
        TC-SUP-002: 时间间隔大于抑制阈值
        
        验证KOL交易时间间隔65分钟，same_token_notification_interval=60分钟时，不被抑制
        """
        # Arrange
        # 设置当前KOL交易时间比历史记录晚65分钟
        current_kol_time = datetime(2025, 6, 19, 10, 25, 0, tzinfo=timezone.utc)  # 当前
        historical_kol_time = datetime(2025, 6, 19, 9, 20, 0, tzinfo=timezone.utc)  # 历史：差65分钟
        
        sample_historical_record.kol_last_trade_timestamp = historical_kol_time
        
        mock_signal_dao.get_signal.return_value = sample_signal
        mock_config_dao.get_by_type.return_value = MagicMock(
            data=MagicMock(
                buy_strategies=[MagicMock(**sample_strategy_config)]
            )
        )
        mock_kol_resolver.resolve_kol_last_trade_time.return_value = (
            current_kol_time,
            ["wallet1", "wallet2"],
            None
        )
        mock_delay_dao.find_recent_records_by_strategy_token.return_value = [sample_historical_record]
        
        # Act
        result = await checker.check_signal_suppression(sample_trade_record)
        
        # Assert
        assert result.is_suppressed is False
        assert result.status == 'passed'
        assert result.time_interval_minutes == 65.0
        assert "time_interval_65.00min_vs_threshold_60min" in result.reason
        assert result.historical_kol_time == historical_kol_time

    @pytest.mark.asyncio
    async def test_tc_sup_003_time_interval_equals_threshold(
        self, checker, mock_delay_dao, mock_signal_dao, mock_config_dao, mock_kol_resolver,
        sample_trade_record, sample_signal, sample_strategy_config, sample_historical_record
    ):
        """
        TC-SUP-003: 时间间隔等于抑制阈值
        
        验证时间间隔恰好等于same_token_notification_interval时被抑制
        """
        # Arrange
        # 设置当前KOL交易时间比历史记录晚恰好60分钟
        current_kol_time = datetime(2025, 6, 19, 10, 20, 0, tzinfo=timezone.utc)
        historical_kol_time = datetime(2025, 6, 19, 9, 20, 0, tzinfo=timezone.utc)  # 差60分钟
        
        sample_historical_record.kol_last_trade_timestamp = historical_kol_time
        
        mock_signal_dao.get_signal.return_value = sample_signal
        mock_config_dao.get_by_type.return_value = MagicMock(
            data=MagicMock(
                buy_strategies=[MagicMock(**sample_strategy_config)]
            )
        )
        mock_kol_resolver.resolve_kol_last_trade_time.return_value = (
            current_kol_time,
            ["wallet1", "wallet2"],
            None
        )
        mock_delay_dao.find_recent_records_by_strategy_token.return_value = [sample_historical_record]
        
        # Act
        result = await checker.check_signal_suppression(sample_trade_record)
        
        # Assert
        assert result.is_suppressed is True
        assert result.status == 'suppressed'
        assert result.time_interval_minutes == 60.0
        assert "time_interval_60.00min_vs_threshold_60min" in result.reason

    @pytest.mark.asyncio
    async def test_tc_sup_004_time_interval_less_than_threshold(
        self, checker, mock_delay_dao, mock_signal_dao, mock_config_dao, mock_kol_resolver,
        sample_trade_record, sample_signal, sample_strategy_config, sample_historical_record
    ):
        """
        TC-SUP-004: 时间间隔小于抑制阈值
        
        验证时间间隔小于same_token_notification_interval时被抑制
        """
        # Arrange
        # 设置当前KOL交易时间比历史记录晚30分钟（小于60分钟阈值）
        current_kol_time = datetime(2025, 6, 19, 9, 50, 0, tzinfo=timezone.utc)
        historical_kol_time = datetime(2025, 6, 19, 9, 20, 0, tzinfo=timezone.utc)  # 差30分钟
        
        sample_historical_record.kol_last_trade_timestamp = historical_kol_time
        
        mock_signal_dao.get_signal.return_value = sample_signal
        mock_config_dao.get_by_type.return_value = MagicMock(
            data=MagicMock(
                buy_strategies=[MagicMock(**sample_strategy_config)]
            )
        )
        mock_kol_resolver.resolve_kol_last_trade_time.return_value = (
            current_kol_time,
            ["wallet1", "wallet2"],
            None
        )
        mock_delay_dao.find_recent_records_by_strategy_token.return_value = [sample_historical_record]
        
        # Act
        result = await checker.check_signal_suppression(sample_trade_record)
        
        # Assert
        assert result.is_suppressed is True
        assert result.status == 'suppressed'
        assert result.time_interval_minutes == 30.0
        assert "time_interval_30.00min_vs_threshold_60min" in result.reason

    @pytest.mark.asyncio
    async def test_tc_sup_005_strategy_config_missing(
        self, checker, mock_delay_dao, mock_signal_dao, mock_config_dao, mock_kol_resolver,
        sample_trade_record, sample_signal
    ):
        """
        TC-SUP-005: 策略配置缺失处理
        
        验证无法获取same_token_notification_interval配置时的处理
        """
        # Arrange
        mock_signal_dao.get_signal.return_value = sample_signal
        mock_config_dao.get_by_type.return_value = None  # 配置缺失
        
        # Act
        result = await checker.check_signal_suppression(sample_trade_record)
        
        # Assert
        assert result.is_suppressed is False
        assert result.status == 'config_missing'
        assert result.reason == 'strategy_config_not_found'

    @pytest.mark.asyncio
    async def test_tc_sup_006_historical_kol_time_missing(
        self, checker, mock_delay_dao, mock_signal_dao, mock_config_dao, mock_kol_resolver,
        sample_trade_record, sample_signal, sample_strategy_config, sample_historical_record
    ):
        """
        TC-SUP-006: 历史记录KOL时间缺失
        
        验证历史延迟记录存在但kol_last_trade_timestamp为空时的处理
        """
        # Arrange
        sample_historical_record.kol_last_trade_timestamp = None  # KOL时间缺失
        
        mock_signal_dao.get_signal.return_value = sample_signal
        mock_config_dao.get_by_type.return_value = MagicMock(
            data=MagicMock(
                buy_strategies=[MagicMock(**sample_strategy_config)]
            )
        )
        mock_kol_resolver.resolve_kol_last_trade_time.return_value = (
            datetime(2025, 6, 19, 10, 20, 0, tzinfo=timezone.utc),
            ["wallet1", "wallet2"],
            None
        )
        mock_delay_dao.find_recent_records_by_strategy_token.return_value = [sample_historical_record]
        
        # Act
        result = await checker.check_signal_suppression(sample_trade_record)
        
        # Assert
        assert result.is_suppressed is False
        assert result.status == 'error'
        assert result.reason == 'historical_kol_time_missing'

    @pytest.mark.asyncio
    async def test_tc_sup_007_current_kol_time_resolution_failed(
        self, checker, mock_delay_dao, mock_signal_dao, mock_config_dao, mock_kol_resolver,
        sample_trade_record, sample_signal, sample_strategy_config
    ):
        """
        TC-SUP-007: 当前信号KOL时间获取失败
        
        验证KOL交易时间解析失败时的处理
        """
        # Arrange
        mock_signal_dao.get_signal.return_value = sample_signal
        mock_config_dao.get_by_type.return_value = MagicMock(
            data=MagicMock(
                buy_strategies=[MagicMock(**sample_strategy_config)]
            )
        )
        mock_kol_resolver.resolve_kol_last_trade_time.return_value = (
            None,  # KOL时间获取失败
            [],
            "no_kol_activities_found"  # 错误信息
        )
        
        # Act
        result = await checker.check_signal_suppression(sample_trade_record)
        
        # Assert
        assert result.is_suppressed is False
        assert result.status == 'error'
        assert "kol_time_resolution_failed: no_kol_activities_found" in result.reason

    @pytest.mark.asyncio
    async def test_tc_sup_008_different_strategy_same_token(
        self, checker, mock_delay_dao, mock_signal_dao, mock_config_dao, mock_kol_resolver,
        sample_trade_record, sample_signal, sample_strategy_config, sample_historical_record
    ):
        """
        TC-SUP-008: 不同策略相同代币
        
        验证相同代币但不同策略的历史记录不影响抑制检查
        """
        # Arrange
        # 修改历史记录为不同策略
        sample_historical_record.strategy_name = "different_strategy"
        
        mock_signal_dao.get_signal.return_value = sample_signal
        mock_config_dao.get_by_type.return_value = MagicMock(
            data=MagicMock(
                buy_strategies=[MagicMock(**sample_strategy_config)]
            )
        )
        mock_kol_resolver.resolve_kol_last_trade_time.return_value = (
            datetime(2025, 6, 19, 10, 20, 0, tzinfo=timezone.utc),
            ["wallet1", "wallet2"],
            None
        )
        # 模拟查询结果：不同策略的记录不会被查询到
        mock_delay_dao.find_recent_records_by_strategy_token.return_value = []
        
        # Act
        result = await checker.check_signal_suppression(sample_trade_record)
        
        # Assert
        assert result.is_suppressed is False
        assert result.status == 'no_history'
        assert result.reason == 'first_signal_for_strategy_token_combination'
        
        # 验证查询条件：只查询相同策略的记录
        mock_delay_dao.find_recent_records_by_strategy_token.assert_called_once_with(
            strategy_name="test_strategy",  # 当前策略
            token_address="0x123456789",
            before_timestamp=sample_signal.trigger_timestamp,
            limit=1
        )

    @pytest.mark.asyncio
    async def test_tc_sup_009_same_strategy_different_token(
        self, checker, mock_delay_dao, mock_signal_dao, mock_config_dao, mock_kol_resolver,
        sample_trade_record, sample_signal, sample_strategy_config, sample_historical_record
    ):
        """
        TC-SUP-009: 相同策略不同代币
        
        验证相同策略但不同代币的历史记录不影响抑制检查
        """
        # Arrange
        # 修改历史记录为不同代币
        sample_historical_record.token_address = "0x987654321"
        
        mock_signal_dao.get_signal.return_value = sample_signal
        mock_config_dao.get_by_type.return_value = MagicMock(
            data=MagicMock(
                buy_strategies=[MagicMock(**sample_strategy_config)]
            )
        )
        mock_kol_resolver.resolve_kol_last_trade_time.return_value = (
            datetime(2025, 6, 19, 10, 20, 0, tzinfo=timezone.utc),
            ["wallet1", "wallet2"],
            None
        )
        # 模拟查询结果：不同代币的记录不会被查询到
        mock_delay_dao.find_recent_records_by_strategy_token.return_value = []
        
        # Act
        result = await checker.check_signal_suppression(sample_trade_record)
        
        # Assert
        assert result.is_suppressed is False
        assert result.status == 'no_history'
        assert result.reason == 'first_signal_for_strategy_token_combination'
        
        # 验证查询条件：只查询相同代币的记录
        mock_delay_dao.find_recent_records_by_strategy_token.assert_called_once_with(
            strategy_name="test_strategy",
            token_address="0x123456789",  # 当前代币
            before_timestamp=sample_signal.trigger_timestamp,
            limit=1
        )

    @pytest.mark.asyncio
    async def test_signal_not_found_error(
        self, checker, mock_delay_dao, mock_signal_dao, mock_config_dao, mock_kol_resolver,
        sample_trade_record
    ):
        """测试信号不存在的错误处理"""
        # Arrange
        mock_signal_dao.get_signal.return_value = None
        
        # Act
        result = await checker.check_signal_suppression(sample_trade_record)
        
        # Assert
        assert result.is_suppressed is False
        assert result.status == 'error'
        assert result.reason == 'signal_not_found'

    @pytest.mark.asyncio
    async def test_same_token_notification_interval_not_configured(
        self, checker, mock_delay_dao, mock_signal_dao, mock_config_dao, mock_kol_resolver,
        sample_trade_record, sample_signal
    ):
        """测试same_token_notification_interval未配置的情况"""
        # Arrange
        mock_signal_dao.get_signal.return_value = sample_signal
        mock_config_dao.get_by_type.return_value = MagicMock(
            data=MagicMock(
                buy_strategies=[MagicMock(
                    strategy_name="test_strategy"
                    # 缺少same_token_notification_interval配置
                )]
            )
        )
        
        # Act
        result = await checker.check_signal_suppression(sample_trade_record)
        
        # Assert
        assert result.is_suppressed is False
        assert result.status == 'config_missing'
        assert result.reason == 'same_token_notification_interval_not_configured'

    @pytest.mark.asyncio
    async def test_unexpected_exception_handling(
        self, checker, mock_delay_dao, mock_signal_dao, mock_config_dao, mock_kol_resolver,
        sample_trade_record
    ):
        """测试意外异常的处理"""
        # Arrange
        mock_signal_dao.get_signal.side_effect = Exception("Database connection failed")
        
        # Act
        result = await checker.check_signal_suppression(sample_trade_record)
        
        # Assert
        assert result.is_suppressed is False
        assert result.status == 'error'
        assert "unexpected_error: Database connection failed" in result.reason 