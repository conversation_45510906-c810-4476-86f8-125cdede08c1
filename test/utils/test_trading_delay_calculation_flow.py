#!/usr/bin/env python3
"""
交易延迟计算流程的完整单元测试

测试整个延迟计算流程，包括：
1. KOL时间戳的8小时时区修正
2. 延迟计算逻辑
3. 延迟记录创建
4. 字段提取和映射
"""

import unittest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timezone, timedelta
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from utils.trading_delay_calculator import TradingDelayCalculator
from utils.kol_trade_time_resolver import KOLTradeTimeResolver
from models.trading_delay_record import DelayStatus


class TestTradingDelayCalculationFlow(unittest.IsolatedAsyncioTestCase):
    """交易延迟计算流程测试"""

    def setUp(self):
        """测试初始化"""
        self.calculator = TradingDelayCalculator()
        self.resolver = KOLTradeTimeResolver()

    def test_kol_timestamp_conversion(self):
        """测试KOL东八区时间戳到UTC的转换"""
        # 模拟一个东八区时间 2025-06-24 10:00:00 GMT+8
        # 其对应的UTC时间是 2025-06-24 02:00:00 UTC
        gmt8_time = datetime(2025, 6, 24, 10, 0, 0) # Naive datetime
        gmt8_timestamp = int(gmt8_time.timestamp())
        
        # 执行转换
        converted_utc_time = self.resolver._convert_timestamp_to_datetime(gmt8_timestamp)
        
        # 验证结果
        expected_utc_time = datetime(2025, 6, 24, 2, 0, 0, tzinfo=timezone.utc)
        
        # 由于timestamp()的精度问题，允许秒级误差
        self.assertAlmostEqual(converted_utc_time, expected_utc_time, delta=timedelta(seconds=1))

    def test_field_extraction_methods(self):
        """测试字段提取方法"""
        # 创建模拟的交易记录
        mock_trade_record = Mock()
        mock_trade_record.token_out_address = "test_token_out"
        mock_trade_record.token_in_address = "test_token_in"
        mock_trade_record.amount_out = 100.0
        mock_trade_record.amount_in = 50.0
        mock_trade_record.trade_type = Mock()
        mock_trade_record.trade_type.value = "buy"
        
        # 测试token地址提取（优先使用token_out_address）
        token_address = self.calculator._extract_token_address(mock_trade_record)
        self.assertEqual(token_address, "test_token_out")
        
        # 测试交易金额提取（优先使用amount_out）
        trade_amount = self.calculator._extract_trade_amount(mock_trade_record)
        self.assertEqual(trade_amount, 100.0)

    def test_timezone_consistency_in_delay_calculation(self):
        """测试延迟计算中的时区一致性处理"""
        # 测试场景：混合时区的datetime对象
        naive_datetime = datetime(2025, 6, 6, 14, 42, 33)  # 无时区信息
        utc_datetime = datetime(2025, 6, 6, 14, 42, 7, tzinfo=timezone.utc)  # UTC时区
        
        # 测试时区统一化方法
        fixed_naive = self.calculator._ensure_utc_timezone(naive_datetime)
        fixed_utc = self.calculator._ensure_utc_timezone(utc_datetime)
        
        # 验证都转换为UTC时区
        self.assertEqual(fixed_naive.tzinfo, timezone.utc)
        self.assertEqual(fixed_utc.tzinfo, timezone.utc)
        self.assertEqual(fixed_utc, utc_datetime)  # UTC时区的应该保持不变

    def test_real_world_scenario_from_database(self):
        """测试来自真实数据库的场景（考虑时区修正）"""
        # KOL活动时间戳 (东八区): 1748047598 -> 2025-05-24 00:46:38 GMT+8
        kol_gmt8_timestamp = 1748047598
        
        # 信号触发时间 (UTC): 2025-05-23 18:36:35.736Z (本地时间戳)
        # 注意：Python的datetime.fromtimestamp()会将本地时间戳转为本地时间
        # 为保证测试一致性，直接使用UTC时间对象
        signal_utc_time = datetime(2025, 5, 23, 2, 36, 35, 736000, tzinfo=timezone.utc)
        
        # 交易执行时间 (UTC)
        trade_execution_time = datetime(2025, 5, 23, 2, 36, 35, 743000, tzinfo=timezone.utc)
        
        # 1. 转换KOL时间戳为UTC
        kol_utc_time = self.resolver._convert_timestamp_to_datetime(kol_gmt8_timestamp)
        
        # 2. 计算信号到交易的延迟
        delay = (trade_execution_time - signal_utc_time).total_seconds()

        # 验证延迟（信号到交易执行），应该是毫秒级
        self.assertAlmostEqual(delay, 0.007, places=3)

    def test_edge_cases_in_delay_calculation(self):
        """测试延迟计算的边界情况"""
        
        # === 测试场景1: 负延迟（用户交易早于KOL活动） ===
        early_trade_time = datetime(2025, 6, 6, 14, 40, 0, tzinfo=timezone.utc)
        kol_time = datetime(2025, 6, 6, 14, 42, 0, tzinfo=timezone.utc)
        
        trade_time_fixed = self.calculator._ensure_utc_timezone(early_trade_time)
        kol_time_fixed = self.calculator._ensure_utc_timezone(kol_time)
        
        negative_delay = (trade_time_fixed - kol_time_fixed).total_seconds()
        self.assertEqual(negative_delay, -120.0)  # -2分钟
        
        # === 测试场景2: 零延迟（同一时间） ===
        same_time = datetime(2025, 6, 6, 14, 42, 0, tzinfo=timezone.utc)
        
        time1_fixed = self.calculator._ensure_utc_timezone(same_time)
        time2_fixed = self.calculator._ensure_utc_timezone(same_time)
        
        zero_delay = (time1_fixed - time2_fixed).total_seconds()
        self.assertEqual(zero_delay, 0.0)
        
        # === 测试场景3: 极大延迟 ===
        late_trade_time = datetime(2025, 6, 7, 14, 42, 0, tzinfo=timezone.utc)  # 第二天
        early_kol_time = datetime(2025, 6, 6, 14, 42, 0, tzinfo=timezone.utc)
        
        late_trade_fixed = self.calculator._ensure_utc_timezone(late_trade_time)
        early_kol_fixed = self.calculator._ensure_utc_timezone(early_kol_time)
        
        large_delay = (late_trade_fixed - early_kol_fixed).total_seconds()
        self.assertEqual(large_delay, 86400.0)  # 24小时


if __name__ == '__main__':
    unittest.main()