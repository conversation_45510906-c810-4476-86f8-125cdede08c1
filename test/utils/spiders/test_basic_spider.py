"""BasicSpider重试机制测试用例

这些测试用例锁定了重构前的BasicSpider重试行为，确保重构后的行为保持一致。
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, Mock, patch
from curl_cffi.requests import Response
from utils.spiders.smart_money import BasicSpider, ProxyType


class ConcreteBasicSpider(BasicSpider):
    """用于测试的BasicSpider子类"""
    
    async def setup(self):
        """测试用的setup方法"""
        pass


@pytest.fixture
def basic_spider():
    """创建BasicSpider测试实例"""
    return ConcreteBasicSpider(max_retries=3, retry_interval=0.1)


@pytest.mark.asyncio
async def test_successful_request(basic_spider):
    """测试成功请求的情况"""
    # Mock成功响应
    mock_response = Mock(spec=Response)
    mock_response.status_code = 200
    
    with patch.object(basic_spider, 'init_sessions') as mock_init, \
         patch.object(basic_spider, 'setup') as mock_setup:
        
        # Mock session
        mock_session = AsyncMock()
        mock_session.request.return_value = mock_response
        basic_spider.session = mock_session
        
        result = await basic_spider.request_with_retry('GET', 'http://test.com')
        
        assert result == mock_response
        assert mock_session.request.call_count == 1
        # 因为session已经存在，所以不会调用init_sessions
        mock_init.assert_not_called()
        mock_setup.assert_called_once()


@pytest.mark.asyncio
async def test_retry_on_failure(basic_spider):
    """测试重试机制"""
    # Mock失败后成功的响应
    mock_response_fail = Mock(spec=Response)
    mock_response_fail.status_code = 500
    
    mock_response_success = Mock(spec=Response)
    mock_response_success.status_code = 200
    
    with patch.object(basic_spider, 'init_sessions') as mock_init, \
         patch.object(basic_spider, 'setup') as mock_setup, \
         patch.object(basic_spider, 'switch_session') as mock_switch:
        
        # Mock session
        mock_session = AsyncMock()
        mock_session.request.side_effect = [mock_response_fail, mock_response_success]
        basic_spider.session = mock_session
        
        # 确保switch_session不会替换我们的mock_session
        async def mock_switch_session(*args, **kwargs):
            # 不替换session，保持使用mock_session
            pass
        mock_switch.side_effect = mock_switch_session
        
        result = await basic_spider.request_with_retry('GET', 'http://test.com')
        
        assert result == mock_response_success
        assert mock_session.request.call_count == 2
        # 因为session已经存在，第一次不会调用init_sessions
        mock_init.assert_not_called()
        assert mock_setup.call_count == 2
        mock_switch.assert_called_once()


@pytest.mark.asyncio
async def test_max_retries_exceeded(basic_spider):
    """测试超过最大重试次数"""
    # Mock持续失败的响应
    mock_response_fail = Mock(spec=Response)
    mock_response_fail.status_code = 500
    
    with patch.object(basic_spider, 'init_sessions'), \
         patch.object(basic_spider, 'setup'), \
         patch.object(basic_spider, 'switch_session') as mock_switch:
        
        # Mock session
        mock_session = AsyncMock()
        mock_session.request.return_value = mock_response_fail
        basic_spider.session = mock_session
        
        # 确保switch_session不会替换我们的mock_session
        async def mock_switch_session(*args, **kwargs):
            pass
        mock_switch.side_effect = mock_switch_session
        
        result = await basic_spider.request_with_retry('GET', 'http://test.com')
        
        # 应该返回最后一次失败的响应
        assert result == mock_response_fail
        assert mock_session.request.call_count == basic_spider.max_retries


@pytest.mark.asyncio
async def test_exception_handling(basic_spider):
    """测试异常处理和重试"""
    mock_response_success = Mock(spec=Response)
    mock_response_success.status_code = 200
    
    with patch.object(basic_spider, 'init_sessions'), \
         patch.object(basic_spider, 'setup'), \
         patch.object(basic_spider, 'switch_session') as mock_switch:
        
        # Mock session
        mock_session = AsyncMock()
        mock_session.request.side_effect = [
            Exception("Network error"),
            Exception("Timeout error"),
            mock_response_success
        ]
        basic_spider.session = mock_session
        
        # 确保switch_session不会替换我们的mock_session
        async def mock_switch_session(*args, **kwargs):
            pass
        mock_switch.side_effect = mock_switch_session
        
        result = await basic_spider.request_with_retry('GET', 'http://test.com')
        
        assert result == mock_response_success
        assert mock_session.request.call_count == 3


@pytest.mark.asyncio
async def test_all_retries_fail_with_exception(basic_spider):
    """测试所有重试都失败并抛出异常"""
    with patch.object(basic_spider, 'init_sessions'), \
         patch.object(basic_spider, 'setup'), \
         patch.object(basic_spider, 'switch_session') as mock_switch:
        
        # Mock session
        mock_session = AsyncMock()
        mock_session.request.side_effect = Exception("Persistent network error")
        basic_spider.session = mock_session
        
        # 确保switch_session不会替换我们的mock_session
        async def mock_switch_session(*args, **kwargs):
            pass
        mock_switch.side_effect = mock_switch_session
        
        with pytest.raises(Exception, match="Persistent network error"):
            await basic_spider.request_with_retry('GET', 'http://test.com')
        
        assert mock_session.request.call_count == basic_spider.max_retries


@pytest.mark.asyncio
async def test_convenience_methods(basic_spider):
    """测试便利方法（get, post, put, delete）"""
    mock_response = Mock(spec=Response)
    mock_response.status_code = 200
    
    with patch.object(basic_spider, 'request_with_retry') as mock_request:
        mock_request.return_value = mock_response
        
        # 测试GET方法
        result = await basic_spider.get('http://test.com', param1='value1')
        mock_request.assert_called_with('GET', 'http://test.com', retry_interval=None, param1='value1')
        assert result == mock_response
        
        # 测试POST方法
        result = await basic_spider.post('http://test.com', json={'key': 'value'})
        mock_request.assert_called_with('POST', 'http://test.com', retry_interval=None, json={'key': 'value'})
        assert result == mock_response
        
        # 测试PUT方法
        result = await basic_spider.put('http://test.com', data='test')
        mock_request.assert_called_with('PUT', 'http://test.com', retry_interval=None, data='test')
        assert result == mock_response
        
        # 测试DELETE方法
        result = await basic_spider.delete('http://test.com')
        mock_request.assert_called_with('DELETE', 'http://test.com', retry_interval=None)
        assert result == mock_response


@pytest.mark.asyncio
async def test_context_manager(basic_spider):
    """测试异步上下文管理器"""
    with patch.object(basic_spider, 'init_sessions') as mock_init, \
         patch.object(basic_spider, 'close') as mock_close:
        
        async with basic_spider as spider:
            assert spider == basic_spider
            mock_init.assert_called_once()
        
        mock_close.assert_called_once()


@pytest.mark.asyncio
async def test_session_initialization(basic_spider):
    """测试会话初始化"""
    with patch.object(basic_spider, 'switch_session') as mock_switch:
        await basic_spider.init_sessions()
        mock_switch.assert_called_once()


@pytest.mark.asyncio
async def test_user_agent_generation(basic_spider):
    """测试User-Agent生成"""
    user_agent = await basic_spider.get_random_user_agent()
    assert isinstance(user_agent, str)
    assert len(user_agent) > 0


@pytest.mark.asyncio
async def test_headers_generation(basic_spider):
    """测试请求头生成"""
    headers = await basic_spider.get_random_headers()
    assert isinstance(headers, dict)
    assert 'user-agent' in headers
    assert 'accept' in headers
    assert 'accept-language' in headers 