"""
测试 check_message_id_continuous 函数在 Kafka offset 重置场景下的修复验证

这个测试文件专门用于验证工作流只执行一次Bug的修复效果，
该Bug是由于 check_message_id_continuous 函数在处理offset不连续时过于严格导致的。

Bug详情见: @BUGFIX_PLAN_WorkflowSingleExecution_KafkaMessageAck_20250108.md
"""

import unittest
import logging
from utils.common import check_message_id_continuous


class TestMessageContinuityBugFix(unittest.TestCase):
    """测试消息连续性检查函数的Bug修复"""

    def setUp(self):
        """设置测试环境，捕获日志输出"""
        self.log_messages = []
        
        # 创建一个自定义的日志处理器来捕获警告消息
        class TestLogHandler(logging.Handler):
            def __init__(self, test_instance):
                super().__init__()
                self.test_instance = test_instance
            
            def emit(self, record):
                self.test_instance.log_messages.append(record.getMessage())
        
        # 设置日志处理器
        self.test_handler = TestLogHandler(self)
        logger = logging.getLogger('utils.common')
        logger.addHandler(self.test_handler)
        logger.setLevel(logging.WARNING)

    def tearDown(self):
        """清理测试环境"""
        logger = logging.getLogger('utils.common')
        logger.removeHandler(self.test_handler)

    def test_fixed_behavior_offset_discontinuity(self):
        """
        修复验证测试：验证Kafka offset重置场景的修复效果
        
        场景描述：
        - last_ack_message_id = 50998 (上次确认的offset)
        - 实际消息ID = [51198, 51199] (offset重置后的新消息)
        - 修复后应该能正确处理这种跳跃，重新建立连续性
        """
        # 模拟真实场景的数据
        start_message_id = 50998  # 上次确认的offset
        message_ids = [51198, 51199]  # 新的消息ID，中间有跳跃
        
        # 调用修复后的实现
        continuous_ids, next_id = check_message_id_continuous(start_message_id, message_ids)
        
        # 修复后的期望行为：能够处理offset跳跃，从新起点建立连续性
        self.assertEqual(continuous_ids, [51198, 51199],
                        "修复验证失败：应该能处理offset跳跃并返回连续消息")
        self.assertEqual(next_id, 51200,
                        "修复验证失败：next_id应该正确更新")
        
        # 验证警告日志是否正确记录
        self.assertGreater(len(self.log_messages), 0, "应该记录offset重置警告")
        self.assertIn("Detected Kafka offset reset", self.log_messages[0])
        print("✓ 修复验证通过：offset重置场景能正确处理")

    def test_fixed_behavior_small_gap(self):
        """
        修复验证测试：验证小幅度offset跳跃的修复效果
        
        即使是小的跳跃，修复后也应该能正确处理
        """
        start_message_id = 120
        message_ids = [123, 124, 125]  # 跳过了121, 122
        
        continuous_ids, next_id = check_message_id_continuous(start_message_id, message_ids)
        
        # 修复后的期望行为：从新起点重建连续性
        self.assertEqual(continuous_ids, [123, 124, 125],
                        "修复验证失败：应该能处理小幅度跳跃")
        self.assertEqual(next_id, 126,
                        "修复验证失败：next_id应该正确更新")
        
        print("✓ 修复验证通过：小幅度跳跃能正确处理")

    def test_fixed_behavior_single_message_gap(self):
        """
        修复验证测试：验证单个消息跳跃的修复效果
        """
        start_message_id = 100
        message_ids = [102]  # 跳过了101
        
        continuous_ids, next_id = check_message_id_continuous(start_message_id, message_ids)
        
        # 修复后的期望行为：能处理单个消息跳跃
        self.assertEqual(continuous_ids, [102],
                        "修复验证失败：应该能处理单个消息跳跃")
        self.assertEqual(next_id, 103,
                        "修复验证失败：next_id应该正确更新")
        
        print("✓ 修复验证通过：单个消息跳跃能正确处理")

    def test_rollback_scenario(self):
        """
        测试offset回退场景的处理
        
        当期望的start_message_id大于实际最小消息ID时的行为
        """
        start_message_id = 200
        message_ids = [150, 151, 152]  # 消息ID小于期望的起始ID
        
        continuous_ids, next_id = check_message_id_continuous(start_message_id, message_ids)
        
        # 在回退场景下，应该记录警告但不处理旧消息
        self.assertEqual(continuous_ids, [],
                        "回退场景：不应处理旧于期望起始ID的消息")
        self.assertEqual(next_id, 200,
                        "回退场景：next_id应保持期望值")
        
        # 验证回退警告日志
        self.assertGreater(len(self.log_messages), 0, "应该记录offset回退警告")
        self.assertIn("Detected offset rollback", self.log_messages[0])
        print("✓ 回退场景验证通过")

    def test_unordered_messages_handling(self):
        """
        测试乱序消息的处理
        
        验证函数能正确排序并处理乱序的消息ID
        """
        start_message_id = 300
        message_ids = [303, 301, 302, 305, 304]  # 乱序消息
        
        continuous_ids, next_id = check_message_id_continuous(start_message_id, message_ids)
        
        # 修复后应该：从301开始重建连续性，这些消息实际上是完全连续的
        expected_continuous = [301, 302, 303, 304, 305]  # 排序后的连续部分
        self.assertEqual(continuous_ids, expected_continuous,
                        "乱序场景：应该正确排序并找到连续部分")
        self.assertEqual(next_id, 306,
                        "乱序场景：next_id应该正确计算")
        
        print("✓ 乱序消息处理验证通过")

    def test_current_working_scenario(self):
        """
        测试当前实现正常工作的场景
        
        验证完全连续的情况下，修复不会影响正常功能
        """
        start_message_id = 120
        message_ids = [120, 121, 122, 123]
        
        continuous_ids, next_id = check_message_id_continuous(start_message_id, message_ids)
        
        # 正常情况应该工作
        self.assertEqual(continuous_ids, [120, 121, 122, 123])
        self.assertEqual(next_id, 124)
        
        print("✓ 正常连续场景验证通过：修复不影响正常功能")

    def test_current_partial_continuity(self):
        """
        测试当前实现对部分连续性的处理
        
        当从连续开始但中间有断点时的行为
        """
        start_message_id = 120
        message_ids = [120, 121, 122, 125, 126]  # 123, 124缺失
        
        continuous_ids, next_id = check_message_id_continuous(start_message_id, message_ids)
        
        # 应该返回连续的部分
        self.assertEqual(continuous_ids, [120, 121, 122])
        self.assertEqual(next_id, 123)
        
        print("✓ 部分连续场景验证通过：修复不影响部分连续处理")

    def test_empty_message_list(self):
        """
        测试空消息列表的处理
        
        确保边界条件处理正确
        """
        start_message_id = 100
        message_ids = []
        
        continuous_ids, next_id = check_message_id_continuous(start_message_id, message_ids)
        
        self.assertEqual(continuous_ids, [])
        self.assertEqual(next_id, 100)
        
        print("✓ 空消息列表场景验证通过")


if __name__ == "__main__":
    unittest.main() 