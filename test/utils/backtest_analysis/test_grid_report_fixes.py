"""
Grid报告Bug修复测试用例

测试内容：
1. 格式一致性验证：max_profit应显示为百分比格式
2. 过滤功能验证：策略ID、总交易数、胜率、整体收益率的过滤功能

创建日期：2025-06-17
测试方法：自动化测试
测试级别：单元测试
"""

import pytest
import json
import tempfile
import os
from unittest.mock import patch, MagicMock
import pandas as pd

from utils.backtest_analysis.report_generator import generate_html_report


class TestGridReportFixes:
    """Grid报告Bug修复测试类"""

    @pytest.fixture
    def sample_results_data(self):
        """提供测试用的回测结果数据"""
        return {
            "results": [
                {
                    "param_index": 1,
                    "params": {
                        "transaction_min_amount": 100,
                        "kol_account_min_count": 6,
                        "fixed_trade_amount": 100.0
                    },
                    "statistics": {
                        "total_trades": 25,
                        "winning_trades": 12,
                        "losing_trades": 13,
                        "win_rate": 0.48,
                        "return_rate": 0.4345,
                        "max_drawdown": -0.9552,
                        "max_profit": 2.4165,  # 这是需要修复的格式问题
                        "kelly_fraction_calculated": 0.344
                    },
                    "execution_time": 1.5,
                    "single_report_rel_path": "single_run_report.html"
                },
                {
                    "param_index": 2,
                    "params": {
                        "transaction_min_amount": 200,
                        "kol_account_min_count": 8,
                        "fixed_trade_amount": 150.0
                    },
                    "statistics": {
                        "total_trades": 30,
                        "winning_trades": 18,
                        "losing_trades": 12,
                        "win_rate": 0.60,
                        "return_rate": 0.2156,
                        "max_drawdown": -0.3245,
                        "max_profit": 1.8923,
                        "kelly_fraction_calculated": 0.287
                    },
                    "execution_time": 2.1,
                    "single_report_rel_path": "single_run_report_2.html"
                }
            ],
            "time_stats": {
                "total_execution_time_formatted": "3.6s",
                "average_execution_time_formatted": "1.8s"
            }
        }

    def test_max_profit_percentage_formatting(self, sample_results_data):
        """
        测试用例1：验证max_profit格式化为百分比
        
        Bug复现：max_profit应该显示为百分比格式，与max_drawdown一致
        """
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as temp_html:
            try:
                # 调用报告生成函数
                generate_html_report(
                    json_file_path="dummy_path.json",  # 不会被使用，因为传入了results_data
                    output_html_path=temp_html.name,
                    results_data=sample_results_data
                )
                
                # 读取生成的HTML内容
                with open(temp_html.name, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # 验证max_profit显示为百分比格式
                # 第一个记录：max_profit = 2.4165 应该显示为 241.65%
                assert "241.65%" in html_content, "max_profit应该格式化为百分比"
                
                # 第二个记录：max_profit = 1.8923 应该显示为 189.23%
                assert "189.23%" in html_content, "第二个记录的max_profit应该格式化为百分比"
                
                # 验证max_drawdown仍然正确显示为百分比
                assert "95.52%" in html_content, "max_drawdown应该保持百分比格式"
                assert "32.45%" in html_content, "第二个记录的max_drawdown应该保持百分比格式"
                
            finally:
                # 清理临时文件
                if os.path.exists(temp_html.name):
                    os.unlink(temp_html.name)

    def test_filter_functionality_elements_present(self, sample_results_data):
        """
        测试用例2：验证过滤功能UI元素存在
        
        验证HTML中包含策略ID、总交易数、胜率、整体收益率的过滤器
        """
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as temp_html:
            try:
                # 调用报告生成函数
                generate_html_report(
                    json_file_path="dummy_path.json",
                    output_html_path=temp_html.name,
                    results_data=sample_results_data
                )
                
                # 读取生成的HTML内容
                with open(temp_html.name, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # 验证过滤器UI元素存在
                assert 'id="filter-strategy-id"' in html_content, "策略ID过滤器应该存在"
                assert 'id="filter-total-trades-min"' in html_content, "总交易数最小值过滤器应该存在"
                assert 'id="filter-total-trades-max"' in html_content, "总交易数最大值过滤器应该存在"
                assert 'id="filter-win-rate-min"' in html_content, "胜率最小值过滤器应该存在"
                assert 'id="filter-win-rate-max"' in html_content, "胜率最大值过滤器应该存在"
                assert 'id="filter-return-rate-min"' in html_content, "整体收益率最小值过滤器应该存在"
                assert 'id="filter-return-rate-max"' in html_content, "整体收益率最大值过滤器应该存在"
                
                # 验证重置按钮存在
                assert 'id="reset-filters"' in html_content, "重置过滤器按钮应该存在"
                
                # 验证过滤JavaScript函数存在
                assert 'function applyFilters()' in html_content, "过滤函数应该存在"
                assert 'function resetFilters()' in html_content, "重置函数应该存在"
                
            finally:
                # 清理临时文件
                if os.path.exists(temp_html.name):
                    os.unlink(temp_html.name)

    def test_format_consistency_before_fix(self, sample_results_data):
        """
        测试用例3：复现Bug - 验证修复前的格式不一致问题
        
        这个测试用例用于确认Bug确实存在（在修复前运行应该失败）
        """
        # 创建一个临时的report_generator模块副本，模拟修复前的状态
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as temp_html:
            try:
                # 使用未修复的逻辑：max_profit不在百分比格式化列表中
                with patch('utils.backtest_analysis.report_generator.generate_html_report') as mock_generate:
                    # 模拟原始的格式化逻辑（Bug状态）
                    def original_formatting_logic():
                        # 这里模拟原始逻辑：max_profit显示为小数，max_drawdown显示为百分比
                        html_content = """
                        <td>2.4165</td>  <!-- max_profit显示为小数 -->
                        <td>95.52%</td>  <!-- max_drawdown显示为百分比 -->
                        """
                        return html_content
                    
                    # 验证Bug存在：格式不一致
                    buggy_content = original_formatting_logic()
                    
                    # 在修复前，这应该是true（显示不一致）
                    has_decimal_max_profit = "2.4165" in buggy_content and "%" not in buggy_content.split("2.4165")[0].split("2.4165")[0]
                    has_percentage_max_drawdown = "95.52%" in buggy_content
                    
                    # 确认Bug存在：一个是小数，一个是百分比
                    assert has_decimal_max_profit, "Bug复现：max_profit应该显示为小数（修复前）"
                    assert has_percentage_max_drawdown, "max_drawdown应该显示为百分比"
                    
            finally:
                if os.path.exists(temp_html.name):
                    os.unlink(temp_html.name)


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 