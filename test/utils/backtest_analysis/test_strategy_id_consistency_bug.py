"""
策略ID一致性Bug复现和修复验证测试用例

此测试用例用于复现命令行工具和报告生成器生成不同策略ID的Bug，
并验证修复后的一致性。

Bug描述：相同配置在命令行工具和报告生成器中生成不同的策略ID
期望结果：修复后应该生成一致的策略ID
"""

import pytest
import json
import hashlib
from typing import Dict, Any

# 导入待测试的函数
from utils.backtest_analysis.strategy_hash import generate_strategy_hash


class TestStrategyIdConsistencyBug:
    """策略ID一致性Bug测试类"""
    
    def setup_method(self):
        """测试前准备"""
        # 基于 config_single.json 的实际配置
        self.config_single = {
            "transaction_lookback_hours": 2,
            "transaction_min_amount": 500,
            "kol_account_min_count": 6,
            "token_mint_lookback_hours": 72,
            "kol_account_min_txs": 10,
            "kol_account_max_txs": 400,
            "backtest_start_time": **********,
            "backtest_end_time": **********,
            "sell_strategy_hours": 24,
            "sell_kol_ratio": 0.3,
            "same_token_notification_interval_minutes": 60,
            "kol_min_winrate": 0.3
        }
        
        # 报告生成器中实际生成的策略ID（用户提供的期望值）
        self.expected_strategy_id = "3b974663"
    
    def test_reproduce_strategy_id_inconsistency_bug(self):
        """测试用例1：验证策略ID不一致Bug已被修复
        
        此测试验证修复前存在的策略ID不一致问题已被解决
        """
        # 使用修复前的命令行工具逻辑生成策略ID（仅用于对比展示）
        old_cmd_strategy_id = self._generate_strategy_id_old_logic(self.config_single)
        
        # 使用报告生成器逻辑生成策略ID（正确的逻辑）
        report_strategy_id = self._generate_strategy_id_report_logic(self.config_single)
        
        # 使用修复后的实际函数生成策略ID
        current_strategy_id = generate_strategy_hash(self.config_single)
        
        print(f"修复前命令行工具策略ID: {old_cmd_strategy_id}")
        print(f"报告生成器策略ID: {report_strategy_id}")
        print(f"修复后命令行工具策略ID: {current_strategy_id}")
        print(f"期望的策略ID: {self.expected_strategy_id}")
        
        # 验证报告生成器的逻辑生成正确的策略ID
        assert report_strategy_id == self.expected_strategy_id, \
            f"报告生成器逻辑验证失败，期望: {self.expected_strategy_id}, 实际: {report_strategy_id}"
        
        # 验证修复后的命令行工具与报告生成器生成一致的策略ID
        assert current_strategy_id == self.expected_strategy_id, \
            f"修复验证失败：命令行工具策略ID仍不匹配期望值，当前: {current_strategy_id}, 期望: {self.expected_strategy_id}"
        
        # 验证修复后的命令行工具与报告生成器完全一致
        assert current_strategy_id == report_strategy_id, \
            f"一致性验证失败：命令行工具与报告生成器策略ID不一致，命令行: {current_strategy_id}, 报告: {report_strategy_id}"
        
        # 验证修复确实解决了原有的不一致问题
        assert old_cmd_strategy_id != current_strategy_id, \
            f"修复验证失败：修复前后的策略ID相同，说明修复没有生效，修复前: {old_cmd_strategy_id}, 修复后: {current_strategy_id}"
    
    def test_strategy_id_after_fix(self):
        """测试用例2：验证修复后的策略ID一致性
        
        此测试用例验证修复后的 generate_strategy_hash 函数
        """
        # 使用修复后的函数生成策略ID
        fixed_strategy_id = generate_strategy_hash(self.config_single)
        
        print(f"修复后生成的策略ID: {fixed_strategy_id}")
        print(f"期望的策略ID: {self.expected_strategy_id}")
        
        # 验证修复后的策略ID与期望一致
        assert fixed_strategy_id == self.expected_strategy_id, \
            f"修复验证失败，期望: {self.expected_strategy_id}, 实际: {fixed_strategy_id}"
        
        # 验证策略ID格式正确
        assert len(fixed_strategy_id) == 8, f"策略ID长度应为8，实际: {len(fixed_strategy_id)}"
        assert all(c in '0123456789abcdef' for c in fixed_strategy_id), \
            f"策略ID应为十六进制格式，实际: {fixed_strategy_id}"
    
    def test_parameter_filtering_consistency(self):
        """测试用例3：验证参数过滤逻辑的一致性"""
        # 添加内部技术参数的配置
        config_with_tech_params = self.config_single.copy()
        config_with_tech_params.update({
            'use_real_price': True,
            'skip_price_api_query': False,
            'processing_interval': 300
        })
        
        # 修复后，两个配置应该生成相同的策略ID
        id_without_tech = generate_strategy_hash(self.config_single)
        id_with_tech = generate_strategy_hash(config_with_tech_params)
        
        print(f"不含技术参数的策略ID: {id_without_tech}")
        print(f"包含技术参数的策略ID: {id_with_tech}")
        
        assert id_without_tech == id_with_tech, \
            f"参数过滤测试失败，内部技术参数应被过滤掉，但结果不同: {id_without_tech} vs {id_with_tech}"
        
        # 都应该等于期望的策略ID
        assert id_without_tech == self.expected_strategy_id, \
            f"不含技术参数的策略ID不匹配期望值: {id_without_tech} vs {self.expected_strategy_id}"
    
    def test_config_parameter_order_independence(self):
        """测试用例4：验证参数顺序不影响策略ID生成"""
        # 创建参数顺序不同的配置
        config_reordered = {
            "kol_min_winrate": 0.3,
            "transaction_lookback_hours": 2,
            "sell_kol_ratio": 0.3,
            "transaction_min_amount": 500,
            "sell_strategy_hours": 24,
            "kol_account_min_count": 6,
            "backtest_end_time": **********,
            "token_mint_lookback_hours": 72,
            "same_token_notification_interval_minutes": 60,
            "kol_account_min_txs": 10,
            "backtest_start_time": **********,
            "kol_account_max_txs": 400
        }
        
        id_original = generate_strategy_hash(self.config_single)
        id_reordered = generate_strategy_hash(config_reordered)
        
        print(f"原始顺序策略ID: {id_original}")
        print(f"重排顺序策略ID: {id_reordered}")
        
        assert id_original == id_reordered, \
            f"参数顺序不应影响策略ID生成: {id_original} vs {id_reordered}"
        
        assert id_original == self.expected_strategy_id, \
            f"策略ID不匹配期望值: {id_original} vs {self.expected_strategy_id}"
    
    def _generate_strategy_id_old_logic(self, params_dict: Dict[str, Any]) -> str:
        """模拟修复前命令行工具的策略ID生成逻辑
        
        只过滤时间相关参数，不过滤内部技术参数
        """
        # 修复前的过滤逻辑：只过滤时间参数
        excluded_params = {
            'start_date', 'end_date', 'backtest_start_date', 'backtest_end_date',
            'backtest_start_time', 'backtest_end_time'
        }
        
        strategy_params = {
            k: v for k, v in params_dict.items() 
            if k not in excluded_params
        }
        
        # 对参数字典按键排序，确保一致性
        sorted_params = dict(sorted(strategy_params.items()))
        
        # 转换为JSON字符串
        params_str = json.dumps(sorted_params, sort_keys=True, separators=(',', ':'))
        
        # 生成SHA256哈希的前8位
        hash_obj = hashlib.sha256(params_str.encode('utf-8'))
        strategy_id = hash_obj.hexdigest()[:8]
        
        return strategy_id
    
    def _generate_strategy_id_report_logic(self, params_dict: Dict[str, Any]) -> str:
        """模拟报告生成器的策略ID生成逻辑（基于调试结果确定的实际过滤逻辑）
        
        根据反向工程调试，报告生成器实际过滤的参数包括通知间隔参数
        """
        # 报告生成器的实际过滤逻辑（根据调试结果确定）
        excluded_params = {
            # 时间相关参数
            'start_date', 'end_date', 'backtest_start_date', 'backtest_end_date',
            'backtest_start_time', 'backtest_end_time',
            # 内部技术参数
            'use_real_price', 'skip_price_api_query', 'processing_interval',
            # 通知间隔参数（关键差异！）
            'same_token_notification_interval_minutes'
        }
        
        strategy_params = {
            k: v for k, v in params_dict.items() 
            if k not in excluded_params
        }
        
        # 对参数字典按键排序，确保一致性
        sorted_params = dict(sorted(strategy_params.items()))
        
        # 转换为JSON字符串
        params_str = json.dumps(sorted_params, sort_keys=True, separators=(',', ':'))
        
        # 生成SHA256哈希的前8位
        hash_obj = hashlib.sha256(params_str.encode('utf-8'))
        strategy_id = hash_obj.hexdigest()[:8]
        
        return strategy_id


# 独立的调试函数，用于手动验证
def debug_strategy_id_generation():
    """调试函数：手动验证策略ID生成过程"""
    config = {
        "transaction_lookback_hours": 2,
        "transaction_min_amount": 500,
        "kol_account_min_count": 6,
        "token_mint_lookback_hours": 72,
        "kol_account_min_txs": 10,
        "kol_account_max_txs": 400,
        "backtest_start_time": **********,
        "backtest_end_time": **********,
        "sell_strategy_hours": 24,
        "sell_kol_ratio": 0.3,
        "same_token_notification_interval_minutes": 60,
        "kol_min_winrate": 0.3
    }
    
    expected_id = "3b974663"
    
    print("=== 策略ID生成调试 ===")
    print(f"原始配置: {json.dumps(config, indent=2)}")
    
    # 模拟旧逻辑（只过滤时间参数）
    old_excluded = {'start_date', 'end_date', 'backtest_start_date', 'backtest_end_date',
                    'backtest_start_time', 'backtest_end_time'}
    old_params = {k: v for k, v in config.items() if k not in old_excluded}
    old_sorted = dict(sorted(old_params.items()))
    old_str = json.dumps(old_sorted, sort_keys=True, separators=(',', ':'))
    old_id = hashlib.sha256(old_str.encode('utf-8')).hexdigest()[:8]
    
    print(f"\n--- 旧逻辑（命令行工具）---")
    print(f"过滤后参数: {json.dumps(old_sorted, indent=2)}")
    print(f"JSON字符串: {old_str}")
    print(f"生成的策略ID: {old_id}")
    
    # 模拟新逻辑（过滤时间参数和技术参数）
    new_excluded = {'start_date', 'end_date', 'backtest_start_date', 'backtest_end_date',
                    'backtest_start_time', 'backtest_end_time',
                    'use_real_price', 'skip_price_api_query', 'processing_interval'}
    new_params = {k: v for k, v in config.items() if k not in new_excluded}
    new_sorted = dict(sorted(new_params.items()))
    new_str = json.dumps(new_sorted, sort_keys=True, separators=(',', ':'))
    new_id = hashlib.sha256(new_str.encode('utf-8')).hexdigest()[:8]
    
    print(f"\n--- 新逻辑（报告生成器）---")
    print(f"过滤后参数: {json.dumps(new_sorted, indent=2)}")
    print(f"JSON字符串: {new_str}")
    print(f"生成的策略ID: {new_id}")
    
    print(f"\n--- 比较结果 ---")
    print(f"期望策略ID: {expected_id}")
    print(f"旧逻辑结果: {old_id} {'✓' if old_id == expected_id else '✗'}")
    print(f"新逻辑结果: {new_id} {'✓' if new_id == expected_id else '✗'}")
    print(f"逻辑一致性: {'✓' if old_id == new_id else '✗'}")


if __name__ == "__main__":
    # 运行调试函数
    debug_strategy_id_generation() 