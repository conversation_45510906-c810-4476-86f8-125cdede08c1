"""
回测V2买入即卖出过滤功能测试

测试目标：验证买入即卖出过滤功能是否正常工作
Bug场景：信号分析器初始化时卖出数据缓存为空，导致过滤功能失效
"""

import unittest
from unittest.mock import AsyncMock, MagicMock, patch
import asyncio
from datetime import datetime
from typing import Dict, Any, List

from utils.backtest_v2.signal_analyzer import SignalAnalyzer
from utils.backtest_v2.config_manager import BacktestConfigV2


class TestBuySellImmediatelyFilter(unittest.TestCase):
    """测试买入即卖出过滤功能"""

    def setUp(self):
        """设置测试环境"""
        # 创建测试配置
        self.config = BacktestConfigV2(
            sell_kol_ratio=0.3,  # 30%的KOL卖出时触发过滤
            backtest_start_time=1749493000,
            backtest_end_time=1749494000
        )
        
        # 测试token地址
        self.test_token_address = "vRseBFqTy9QLmmo5qGiwo74AVpdqqMTnxPqWoWMpump"
        self.test_timestamp = 1749493990
        
    def test_bug_reproduction_empty_sell_cache(self):
        """测试Bug复现：空的卖出数据缓存导致过滤失效"""
        
        # 创建空的卖出数据缓存（模拟Bug场景）
        empty_sell_cache = {}
        signal_analyzer = SignalAnalyzer(self.config, empty_sell_cache)
        
        # 创建候选信号（包含5个KOL）
        candidate_signal = {
            'signal_timestamp': self.test_timestamp,
            'window_start': self.test_timestamp - 300,  # 5分钟前开始
            'window_end': self.test_timestamp,
            'kol_wallets': [
                'kol_wallet_1',
                'kol_wallet_2', 
                'kol_wallet_3',
                'kol_wallet_4',
                'kol_wallet_5'
            ]
        }
        
        # 执行买入即卖出检查
        should_skip = signal_analyzer._check_buy_sell_immediately(
            candidate_signal, 
            self.test_token_address
        )
        
        # Bug场景：由于卖出数据缓存为空，函数返回False（不跳过）
        # 这是错误的行为，因为实际上有5个KOL同时卖出
        self.assertFalse(should_skip, "Bug复现：空缓存导致过滤失效，应该被过滤的信号没有被过滤")
        
    def test_correct_behavior_with_sell_data(self):
        """测试正确行为：有卖出数据时过滤功能正常工作"""
        
        # 创建包含卖出数据的缓存（模拟修复后场景）
        sell_cache_with_data = {
            self.test_token_address: {
                'records': [
                    # 模拟5个KOL在买入信号时间窗口内的卖出记录
                    {
                        'wallet': 'kol_wallet_1',
                        'timestamp': self.test_timestamp - 240,  # 4分钟前卖出
                        'event_type': 'sell'
                    },
                    {
                        'wallet': 'kol_wallet_2', 
                        'timestamp': self.test_timestamp - 180,  # 3分钟前卖出
                        'event_type': 'sell'
                    },
                    {
                        'wallet': 'kol_wallet_3',
                        'timestamp': self.test_timestamp - 120,  # 2分钟前卖出
                        'event_type': 'sell'
                    },
                    {
                        'wallet': 'kol_wallet_4',
                        'timestamp': self.test_timestamp - 60,   # 1分钟前卖出
                        'event_type': 'sell'
                    },
                    {
                        'wallet': 'kol_wallet_5',
                        'timestamp': self.test_timestamp,        # 信号时刻卖出
                        'event_type': 'sell'
                    }
                ],
                'kol_wallets': [],
                'kol_wallets_count': 0
            }
        }
        
        signal_analyzer = SignalAnalyzer(self.config, sell_cache_with_data)
        
        # 创建候选信号（包含5个KOL）
        candidate_signal = {
            'signal_timestamp': self.test_timestamp,
            'window_start': self.test_timestamp - 300,  # 5分钟前开始
            'window_end': self.test_timestamp,
            'kol_wallets': [
                'kol_wallet_1',
                'kol_wallet_2',
                'kol_wallet_3', 
                'kol_wallet_4',
                'kol_wallet_5'
            ]
        }
        
        # 执行买入即卖出检查
        should_skip = signal_analyzer._check_buy_sell_immediately(
            candidate_signal,
            self.test_token_address
        )
        
        # 正确行为：5个KOL都卖出，卖出比例100% > 30%，应该被过滤
        self.assertTrue(should_skip, "正确行为：当卖出比例超过阈值时，信号应该被过滤")
        
    def test_partial_sell_below_threshold(self):
        """测试部分卖出低于阈值的情况"""
        
        # 创建包含部分卖出数据的缓存
        sell_cache_partial = {
            self.test_token_address: {
                'records': [
                    # 只有1个KOL卖出（20% < 30%阈值）
                    {
                        'wallet': 'kol_wallet_1',
                        'timestamp': self.test_timestamp + 60,
                        'event_type': 'sell'
                    }
                ],
                'kol_wallets': [],
                'kol_wallets_count': 0
            }
        }
        
        signal_analyzer = SignalAnalyzer(self.config, sell_cache_partial)
        
        # 创建候选信号（包含5个KOL）
        candidate_signal = {
            'signal_timestamp': self.test_timestamp,
            'window_start': self.test_timestamp - 300,  # 5分钟前开始
            'window_end': self.test_timestamp,
            'kol_wallets': [
                'kol_wallet_1',
                'kol_wallet_2',
                'kol_wallet_3',
                'kol_wallet_4', 
                'kol_wallet_5'
            ]
        }
        
        # 执行买入即卖出检查
        should_skip = signal_analyzer._check_buy_sell_immediately(
            candidate_signal,
            self.test_token_address
        )
        
        # 正确行为：只有1/5=20%的KOL卖出，低于30%阈值，不应该被过滤
        self.assertFalse(should_skip, "正确行为：当卖出比例低于阈值时，信号不应该被过滤")


class TestBacktestEngineFlowOrder(unittest.TestCase):
    """测试回测引擎流程顺序"""
    
    def setUp(self):
        """设置测试环境"""
        self.config = BacktestConfigV2(
            backtest_start_time=1749493000,
            backtest_end_time=1749494000,
            sell_strategy_hours=24
        )
    
    @patch('utils.backtest_v2.backtest_engine.DataQuery')
    @patch('utils.backtest_v2.backtest_engine.SellStrategy')
    @patch('utils.backtest_v2.backtest_engine.SignalAnalyzer')
    @patch('utils.backtest_v2.backtest_engine.ResultAnalyzer')
    async def test_bug_flow_order(self, mock_result_analyzer, mock_signal_analyzer, 
                                  mock_sell_strategy, mock_data_query):
        """测试Bug场景：错误的流程顺序"""
        
        from utils.backtest_v2.backtest_engine import BacktestEngineV2
        
        # 模拟当前Bug场景的流程
        engine = BacktestEngineV2(self.config)
        
        # 模拟数据查询返回
        mock_data_query_instance = mock_data_query.return_value
        mock_data_query_instance.build_buy_data_aggregation_pipeline = AsyncMock(return_value=[])
        mock_data_query_instance.execute_aggregation_query = AsyncMock(return_value={'test_token': {}})
        mock_data_query_instance.get_token_info = AsyncMock(return_value={'test_token': {}})
        mock_data_query_instance.filter_new_token_records = AsyncMock(return_value={'test_token': {}})
        
        # 模拟卖出策略
        mock_sell_strategy_instance = mock_sell_strategy.return_value
        mock_sell_strategy_instance.sell_data_cache = {}  # 空缓存（Bug场景）
        mock_sell_strategy_instance.preload_sell_data_for_tokens = AsyncMock()
        mock_sell_strategy_instance.determine_sell_signals = AsyncMock(return_value=[])
        
        # 模拟信号分析器
        mock_signal_analyzer_instance = mock_signal_analyzer.return_value
        
        # 模拟结果分析器
        mock_result_analyzer_instance = mock_result_analyzer.return_value
        mock_result_analyzer_instance.analyze = MagicMock(return_value={})
        
        # 执行回测（这会触发Bug场景）
        try:
            await engine.run_backtest()
            
            # 验证Bug场景：信号分析器初始化时传入了空缓存
            mock_signal_analyzer.assert_called_with(self.config, {})
            
        except Exception as e:
            # 预期可能会有其他错误，但重点是验证初始化参数
            pass


if __name__ == '__main__':
    # 运行测试
    unittest.main() 