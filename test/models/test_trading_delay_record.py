"""
测试交易延迟记录模型

测试用例遵循测试设计文档:
@trading_delay_monitor_test_cases_ai.md

关联功能需求:
@trading_delay_monitor_requirements_ai.md - FR007, FR008
"""

import pytest
import pytest_asyncio
import asyncio
from datetime import datetime, timezone
from beanie import PydanticObjectId
from pymongo.errors import DuplicateKeyError
from beanie.exceptions import RevisionIdWasChanged
from motor.motor_asyncio import AsyncIOMotorClient
from beanie import init_beanie

# 导入被测试的模型
from models.trading_delay_record import TradingDelayRecord, DelayStatus


@pytest_asyncio.fixture(scope="function")
async def init_test_db():
    """初始化测试数据库连接"""
    # 使用本地开发数据库（根据测试用例文档的要求）
    client = AsyncIOMotorClient("mongodb://root:GoGmgnSpiders!@192.168.5.101:27017/?authSource=admin")
    
    # 先删除可能存在的集合（清理索引）
    try:
        await client["dev"].drop_collection("trading_delay_records")
    except Exception:
        pass  # 集合不存在时忽略错误
    
    # 初始化 Beanie，只注册测试需要的模型
    await init_beanie(
        database=client["dev"],
        document_models=[TradingDelayRecord]
    )
    
    yield client
    
    # 测试完成后清理（删除整个集合）
    try:
        await client["dev"].drop_collection("trading_delay_records")
    except Exception:
        pass  # 集合不存在时忽略错误
    client.close()


class TestTradingDelayRecord:
    """TradingDelayRecord模型测试类"""

    @pytest.mark.asyncio
    async def test_trading_delay_record_creation_basic_fields(self, init_test_db):
        """
        TC-001: 验证TradingDelayRecord模型基本字段创建
        
        关联需求: @FR007
        
        测试步骤:
        1. 使用有效数据创建TradingDelayRecord实例
        2. 验证所有必填字段
        
        预期结果: 模型实例创建成功，字段值正确
        """
        
        # 准备测试数据
        trade_record_id = PydanticObjectId()
        signal_id = PydanticObjectId()
        test_timestamp = datetime.now(timezone.utc)
        
        # 创建TradingDelayRecord实例
        delay_record = TradingDelayRecord(
            trade_record_id=trade_record_id,
            signal_id=signal_id,
            strategy_name="test_strategy",
            token_address="So11111111111111111111111111111111111111112",
            trade_type="buy",
            trade_amount=100.0,
            hit_kol_wallets=["wallet1", "wallet2"],
            kol_last_trade_timestamp=test_timestamp,
            signal_trigger_timestamp=test_timestamp,
            trade_execution_timestamp=test_timestamp,
            delay_seconds=30.5,
            delay_status=DelayStatus.CALCULATED,
            suppression_check_result="allowed",
            calculation_metadata={"test": "data"}
        )
        
        # 验证字段值
        assert delay_record.trade_record_id == trade_record_id
        assert delay_record.signal_id == signal_id
        assert delay_record.strategy_name == "test_strategy"
        assert delay_record.token_address == "So11111111111111111111111111111111111111112"
        assert delay_record.trade_type == "buy"
        assert delay_record.trade_amount == 100.0
        assert delay_record.hit_kol_wallets == ["wallet1", "wallet2"]
        assert delay_record.kol_last_trade_timestamp == test_timestamp
        assert delay_record.signal_trigger_timestamp == test_timestamp
        assert delay_record.trade_execution_timestamp == test_timestamp
        assert delay_record.delay_seconds == 30.5
        assert delay_record.delay_status == DelayStatus.CALCULATED
        assert delay_record.suppression_check_result == "allowed"
        assert delay_record.calculation_metadata == {"test": "data"}
        
        # 验证自动生成字段
        assert delay_record.created_at is not None
        assert delay_record.updated_at is not None

    @pytest.mark.asyncio
    async def test_trading_delay_record_unique_constraint(self, init_test_db):
        """
        TC-002: 验证trade_record_id唯一性约束
        
        关联需求: @FR007
        
        测试步骤:
        1. 创建并保存一条延迟记录
        2. 尝试创建相同trade_record_id的记录
        3. 保存到数据库
        
        预期结果: 抛出唯一性约束异常
        """
            
        trade_record_id = PydanticObjectId()
        test_timestamp = datetime.now(timezone.utc)
        
        # 创建第一条记录（只使用必填字段）
        delay_record1 = TradingDelayRecord(
            trade_record_id=trade_record_id,
            trade_execution_timestamp=test_timestamp,
            delay_status=DelayStatus.CALCULATED
        )
        
        # 插入第一条记录
        await delay_record1.insert()

        # 尝试创建相同trade_record_id的第二条记录
        delay_record2 = TradingDelayRecord(
            trade_record_id=trade_record_id,  # 相同的trade_record_id
            trade_execution_timestamp=test_timestamp,
            delay_status=DelayStatus.SIGNAL_MISSING
        )

        # 应该抛出唯一性约束异常
        with pytest.raises(DuplicateKeyError):
            await delay_record2.insert()

    def test_delay_status_enum_values(self):
        """
        TC-003: 验证DelayStatus枚举值
        
        关联需求: @FR006, @FR009
        
        测试步骤:
        1. 测试所有有效的DelayStatus值
        2. 测试无效的状态值
        
        预期结果: 有效值正常，无效值抛出验证错误
        """
        
        # 测试所有有效的DelayStatus值
        valid_statuses = [
            DelayStatus.CALCULATED,
            DelayStatus.SIGNAL_MISSING,
            DelayStatus.KOL_ACTIVITY_MISSING,
            DelayStatus.TIMESTAMP_ANOMALY,
            DelayStatus.SIGNAL_SUPPRESSED,  # 新增的信号抑制状态
            DelayStatus.EXCESSIVE_DELAY
        ]
        
        for status in valid_statuses:
            assert isinstance(status, DelayStatus)
            assert isinstance(status.value, str)
        
        # 验证特定枚举值
        assert DelayStatus.CALCULATED.value == "calculated"
        assert DelayStatus.SIGNAL_MISSING.value == "signal_missing"
        assert DelayStatus.KOL_ACTIVITY_MISSING.value == "kol_activity_missing"
        assert DelayStatus.TIMESTAMP_ANOMALY.value == "timestamp_anomaly"
        assert DelayStatus.SIGNAL_SUPPRESSED.value == "signal_suppressed"
        assert DelayStatus.EXCESSIVE_DELAY.value == "excessive_delay"

    def test_delay_seconds_precision(self, init_test_db):
        """
        TC-004: 验证延迟秒数精度
        
        关联需求: @FR005
        
        测试步骤:
        1. 设置delay_seconds为3.1415926
        2. 验证保存和读取的精度
        
        预期结果: 精度保持到小数点后2位
        """
        
        trade_record_id = PydanticObjectId()
        test_timestamp = datetime.now(timezone.utc)
        
        # 创建带有高精度延迟的记录
        delay_record = TradingDelayRecord(
            trade_record_id=trade_record_id,
            trade_execution_timestamp=test_timestamp,
            delay_seconds=3.1415926,  # 高精度值
            delay_status=DelayStatus.CALCULATED
        )
        
        # 验证精度处理 - 应该保持足够精度进行计算
        # 注意：根据需求文档，延迟值以秒为单位，保留到小数点后2位
        assert delay_record.delay_seconds == 3.1415926  # 模型内部保持原精度
        
        # 在实际使用中，我们可能需要格式化显示
        if delay_record.delay_seconds is not None:
            formatted_delay = round(delay_record.delay_seconds, 2)
            assert formatted_delay == 3.14

    @pytest.mark.asyncio
    async def test_optional_fields_handling(self, init_test_db):
        """
        测试可选字段的处理
        
        关联需求: @FR004, @FR007
        
        测试步骤:
        1. 创建只包含必填字段的记录
        2. 验证可选字段的默认值
        
        预期结果: 可选字段具有正确的默认值
        """
        
        trade_record_id = PydanticObjectId()
        test_timestamp = datetime.now(timezone.utc)
        
        # 创建只包含必填字段的记录
        delay_record = TradingDelayRecord(
            trade_record_id=trade_record_id,
            trade_execution_timestamp=test_timestamp,
            delay_status=DelayStatus.SIGNAL_MISSING
        )
        
        # 验证可选字段的默认值
        assert delay_record.signal_id is None
        assert delay_record.strategy_name is None
        assert delay_record.token_address is None
        assert delay_record.trade_type is None
        assert delay_record.trade_amount is None
        assert delay_record.hit_kol_wallets == []  # 默认空列表
        assert delay_record.kol_last_trade_timestamp is None
        assert delay_record.signal_trigger_timestamp is None
        assert delay_record.delay_seconds is None
        assert delay_record.calculation_metadata is None
        assert delay_record.suppression_check_result is None
        
        # 验证自动字段
        assert delay_record.created_at is not None
        assert delay_record.updated_at is not None

    @pytest.mark.asyncio
    async def test_signal_suppressed_status_fields(self, init_test_db):
        """
        测试信号抑制状态相关字段
        
        关联需求: @FR009
        
        测试步骤:
        1. 创建信号被抑制状态的延迟记录
        2. 验证抑制检查相关字段
        
        预期结果: 信号抑制字段正确设置
        """
        
        trade_record_id = PydanticObjectId()
        signal_id = PydanticObjectId()
        test_timestamp = datetime.now(timezone.utc)
        
        # 创建信号被抑制的记录
        delay_record = TradingDelayRecord(
            trade_record_id=trade_record_id,
            signal_id=signal_id,
            strategy_name="suppression_test_strategy",
            token_address="suppression_token",
            trade_type="buy",
            trade_execution_timestamp=test_timestamp,
            delay_status=DelayStatus.SIGNAL_SUPPRESSED,
            suppression_check_result="suppressed",
            calculation_metadata={
                "suppression_reason": "time_interval_25min_vs_threshold_60min",
                "time_interval_minutes": 25.5,
                "processed_at": test_timestamp.isoformat()
            }
        )
        
        # 验证信号抑制相关字段
        assert delay_record.delay_status == DelayStatus.SIGNAL_SUPPRESSED
        assert delay_record.suppression_check_result == "suppressed"
        assert delay_record.calculation_metadata is not None
        assert delay_record.calculation_metadata["suppression_reason"] == "time_interval_25min_vs_threshold_60min"
        assert delay_record.calculation_metadata["time_interval_minutes"] == 25.5 