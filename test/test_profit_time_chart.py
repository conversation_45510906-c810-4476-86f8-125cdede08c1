"""
回测V2盈利率时间分布图表功能测试用例

测试覆盖:
- _prepare_profit_time_chart_data()函数
- _format_timestamp_for_chart()函数  
- _calculate_point_size()函数
- _generate_tooltip_info()函数
"""

import unittest
import sys
import os
from datetime import datetime
from unittest.mock import patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from utils.backtest_analysis.report_generator import (
    _prepare_profit_time_chart_data,
    _format_timestamp_for_chart,
    _calculate_point_size,
    _generate_tooltip_info
)


class TestProfitTimeChartData(unittest.TestCase):
    """测试利润时间图表数据准备函数"""
    
    def test_empty_trades_list(self):
        """测试空交易列表的处理"""
        result = _prepare_profit_time_chart_data([], 1672531200, 1672617600)  # 添加测试时间范围
        
        # 验证返回的数据结构
        self.assertIn('chart_data', result)
        self.assertIn('chart_config', result)
        self.assertIn('statistics', result)
        
        # 验证空数据的统计信息
        stats = result['statistics']
        self.assertEqual(stats['total_trades'], 0)
        self.assertEqual(stats['profit_trades'], 0)
        self.assertEqual(stats['loss_trades'], 0)
        self.assertEqual(stats['neutral_trades'], 0)
        
        # 验证空的数据点
        chart_data = result['chart_data']
        self.assertEqual(len(chart_data['profit_points']), 0)
        self.assertEqual(len(chart_data['loss_points']), 0)
        self.assertEqual(len(chart_data['neutral_points']), 0)
        
        # 验证time_range信息
        time_range = result['chart_config']['time_range']
        self.assertIsNotNone(time_range['backtest_start_timestamp'])
        self.assertIsNotNone(time_range['backtest_end_timestamp'])

    def test_single_profit_trade(self):
        """测试单笔盈利交易 (return_rate > 0)"""
        trade = {
            'buy_timestamp': 1718582400,
            'sell_timestamp': 1718586000,
            'return_rate': 0.25,  # 25% 盈利
            'profit_usd': 100.50,
            'token_symbol': 'PEPE',
            'token_address': '0x123...',
            'buy_price': 0.000012,
            'sell_price': 0.000015,
            'holding_hours': 1.0,
            'kol_count': 8
        }
        
        result = _prepare_profit_time_chart_data([trade], 1672531200, 1672617600)
        
        # 验证统计信息
        stats = result['statistics']
        self.assertEqual(stats['total_trades'], 1)
        self.assertEqual(stats['profit_trades'], 1)
        self.assertEqual(stats['loss_trades'], 0)
        self.assertEqual(stats['neutral_trades'], 0)
        
        # 验证数据点分类
        chart_data = result['chart_data']
        self.assertEqual(len(chart_data['profit_points']), 1)
        self.assertEqual(len(chart_data['loss_points']), 0)
        self.assertEqual(len(chart_data['neutral_points']), 0)
        
        # 验证数据点内容
        profit_point = chart_data['profit_points'][0]
        self.assertEqual(len(profit_point), 4)  # [timestamp_ms, rate, tooltip, size]
        self.assertEqual(profit_point[1], 25.0)  # 25% 转换为百分比
        self.assertIsInstance(profit_point[0], int)  # 时间戳应该是整数（毫秒）

    def test_single_loss_trade(self):
        """测试单笔亏损交易 (return_rate < 0)"""
        trade = {
            'buy_timestamp': 1718582400,
            'return_rate': -0.15,  # 15% 亏损
            'profit_usd': -50.25,
            'token_symbol': 'DOGE'
        }
        
        result = _prepare_profit_time_chart_data([trade], 1672531200, 1672617600)
        
        # 验证统计信息
        stats = result['statistics']
        self.assertEqual(stats['total_trades'], 1)
        self.assertEqual(stats['profit_trades'], 0)
        self.assertEqual(stats['loss_trades'], 1)
        self.assertEqual(stats['neutral_trades'], 0)
        
        # 验证数据点分类
        chart_data = result['chart_data']
        self.assertEqual(len(chart_data['loss_points']), 1)
        
        # 验证盈利率转换
        loss_point = chart_data['loss_points'][0]
        self.assertEqual(loss_point[1], -15.0)  # -15%

    def test_single_neutral_trade(self):
        """测试单笔平盈交易 (return_rate = 0)"""
        trade = {
            'buy_timestamp': 1718582400,
            'return_rate': 0.0,  # 0% 收益
            'profit_usd': 0.0,
            'token_symbol': 'BTC'
        }
        
        result = _prepare_profit_time_chart_data([trade], 1672531200, 1672617600)
        
        # 验证统计信息
        stats = result['statistics']
        self.assertEqual(stats['neutral_trades'], 1)
        
        # 验证数据点分类
        chart_data = result['chart_data']
        self.assertEqual(len(chart_data['neutral_points']), 1)

    def test_mixed_trades(self):
        """测试包含盈利、亏损、平盈的混合交易"""
        trades = [
            {'buy_timestamp': 1718582400, 'return_rate': 0.25, 'profit_usd': 100},  # 盈利
            {'buy_timestamp': 1718582500, 'return_rate': -0.15, 'profit_usd': -50}, # 亏损
            {'buy_timestamp': 1718582600, 'return_rate': 0.0, 'profit_usd': 0},     # 平盈
            {'buy_timestamp': 1718582700, 'return_rate': 0.10, 'profit_usd': 30},   # 盈利
        ]
        
        result = _prepare_profit_time_chart_data(trades, 1672531200, 1672617600)
        
        # 验证统计信息
        stats = result['statistics']
        self.assertEqual(stats['total_trades'], 4)
        self.assertEqual(stats['profit_trades'], 2)
        self.assertEqual(stats['loss_trades'], 1)
        self.assertEqual(stats['neutral_trades'], 1)
        
        # 验证数据点分类
        chart_data = result['chart_data']
        self.assertEqual(len(chart_data['profit_points']), 2)
        self.assertEqual(len(chart_data['loss_points']), 1)
        self.assertEqual(len(chart_data['neutral_points']), 1)

    def test_missing_fields_handling(self):
        """测试缺少字段的处理"""
        # 只包含核心字段的交易记录
        incomplete_trade = {
            'buy_timestamp': 1718582400,
            'return_rate': 0.1
            # 缺少其他字段
        }
        
        result = _prepare_profit_time_chart_data([incomplete_trade], 1672531200, 1672617600)
        
        # 应该能正常处理，不抛出异常
        self.assertEqual(result['statistics']['total_trades'], 1)
        self.assertEqual(len(result['chart_data']['profit_points']), 1)
        
        # 验证数据点包含默认值
        point = result['chart_data']['profit_points'][0]
        self.assertEqual(point[1], 10.0)  # 10% 转换为百分比


class TestChartUtilityFunctions(unittest.TestCase):
    """测试图表相关工具函数"""
    
    def test_format_timestamp_for_chart_valid(self):
        """测试有效时间戳的格式化"""
        timestamp = 1718582400  # 2024-06-16 20:00:00 UTC
        result = _format_timestamp_for_chart(timestamp)
        
        # 验证格式 YYYY-MM-DD HH:mm:ss
        self.assertRegex(result, r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}')
        self.assertIn('2024', result)

    def test_format_timestamp_for_chart_invalid(self):
        """测试无效时间戳的处理"""
        invalid_timestamp = -1
        result = _format_timestamp_for_chart(invalid_timestamp)
        
        # 应该返回字符串形式的原始值
        self.assertEqual(result, str(invalid_timestamp))

    def test_calculate_point_size_normal_range(self):
        """测试正常范围的散点大小计算"""
        profit_usd = 50.0
        min_profit = 0.0
        max_profit = 100.0
        
        result = _calculate_point_size(profit_usd, min_profit, max_profit)
        
        # 应该在基础大小(8)和最大大小(20)之间
        self.assertGreaterEqual(result, 8)
        self.assertLessEqual(result, 20)
        # 对于中间值，应该是 8 + (20-8) * 0.5 = 14
        self.assertEqual(result, 14)

    def test_calculate_point_size_edge_cases(self):
        """测试边界情况的散点大小计算"""
        # 当 min_profit == max_profit 时
        result = _calculate_point_size(50.0, 50.0, 50.0)
        self.assertEqual(result, 8)  # 应该返回base_size
        
        # 当 max_profit == 0 时
        result = _calculate_point_size(0.0, 0.0, 0.0)
        self.assertEqual(result, 8)

    def test_calculate_point_size_negative_profit(self):
        """测试负利润的散点大小计算"""
        profit_usd = -30.0  # 负利润
        min_profit = 0.0
        max_profit = 100.0
        
        result = _calculate_point_size(profit_usd, min_profit, max_profit)
        
        # 使用绝对值计算，abs(-30) = 30
        # (30 - 0) / (100 - 0) = 0.3, 8 + (20-8) * 0.3 = 11.6 -> 11
        self.assertEqual(result, 11)

    def test_generate_tooltip_info_complete(self):
        """测试完整交易记录的tooltip生成"""
        trade = {
            'token_symbol': 'PEPE',
            'token_address': '0x123456789abcdef',
            'buy_timestamp': 1718582400,
            'sell_timestamp': 1718586000,
            'buy_price': 0.000012,
            'sell_price': 0.000015,
            'profit_usd': 45.67,
            'return_rate': 0.25,
            'holding_hours': 1.0,
            'kol_count': 8
        }
        
        result = _generate_tooltip_info(trade)
        
        # 验证所有字段都存在
        expected_fields = [
            'token_symbol', 'token_address', 'buy_time', 'sell_time',
            'buy_price', 'sell_price', 'profit_usd', 'return_rate',
            'holding_time', 'kol_count'
        ]
        
        for field in expected_fields:
            self.assertIn(field, result)
        
        # 验证格式化
        self.assertEqual(result['token_symbol'], 'PEPE')
        self.assertEqual(result['return_rate'], '25.00%')
        self.assertEqual(result['profit_usd'], '$45.67')
        self.assertIn('小时', result['holding_time'])

    def test_generate_tooltip_info_partial(self):
        """测试部分字段的tooltip生成"""
        # 只包含核心字段的交易记录
        partial_trade = {
            'token_address': '0x123456789abcdef',
            'buy_timestamp': 1718582400,
            'return_rate': 0.1
        }
        
        result = _generate_tooltip_info(partial_trade)
        
        # 应该不抛出异常
        self.assertIn('token_symbol', result)
        self.assertIn('return_rate', result)
        
        # 缺失字段应该使用默认值
        self.assertEqual(result['return_rate'], '10.00%')
        self.assertEqual(result['kol_count'], 0)

    def test_generate_tooltip_info_token_address_fallback(self):
        """测试当没有token_symbol时使用token_address的前缀"""
        trade = {
            'token_address': '0x123456789abcdef123456',
            'buy_timestamp': 1718582400,
            'return_rate': 0.05
        }
        
        result = _generate_tooltip_info(trade)
        
        # 应该使用token_address的前8位 + '...'
        self.assertEqual(result['token_symbol'], '0x123456...')


class TestDataValidation(unittest.TestCase):
    """测试数据验证相关功能"""
    
    def test_chart_data_structure_validity(self):
        """测试图表数据结构的有效性"""
        trades = [
            {'buy_timestamp': 1718582400, 'return_rate': 0.1, 'profit_usd': 50},
            {'buy_timestamp': 1718582500, 'return_rate': -0.05, 'profit_usd': -25}
        ]
        
        result = _prepare_profit_time_chart_data(trades)
        
        # 验证顶层结构
        required_keys = ['chart_data', 'chart_config', 'statistics']
        for key in required_keys:
            self.assertIn(key, result)
        
        # 验证chart_data结构
        chart_data = result['chart_data']
        data_keys = ['profit_points', 'loss_points', 'neutral_points']
        for key in data_keys:
            self.assertIn(key, chart_data)
            self.assertIsInstance(chart_data[key], list)
        
        # 验证chart_config结构
        chart_config = result['chart_config']
        config_keys = ['colors', 'x_axis', 'y_axis']
        for key in config_keys:
            self.assertIn(key, chart_config)
        
        # 验证colors配置
        colors = chart_config['colors']
        color_keys = ['profit', 'loss', 'neutral']
        for key in color_keys:
            self.assertIn(key, colors)
            self.assertIsInstance(colors[key], str)
            self.assertTrue(colors[key].startswith('#'))  # 应该是hex颜色


if __name__ == '__main__':
    unittest.main() 