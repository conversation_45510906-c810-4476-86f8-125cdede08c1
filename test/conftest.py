"""
pytest 公共配置文件

提供测试中需要的公共fixtures和配置，包括：
1. 异步测试支持
2. 数据库mock
3. 通用测试数据
4. 环境变量设置
"""

import pytest
import asyncio
import os
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timezone
from typing import Dict, Any
from beanie import PydanticObjectId

# 设置测试环境变量
os.environ.update({
    "MONGODB_URL": "mongodb://localhost:27017/test_db",
    "DATABASE_NAME": "test_db",
    "ENVIRONMENT": "test",
    "MONGODB_HOST": "mongodb://localhost:27017",
    "MONGODB_DB": "meme_monitor_test",
    "MONGODB_AUTH_SOURCE": "admin",
    "MONGODB_USERNAME": "",
    "MONGODB_PASSWORD": ""
})

@pytest.fixture(scope="function")
def event_loop():
    """创建事件循环以支持异步测试"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
def mock_mongodb():
    """Mock MongoDB连接"""
    with patch('motor.motor_asyncio.AsyncIOMotorClient') as mock_client:
        mock_db = Mock()
        mock_collection = AsyncMock()
        
        # 配置mock返回值
        mock_client.return_value.__getitem__.return_value = mock_db
        mock_db.__getitem__.return_value = mock_collection
        
        # 配置collection的常用方法
        mock_collection.find_one.return_value = None
        mock_collection.insert_one.return_value = Mock(inserted_id=PydanticObjectId())
        mock_collection.update_one.return_value = Mock(modified_count=1)
        mock_collection.delete_one.return_value = Mock(deleted_count=1)
        
        yield {
            "client": mock_client.return_value,
            "db": mock_db,
            "collection": mock_collection
        }

@pytest.fixture
def mock_beanie():
    """Mock Beanie ODM"""
    with patch('beanie.init_beanie') as mock_init:
        mock_init.return_value = None
        yield mock_init

@pytest.fixture
def mock_logger():
    """Mock日志记录器"""
    with patch('logging.getLogger') as mock_get_logger:
        mock_logger = Mock()
        mock_get_logger.return_value = mock_logger
        yield mock_logger

@pytest.fixture
def sample_token_address():
    """示例代币地址"""
    return "So11111111111111111111111111111111111111112"  # SOL的wrapped token地址

@pytest.fixture
def sample_trade_data():
    """示例交易数据"""
    return {
        "token_address": "So11111111111111111111111111111111111111112",
        "trade_type": "buy",
        "amount_sol": 0.1,
        "slippage_percentage": 1.0
    }

@pytest.fixture
def sample_config_id():
    """示例配置ID"""
    return "test_config_12345"

@pytest.fixture
def sample_strategy_id():
    """示例策略ID"""
    return "test_strategy_67890"

@pytest.fixture
def mock_datetime():
    """Mock datetime以提供固定时间"""
    fixed_time = datetime(2024, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
    with patch('datetime.datetime') as mock_dt:
        mock_dt.now.return_value = fixed_time
        mock_dt.utcnow.return_value = fixed_time
        yield fixed_time

@pytest.fixture
def mock_httpx_session():
    """Mock httpx异步会话"""
    session = AsyncMock()
    
    # 配置默认成功响应
    mock_response = Mock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "success": True,
        "data": {
            "hash": "mock_transaction_hash",
            "status": "confirmed"
        }
    }
    mock_response.text = "success"
    
    session.post.return_value = mock_response
    session.get.return_value = mock_response
    session.put.return_value = mock_response
    session.delete.return_value = mock_response
    
    return session

@pytest.fixture
def mock_environment_variables():
    """Mock环境变量"""
    env_vars = {
        "GMGN_API_KEY": "test_gmgn_key",
        "JUPITER_API_KEY": "test_jupiter_key",
        "PROXY_HOST": "test_proxy_host",
        "PROXY_PORT": "8080",
        "PROXY_USERNAME": "test_user",
        "PROXY_PASSWORD": "test_pass"
    }
    
    with patch.dict(os.environ, env_vars):
        yield env_vars

@pytest.fixture
def mock_object_ids():
    """提供mock ObjectId实例"""
    return {
        "trade_record_id": PydanticObjectId(),
        "config_id": PydanticObjectId(),
        "strategy_id": PydanticObjectId(),
        "signal_id": PydanticObjectId()
    }

@pytest.fixture
def mock_file_operations():
    """Mock文件操作"""
    with patch('builtins.open', mock_open_factory()) as mock_file:
        with patch('os.path.exists', return_value=True):
            with patch('os.makedirs'):
                yield mock_file

def mock_open_factory():
    """创建mock_open工厂函数"""
    from unittest.mock import mock_open
    return mock_open(read_data='{"test": "data"}')

@pytest.fixture
def cleanup_test_files():
    """清理测试文件"""
    test_files = []
    
    def add_file(filepath):
        test_files.append(filepath)
    
    yield add_file
    
    # 清理创建的测试文件
    import os
    for filepath in test_files:
        if os.path.exists(filepath):
            try:
                os.remove(filepath)
            except Exception:
                pass  # 忽略清理错误

@pytest.fixture
def mock_network_responses():
    """Mock网络响应的预设数据"""
    return {
        "gmgn_success": {
            "status_code": 200,
            "json": {
                "success": True,
                "data": {
                    "hash": "gmgn_mock_hash",
                    "status": "confirmed",
                    "amount": 100.0
                }
            }
        },
        "gmgn_v2_success": {
            "status_code": 200,
            "json": {
                "success": True,
                "data": {
                    "hash": "gmgn_v2_mock_hash",
                    "status": "confirmed",
                    "amount": 100.0
                }
            }
        },
        "jupiter_success": {
            "status_code": 200,
            "json": {
                "success": True,
                "data": {
                    "hash": "jupiter_mock_hash",
                    "status": "confirmed",
                    "amount": 100.0
                }
            }
        },
        "network_error": {
            "status_code": 500,
            "json": {
                "error": "Internal server error"
            }
        },
        "slippage_error": {
            "status_code": 400,
            "json": {
                "error": "slippage tolerance exceeded"
            }
        },
        "insufficient_funds": {
            "status_code": 400,
            "json": {
                "error": "insufficient funds for transaction"
            }
        }
    }

@pytest.fixture
def performance_timer():
    """性能测试计时器"""
    import time
    
    class Timer:
        def __init__(self):
            self.start_time = None
            self.end_time = None
        
        def start(self):
            self.start_time = time.time()
        
        def stop(self):
            self.end_time = time.time()
            return self.elapsed()
        
        def elapsed(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return 0
    
    return Timer()

# 配置pytest插件
def pytest_configure(config):
    """配置pytest"""
    # 添加自定义标记
    config.addinivalue_line(
        "markers", "integration: 标记为集成测试"
    )
    config.addinivalue_line(
        "markers", "unit: 标记为单元测试" 
    )
    config.addinivalue_line(
        "markers", "slow: 标记为慢速测试"
    )
    config.addinivalue_line(
        "markers", "network: 需要网络访问的测试"
    )

def pytest_collection_modifyitems(config, items):
    """修改测试收集行为"""
    # 为所有异步测试添加asyncio标记
    for item in items:
        if asyncio.iscoroutinefunction(item.function):
            item.add_marker(pytest.mark.asyncio)

# 异步测试的fixture清理
@pytest.fixture
async def async_cleanup():
    """异步测试清理"""
    cleanup_tasks = []
    
    def add_cleanup(coro):
        cleanup_tasks.append(coro)
    
    yield add_cleanup
    
    # 执行所有清理任务
    if cleanup_tasks:
        await asyncio.gather(*cleanup_tasks, return_exceptions=True)

@pytest.fixture
def suppress_logs():
    """抑制测试期间的日志输出"""
    import logging
    
    # 保存原始日志级别
    original_level = logging.getLogger().level
    
    # 设置为ERROR级别以减少输出
    logging.getLogger().setLevel(logging.ERROR)
    
    yield
    
    # 恢复原始日志级别
    logging.getLogger().setLevel(original_level)

@pytest.fixture(scope="session", autouse=True)
async def setup_test_database():
    """
    设置测试数据库 - 会话级fixture，自动执行
    确保测试使用干净的数据库环境
    """
    import motor.motor_asyncio
    from models import init_db
    
    # 连接到测试数据库
    test_client = motor.motor_asyncio.AsyncIOMotorClient("mongodb://localhost:27017")
    test_db = test_client["meme_monitor_test"]
    
    # 清理测试数据库
    await test_db.drop_collection("tokens")
    await test_db.drop_collection("trading_delay_records")
    await test_db.drop_collection("trade_records") 
    await test_db.drop_collection("signals")
    await test_db.drop_collection("kol_wallet_activities")
    
    # 初始化测试数据库
    try:
        await init_db()
    except Exception as e:
        # 如果初始化失败，记录但不阻断测试
        print(f"测试数据库初始化警告: {e}")
    
    yield test_db
    
    # 测试结束后清理
    test_client.close() 