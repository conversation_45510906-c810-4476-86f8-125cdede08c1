"""
TradingDelayRecordDAO 自动化测试

基于测试用例设计文档 @trading_delay_monitor_test_cases_ai.md
覆盖TC-301~TC-309的所有测试场景，确保DAO层功能正确性

测试用例映射：
- TC-301: test_save_delay_record - 保存延迟记录
- TC-302: test_find_by_trade_record_id_success - 根据交易ID查找
- TC-303: test_find_by_trade_record_id_not_found - 查找不存在的记录
- TC-304: test_get_delay_statistics - 获取延迟统计
- TC-305: test_find_delays_for_analysis - 分析查询性能测试
- TC-306: test_find_recent_records_by_strategy_token - 信号抑制查询
- TC-307: test_find_recent_records_no_match - 无匹配策略-代币记录
- TC-308: test_find_recent_records_time_boundary - 时间范围边界测试
- TC-309: test_find_recent_records_limit - 限制返回数量测试
"""

import unittest
from unittest.mock import patch, AsyncMock, MagicMock, call
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta, timezone

from beanie import PydanticObjectId
import pytest

from dao.trading_delay_record_dao import TradingDelayRecordDAO
from models.trading_delay_record import TradingDelayRecord, DelayStatus


class TestTradingDelayRecordDAO(unittest.IsolatedAsyncioTestCase):
    """TradingDelayRecordDAO测试类"""

    def setUp(self):
        """测试初始化"""
        self.dao = TradingDelayRecordDAO()
        self.test_strategy_name = "test_strategy"
        self.test_token_address = "So11111111111111111111111111111111111111112"
        self.test_trade_record_id = PydanticObjectId()

    def _setup_field_comparisons(self, mock_model_class):
        """配置字段比较操作的通用方法"""
        # 设置字段属性为Mock对象，这样DAO代码就能正常访问这些字段
        mock_model_class.trade_record_id = MagicMock(name='trade_record_id_field')
        mock_model_class.strategy_name = MagicMock(name='strategy_name_field')
        mock_model_class.token_address = MagicMock(name='token_address_field')
        mock_model_class.created_at = MagicMock(name='created_at_field')
        mock_model_class.delay_status = MagicMock(name='delay_status_field')
        
        # 配置字段比较操作，返回Mock对象而不是进行实际比较
        # 这些Mock对象将被传递给find()方法作为查询条件
        mock_model_class.trade_record_id.__eq__ = MagicMock(return_value=MagicMock(name='trade_record_id_condition'))
        mock_model_class.strategy_name.__eq__ = MagicMock(return_value=MagicMock(name='strategy_name_condition'))
        mock_model_class.token_address.__eq__ = MagicMock(return_value=MagicMock(name='token_address_condition'))
        mock_model_class.created_at.__lt__ = MagicMock(return_value=MagicMock(name='created_at_lt_condition'))
        mock_model_class.created_at.__ge__ = MagicMock(return_value=MagicMock(name='created_at_ge_condition'))
        mock_model_class.created_at.__le__ = MagicMock(return_value=MagicMock(name='created_at_le_condition'))
        mock_model_class.delay_status.__eq__ = MagicMock(return_value=MagicMock(name='delay_status_condition'))
        
        # 为了支持负号操作（sort中的-TradingDelayRecord.created_at）
        mock_model_class.created_at.__neg__ = MagicMock(return_value=MagicMock(name='created_at_neg'))
        
        return mock_model_class

    @patch('dao.trading_delay_record_dao.TradingDelayRecord')
    async def test_save_delay_record(self, mock_model_class):
        """
        TC-301: 保存延迟记录测试
        验证save方法能正确保存记录并返回保存后的对象
        """
        # Arrange - 准备测试数据
        mock_record = MagicMock(spec=TradingDelayRecord)
        mock_record.save = AsyncMock(return_value=mock_record)
        
        # Act - 执行被测试方法
        result = await self.dao.save(mock_record)
        
        # Assert - 验证结果
        mock_record.save.assert_called_once()
        self.assertEqual(result, mock_record)

    @patch('dao.trading_delay_record_dao.TradingDelayRecord')
    async def test_find_by_trade_record_id_success(self, mock_model_class):
        """
        TC-302: 根据交易ID成功查找记录
        验证find_by_trade_record_id方法能正确查找并返回记录
        """
        # Arrange - 准备测试数据
        expected_record = MagicMock(spec=TradingDelayRecord)
        mock_find_one = AsyncMock(return_value=expected_record)
        mock_model_class.find_one = mock_find_one
        
        # Act - 执行被测试方法
        result = await self.dao.find_by_trade_record_id(self.test_trade_record_id)
        
        # Assert - 验证结果
        mock_find_one.assert_called_once()
        # 验证查询条件：应该使用trade_record_id字段进行查询
        call_args = mock_find_one.call_args
        self.assertIsNotNone(call_args)
        self.assertEqual(result, expected_record)

    @patch('dao.trading_delay_record_dao.TradingDelayRecord')
    async def test_find_by_trade_record_id_not_found(self, mock_model_class):
        """
        TC-303: 查找不存在的交易记录
        验证当记录不存在时返回None
        """
        # Arrange - 准备测试数据
        mock_find_one = AsyncMock(return_value=None)
        mock_model_class.find_one = mock_find_one
        
        # Act - 执行被测试方法
        result = await self.dao.find_by_trade_record_id(self.test_trade_record_id)
        
        # Assert - 验证结果
        mock_find_one.assert_called_once()
        self.assertIsNone(result)

    @patch('dao.trading_delay_record_dao.TradingDelayRecord')
    async def test_get_delay_statistics(self, mock_model_class):
        """
        TC-304: 获取延迟统计信息
        验证聚合查询能正确返回统计数据
        """
        # Arrange - 准备测试数据
        mock_aggregation_results = [
            {
                '_id': DelayStatus.CALCULATED.value,
                'count': 15,
                'avg_delay': 3.2,
                'max_delay': 8.5,
                'min_delay': 0.1
            },
            {
                '_id': DelayStatus.KOL_ACTIVITY_MISSING.value,
                'count': 3,
                'avg_delay': 0,
                'max_delay': None,
                'min_delay': None
            }
        ]
        
        # 创建Mock聚合对象
        mock_aggregate = AsyncMock()
        mock_aggregate.to_list = AsyncMock(return_value=mock_aggregation_results)
        mock_model_class.aggregate = MagicMock(return_value=mock_aggregate)
        
        start_time = datetime.now(timezone.utc) - timedelta(days=7)
        end_time = datetime.now(timezone.utc)
        
        # Act - 执行被测试方法
        result = await self.dao.get_delay_statistics(start_time, end_time)
        
        # Assert - 验证结果
        mock_model_class.aggregate.assert_called_once()
        mock_aggregate.to_list.assert_called_once()
        
        # 验证聚合管道结构
        aggregate_call_args = mock_model_class.aggregate.call_args[0][0]
        self.assertIsInstance(aggregate_call_args, list)
        self.assertTrue(len(aggregate_call_args) >= 2)  # 至少包含$match和$group阶段
        
        # 验证返回结果格式 - 应该是list而不是dict
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 2)
        
        # 验证结果内容
        self.assertEqual(result, mock_aggregation_results)

    @patch('dao.trading_delay_record_dao.TradingDelayRecord.find')
    async def test_find_delays_for_analysis(self, mock_find):
        """
        TC-305: 查找用于分析的延迟记录
        验证分析查询的性能和结果正确性
        """
        # Arrange - 准备测试数据
        mock_records = [
            MagicMock(spec=TradingDelayRecord),
            MagicMock(spec=TradingDelayRecord)
        ]
        
        # 重要：配置字段访问，这样DAO代码就能正常访问TradingDelayRecord.created_at等字段
        # 获取被patch的TradingDelayRecord类
        from dao.trading_delay_record_dao import TradingDelayRecord as MockedTradingDelayRecord
        self._setup_field_comparisons(MockedTradingDelayRecord)
        
        # 配置完整的查询链：find().sort().limit().to_list()
        mock_to_list = AsyncMock(return_value=mock_records)
        mock_limit_result = MagicMock()
        mock_limit_result.to_list = mock_to_list
        
        mock_sort_result = MagicMock()
        mock_sort_result.limit = MagicMock(return_value=mock_limit_result)
        
        mock_find_result = MagicMock()
        mock_find_result.sort = MagicMock(return_value=mock_sort_result)
        
        # 配置TradingDelayRecord.find()方法
        mock_find.return_value = mock_find_result
        
        start_time = datetime.now(timezone.utc) - timedelta(days=1)
        end_time = datetime.now(timezone.utc)
        
        # Act - 执行被测试方法
        result = await self.dao.find_delays_for_analysis(
            start_time=start_time,
            end_time=end_time,
            limit=100
        )
        
        # Assert - 验证结果和调用
        self.assertEqual(result, mock_records)
        
        # 验证find方法被正确调用
        mock_find.assert_called_once()
        call_args = mock_find.call_args[0]  # 获取位置参数
        self.assertEqual(len(call_args), 3)  # 应该有3个查询条件
        
        # 验证查询链的调用
        # 注意：sort()接收的是Mock对象（-TradingDelayRecord.created_at的结果）
        mock_find_result.sort.assert_called_once()
        sort_call_args = mock_find_result.sort.call_args[0]
        self.assertEqual(len(sort_call_args), 1)  # sort应该接收1个参数
        
        mock_sort_result.limit.assert_called_once_with(100)
        mock_limit_result.to_list.assert_called_once()
        
        # 验证字段比较操作被正确调用（这是验证细节的关键部分）
        MockedTradingDelayRecord.created_at.__ge__.assert_called_once_with(start_time)
        MockedTradingDelayRecord.created_at.__le__.assert_called_once_with(end_time)
        MockedTradingDelayRecord.delay_status.__eq__.assert_called_once_with(DelayStatus.CALCULATED)
        MockedTradingDelayRecord.created_at.__neg__.assert_called_once()  # 负号操作用于排序

    @patch('dao.trading_delay_record_dao.TradingDelayRecord')
    async def test_find_recent_records_by_strategy_token(self, mock_model_class):
        """
        TC-306: 查询相同策略和代币的最近记录
        验证信号抑制检查的核心查询方法
        """
        # Arrange - 准备测试数据
        mock_records = [
            MagicMock(spec=TradingDelayRecord),
            MagicMock(spec=TradingDelayRecord)
        ]
        
        # 创建Mock查询链 - 更精确的Mock配置
        mock_to_list = AsyncMock(return_value=mock_records)
        mock_limit_result = MagicMock()
        mock_limit_result.to_list = mock_to_list
        
        mock_sort_result = MagicMock()
        mock_sort_result.limit = MagicMock(return_value=mock_limit_result)
        
        mock_find_result = MagicMock()
        mock_find_result.sort = MagicMock(return_value=mock_sort_result)
        
        # 配置TradingDelayRecord.find()方法
        mock_model_class.find = MagicMock(return_value=mock_find_result)
        
        # 配置字段比较操作 - 让它们返回可以传递给find()的Mock对象
        strategy_condition = MagicMock(name='strategy_condition')
        token_condition = MagicMock(name='token_condition') 
        time_condition = MagicMock(name='time_condition')
        
        mock_model_class.strategy_name.__eq__ = MagicMock(return_value=strategy_condition)
        mock_model_class.token_address.__eq__ = MagicMock(return_value=token_condition)
        mock_model_class.created_at.__lt__ = MagicMock(return_value=time_condition)
        
        # 配置排序字段
        sort_field = MagicMock(name='sort_field')
        mock_model_class.created_at.__neg__ = MagicMock(return_value=sort_field)
        
        before_timestamp = datetime.now(timezone.utc)
        
        # Act - 执行被测试方法
        result = await self.dao.find_recent_records_by_strategy_token(
            strategy_name=self.test_strategy_name,
            token_address=self.test_token_address,
            before_timestamp=before_timestamp,
            limit=5
        )
        
        # Assert - 验证详细的调用参数
        # 1. 验证字段比较操作被正确调用
        mock_model_class.strategy_name.__eq__.assert_called_once_with(self.test_strategy_name)
        mock_model_class.token_address.__eq__.assert_called_once_with(self.test_token_address)
        mock_model_class.created_at.__lt__.assert_called_once_with(before_timestamp)
        
        # 2. 验证find()方法被调用，并传入了正确的查询条件
        mock_model_class.find.assert_called_once_with(
            strategy_condition,
            token_condition, 
            time_condition
        )
        
        # 3. 验证排序操作
        mock_find_result.sort.assert_called_once()
        # 获取sort调用的参数
        sort_call_args = mock_find_result.sort.call_args[0]
        self.assertEqual(len(sort_call_args), 1)  # 应该只有一个排序字段
        
        # 4. 验证limit操作
        mock_sort_result.limit.assert_called_once_with(5)
        
        # 5. 验证to_list操作
        mock_to_list.assert_called_once()
        
        # 6. 验证返回结果
        self.assertEqual(result, mock_records)
        self.assertEqual(len(result), 2)

    @patch('dao.trading_delay_record_dao.TradingDelayRecord')
    async def test_find_recent_records_no_match(self, mock_model_class):
        """
        TC-307: 无匹配策略-代币记录查询
        验证当没有匹配记录时返回空列表
        """
        # Arrange - 准备测试数据
        # 配置字段比较操作
        self._setup_field_comparisons(mock_model_class)
        
        # 配置完整的查询链：find().sort().limit().to_list()
        mock_to_list = AsyncMock(return_value=[])
        mock_limit_result = MagicMock()
        mock_limit_result.to_list = mock_to_list
        
        mock_sort_result = MagicMock()
        mock_sort_result.limit = MagicMock(return_value=mock_limit_result)
        
        mock_find_result = MagicMock()
        mock_find_result.sort = MagicMock(return_value=mock_sort_result)
        
        # 配置TradingDelayRecord.find()方法
        mock_model_class.find = MagicMock(return_value=mock_find_result)
        
        before_timestamp = datetime.now(timezone.utc)
        
        # Act - 执行被测试方法
        result = await self.dao.find_recent_records_by_strategy_token(
            strategy_name="nonexistent_strategy",
            token_address="nonexistent_token",
            before_timestamp=before_timestamp
        )
        
        # Assert - 验证结果
        mock_model_class.find.assert_called_once()
        self.assertEqual(result, [])
        self.assertEqual(len(result), 0)

    @patch('dao.trading_delay_record_dao.TradingDelayRecord')
    async def test_find_recent_records_time_boundary(self, mock_model_class):
        """
        TC-308: 时间范围边界测试
        验证时间戳边界条件的严格性（严格小于）
        """
        # Arrange - 准备测试数据
        # 配置字段比较操作
        self._setup_field_comparisons(mock_model_class)
        
        # 配置完整的查询链：find().sort().limit().to_list()
        mock_to_list = AsyncMock(return_value=[])
        mock_limit_result = MagicMock()
        mock_limit_result.to_list = mock_to_list
        
        mock_sort_result = MagicMock()
        mock_sort_result.limit = MagicMock(return_value=mock_limit_result)
        
        mock_find_result = MagicMock()
        mock_find_result.sort = MagicMock(return_value=mock_sort_result)
        
        # 配置TradingDelayRecord.find()方法
        mock_model_class.find = MagicMock(return_value=mock_find_result)
        
        boundary_timestamp = datetime.now(timezone.utc)
        
        # Act - 执行被测试方法
        result = await self.dao.find_recent_records_by_strategy_token(
            strategy_name=self.test_strategy_name,
            token_address=self.test_token_address,
            before_timestamp=boundary_timestamp
        )
        
        # Assert - 验证时间条件使用了严格小于比较
        mock_model_class.created_at.__lt__.assert_called_once_with(boundary_timestamp)
        self.assertEqual(result, [])

    @patch('dao.trading_delay_record_dao.TradingDelayRecord')
    async def test_find_recent_records_limit(self, mock_model_class):
        """
        TC-309: 限制返回数量测试
        验证limit参数能正确限制返回的记录数量
        """
        # Arrange - 准备测试数据（模拟有多条记录但只返回限制数量）
        single_record = [MagicMock(spec=TradingDelayRecord)]
        
        # 配置字段比较操作
        self._setup_field_comparisons(mock_model_class)
        
        # 配置完整的查询链：find().sort().limit().to_list()
        mock_to_list = AsyncMock(return_value=single_record)
        mock_limit_result = MagicMock()
        mock_limit_result.to_list = mock_to_list
        
        mock_sort_result = MagicMock()
        mock_sort_result.limit = MagicMock(return_value=mock_limit_result)
        
        mock_find_result = MagicMock()
        mock_find_result.sort = MagicMock(return_value=mock_sort_result)
        
        # 配置TradingDelayRecord.find()方法
        mock_model_class.find = MagicMock(return_value=mock_find_result)
        
        before_timestamp = datetime.now(timezone.utc)
        
        # Act - 执行被测试方法，设置limit=1
        result = await self.dao.find_recent_records_by_strategy_token(
            strategy_name=self.test_strategy_name,
            token_address=self.test_token_address,
            before_timestamp=before_timestamp,
            limit=1
        )
        
        # Assert - 验证limit参数被正确传递
        mock_sort_result.limit.assert_called_once_with(1)
        self.assertEqual(result, single_record)
        self.assertEqual(len(result), 1)

    @patch('dao.trading_delay_record_dao.TradingDelayRecord')
    async def test_find_recent_records_default_limit(self, mock_model_class):
        """
        额外测试：验证默认limit行为
        当不指定limit时，应该使用默认值
        """
        # Arrange
        # 配置字段比较操作
        self._setup_field_comparisons(mock_model_class)
        
        # 配置完整的查询链：find().sort().limit().to_list()
        mock_to_list = AsyncMock(return_value=[])
        mock_limit_result = MagicMock()
        mock_limit_result.to_list = mock_to_list
        
        mock_sort_result = MagicMock()
        mock_sort_result.limit = MagicMock(return_value=mock_limit_result)
        
        mock_find_result = MagicMock()
        mock_find_result.sort = MagicMock(return_value=mock_sort_result)
        
        # 配置TradingDelayRecord.find()方法
        mock_model_class.find = MagicMock(return_value=mock_find_result)
        
        before_timestamp = datetime.now(timezone.utc)
        
        # Act - 不指定limit参数
        result = await self.dao.find_recent_records_by_strategy_token(
            strategy_name=self.test_strategy_name,
            token_address=self.test_token_address,
            before_timestamp=before_timestamp
        )
        
        # Assert - 验证使用了默认limit值10
        mock_sort_result.limit.assert_called_once_with(10)
        self.assertEqual(result, [])


if __name__ == '__main__':
    # 运行测试
    unittest.main() 