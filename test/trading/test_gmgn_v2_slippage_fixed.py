"""
GMGN V2交易服务滑点参数修复后的验证测试
测试验证修复后GmgnTradeServiceV2能正确获取滑点参数
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import pytest
from unittest.mock import MagicMock, patch
from utils.trading.solana.gmgn_trade_service_v2 import GmgnTradeServiceV2
from utils.trading.solana.trade_interface import TradeType


class TestGmgnV2SlippageFixed:
    """验证GMGN V2滑点参数修复后的正确行为"""
    
    def setup_method(self):
        """测试前的设置"""
        self.service = GmgnTradeServiceV2(gmgn_api_host="https://test.gmgn.ai")
    
    def test_buy_slippage_parameter_fixed(self):
        """
        测试买入时滑点参数修复后能正确获取
        
        修复后应该：
        - TradeOrchestrator设置buy_slippage_percentage=2.5
        - GmgnTradeServiceV2正确读取buy_slippage_percentage
        - 结果使用期望的2.5而不是默认值0.5
        """
        # 模拟TradeOrchestrator传递的策略快照（标准字段名）
        strategy_snapshot = {
            'buy_slippage_percentage': 2.5,  # 标准字段名
            'buy_priority_fee_sol': 0.0001
        }
        
        # 调用_prepare_route_params方法
        result = self.service._prepare_route_params(
            input_token_address="So11111111111111111111111111111111111111112",
            output_token_address="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
            amount_input_token=0.01,
            wallet_address="DapiQqsGjomQeKEbyvHePgGHEyLEU8XYt8VBTvaVdBn6",
            strategy_snapshot=strategy_snapshot,
            trade_type=TradeType.BUY
        )
        
        # 验证修复：应该使用期望的滑点值
        actual_slippage = result['slippage']
        expected_slippage = 2.5
        
        assert actual_slippage == expected_slippage, f"修复验证失败：实际滑点{actual_slippage}应该等于期望滑点{expected_slippage}"
        
        print(f"✓ 修复验证成功：期望滑点{expected_slippage}%，实际滑点{actual_slippage}%")
    
    def test_sell_slippage_parameter_fixed(self):
        """
        测试卖出时滑点参数修复后能正确获取
        """
        # 模拟策略快照
        strategy_snapshot = {
            'sell_slippage_percentage': 3.0,  # 标准字段名
            'sell_priority_fee_sol': 0.0002
        }
        
        # 调用_prepare_route_params方法
        result = self.service._prepare_route_params(
            input_token_address="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
            output_token_address="So11111111111111111111111111111111111111112",
            amount_input_token=100.0,
            wallet_address="DapiQqsGjomQeKEbyvHePgGHEyLEU8XYt8VBTvaVdBn6",
            strategy_snapshot=strategy_snapshot,
            trade_type=TradeType.SELL
        )
        
        # 验证修复：应该使用期望的滑点值
        actual_slippage = result['slippage']
        expected_slippage = 3.0
        
        assert actual_slippage == expected_slippage, f"修复验证失败：实际滑点{actual_slippage}应该等于期望滑点{expected_slippage}"
        
        print(f"✓ 修复验证成功：期望滑点{expected_slippage}%，实际滑点{actual_slippage}%")
    
    def test_priority_fee_parameter_fixed(self):
        """
        测试优先费参数修复后能正确获取
        """
        # 模拟策略快照
        strategy_snapshot = {
            'buy_slippage_percentage': 1.5,
            'buy_priority_fee_sol': 0.0005  # 标准字段名
        }
        
        # 调用_prepare_route_params方法
        result = self.service._prepare_route_params(
            input_token_address="So11111111111111111111111111111111111111112",
            output_token_address="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
            amount_input_token=0.01,
            wallet_address="DapiQqsGjomQeKEbyvHePgGHEyLEU8XYt8VBTvaVdBn6",
            strategy_snapshot=strategy_snapshot,
            trade_type=TradeType.BUY
        )
        
        # 验证修复：应该使用期望的优先费值
        actual_fee = result['fee']
        expected_fee = 0.0005
        
        assert actual_fee == expected_fee, f"修复验证失败：实际优先费{actual_fee}应该等于期望优先费{expected_fee}"
        
        print(f"✓ 修复验证成功：期望优先费{expected_fee} SOL，实际优先费{actual_fee} SOL")
    
    def test_slippage_retry_mechanism_fixed(self):
        """
        测试修复后滑点递增重试机制能正常工作
        
        修复后应该：
        1. 第一次尝试：buy_slippage_percentage=1.0 → 实际使用1.0
        2. 滑点递增后：buy_slippage_percentage=1.5 → 实际使用1.5
        """
        # 模拟滑点递增重试的两次尝试
        attempts = [
            {'buy_slippage_percentage': 1.0},  # 第一次尝试
            {'buy_slippage_percentage': 1.5},  # 滑点递增后的重试
        ]
        
        results = []
        expected_values = [1.0, 1.5]
        
        for i, strategy_snapshot in enumerate(attempts):
            result = self.service._prepare_route_params(
                input_token_address="So11111111111111111111111111111111111111112",
                output_token_address="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                amount_input_token=0.01,
                wallet_address="DapiQqsGjomQeKEbyvHePgGHEyLEU8XYt8VBTvaVdBn6",
                strategy_snapshot=strategy_snapshot,
                trade_type=TradeType.BUY
            )
            results.append(result['slippage'])
            print(f"尝试 {i+1}: 期望滑点 {expected_values[i]}%, 实际滑点 {result['slippage']}%")
        
        # 验证修复：两次尝试应该使用不同的递增滑点值
        assert results[0] == 1.0 and results[1] == 1.5, f"修复验证失败：期望滑点递增[1.0, 1.5]，实际结果：{results}"
        assert results[0] != results[1], "修复验证失败：滑点递增机制应该产生不同的值"
        
        print("✓ 修复验证成功：滑点递增重试机制恢复正常工作")
    
    def test_default_values_when_no_parameters(self):
        """
        测试当策略快照中没有提供参数时，仍然使用默认值
        """
        # 空的策略快照
        strategy_snapshot = {}
        
        # 测试买入默认值
        buy_result = self.service._prepare_route_params(
            input_token_address="So11111111111111111111111111111111111111112",
            output_token_address="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
            amount_input_token=0.01,
            wallet_address="DapiQqsGjomQeKEbyvHePgGHEyLEU8XYt8VBTvaVdBn6",
            strategy_snapshot=strategy_snapshot,
            trade_type=TradeType.BUY
        )
        
        # 测试卖出默认值
        sell_result = self.service._prepare_route_params(
            input_token_address="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
            output_token_address="So11111111111111111111111111111111111111112",
            amount_input_token=100.0,
            wallet_address="DapiQqsGjomQeKEbyvHePgGHEyLEU8XYt8VBTvaVdBn6",
            strategy_snapshot=strategy_snapshot,
            trade_type=TradeType.SELL
        )
        
        # 验证默认值
        assert buy_result['slippage'] == 0.5, f"买入默认滑点应该是0.5，实际：{buy_result['slippage']}"
        assert buy_result['fee'] == 0.00005, f"买入默认优先费应该是0.00005，实际：{buy_result['fee']}"
        assert sell_result['slippage'] == 1.0, f"卖出默认滑点应该是1.0，实际：{sell_result['slippage']}"
        assert sell_result['fee'] == 0.0001, f"卖出默认优先费应该是0.0001，实际：{sell_result['fee']}"
        
        print("✓ 默认值验证成功：当无参数时正确使用默认值")


if __name__ == "__main__":
    # 运行修复验证测试
    test_instance = TestGmgnV2SlippageFixed()
    test_instance.setup_method()
    
    print("=== GMGN V2滑点参数修复验证测试 ===")
    
    try:
        test_instance.test_buy_slippage_parameter_fixed()
        test_instance.test_sell_slippage_parameter_fixed()
        test_instance.test_priority_fee_parameter_fixed()
        test_instance.test_slippage_retry_mechanism_fixed()
        test_instance.test_default_values_when_no_parameters()
        print("\n✅ 所有修复验证测试通过！Bug已成功修复。")
    except AssertionError as e:
        print(f"\n❌ 修复验证失败: {e}")
    except Exception as e:
        print(f"\n❌ 测试执行出错: {e}")
    
    print("\n=== 修复验证测试完成 ===") 