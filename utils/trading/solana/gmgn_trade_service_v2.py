import asyncio
import base64
import logging
from datetime import datetime, timezone
from typing import Optional, Dict, Any

import httpx
import base58
from solders.keypair import Keypair
from solders.transaction import VersionedTransaction

from utils.trading.solana.trade_interface import TradeInterface, TradeResult, TradeStatus, TradeType
from beanie import PydanticObjectId

logger = logging.getLogger(__name__)

# GMGN v2 API端点常量
GMGN_V2_API_ENDPOINTS = {
    "GET_SWAP_ROUTE": "/defi/router/v1/sol/tx/get_swap_route",
    "SUBMIT_TRANSACTION": "/txproxy/v1/send_transaction",
    "GET_TRANSACTION_STATUS": "/defi/router/v1/sol/tx/get_transaction_status"
}

# Solana SOL代币地址
SOL_MINT_ADDRESS = "So11111111111111111111111111111111111111112"


class GmgnTradeServiceV2(TradeInterface):
    """
    GMGN v2 API交易服务实现
    
    基于最新的GMGN API实现，提供完整的交易流程：
    1. 获取交易路由
    2. 签名交易
    3. 提交交易
    4. 监控交易状态
    """
    
    def __init__(self, gmgn_api_host: str = "https://gmgn.ai", http_client: Optional[httpx.AsyncClient] = None):
        """
        初始化GMGN v2交易服务
        
        Args:
            gmgn_api_host: GMGN API主机地址
            http_client: 可选的HTTP客户端实例
        """
        self.gmgn_api_host = gmgn_api_host.rstrip('/')
        self.http_client = http_client or httpx.AsyncClient(
            timeout=30.0,
            limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)
        )
        self._should_close_client = http_client is None
        
    async def execute_trade(
        self,
        trade_type: TradeType,
        input_token_address: str,
        output_token_address: str,
        amount_input_token: float,
        wallet_private_key_b58: str,
        wallet_address: str,
        strategy_snapshot: Dict[str, Any],
        signal_id: PydanticObjectId,
        trade_record_id: PydanticObjectId
    ) -> TradeResult:
        """
        执行交易操作（实现TradeInterface抽象方法）
        
        Args:
            trade_type: 交易类型 (BUY 或 SELL)
            input_token_address: 输入代币的地址
            output_token_address: 输出代币的地址
            amount_input_token: 输入代币的数量
            wallet_private_key_b58: 执行交易的钱包的Base58编码私钥
            wallet_address: 执行交易的钱包公钥
            strategy_snapshot: 当前策略配置的快照
            signal_id: 关联的信号ID
            trade_record_id: 关联的交易记录ID
            
        Returns:
            TradeResult: 包含交易结果的对象
        """
        logger.info(f"[TradeRec:{trade_record_id}] Starting GMGN v2 trade: {amount_input_token} {input_token_address} -> {output_token_address}")
        
        try:
            # 1. 获取交易路由
            route_params = await self._prepare_route_params_async(
                input_token_address=input_token_address,
                output_token_address=output_token_address,
                amount_input_token=amount_input_token,
                wallet_address=wallet_address,
                strategy_snapshot=strategy_snapshot,
                trade_type=trade_type
            )
            
            logger.debug(f"[TradeRec:{trade_record_id}] Route params: {route_params}")
            route_response = await self._get_swap_route(**route_params)
            
            # 检查路由响应
            if route_response.get("code") != 0:
                error_msg = route_response.get("msg", "Unknown route error")
                logger.error(f"[TradeRec:{trade_record_id}] Route failed: {error_msg}")
                return TradeResult(
                    status=TradeStatus.FAILED,
                    error_message=f"Route failed: {error_msg}",
                    provider_response_raw={"route_response": route_response},
                    executed_at=datetime.now(timezone.utc)
                )
            
            # 提取路由数据
            route_data = route_response.get("data", {})
            quote_data = route_data.get("quote", {})
            raw_tx_data = route_data.get("raw_tx", {})
            
            if not raw_tx_data.get("swapTransaction"):
                error_msg = "No swap transaction in route response"
                logger.error(f"[TradeRec:{trade_record_id}] {error_msg}")
                return TradeResult(
                    status=TradeStatus.FAILED,
                    error_message=error_msg,
                    provider_response_raw={"route_response": route_response},
                    executed_at=datetime.now(timezone.utc)
                )
            
            # 2. 签名交易
            swap_transaction_b64 = raw_tx_data["swapTransaction"]
            last_valid_height = raw_tx_data.get("lastValidBlockHeight")
            
            try:
                signed_tx = self._sign_transaction(swap_transaction_b64, wallet_private_key_b58)
                logger.debug(f"[TradeRec:{trade_record_id}] Transaction signed successfully")
            except Exception as e:
                error_msg = f"Transaction signing failed: {str(e)}"
                logger.error(f"[TradeRec:{trade_record_id}] {error_msg}")
                return TradeResult(
                    status=TradeStatus.FAILED,
                    error_message=error_msg,
                    provider_response_raw={"route_response": route_response},
                    executed_at=datetime.now(timezone.utc)
                )
            
            # 3. 提交交易
            is_anti_mev = strategy_snapshot.get('gmgn_v2_anti_mev', False)
            submit_response = await self._submit_transaction(signed_tx, is_anti_mev)
            
            # 检查提交响应
            if submit_response.get("code") != 0:
                error_msg = submit_response.get("msg", "Unknown submit error")
                logger.error(f"[TradeRec:{trade_record_id}] Submit failed: {error_msg}")
                return TradeResult(
                    status=TradeStatus.FAILED,
                    error_message=f"Submit failed: {error_msg}",
                    provider_response_raw={
                        "route_response": route_response,
                        "submit_response": submit_response
                    },
                    executed_at=datetime.now(timezone.utc)
                )
            
            # 提取交易哈希
            submit_data = submit_response.get("data", {})
            tx_hash = submit_data.get("hash")
            
            if not tx_hash:
                error_msg = "No transaction hash in submit response"
                logger.error(f"[TradeRec:{trade_record_id}] {error_msg}")
                return TradeResult(
                    status=TradeStatus.FAILED,
                    error_message=error_msg,
                    provider_response_raw={
                        "route_response": route_response,
                        "submit_response": submit_response
                    },
                    executed_at=datetime.now(timezone.utc)
                )
            
            logger.info(f"[TradeRec:{trade_record_id}] Transaction submitted: {tx_hash}")
            
            # 4. 监控交易状态
            if last_valid_height:
                max_poll_attempts = strategy_snapshot.get('gmgn_v2_max_poll_attempts', 12)
                poll_interval = strategy_snapshot.get('gmgn_v2_poll_interval', 5)
                
                status_response = await self._monitor_transaction_status(
                    tx_hash=tx_hash,
                    last_valid_height=last_valid_height,
                    max_attempts=max_poll_attempts,
                    poll_interval=poll_interval
                )
            else:
                # 如果没有last_valid_height，跳过状态监控
                logger.warning(f"[TradeRec:{trade_record_id}] No lastValidBlockHeight, skipping status monitoring")
                status_response = {"data": {"success": None, "expired": False, "failed": False}}
            
            # 5. 解析最终结果
            result = self._parse_trade_result(
                route_response=route_response,
                submit_response=submit_response,
                status_response=status_response,
                trade_record_id=trade_record_id
            )
            
            logger.info(f"[TradeRec:{trade_record_id}] Trade completed: {result.status}")
            return result
            
        except Exception as e:
            error_msg = f"Unexpected error during trade execution: {str(e)}"
            logger.error(f"[TradeRec:{trade_record_id}] {error_msg}", exc_info=True)
            return TradeResult(
                status=TradeStatus.FAILED,
                error_message=error_msg,
                executed_at=datetime.now(timezone.utc)
            )
    
    def is_slippage_related_error(
        self, 
        error_message: Optional[str], 
        provider_response: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        实现TradeInterface的抽象方法 - GMGN API特定的滑点错误识别
        
        Args:
            error_message: 错误信息文本
            provider_response: 原始API响应（可选，用于更精确的判断）
            
        Returns:
            bool: 是否为滑点相关错误
        """
        # GMGN API特定的滑点相关关键词
        gmgn_slippage_keywords = [
            "route failed", "swap failed", "price change", 
            "market impact", "liquidity insufficient",
            "output amount too low", "insufficient output amount",
            "price moved too much", "slippage", "price impact",
            "slippage tolerance exceeded", "exceeds maximum slippage"
        ]
        
        # 检查错误消息
        if error_message:
            error_lower = error_message.lower()
            for keyword in gmgn_slippage_keywords:
                if keyword in error_lower:
                    return True
                
        # 检查provider_response中的错误码（GMGN API特定）
        if provider_response and isinstance(provider_response, dict):
            # 检查路由响应中的错误
            route_response = provider_response.get('route_response', {})
            if route_response.get('code') != 0:
                route_msg = route_response.get('msg', '').lower()
                if any(keyword in route_msg for keyword in gmgn_slippage_keywords):
                    return True
            
            # 检查提交响应中的错误
            submit_response = provider_response.get('submit_response', {})
            if submit_response.get('code') != 0:
                submit_msg = submit_response.get('msg', '').lower()
                if any(keyword in submit_msg for keyword in gmgn_slippage_keywords):
                    return True
                    
        return False
    
    def is_non_retryable_error(
        self, 
        error_message: Optional[str], 
        provider_response: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        扩展TradeInterface的默认实现 - 添加GMGN API特定的不可重试错误
        
        Args:
            error_message: 错误信息文本
            provider_response: 原始API响应（可选）
            
        Returns:
            bool: 是否为不可重试错误
        """
        # 调用父类通用实现（TradeInterface提供的默认实现）
        if super().is_non_retryable_error(error_message, provider_response):
            return True
            
        # GMGN API特定的不可重试错误关键词
        gmgn_non_retryable_keywords = [
            "invalid wallet", "private key invalid", "token not supported",
            "invalid token address", "api key invalid", "unauthorized access",
            "invalid parameters", "amount too small", "amount too large",
            "wallet not found", "transaction malformed"
        ]
        
        # 检查错误消息
        if error_message:
            error_lower = error_message.lower()
            for keyword in gmgn_non_retryable_keywords:
                if keyword in error_lower:
                    return True
        
        # 检查GMGN API响应中的特定错误码
        if provider_response and isinstance(provider_response, dict):
            route_response = provider_response.get('route_response', {})
            if route_response.get('code') in [400, 401, 403, 404]:  # HTTP客户端错误
                return True
                
        return False
    
    async def _get_swap_route(
        self,
        token_in_address: str,
        token_out_address: str,
        in_amount: str,
        from_address: str,
        slippage: float,
        fee: float,
        is_anti_mev: bool = False,
        partner: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        调用GMGN API获取交易路由
        
        Returns:
            包含quote和raw_tx的响应数据
        """
        params = {
            "token_in_address": token_in_address,
            "token_out_address": token_out_address,
            "in_amount": in_amount,
            "from_address": from_address,
            "slippage": slippage,
            "fee": fee
        }
        
        if is_anti_mev:
            params["is_anti_mev"] = "true"
        if partner:
            params["partner"] = partner
            
        url = f"{self.gmgn_api_host}{GMGN_V2_API_ENDPOINTS['GET_SWAP_ROUTE']}"
        
        try:
            response = await self.http_client.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error getting swap route: {e.response.status_code} - {e.response.text}")
            return {"code": e.response.status_code, "msg": f"HTTP {e.response.status_code}: {e.response.text}"}
        except Exception as e:
            logger.error(f"Error getting swap route: {str(e)}")
            return {"code": -1, "msg": f"Request failed: {str(e)}"}
    
    def _sign_transaction(
        self, 
        swap_transaction_b64: str, 
        private_key_b58: str
    ) -> str:
        """
        使用私钥签名交易
        
        Args:
            swap_transaction_b64: base64编码的待签名交易
            private_key_b58: base58编码的私钥
            
        Returns:
            base64编码的已签名交易
        """
        try:
            # 解码交易
            transaction_bytes = base64.b64decode(swap_transaction_b64)
            transaction = VersionedTransaction.from_bytes(transaction_bytes)
            
            # 创建密钥对
            private_key_bytes = base58.b58decode(private_key_b58)
            keypair = Keypair.from_bytes(private_key_bytes)
            
            # 在solders中，需要重新创建VersionedTransaction来签名
            # 获取消息并创建新的已签名交易
            message = transaction.message
            signed_transaction = VersionedTransaction(message, [keypair])
            
            # 返回base64编码的签名交易
            return base64.b64encode(bytes(signed_transaction)).decode()
            
        except Exception as e:
            logger.error(f"Transaction signing error: {str(e)}")
            raise ValueError(f"Failed to sign transaction: {str(e)}")
    
    async def _submit_transaction(
        self,
        signed_tx: str,
        is_anti_mev: bool = False
    ) -> Dict[str, Any]:
        """
        提交已签名的交易
        
        Returns:
            包含交易哈希的响应数据
        """
        payload = {
            "chain": "sol",
            "signedTx": signed_tx
        }
        
        if is_anti_mev:
            payload["isAntiMev"] = True
            
        url = f"{self.gmgn_api_host}{GMGN_V2_API_ENDPOINTS['SUBMIT_TRANSACTION']}"
        
        try:
            response = await self.http_client.post(url, json=payload)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error submitting transaction: {e.response.status_code} - {e.response.text}")
            return {"code": e.response.status_code, "msg": f"HTTP {e.response.status_code}: {e.response.text}"}
        except Exception as e:
            logger.error(f"Error submitting transaction: {str(e)}")
            return {"code": -1, "msg": f"Request failed: {str(e)}"}
    
    async def _monitor_transaction_status(
        self,
        tx_hash: str,
        last_valid_height: int,
        max_attempts: int = 12,
        poll_interval: int = 5
    ) -> Dict[str, Any]:
        """
        监控交易状态直到确认或超时
        
        Returns:
            最终的交易状态
        """
        for attempt in range(max_attempts):
            params = {
                "hash": tx_hash,
                "last_valid_height": last_valid_height
            }
            
            url = f"{self.gmgn_api_host}{GMGN_V2_API_ENDPOINTS['GET_TRANSACTION_STATUS']}"
            
            try:
                response = await self.http_client.get(url, params=params)
                response.raise_for_status()
                status_data = response.json()
                
                # 检查是否有明确的状态
                data = status_data.get("data", {})
                if data.get("success") or data.get("expired") or data.get("failed"):
                    return status_data
                    
            except Exception as e:
                logger.warning(f"Error checking transaction status (attempt {attempt + 1}): {str(e)}")
                
            # 如果不是最后一次尝试，等待后继续
            if attempt < max_attempts - 1:
                await asyncio.sleep(poll_interval)
        
        # 超时情况
        logger.warning(f"Transaction status monitoring timed out after {max_attempts} attempts")
        return {"data": {"expired": True, "success": False, "failed": False}}
    
    async def _get_token_decimals(self, token_address: str) -> int:
        """
        获取代币的精度信息 (参考Jupiter实现)
        
        Args:
            token_address: 代币地址
            
        Returns:
            int: 代币精度
        """
        # SOL的精度固定为9
        if token_address == SOL_MINT_ADDRESS:
            return 9
        
        # 检查缓存
        if hasattr(self, '_token_decimals_cache') and token_address in self._token_decimals_cache:
            logger.debug(f"Using cached decimals for token {token_address}: {self._token_decimals_cache[token_address]}")
            return self._token_decimals_cache[token_address]
        
        # 使用TokenInfo类获取代币信息（按照数据库 -> GMGN -> Solscan的顺序）
        try:
            from utils.spiders.solana.token_info import TokenInfo
            
            token_info_fetcher = TokenInfo(token_address, chain="sol")
            token_info = await token_info_fetcher.get_token_info()
            
            if token_info and 'decimals' in token_info:
                decimals = int(token_info['decimals'])
                
                # 缓存结果
                if not hasattr(self, '_token_decimals_cache'):
                    self._token_decimals_cache = {}
                self._token_decimals_cache[token_address] = decimals
                
                logger.debug(f"Retrieved decimals for token {token_address}: {decimals}")
                return decimals
            else:
                logger.warning(f"Token info not found or missing decimals for {token_address}")
                
        except Exception as e:
            logger.warning(f"Failed to get token decimals for {token_address}: {e}")
        
        # 如果查询失败，返回常见的默认值6位小数
        logger.warning(f"Using default decimals (6) for token {token_address}")
        return 6
    
    def _prepare_route_params(
        self,
        input_token_address: str,
        output_token_address: str,
        amount_input_token: float,
        wallet_address: str,
        strategy_snapshot: Dict[str, Any],
        trade_type: TradeType
    ) -> Dict[str, Any]:
        """
        将交易参数转换为GMGN v2 API格式（同步版本，用于向后兼容现有测试）
        
        注意：此方法使用固定的代币精度来保持向后兼容性。
        新代码应使用 _prepare_route_params_async 方法获得动态精度支持。
        """
        # 为了向后兼容，优先根据代币地址确定精度，然后考虑配置
        if input_token_address == SOL_MINT_ADDRESS:
            decimals = 9  # SOL固定9位小数
        else:
            decimals = strategy_snapshot.get('input_token_decimals', 6)  # SPL代币使用配置或默认6位
        
        in_amount = str(int(amount_input_token * (10 ** decimals)))
        
        # 提取策略参数（使用标准字段名）
        if trade_type == TradeType.BUY:
            slippage = strategy_snapshot.get('buy_slippage_percentage', 0.5)
            priority_fee = strategy_snapshot.get('buy_priority_fee_sol', 0.00005)
        else:  # SELL
            slippage = strategy_snapshot.get('sell_slippage_percentage', 1.0)
            priority_fee = strategy_snapshot.get('sell_priority_fee_sol', 0.0001)
        
        is_anti_mev = strategy_snapshot.get('gmgn_v2_anti_mev', False)
        partner = strategy_snapshot.get('gmgn_v2_partner')
        
        params = {
            "token_in_address": input_token_address,
            "token_out_address": output_token_address,
            "in_amount": in_amount,
            "from_address": wallet_address,
            "slippage": slippage,
            "fee": priority_fee,
            "is_anti_mev": is_anti_mev
        }
        
        if partner:
            params["partner"] = partner
            
        return params
    
    async def _prepare_route_params_async(
        self,
        input_token_address: str,
        output_token_address: str,
        amount_input_token: float,
        wallet_address: str,
        strategy_snapshot: Dict[str, Any],
        trade_type: TradeType
    ) -> Dict[str, Any]:
        """
        将交易参数转换为GMGN v2 API格式（异步版本，动态获取代币精度）
        """
        # 动态获取代币小数位数
        decimals = await self._get_token_decimals(input_token_address)
        
        in_amount = str(int(amount_input_token * (10 ** decimals)))
        
        # 提取策略参数（使用标准字段名）
        if trade_type == TradeType.BUY:
            slippage = strategy_snapshot.get('buy_slippage_percentage', 0.5)
            priority_fee = strategy_snapshot.get('buy_priority_fee_sol', 0.00005)
        else:  # SELL
            slippage = strategy_snapshot.get('sell_slippage_percentage', 1.0)
            priority_fee = strategy_snapshot.get('sell_priority_fee_sol', 0.0001)
        
        is_anti_mev = strategy_snapshot.get('gmgn_v2_anti_mev', False)
        partner = strategy_snapshot.get('gmgn_v2_partner')
        
        params = {
            "token_in_address": input_token_address,
            "token_out_address": output_token_address,
            "in_amount": in_amount,
            "from_address": wallet_address,
            "slippage": slippage,
            "fee": priority_fee,
            "is_anti_mev": is_anti_mev
        }
        
        if partner:
            params["partner"] = partner
            
        return params
    
    def _parse_trade_result(
        self,
        route_response: Dict[str, Any],
        submit_response: Dict[str, Any],
        status_response: Dict[str, Any],
        trade_record_id: PydanticObjectId
    ) -> TradeResult:
        """
        将GMGN API响应转换为TradeResult格式
        """
        # 提取交易哈希
        tx_hash = submit_response.get("data", {}).get("hash")
        
        # 确定交易状态
        status_data = status_response.get("data", {})
        if status_data.get("success"):
            status = TradeStatus.SUCCESS
            error_message = None
        elif status_data.get("failed"):
            status = TradeStatus.FAILED
            error_message = "Transaction failed on chain"
        elif status_data.get("expired"):
            status = TradeStatus.FAILED
            error_message = "Transaction expired"
        else:
            # 如果状态未知，但有交易哈希，认为是成功的
            if tx_hash:
                status = TradeStatus.SUCCESS
                error_message = None
            else:
                status = TradeStatus.FAILED
                error_message = "Transaction status unknown"
        
        # 提取实际交易数量
        quote_data = route_response.get("data", {}).get("quote", {})
        actual_amount_in = quote_data.get("inAmount")
        actual_amount_out = quote_data.get("outAmount")
        
        # 转换为浮点数（从lamports）
        try:
            if actual_amount_in:
                actual_amount_in = float(actual_amount_in)
            if actual_amount_out:
                actual_amount_out = float(actual_amount_out)
        except (ValueError, TypeError):
            logger.warning(f"Failed to convert amounts to float: in={actual_amount_in}, out={actual_amount_out}")
            actual_amount_in = None
            actual_amount_out = None
        
        return TradeResult(
            status=status,
            tx_hash=tx_hash,
            error_message=error_message,
            provider_response_raw={
                "route_response": route_response,
                "submit_response": submit_response,
                "status_response": status_response
            },
            actual_amount_in=actual_amount_in,
            actual_amount_out=actual_amount_out,
            executed_at=datetime.now(timezone.utc)
        )
    
    async def close(self):
        """资源清理"""
        if self._should_close_client and self.http_client:
            await self.http_client.aclose() 