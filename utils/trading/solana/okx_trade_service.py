import os
import asyncio
import traceback
import base64
import hmac
import hashlib
import logging
import json
import time
import random
import os
from decimal import Decimal
from typing import Dict, Any, Optional, NamedTuple
from datetime import datetime, timezone, timedelta

from dotenv import load_dotenv
import httpx
from solders.keypair import Keypair
from solders.transaction import VersionedTransaction, Transaction
from solders.pubkey import Pubkey
from solders.message import Message, MessageV0
from solders.hash import Hash
from solders.signature import Signature
from solana.rpc.async_api import AsyncClient
from solana.rpc.commitment import Confirmed, Commitment
from solana.rpc.types import TxOpts
from spl.token.constants import ASSOCIATED_TOKEN_PROGRAM_ID
from spl.token.instructions import create_associated_token_account, get_associated_token_address
import base58
from fake_useragent import UserAgent

from utils.trading.solana.trade_interface import TradeInterface, TradeResult, TradeType, TradeStatus
from beanie import PydanticObjectId
from utils.session import AsyncProxyClient
from utils.proxy.proxy_poll import Free<PERSON>roxy, Paid<PERSON><PERSON>xy, Proxy<PERSON><PERSON>, QgNetProxyPoll
from utils.spiders.smart_money import ProxyType, PROXYs

logger = logging.getLogger(__name__)

# SOL代币地址常量
SOL_MINT_ADDRESS = "So11111111111111111111111111111111111111112"

class QuoteResult(NamedTuple):
    """报价结果"""
    is_success: bool
    encoded_transaction: Optional[str] = None
    min_receive_amount: Optional[str] = None
    max_spend_amount: Optional[str] = None
    estimated_gas: Optional[str] = None
    price_impact: Optional[str] = None
    error_message: Optional[str] = None
    api_response: Optional[Dict[str, Any]] = None

class CachedApiResponse(NamedTuple):
    """缓存的API响应"""
    response: Dict[str, Any]
    timestamp: float

class OkxApiException(Exception):
    """OKX API 异常"""
    def __init__(self, message, error_code=None, provider_response=None):
        super().__init__(message)
        self.error_code = error_code
        self.provider_response = provider_response


class OkxTradeService(TradeInterface):
    """
    OKX DEX 交易服务 (优化版本)
    
    - 使用OKX直接swap API，避免复杂的指令处理和地址查找表问题
    - 自动检查并创建关联代币账户 (ATA)
    - 使用OKX官方API验证交易最终状态
    - 支持IP代理，避免IP限制问题
    """
    
    # 类变量，用于存储UserAgent实例
    _user_agent = None
    
    def __init__(self, channel_config: Dict[str, Any]):
        """
        初始化 OKX 交易服务
        
        Args:
            channel_config: 渠道配置，包含 api_host 和环境变量名称
        """
        self.channel_params = channel_config
        self.api_host = self.channel_params.get("api_host", "https://web3.okx.com")
        
        # 从环境变量加载 API 凭证
        api_key_env = self.channel_params.get("api_key_env", "OKX_API_KEY")
        api_secret_env = self.channel_params.get("api_secret_env", "OKX_API_SECRET")
        api_passphrase_env = self.channel_params.get("api_passphrase_env", "OKX_API_PASSPHRASE")
        
        self.api_key = os.getenv(api_key_env)
        self.api_secret = os.getenv(api_secret_env)
        self.passphrase = os.getenv(api_passphrase_env)
        
        if not all([self.api_key, self.api_secret, self.passphrase]):
            missing = []
            if not self.api_key:
                missing.append(api_key_env)
            if not self.api_secret:
                missing.append(api_secret_env)
            if not self.passphrase:
                missing.append(api_passphrase_env)
            raise ValueError(f"缺少必要的环境变量: {', '.join(missing)}")
        
        # 代理配置
        self.use_proxy = self.channel_params.get("use_proxy", True)  # 默认启用代理
        self.proxy_type = ProxyType(self.channel_params.get("proxy_type", "paid"))  # 默认使用付费代理
        self.max_retries = self.channel_params.get("max_retries", 3)  # 最大重试次数
        
        # 初始化 HTTP 客户端（延迟初始化，支持代理）
        self.http_client = None
        
        # 初始化 Solana RPC 客户端
        rpc_endpoint = self.channel_params.get("rpc_endpoint") or os.getenv("SOLANA_RPC_URL", "https://api.mainnet-beta.solana.com")
        
        # 为 Solana 客户端配置代理
        solana_proxy = None
        if self.use_proxy:
            # 获取代理配置
            proxy_pool = random.choice(PROXYs.free_proxy) if self.proxy_type == ProxyType.FREE else random.choice(PROXYs.paid_proxy)
            # 注意：这里需要同步获取代理，因为__init__不能是async
            # 我们将在后续的方法中重新初始化带代理的客户端
            pass
        
        self.solana_client = AsyncClient(rpc_endpoint, commitment=Confirmed, proxy=solana_proxy)
        
        # API响应缓存
        self._api_cache: Dict[str, CachedApiResponse] = {}
        self._cache_ttl_seconds = 10  # 缓存有效期10秒
        
        logger.info(f"OKX 交易服务初始化完成，API Host: {self.api_host}, 代理: {'启用' if self.use_proxy else '禁用'} ({self.proxy_type.value if self.use_proxy else 'N/A'}), 最大重试: {self.max_retries}")
    
    @classmethod
    def get_user_agent_instance(cls):
        """获取UserAgent实例（单例模式）"""
        if cls._user_agent is None:
            try:
                cls._user_agent = UserAgent()
            except Exception as e:
                logger.warning(f"创建UserAgent实例失败: {e}，使用备用User-Agent列表")
                # 备用User-Agent列表
                cls._user_agent = [
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:123.0) Gecko/20100101 Firefox/123.0",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.1.15",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Edge/*********",
                ]
        return cls._user_agent

    async def get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        ua = self.get_user_agent_instance()
        if isinstance(ua, list):
            return random.choice(ua)
        try:
            return ua.random
        except Exception as e:
            logger.warning(f"获取随机User-Agent失败: {e}，使用备用列表")
            self.__class__._user_agent = None  # 重置实例，下次重新创建
            return await self.get_random_user_agent()
    
    async def get_random_headers(self) -> dict:
        """生成随机请求头，适用于API请求"""
        ua = await self.get_random_user_agent()
        
        # 为API请求优化的请求头
        headers = {
            "user-agent": ua,
            "accept": "application/json, text/plain, */*",
            "accept-language": random.choice(["zh-CN,zh;q=0.9,en;q=0.8", "en-US,en;q=0.9", "zh-TW,zh;q=0.9,en;q=0.8"]),
            "accept-encoding": "gzip, deflate, br",
            "connection": random.choice(["keep-alive", "close"]),
            "cache-control": random.choice(["no-cache", "max-age=0"]),
        }
        
        return headers
    
    async def _init_http_client(self, use_proxy: bool = None) -> AsyncProxyClient:
        """初始化HTTP客户端，支持代理"""
        if use_proxy is None:
            use_proxy = self.use_proxy
            
        headers = await self.get_random_headers()
        
        if use_proxy:
            # 获取随机代理
            proxy_pool = random.choice(PROXYs.free_proxy) if self.proxy_type == ProxyType.FREE else random.choice(PROXYs.paid_proxy)
            proxy_info = await proxy_pool.get_proxy()
            logger.info(f"🌐 使用代理: {proxy_info.proxy_url}")
            
            return AsyncProxyClient(
                proxy_url=proxy_info.proxy_url,
                proxy_user=proxy_info.proxy_user,
                proxy_pass=proxy_info.proxy_pass,
                timeout=httpx.Timeout(30.0, connect=10.0),
                headers=headers
            )
        else:
            return AsyncProxyClient(
                timeout=httpx.Timeout(30.0, connect=10.0),
                headers=headers
            )
    
    async def _ensure_http_client(self):
        """确保HTTP客户端已初始化"""
        if self.http_client is None:
            self.http_client = await self._init_http_client()
    
    async def _switch_proxy(self):
        """切换到新的代理"""
        if self.http_client:
            try:
                await self.http_client.aclose()
            except Exception as e:
                logger.warning(f"关闭旧HTTP客户端时出错: {e}")
        
        self.http_client = await self._init_http_client(use_proxy=True)
        
        # 同时重新初始化Solana客户端的代理
        await self._init_solana_client_with_proxy()
        logger.info("🔄 已切换到新代理")
    
    async def _init_solana_client_with_proxy(self):
        """异步初始化Solana客户端的代理配置"""
        if not self.use_proxy:
            return
            
        try:
            # 关闭现有的Solana客户端
            if hasattr(self.solana_client, 'close'):
                await self.solana_client.close()
            
            # 获取代理配置
            proxy_pool = random.choice(PROXYs.free_proxy) if self.proxy_type == ProxyType.FREE else random.choice(PROXYs.paid_proxy)
            proxy_info = await proxy_pool.get_proxy()
            
            # 重新创建带代理的Solana客户端
            rpc_endpoint = self.channel_params.get("rpc_endpoint") or os.getenv("SOLANA_RPC_URL", "https://api.mainnet-beta.solana.com")
            self.solana_client = AsyncClient(
                rpc_endpoint, 
                commitment=Confirmed, 
                proxy=proxy_info.proxy_url,
                timeout=30.0
            )
            logger.info(f"🌐 Solana客户端已配置代理: {proxy_info.proxy_url}")
            
        except Exception as e:
            logger.warning(f"初始化Solana客户端代理时出错: {e}，将使用无代理模式")
            rpc_endpoint = self.channel_params.get("rpc_endpoint") or os.getenv("SOLANA_RPC_URL", "https://api.mainnet-beta.solana.com")
            self.solana_client = AsyncClient(rpc_endpoint, commitment=Confirmed)
    
    async def _ensure_solana_client_proxy(self):
        """确保Solana客户端已配置代理"""
        if self.use_proxy and not hasattr(self, '_solana_proxy_initialized'):
            await self._init_solana_client_with_proxy()
            self._solana_proxy_initialized = True
    
    async def _confirm_transaction_with_retry(
        self, 
        signature, 
        commitment="confirmed", 
        timeout=30, 
        max_retries=3
    ):
        """
        带有指数退避重试机制的交易确认
        
        Args:
            signature: 交易签名
            commitment: 确认级别
            timeout: 单次确认超时时间
            max_retries: 最大重试次数
            
        Returns:
            确认结果
            
        Raises:
            Exception: 重试耗尽后的最后一个异常
        """
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                # 确保使用代理
                await self._ensure_solana_client_proxy()
                
                # 执行交易确认
                result = await asyncio.wait_for(
                    self.solana_client.confirm_transaction(signature, commitment=commitment),
                    timeout=timeout
                )
                
                # 成功则直接返回
                return result
                
            except Exception as e:
                last_exception = e
                error_str = str(e).lower()
                
                # 检查是否是429错误或网络相关错误
                is_retryable = (
                    "429" in error_str or 
                    "too many requests" in error_str or
                    "timeout" in error_str or
                    "connection" in error_str or
                    "network" in error_str
                )
                
                if is_retryable and attempt < max_retries:
                    # 指数退避延迟
                    delay = min(2 ** attempt, 30)  # 最大30秒
                    logger.warning(
                        f"⚠️ Solana交易确认失败 (尝试 {attempt + 1}/{max_retries + 1}): {str(e)}，"
                        f"{delay}秒后重试..."
                    )
                    
                    # 如果是429错误或网络错误，切换代理
                    if self.use_proxy and ("429" in error_str or "connection" in error_str):
                        logger.info("🔄 检测到网络问题，切换Solana客户端代理...")
                        await self._init_solana_client_with_proxy()
                    
                    await asyncio.sleep(delay)
                    continue
                else:
                    # 不可重试的错误或重试耗尽
                    logger.error(f"❌ Solana交易确认最终失败: {str(e)}")
                    raise e
        
        # 理论上不会到达这里，但为了安全起见
        if last_exception:
            raise last_exception
        else:
            raise Exception("交易确认失败，原因未知")
    
    def _generate_cache_key(self, method: str, request_path: str, params: Optional[Dict[str, Any]] = None) -> str:
        """生成缓存键"""
        import hashlib
        cache_data = {
            "method": method,
            "path": request_path,
            "params": params or {}
        }
        cache_str = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_str.encode()).hexdigest()
    
    def _is_cache_valid(self, cached: CachedApiResponse) -> bool:
        """检查缓存是否有效"""
        current_time = time.time()
        return (current_time - cached.timestamp) < self._cache_ttl_seconds
    
    def _get_cached_response(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """获取缓存的响应"""
        if cache_key in self._api_cache:
            cached = self._api_cache[cache_key]
            if self._is_cache_valid(cached):
                logger.info(f"🚀 使用缓存的API响应 (缓存键: {cache_key[:8]}...)")
                return cached.response
            else:
                # 清理过期缓存
                del self._api_cache[cache_key]
        return None
    
    def _cache_response(self, cache_key: str, response_data: Dict[str, Any]) -> None:
        """缓存API响应"""
        self._api_cache[cache_key] = CachedApiResponse(
            response=response_data,
            timestamp=time.time()
        )
        logger.info(f"💾 缓存API响应 (缓存键: {cache_key[:8]}...)")
    
    def _generate_auth_headers(self, method: str, request_path: str, body: str = "") -> Dict[str, str]:
        """
        生成 OKX API 认证头
        
        Args:
            method: HTTP 方法 (GET, POST 等)
            request_path: 请求路径
            body: 请求体（通常为空字符串用于 GET 请求）
            
        Returns:
            Dict[str, str]: 认证头字典
        """
        # 获取当前 UTC 时间戳 (ISO 8601 格式)
        timestamp = datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
        
        # 创建待签名字符串
        pre_hash_string = timestamp + method.upper() + request_path + body
        
        # 使用 HMAC-SHA256 签名
        signature = base64.b64encode(
            hmac.new(
                self.api_secret.encode('utf-8'),
                pre_hash_string.encode('utf-8'),
                hashlib.sha256
            ).digest()
        ).decode('utf-8')
        
        return {
            'OK-ACCESS-KEY': self.api_key,
            'OK-ACCESS-SIGN': signature,
            'OK-ACCESS-TIMESTAMP': timestamp,
            'OK-ACCESS-PASSPHRASE': self.passphrase,
            'Content-Type': 'application/json'
        }
    
    async def _call_okx_api(
        self, 
        method: str, 
        request_path: str, 
        params: Optional[Dict[str, Any]] = None,
        body: Optional[Dict[str, Any]] = None,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        调用 OKX API（支持缓存和代理重试）
        
        Args:
            method: HTTP 方法
            request_path: 请求路径
            params: URL 参数
            body: 请求体
            use_cache: 是否使用缓存
            
        Returns:
            Dict[str, Any]: API 响应数据
            
        Raises:
            OkxApiException: API 调用失败
        """
        # 检查缓存
        cache_key = None
        if use_cache and method.upper() == "GET":
            cache_key = self._generate_cache_key(method, request_path, params)
            cached_response = self._get_cached_response(cache_key)
            if cached_response:
                return cached_response
        
        # 确保HTTP客户端已初始化
        await self._ensure_http_client()
        
        body_str = json.dumps(body) if body else ""
        
        # 对于 GET 请求，需要将查询参数附加到 request_path 用于签名
        sign_path = request_path
        if method.upper() == "GET" and params:
            # 构建查询字符串
            query_params = []
            for key, value in params.items():
                query_params.append(f"{key}={value}")
            query_string = "&".join(query_params)
            sign_path = f"{request_path}?{query_string}"
        
        url = self.api_host + request_path
        
        # 重试机制
        last_exception = None
        for attempt in range(self.max_retries + 1):
            try:
                # 生成认证头（每次重试都重新生成，因为包含时间戳）
                headers = self._generate_auth_headers(method, sign_path, body_str)
                
                logger.info(f"🌐 调用OKX API: {method} {url} (尝试 {attempt + 1}/{self.max_retries + 1})")
                
                if method.upper() == "GET":
                    response = await self.http_client.get(url, headers=headers, params=params)
                elif method.upper() == "POST":
                    response = await self.http_client.post(url, headers=headers, json=body)
                else:
                    raise ValueError(f"不支持的 HTTP 方法: {method}")
                
                response.raise_for_status()
                response_data = response.json()
                
                # 检查OKX API响应状态
                api_code = response_data.get("code", "")
                api_msg = response_data.get("msg", "")
                
                if api_code != "0":
                    # API调用失败
                    error_msg = f"OKX API 错误 [{api_code}]: {api_msg}"
                    raise OkxApiException(error_msg, error_code=api_code, provider_response=response_data)
                
                # 缓存成功响应
                if use_cache and cache_key and method.upper() == "GET":
                    self._cache_response(cache_key, response_data)
                
                if attempt > 0:
                    logger.info(f"✅ OKX API 请求成功 (重试 {attempt} 次): {request_path}")
                
                return response_data
                
            except (httpx.HTTPStatusError, httpx.ConnectError, httpx.TimeoutException, httpx.ProxyError) as e:
                last_exception = e
                
                # 判断是否需要重试
                should_retry = False
                if isinstance(e, httpx.HTTPStatusError):
                    # HTTP状态码错误，某些状态码可以重试
                    if e.response.status_code in [429, 502, 503, 504]:  # 限流或服务器错误
                        should_retry = True
                        logger.warning(f"OKX API HTTP {e.response.status_code} 错误，准备重试: {e.response.text}")
                elif isinstance(e, (httpx.ConnectError, httpx.TimeoutException, httpx.ProxyError)):
                    # 连接错误、超时或代理错误，可以重试
                    should_retry = True
                    logger.warning(f"OKX API 网络错误，准备重试: {str(e)}")
                
                if should_retry and attempt < self.max_retries:
                    logger.info(f"🔄 OKX API 第 {attempt + 1} 次重试 (共 {self.max_retries} 次): {request_path}")
                    
                    # 如果启用代理且是网络相关错误，切换代理
                    if self.use_proxy and isinstance(e, (httpx.ConnectError, httpx.TimeoutException, httpx.ProxyError)):
                        await self._switch_proxy()
                    
                    # 等待一段时间再重试
                    await asyncio.sleep(min(2 ** attempt, 10))  # 指数退避，最大10秒
                    continue
                else:
                    # 不需要重试或已达到最大重试次数
                    error_details = f"状态码: {e.response.status_code}, 响应: {e.response.text}" if isinstance(e, httpx.HTTPStatusError) else str(e)
                    logger.error(f"OKX API 请求失败: {error_details}")
                    raise OkxApiException(f"HTTP/网络错误: {error_details}")
            
            except httpx.RequestError as e:
                last_exception = e
                error_details = f"请求类型: {type(e).__name__}, 错误: {str(e)}"
                logger.error(f"OKX API 请求错误详情: {error_details}")
                
                # 对于请求错误，如果启用代理可以尝试切换代理重试
                if self.use_proxy and attempt < self.max_retries:
                    logger.info(f"🔄 OKX API 遇到请求错误，尝试切换代理重试 (第 {attempt + 1} 次): {request_path}")
                    await self._switch_proxy()
                    await asyncio.sleep(min(2 ** attempt, 10))
                    continue
                else:
                    raise OkxApiException(f"请求错误: {error_details}")
            
            except json.JSONDecodeError as e:
                logger.error(f"OKX API 响应解析错误: {e}")
                raise OkxApiException(f"响应解析错误: {str(e)}")
            except Exception as e:
                last_exception = e
                error_details = f"未预期错误 ({type(e).__name__}): {str(e)}"
                logger.error(f"OKX API 调用时发生未预期错误: {error_details}", exc_info=True)
                
                # 对于其他异常，如果启用代理可以尝试切换代理重试
                if self.use_proxy and attempt < self.max_retries:
                    logger.info(f"🔄 OKX API 遇到异常，尝试切换代理重试 (第 {attempt + 1} 次): {request_path}")
                    await self._switch_proxy()
                    await asyncio.sleep(min(2 ** attempt, 10))
                    continue
                else:
                    raise OkxApiException(f"未预期错误: {error_details}")
        
        # 如果所有重试都失败了
        if last_exception:
            if isinstance(last_exception, OkxApiException):
                raise last_exception
            else:
                raise OkxApiException(f"请求失败，已重试 {self.max_retries} 次: {str(last_exception)}")
        else:
            raise OkxApiException(f"请求失败，已重试 {self.max_retries} 次")

    async def _ensure_associated_token_account(self, wallet_keypair: Keypair, mint_address: Pubkey) -> bool:
        """
        确保指定的关联代币账户(ATA)存在，如果不存在则创建。

        Args:
            wallet_keypair: 用于支付和所有权的钱包密钥对。
            mint_address: 代币的mint地址。

        Returns:
            bool: 如果账户已存在或成功创建，则返回True。
        
        Raises:
            Exception: 如果创建账户失败。
        """
        owner_pubkey = wallet_keypair.pubkey()
        ata_address = get_associated_token_address(owner_pubkey, mint_address)
        logger.info(f"检查代币账户 (ATA) for mint {mint_address} -> Owner {owner_pubkey} -> ATA {ata_address}")
        
        # 检查账户是否存在
        account_info = await self.solana_client.get_account_info(ata_address, commitment=Commitment("confirmed"))
        if account_info.value is not None:
            logger.info(f"✅ 代币账户 {ata_address} 已存在。")
            return True
            
        logger.warning(f"⚠️ 代币账户 {ata_address} 不存在，准备创建...")
        
        try:
            # 🔥 正确、简单的方式: 使用 Transaction.new_with_payer
            instruction = create_associated_token_account(
                payer=owner_pubkey,
                owner=owner_pubkey,
                mint=mint_address
            )

            # 获取最新的blockhash（确保是最新的，避免过期）
            # 实现blockhash重试机制
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    logger.info(f"  🔄 获取最新blockhash用于ATA创建... (尝试 {attempt + 1}/{max_retries})")
                    blockhash_resp = await self.solana_client.get_latest_blockhash(commitment="finalized")
                    if not blockhash_resp.value:
                        raise Exception("无法获取最新blockhash以创建ATA")
                    
                    logger.info(f"  🏷️ 使用blockhash: {blockhash_resp.value.blockhash}")
                    
                    # 创建包含blockhash的Message对象
                    message = Message.new_with_blockhash(
                        instructions=[instruction],
                        payer=owner_pubkey,
                        blockhash=blockhash_resp.value.blockhash
                    )
                    # 使用Message创建交易
                    transaction = Transaction.new_unsigned(message)
                    
                    # 签名
                    transaction.sign([wallet_keypair], blockhash_resp.value.blockhash)

                    logger.info(f"🚀 发送创建ATA的交易... (尝试 {attempt + 1}/{max_retries})")
                    # 注意：这里我们发送的是旧版的Transaction，对于简单的ATA创建是完全可以的
                    send_result = await self.solana_client.send_transaction(transaction, opts=TxOpts(skip_preflight=False))
                    break  # 成功则跳出重试循环
                    
                except Exception as e:
                    error_str = str(e)
                    if "Blockhash not found" in error_str or "BlockhashNotFound" in error_str:
                        if attempt < max_retries - 1:
                            logger.warning(f"  ⚠️ Blockhash过期，重试获取新的blockhash... (尝试 {attempt + 1}/{max_retries})")
                            await asyncio.sleep(0.5)  # 短暂等待
                            continue
                        else:
                            logger.error(f"  ❌ 重试{max_retries}次后仍然失败: {e}")
                            raise
                    else:
                        # 非blockhash相关错误，直接抛出
                        raise
            tx_hash = str(send_result.value)
            logger.info(f"  💎 创建ATA交易哈希: {tx_hash}")
            logger.info(f"  🔗 Solscan: https://solscan.io/tx/{tx_hash}")

            # 🔥🔥🔥 关键修复: 等待ATA创建交易被最终确认
            logger.info(f"  ⏳ 等待ATA创建交易( {tx_hash} )被最终确认...")
            await self.solana_client.confirm_transaction(
                send_result.value,
                commitment="finalized"
            )

            logger.info(f"✅ 代币账户 {ata_address} 创建成功!")
            return True
        
        except Exception as e:
            logger.error(f"❌ 创建代币账户 {ata_address} 失败: {e}")
            # 可以在这里打印更详细的traceback
            # import traceback
            # traceback.print_exc()
            raise Exception(f"创建关联代币账户失败: {e}")
    
    async def get_quote(
        self,
        input_token_address: str,
        output_token_address: str,
        amount_input_token: float,
        slippage: float,
        wallet_address: str
    ) -> QuoteResult:
        """
        获取交易报价（使用OKX直接swap API）
        
        Args:
            input_token_address: 输入代币地址
            output_token_address: 输出代币地址
            amount_input_token: 输入代币数量
            slippage: 滑点（小数形式，例如0.01表示1%）
            wallet_address: 钱包地址
            
        Returns:
            QuoteResult: 报价结果
        """
        try:
            logger.info(f"📋 获取OKX DEX报价: {amount_input_token} {input_token_address} -> {output_token_address}")
            
            # 获取输入代币的真实精度信息
            if input_token_address == SOL_MINT_ADDRESS:
                input_decimals = 9  # SOL精度固定为9
            else:
                # 使用TokenInfo获取真实的代币精度
                from utils.spiders.solana.token_info import TokenInfo
                token_info_instance = TokenInfo(input_token_address)
                token_info = await token_info_instance.get_token_info()
                if token_info and 'decimals' in token_info:
                    input_decimals = token_info['decimals']
                    logger.info(f"📊 获取到代币 {input_token_address} 的精度: {input_decimals}")
                else:
                    input_decimals = 6  # 默认精度
                    logger.warning(f"⚠️ 无法获取代币 {input_token_address} 的精度信息，使用默认精度: {input_decimals}")
            
            # 将金额转换为最小单位（lamports或最小单位）
            amount_lamports = int(amount_input_token * (10 ** input_decimals))
            
            # 🔥 关键修改：使用直接swap API而不是swap-instruction API
            params = {
                "chainIndex": "501",  # Solana
                "chainId": "501",     # 向后兼容
                "fromTokenAddress": input_token_address,
                "toTokenAddress": output_token_address,
                "amount": str(amount_lamports),
                "slippage": str(slippage),
                "userWalletAddress": wallet_address,
                "swapMode": "exactIn"
            }
            
            logger.info(f"🌐 调用OKX直接swap API")
            api_response = await self._call_okx_api(
                method="GET",
                request_path="/api/v5/dex/aggregator/swap",  # 使用直接swap API
                params=params,
                use_cache=True
            )
            
            # 解析响应
            data = api_response.get("data")
            if not data or len(data) == 0:
                error_msg = "❌ 交易失败：OKX Swap API返回空数据"
                logger.error(error_msg)
                return QuoteResult(
                    is_success=False,
                    error_message=error_msg,
                    api_response=api_response
                )
            
            # OKX直接swap API返回的数据结构
            swap_data = data[0]  # data是数组，取第一个元素
            tx_data = swap_data.get("tx", {})
            router_result = swap_data.get("routerResult", {})
            
            # 检查是否有完整的交易数据
            encoded_tx = tx_data.get("data")
            if not encoded_tx:
                error_msg = "❌ 交易数据为空：OKX API未返回完整交易数据"
                logger.error(error_msg)
                return QuoteResult(
                    is_success=False,
                    error_message=error_msg,
                    api_response=api_response
                )
            
            # 提取关键信息
            min_receive_amount = tx_data.get("minReceiveAmount")
            max_spend_amount = tx_data.get("maxSpendAmount")
            estimated_gas = tx_data.get("gas")
            price_impact = router_result.get("priceImpactPercentage")
            
            logger.info(f"✅ 获取OKX DEX报价成功")
            logger.info(f"  💸 最大花费: {max_spend_amount} 最小单位")
            logger.info(f"  💰 最小接收: {min_receive_amount} 最小单位")
            logger.info(f"  ⛽ 预估gas: {estimated_gas}")
            logger.info(f"  📈 价格影响: {price_impact}%")
            
            return QuoteResult(
                is_success=True,
                encoded_transaction=encoded_tx,
                min_receive_amount=min_receive_amount,
                max_spend_amount=max_spend_amount,
                estimated_gas=estimated_gas,
                price_impact=price_impact,
                api_response=api_response
            )
            
        except Exception as e:
            error_msg = f"获取报价失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return QuoteResult(
                is_success=False,
                error_message=error_msg
            )
    
    async def _update_transaction_blockhash(
        self, 
        encoded_tx: str, 
        wallet_keypair: Keypair,
        force_refresh: bool = False
    ) -> VersionedTransaction:
        """
        更新交易的blockhash并重新签名
        
        Args:
            encoded_tx: 编码的交易数据（base58）
            wallet_keypair: 钱包密钥对
            force_refresh: 是否强制刷新blockhash
            
        Returns:
            VersionedTransaction: 更新后的交易
        """
        try:
            # 解码原始交易
            tx_bytes = base58.b58decode(encoded_tx)
            original_tx = VersionedTransaction.from_bytes(tx_bytes)
            
            logger.info(f"✅ 原始交易解析成功")
            logger.info(f"  📋 指令数量: {len(original_tx.message.instructions)}")
            logger.info(f"  🗂️ 使用地址查找表: {len(original_tx.message.address_table_lookups) > 0}")
            
            # 获取最新blockhash（每次都获取最新的）
            blockhash_response = await self.solana_client.get_latest_blockhash()
            if not blockhash_response.value:
                raise Exception("无法获取最新blockhash")
            
            latest_blockhash = blockhash_response.value.blockhash
            original_message = original_tx.message
            
            logger.info(f"  🏷️ 原始blockhash: {original_message.recent_blockhash}")
            logger.info(f"  🆕 新blockhash: {latest_blockhash}")
            
            # 检查blockhash是否已经是最新的
            if not force_refresh and original_message.recent_blockhash == latest_blockhash:
                logger.info(f"  ✅ Blockhash已是最新，无需更新")
                return original_tx
            
            # 构造新的MessageV0（包含header参数）
            new_message = MessageV0(
                header=original_message.header,
                account_keys=original_message.account_keys,
                recent_blockhash=latest_blockhash,  # 使用最新的blockhash
                instructions=original_message.instructions,
                address_table_lookups=original_message.address_table_lookups
            )
            
            # 使用我们的私钥重新签名交易
            updated_tx = VersionedTransaction(new_message, [wallet_keypair])
            
            logger.info(f"✅ 交易blockhash更新并重新签名完成")
            return updated_tx
            
        except Exception as e:
            error_msg = f"更新交易blockhash失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise OkxApiException(error_msg)
    
    async def get_trade_status_from_okx(self, tx_hash: str) -> Dict[str, Any]:
        """
        使用OKX官方API查询交易状态。

        Args:
            tx_hash: 交易哈希。

        Returns:
            Dict: 包含交易状态信息的字典。
        """
        logger.info(f"🔍 使用OKX API查询交易状态: {tx_hash}")
        try:
            params = {
                "chainIndex": "501", # Solana
                "txHash": tx_hash
            }
            response = await self._call_okx_api(
                method="GET",
                request_path="/api/v5/dex/aggregator/history",
                params=params,
                use_cache=False # 状态查询不应使用缓存
            )
            # OKX API 成功时 data 是一个列表
            if response.get("data") and isinstance(response.get("data"), list) and len(response["data"]) > 0:
                trade_info = response["data"][0]
                logger.info(f"✅ OKX API 状态查询成功: {trade_info.get('state')}")
                return trade_info
            else:
                logger.warning(f"⚠️ OKX API 未找到交易 {tx_hash} 的历史记录。响应: {response}")
                return {"error": "Trade not found in OKX history", "state": "not_found"}

        except OkxApiException as e:
            logger.error(f"❌ OKX API 状态查询失败: {e}")
            return {"error": str(e), "state": "api_error"}


    async def execute_trade(
        self,
        trade_type: TradeType,
        input_token_address: str,
        output_token_address: str,
        amount_input_token: float,
        wallet_private_key_b58: str,
        wallet_address: str,
        strategy_snapshot: Dict[str, Any],
        signal_id: PydanticObjectId,
        trade_record_id: PydanticObjectId,
        cached_quote: Optional[QuoteResult] = None
    ) -> TradeResult:
        """
        执行交易操作（使用OKX直接swap API）
        
        Args:
            trade_type: 交易类型 (BUY 或 SELL)
            input_token_address: 输入代币地址
            output_token_address: 输出代币地址
            amount_input_token: 输入代币数量
            wallet_private_key_b58: 钱包私钥
            wallet_address: 钱包地址
            strategy_snapshot: 策略快照
            signal_id: 信号ID
            trade_record_id: 交易记录ID
            cached_quote: 可选，已缓存的报价结果（避免重复API调用）
            
        Returns:
            TradeResult: 交易结果
        """
        start_time = datetime.now(timezone.utc)
        
        try:
            # 确保Solana客户端已配置代理
            await self._ensure_solana_client_proxy()
            
            logger.info(f"[TradeRec:{trade_record_id}] 开始执行 OKX DEX Swap 交易: {trade_type.value} "
                       f"{amount_input_token} {input_token_address} -> {output_token_address}")
            
            # 解析钱包私钥
            wallet_keypair = Keypair.from_base58_string(wallet_private_key_b58)
            
            # 获取滑点配置
            slippage = strategy_snapshot.get("buy_slippage_percentage", 1.0) / 100.0  # 转换为小数
            
            # 获取交易数据（使用缓存或新调用）
            quote_result = cached_quote
            if not quote_result:
                logger.info(f"[TradeRec:{trade_record_id}] 没有缓存报价，获取新交易数据")
                quote_result = await self.get_quote(
                    input_token_address=input_token_address,
                    output_token_address=output_token_address,
                    amount_input_token=amount_input_token,
                    slippage=slippage,
                    wallet_address=wallet_address
                )
            else:
                logger.info(f"[TradeRec:{trade_record_id}] 🚀 使用缓存的交易数据")
            
            if not quote_result.is_success:
                error_msg = f"获取交易数据失败: {quote_result.error_message}"
                return TradeResult(
                    status=TradeStatus.FAILED,
                    error_message=error_msg,
                    executed_at=start_time
                )
            
            # 检查是否有完整的交易数据
            if not quote_result.encoded_transaction:
                error_msg = "❌ 交易失败：缺少编码的交易数据"
                return TradeResult(
                    status=TradeStatus.FAILED,
                    error_message=error_msg,
                    provider_response_raw=quote_result.api_response,
                    executed_at=start_time
                )
            
            # 🔥 核心修复：确保所有涉及的代币的ATA账户都存在
            if trade_type == TradeType.BUY:
                logger.info(f"[TradeRec:{trade_record_id}] 确保所有交易路径中的ATA账户都存在...")
                # 解析交易找出所有涉及的账户
                tx_bytes = base58.b58decode(quote_result.encoded_transaction)
                original_tx = VersionedTransaction.from_bytes(tx_bytes)
                all_involved_accounts = set(original_tx.message.account_keys)
                
                # 从 provider_response_raw 中提取所有代币地址
                all_tokens_in_route = set()
                if quote_result.api_response:
                    try:
                        data = quote_result.api_response.get("data", [])
                        if data and isinstance(data, list):
                            sub_router_list = data[0].get("routerResult", {}).get("dexRouterList", [{}])[0].get("subRouterList", [])
                            for route in sub_router_list:
                                if "fromToken" in route and "tokenContractAddress" in route["fromToken"]:
                                    all_tokens_in_route.add(route["fromToken"]["tokenContractAddress"])
                                if "toToken" in route and "tokenContractAddress" in route["toToken"]:
                                    all_tokens_in_route.add(route["toToken"]["tokenContractAddress"])
                    except Exception as e:
                        logger.warning(f"无法从API响应中解析路由代币: {e}")

                # 添加我们已知的输入和输出代币
                all_tokens_in_route.add(input_token_address)
                all_tokens_in_route.add(output_token_address)
                # wSOL也是一个常见的中间代币
                all_tokens_in_route.add("So11111111111111111111111111111111111111112")
                
                logger.info(f"交易路径中识别出的代币地址: {all_tokens_in_route}")

                for mint_str in all_tokens_in_route:
                    # 在Solana DeFi交易中，即使输入是原生SOL，WSOL仍然需要ATA账户
                    # 因此我们不应该跳过任何代币的ATA创建，包括WSOL
                    # 注释掉原来错误的跳过逻辑
                    # if mint_str == SOL_MINT_ADDRESS and input_token_address == SOL_MINT_ADDRESS and output_token_address != SOL_MINT_ADDRESS:
                    #     logger.info(f"跳过SOL原生代币的ATA创建（输入代币为SOL，当前处理代币也是SOL）")
                    #     continue
                    try:
                        mint_pubkey = Pubkey.from_string(mint_str)
                        await self._ensure_associated_token_account(wallet_keypair, mint_pubkey)
                    except Exception as e:
                        logger.error(f"为 mint {mint_str} 准备ATA账户时失败: {e}")
                        # 即使一个失败，也继续尝试其他的，因为可能不是所有地址都需要
                        continue
            
            # 🔥 新增：检查WSOL账户余额并自动转换
            logger.info(f"[TradeRec:{trade_record_id}] 检查WSOL账户余额...")
            try:
                wsol_ata = get_associated_token_address(wallet_keypair.pubkey(), Pubkey.from_string(SOL_MINT_ADDRESS))
                balance_resp = await self.solana_client.get_token_account_balance(wsol_ata)
                
                wsol_balance = 0
                if balance_resp.value and balance_resp.value.amount:
                    wsol_balance = int(balance_resp.value.amount)
                
                required_amount = int(quote_result.max_spend_amount) if quote_result.max_spend_amount else 0
                
                logger.info(f"  💰 WSOL账户余额: {wsol_balance} 最小单位")
                logger.info(f"  💸 需要金额: {required_amount} 最小单位")
                
                if wsol_balance < required_amount:
                    # 计算需要转换的SOL数量（添加10%缓冲）
                    shortage = required_amount - wsol_balance
                    amount_to_wrap = int(shortage * 1.1)  # 添加10%缓冲
                    
                    logger.info(f"[TradeRec:{trade_record_id}] 🔄 WSOL余额不足，自动转换 {amount_to_wrap} lamports SOL 为 WSOL")
                    
                    # 检查SOL余额是否足够
                    sol_balance_resp = await self.solana_client.get_balance(wallet_keypair.pubkey())
                    sol_balance = sol_balance_resp.value if sol_balance_resp.value else 0
                    
                    if sol_balance < amount_to_wrap + 10000:  # 预留10000 lamports作为交易费
                        error_msg = f"SOL余额不足以进行自动转换: 当前SOL余额 {sol_balance} lamports，需要 {amount_to_wrap + 10000} lamports（包含交易费）"
                        logger.error(f"[TradeRec:{trade_record_id}] ❌ {error_msg}")
                        return TradeResult(
                            status=TradeStatus.FAILED,
                            error_message=error_msg,
                            provider_response_raw=quote_result.api_response,
                            executed_at=start_time
                        )
                    
                    # 执行自动SOL到WSOL转换
                    # 将 lamports 转换为 SOL（除以 1_000_000_000）
                    amount_sol = amount_to_wrap / 1_000_000_000
                    # 使用完整的64字节私钥（32字节seed + 32字节公钥）
                    full_private_key = bytes(wallet_keypair.secret()) + bytes(wallet_keypair.pubkey())
                    wallet_private_key_b58 = base58.b58encode(full_private_key).decode('utf-8')
                    wallet_address_str = str(wallet_keypair.pubkey())
                    wrap_success = await self._auto_wrap_sol_to_wsol(amount_sol, wallet_private_key_b58, wallet_address_str)
                    if not wrap_success:
                        error_msg = "自动SOL到WSOL转换失败"
                        logger.error(f"[TradeRec:{trade_record_id}] ❌ {error_msg}")
                        return TradeResult(
                            status=TradeStatus.FAILED,
                            error_message=error_msg,
                            provider_response_raw=quote_result.api_response,
                            executed_at=start_time
                        )
                    
                    logger.info(f"[TradeRec:{trade_record_id}] ✅ SOL自动转换为WSOL成功")
                else:
                    logger.info(f"  ✅ WSOL余额充足")
                    
            except Exception as balance_error:
                logger.warning(f"[TradeRec:{trade_record_id}] ⚠️ 检查WSOL余额时出错: {balance_error}，继续执行交易")
            
            # 🔥 关键步骤：更新交易的blockhash并重新签名
            logger.info(f"[TradeRec:{trade_record_id}] 🔍 解码并更新交易数据...")
            final_tx = await self._update_transaction_blockhash(
                quote_result.encoded_transaction, 
                wallet_keypair
            )
            
            # 交易预检查（模拟执行）- 在模拟前再次刷新blockhash
            logger.info(f"[TradeRec:{trade_record_id}] 🧪 交易预检查（模拟执行）...")
            try:
                # 在模拟执行前再次刷新blockhash，确保使用最新的blockhash
                logger.info(f"[TradeRec:{trade_record_id}] 🔄 模拟执行前刷新blockhash...")
                final_tx = await self._update_transaction_blockhash(
                    quote_result.encoded_transaction, 
                    wallet_keypair,
                    force_refresh=True  # 强制刷新
                )
                
                simulate_result = await self.solana_client.simulate_transaction(final_tx)
                if simulate_result.value.err:
                    error_str = str(simulate_result.value.err)
                    logger.error(f"[TradeRec:{trade_record_id}] ❌ 交易预检查失败: {simulate_result.value.err}")
                    if simulate_result.value.logs:
                        logger.error(f"[TradeRec:{trade_record_id}] 💥 预检查错误日志:")
                        for log in simulate_result.value.logs[-5:]:
                            logger.error(f"    {log}")
                    
                    # 检查是否为特定的Token程序错误
                    error_message = f"Transaction simulation failed: {simulate_result.value.err}"
                    
                    # 检查是否为 custom program error: 0x1 (余额不足)
                    if "custom program error: 0x1" in error_str.lower():
                        error_message = "Insufficient token balance for transaction (custom program error: 0x1)"
                        logger.error(f"[TradeRec:{trade_record_id}] 💰 检测到余额不足错误 (0x1)")
                    
                    # 检查是否为其他常见的Token程序错误
                    elif "tokenkegqfezyinwajbnbgkpfxcwubvf9ss623vq5da" in error_str.lower():
                        if "0x0" in error_str:
                            error_message = "Token program error: Lamport balance below rent-exempt threshold"
                        elif "0x3" in error_str:
                            error_message = "Token program error: Owner does not match"
                        elif "0x5" in error_str:
                            error_message = "Token program error: Fixed supply"
                        elif "0x6" in error_str:
                            error_message = "Token program error: Already in use"
                        else:
                            error_message = f"Token program error: {simulate_result.value.err}"
                        logger.error(f"[TradeRec:{trade_record_id}] 🔧 检测到Token程序错误")
                    
                    return TradeResult(
                        status=TradeStatus.FAILED,
                        error_message=error_message,
                        provider_response_raw=quote_result.api_response,
                        executed_at=start_time
                    )
                else:
                    logger.info(f"[TradeRec:{trade_record_id}] ✅ 交易预检查通过!")
                    logger.info(f"  ⚡ 预估计算单元: {simulate_result.value.units_consumed:,}")
                    
            except Exception as sim_error:
                logger.warning(f"[TradeRec:{trade_record_id}] ⚠️ 交易预检查出错，但继续执行: {sim_error}")
            
            # 执行交易（发送到区块链）- 增强重试机制
            logger.info(f"[TradeRec:{trade_record_id}] 🚀 执行交易（发送到区块链）...")
            
            # 交易发送重试配置
            max_send_retries = 5  # 增加发送重试次数
            max_confirm_retries = 3  # 确认重试次数
            send_retry_delay = 2  # 发送重试延迟（秒）
            confirm_timeout = 90  # 确认超时时间（秒）
            
            tx_hash = None
            send_success = False
            
            # 交易发送重试循环
            for send_attempt in range(max_send_retries):
                try:
                    logger.info(f"[TradeRec:{trade_record_id}] 🔄 交易发送尝试 {send_attempt + 1}/{max_send_retries}")
                    
                    # 每次发送前都刷新blockhash，确保使用最新的blockhash
                    logger.info(f"[TradeRec:{trade_record_id}] 🔄 刷新blockhash (发送尝试 {send_attempt + 1})...")
                    final_tx = await self._update_transaction_blockhash(
                        quote_result.encoded_transaction, 
                        wallet_keypair,
                        force_refresh=True  # 强制刷新
                    )
                    
                    # 配置交易选项
                    tx_opts = TxOpts(
                        skip_preflight=False,
                        skip_confirmation=False,
                        preflight_commitment=Confirmed,
                        max_retries=2  # Solana客户端内部重试
                    )
                    
                    # 发送交易
                    send_result = await self.solana_client.send_transaction(final_tx, opts=tx_opts)
                    
                    if send_result.value:
                        tx_hash = str(send_result.value)
                        logger.info(f"[TradeRec:{trade_record_id}] 🎉 交易发送成功! (尝试 {send_attempt + 1})")
                        logger.info(f"  💎 交易哈希: {tx_hash}")
                        logger.info(f"  🔗 Solscan链接: https://solscan.io/tx/{tx_hash}")
                        send_success = True
                        break
                    else:
                        logger.warning(f"[TradeRec:{trade_record_id}] ⚠️ 交易发送失败，未返回交易哈希 (尝试 {send_attempt + 1})")
                        
                except Exception as send_error:
                    logger.warning(f"[TradeRec:{trade_record_id}] ⚠️ 交易发送异常 (尝试 {send_attempt + 1}): {str(send_error)}")
                    
                    # 检查是否为不可重试的错误
                    error_str = str(send_error).lower()
                    non_retryable_errors = [
                        "insufficient funds",
                        "account not found",
                        "invalid signature",
                        "invalid account",
                        "program error",
                        "custom program error: 0x1",  # Token程序余额不足错误
                        "custom program error: 0x0",  # Lamport余额低于免租阈值
                        "custom program error: 0x3",  # 所有者不匹配
                        "custom program error: 0x5",  # 固定供应量
                        "custom program error: 0x6"   # 已在使用中
                    ]
                    
                    # 特殊处理 custom program error: 0x1
                    if "custom program error: 0x1" in error_str:
                        logger.error(f"[TradeRec:{trade_record_id}] 💰 检测到Token余额不足错误 (0x1)")
                        return TradeResult(
                            status=TradeStatus.FAILED,
                            error_message="Insufficient token balance for transaction (custom program error: 0x1)",
                            provider_response_raw=quote_result.api_response,
                            executed_at=start_time
                        )
                    
                    if any(err in error_str for err in non_retryable_errors):
                        logger.error(f"[TradeRec:{trade_record_id}] ❌ 检测到不可重试错误: {send_error}")
                        return TradeResult(
                            status=TradeStatus.FAILED,
                            error_message=f"Non-retryable transaction error: {str(send_error)}",
                            provider_response_raw=quote_result.api_response,
                            executed_at=start_time
                        )
                
                # 如果不是最后一次尝试，等待后重试
                if send_attempt < max_send_retries - 1:
                    retry_delay = send_retry_delay * (2 ** send_attempt)  # 指数退避
                    logger.info(f"[TradeRec:{trade_record_id}] ⏳ 等待 {retry_delay} 秒后重试发送...")
                    await asyncio.sleep(retry_delay)
            
            # 检查发送是否成功
            if not send_success or not tx_hash:
                error_msg = f"交易发送失败，已重试 {max_send_retries} 次"
                logger.error(f"[TradeRec:{trade_record_id}] ❌ {error_msg}")
                return TradeResult(
                    status=TradeStatus.FAILED,
                    error_message=error_msg,
                    provider_response_raw=quote_result.api_response,
                    executed_at=start_time
                )
            
            # 交易确认重试循环
            logger.info(f"[TradeRec:{trade_record_id}] ⏳ 等待交易确认...")
            
            for confirm_attempt in range(max_confirm_retries):
                try:
                    logger.info(f"[TradeRec:{trade_record_id}] 🔄 交易确认尝试 {confirm_attempt + 1}/{max_confirm_retries}")
                    
                    # 确保Solana客户端使用代理
                    await self._ensure_solana_client_proxy()
                    
                    # 等待交易确认，带有429错误重试机制
                    confirm_result = await self._confirm_transaction_with_retry(
                        send_result.value, 
                        commitment=Confirmed,
                        timeout=confirm_timeout,
                        max_retries=3
                    )
                    
                    # 添加轮询间隔，避免频繁请求
                    await asyncio.sleep(2.0)
                    
                    # 查询OKX状态
                    okx_status_info = await self.get_trade_status_from_okx(tx_hash)
                    
                    if confirm_result.value[0].err:
                        logger.error(f"[TradeRec:{trade_record_id}] ❌ 交易确认失败 (尝试 {confirm_attempt + 1}): {confirm_result.value[0].err}")
                        
                        # 如果是最后一次尝试，返回失败
                        if confirm_attempt == max_confirm_retries - 1:
                            return TradeResult(
                                status=TradeStatus.FAILED,
                                tx_hash=tx_hash,
                                error_message=f"Transaction failed on-chain after {max_confirm_retries} confirmation attempts: {confirm_result.value[0].err}",
                                provider_response_raw=okx_status_info,
                                executed_at=start_time
                            )
                    else:
                        logger.info(f"[TradeRec:{trade_record_id}] 🎊 交易确认成功! (尝试 {confirm_attempt + 1})")
                        
                        # 从报价结果中提取实际金额信息
                        actual_amount_in = quote_result.max_spend_amount
                        actual_amount_out = quote_result.min_receive_amount
                        
                        return TradeResult(
                            status=TradeStatus.SUCCESS,
                            tx_hash=tx_hash,
                            actual_amount_in=actual_amount_in,
                            actual_amount_out=actual_amount_out,
                            provider_response_raw=okx_status_info,
                            executed_at=start_time
                        )
                        
                except asyncio.TimeoutError:
                    logger.warning(f"[TradeRec:{trade_record_id}] ⚠️ 交易确认超时 (尝试 {confirm_attempt + 1}/{max_confirm_retries})")
                    
                    # 如果是最后一次尝试，检查OKX状态并返回
                    if confirm_attempt == max_confirm_retries - 1:
                        await asyncio.sleep(2.0)
                        okx_status_info = await self.get_trade_status_from_okx(tx_hash)
                        
                        # 检查OKX状态来判断交易是否真的成功
                        okx_state = okx_status_info.get('state', '').lower()
                        if okx_state in ['success', 'completed', 'confirmed']:
                            logger.info(f"[TradeRec:{trade_record_id}] ✅ 虽然确认超时，但OKX显示交易成功")
                            return TradeResult(
                                status=TradeStatus.SUCCESS,
                                tx_hash=tx_hash,
                                actual_amount_in=quote_result.max_spend_amount,
                                actual_amount_out=quote_result.min_receive_amount,
                                error_message="Transaction confirmation timed out, but OKX shows success",
                                provider_response_raw=okx_status_info,
                                executed_at=start_time
                            )
                        else:
                            logger.warning(f"[TradeRec:{trade_record_id}] ⚠️ 确认超时且OKX状态不明确: {okx_state}")
                            return TradeResult(
                                status=TradeStatus.SUCCESS,  # 保守处理，假设成功
                                tx_hash=tx_hash,
                                error_message=f"Transaction confirmation timed out after {max_confirm_retries} attempts, status unclear",
                                provider_response_raw=okx_status_info,
                                executed_at=start_time
                            )
                
                except Exception as confirm_error:
                    logger.warning(f"[TradeRec:{trade_record_id}] ⚠️ 交易确认异常 (尝试 {confirm_attempt + 1}): {str(confirm_error)}")
                    
                    # 如果是最后一次尝试，返回结果
                    if confirm_attempt == max_confirm_retries - 1:
                        await asyncio.sleep(2.0)
                        okx_status_info = await self.get_trade_status_from_okx(tx_hash)
                        
                        return TradeResult(
                            status=TradeStatus.SUCCESS,  # 保守处理
                            tx_hash=tx_hash,
                            error_message=f"Transaction confirmation error after {max_confirm_retries} attempts: {str(confirm_error)}",
                            provider_response_raw=okx_status_info,
                            executed_at=start_time
                        )
                
                # 确认重试间隔
                if confirm_attempt < max_confirm_retries - 1:
                    confirm_retry_delay = 5 * (confirm_attempt + 1)  # 递增延迟
                    logger.info(f"[TradeRec:{trade_record_id}] ⏳ 等待 {confirm_retry_delay} 秒后重试确认...")
                    await asyncio.sleep(confirm_retry_delay)
            
            # 理论上不应该到达这里
            logger.error(f"[TradeRec:{trade_record_id}] ❌ 交易确认流程异常结束")
            return TradeResult(
                status=TradeStatus.FAILED,
                tx_hash=tx_hash,
                error_message="Transaction confirmation process ended unexpectedly",
                executed_at=start_time
            )
                
        except OkxApiException as e:
            error_msg = f"OKX API 错误: {str(e)}"
            logger.error(f"[TradeRec:{trade_record_id}] {error_msg}")
            
            return TradeResult(
                status=TradeStatus.FAILED,
                error_message=error_msg,
                executed_at=start_time
            )
            
        except Exception as e:
            error_msg = f"交易执行失败: {str(e)}"
            logger.error(f"[TradeRec:{trade_record_id}] {error_msg}", exc_info=True)
            
            return TradeResult(
                status=TradeStatus.FAILED,
                error_message=error_msg,
                executed_at=start_time
            )
    
    async def _auto_wrap_sol_to_wsol(self, amount_sol: float, wallet_private_key_b58: str, wallet_address: str) -> bool:
        """
        自动将 SOL 转换为 WSOL
        
        Args:
            amount_sol: 需要转换的 SOL 数量
            wallet_private_key_b58: 钱包私钥（Base58格式）
            wallet_address: 钱包地址
            
        Returns:
            bool: 转换是否成功
        """
        try:
            # 确保Solana客户端已配置代理
            await self._ensure_solana_client_proxy()
            
            logger.info(f"🔄 开始自动转换 {amount_sol} SOL 为 WSOL...")
            
            # 导入必要的模块
            from solders.keypair import Keypair
            from solders.pubkey import Pubkey
            from solders.system_program import transfer, TransferParams
            from solders.message import Message
            from solders.transaction import Transaction
            from spl.token.instructions import (
                create_associated_token_account,
                get_associated_token_address,
                sync_native,
                SyncNativeParams
            )
            from spl.token.constants import TOKEN_PROGRAM_ID
            import base58
            
            # 解析私钥
            private_key_bytes = base58.b58decode(wallet_private_key_b58)
            keypair = Keypair.from_bytes(private_key_bytes)
            
            # WSOL mint 地址
            wsol_mint = Pubkey.from_string("So11111111111111111111111111111111111111112")
            
            # 获取 WSOL ATA 地址
            wsol_ata = get_associated_token_address(keypair.pubkey(), wsol_mint)
            logger.info(f"📍 WSOL ATA 地址: {wsol_ata}")
            
            # 检查 ATA 是否存在
            ata_info = await self.solana_client.get_account_info(wsol_ata)
            instructions = []
            
            # 如果 ATA 不存在，创建它
            if not ata_info.value:
                logger.info("🔨 创建 WSOL ATA 账户...")
                create_ata_ix = create_associated_token_account(
                    payer=keypair.pubkey(),
                    owner=keypair.pubkey(),
                    mint=wsol_mint
                )
                instructions.append(create_ata_ix)
            
            # 转账 SOL 到 ATA
            logger.info(f"💸 转账 {amount_sol} SOL 到 WSOL ATA...")
            transfer_ix = transfer(
                TransferParams(
                    from_pubkey=keypair.pubkey(),
                    to_pubkey=wsol_ata,
                    lamports=int(amount_sol * 1_000_000_000)  # SOL 转 lamports
                )
            )
            instructions.append(transfer_ix)
            
            # 同步原生代币
            logger.info("🔄 同步原生代币...")
            sync_ix = sync_native(
                SyncNativeParams(
                    account=wsol_ata,
                    program_id=TOKEN_PROGRAM_ID
                )
            )
            instructions.append(sync_ix)
            
            # 获取最新 blockhash
            recent_blockhash_resp = await self.solana_client.get_latest_blockhash()
            recent_blockhash = recent_blockhash_resp.value.blockhash
            
            # 创建交易
            message = Message.new_with_blockhash(
                instructions,
                keypair.pubkey(),
                recent_blockhash
            )
            transaction = Transaction.new_unsigned(message)
            transaction.sign([keypair], recent_blockhash)
            
            # 发送交易
            logger.info("📤 发送 SOL 包装交易...")
            tx_resp = await self.solana_client.send_transaction(transaction)
            tx_hash = str(tx_resp.value)
            logger.info(f"📋 交易哈希: {tx_hash}")
            
            # 等待交易确认
            logger.info("⏳ 等待交易确认...")
            
            # 确保Solana客户端使用代理
            await self._ensure_solana_client_proxy()
            
            # 使用带重试机制的确认方法
            confirmation = await self._confirm_transaction_with_retry(
                tx_resp.value,
                commitment="finalized",
                timeout=60,
                max_retries=5
            )
            
            if confirmation.value[0].err:
                logger.error(f"❌ SOL 包装交易失败: {confirmation.value[0].err}")
                return False
            
            logger.info("✅ SOL 包装交易确认成功!")
            logger.info(f"🔗 Solscan链接: https://solscan.io/tx/{tx_hash}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ SOL 自动包装失败: {str(e)}")
            return False
    
    def is_slippage_related_error(
        self, 
        error_message: Optional[str], 
        provider_response: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        判断错误是否与滑点相关
        
        Args:
            error_message: 错误信息
            provider_response: API 响应
            
        Returns:
            bool: 是否为滑点相关错误
        """
        if not error_message:
            return False
        
        error_lower = error_message.lower()
        
        # OKX API 滑点相关错误关键词
        slippage_keywords = [
            "slippage",
            "price impact",
            "insufficient output amount",
            "minimum received",
            "swap failed",
            "liquidity"
        ]
        
        for keyword in slippage_keywords:
            if keyword in error_lower:
                return True
        
        # 检查 OKX API 响应中的特定错误码
        if provider_response and isinstance(provider_response, dict):
            error_code = provider_response.get("code")
            if error_code in ["51001", "51002", "51003"]:  # OKX 特定的滑点错误码
                return True
        
        return False
    
    def is_non_retryable_error(
        self, 
        error_message: Optional[str], 
        provider_response: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        判断错误是否为不可重试错误
        
        Args:
            error_message: 错误信息
            provider_response: API 响应
            
        Returns:
            bool: 是否为不可重试错误
        """
        # 滑点错误优先级判断 - 如果是滑点相关错误，则应该可重试
        if self.is_slippage_related_error(error_message, provider_response):
            return False  # 滑点错误永远可重试
        
        # 调用父类默认实现
        if super().is_non_retryable_error(error_message, provider_response):
            return True
        
        if not error_message:
            return False
        
        error_lower = error_message.lower()
        
        # OKX 特有的不可重试错误
        okx_non_retryable_keywords = [
            "api key invalid",
            "signature invalid",
            "timestamp invalid",
            "request expired",
            "token not supported",
            "pair not supported"
        ]
        
        for keyword in okx_non_retryable_keywords:
            if keyword in error_lower:
                return True
        
        # 检查 OKX API 响应中的特定错误码
        if provider_response and isinstance(provider_response, dict):
            error_code = provider_response.get("code")
            if error_code in ["50001", "50002", "50004", "50005"]:  # 认证相关错误
                return True
        
        return False
    
    async def close(self):
        """关闭服务，清理资源"""
        try:
            if hasattr(self, 'http_client'):
                await self.http_client.aclose()
            
            if hasattr(self, 'solana_client'):
                await self.solana_client.close()
            
            logger.info("OKX 交易服务已关闭")
            
        except Exception as e:
            logger.error(f"关闭 OKX 交易服务时出错: {e}")

    def clear_cache(self) -> None:
        """清理所有缓存"""
        self._api_cache.clear()
        logger.info("🧹 清理了所有API响应缓存")


async def main():
    """
    OKX 交易服务测试主函数
    """
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    from dotenv import load_dotenv
    
    load_dotenv()
    
    # 测试配置
    test_config = {
        "api_host": "https://web3.okx.com",
        "api_key_env": "OKX_API_KEY",
        "api_secret_env": "OKX_API_SECRET", 
        "api_passphrase_env": "OKX_API_PASSPHRASE",
        "rpc_endpoint": "https://api.mainnet-beta.solana.com"
    }
    
    # 检查环境变量
    required_env_vars = ["OKX_API_KEY", "OKX_API_SECRET", "OKX_API_PASSPHRASE"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.warning(f"缺少环境变量: {missing_vars}")
        logger.info("请设置以下环境变量进行测试:")
        for var in required_env_vars:
            logger.info(f"  export {var}=your_value_here")
        return
    
    try:
        # 创建 OKX 交易服务实例
        logger.info("创建 OKX 交易服务实例...")
        okx_service = OkxTradeService(channel_config=test_config)
        logger.info("✅ OKX 交易服务创建成功")
        
        # 测试报价获取
        logger.info("🔄 测试报价获取...")
        test_amount_sol = 0.00005  # 0.00005 SOL（约 $0.01，安全的测试金额）
        test_wallet_address = "AHnQjWa4jxYNxzfJantHDpcUKAE9pzVSGGPLhthPxCSW"  # 测试地址
        test_slippage = 0.01  # 1% slippage
        
        quote_result = await okx_service.get_quote(
            input_token_address=SOL_MINT_ADDRESS,
            output_token_address="9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump",  # USDC
            amount_input_token=test_amount_sol,
            slippage=test_slippage,
            wallet_address=test_wallet_address
        )
        
        if quote_result.is_success:
            logger.info("✅ OKX API 报价获取成功!")
            logger.info(f"  编码交易长度: {len(quote_result.encoded_transaction or '')} 字符")
            logger.info(f"  最小接收量: {quote_result.min_receive_amount}")
            logger.info(f"  最大花费量: {quote_result.max_spend_amount}")
            logger.info(f"  价格影响: {quote_result.price_impact}%")
            
            # 询问是否执行真实交易
            logger.info("")
            logger.info("🚨 检测到OKX API可以生成有效交易!")
            logger.info("如需执行真实交易测试，请设置环境变量：")
            logger.info("  export EXECUTE_REAL_TRADE=true")
            logger.info("  export WALLET_PRIVATE_KEY_B58='your_private_key'")
            logger.info("  export WALLET_ADDRESS='your_wallet_address'")
            
            if os.getenv("EXECUTE_REAL_TRADE", "").lower() == "true":
                wallet_private_key = os.getenv("WALLET_PRIVATE_KEY_B58")
                wallet_address = os.getenv("WALLET_ADDRESS") 
                
                if wallet_private_key and wallet_address:
                    logger.info("🔥 开始执行完整交易流程测试...")
                    
                    from beanie import PydanticObjectId
                    from bson import ObjectId
                    
                    # 准备交易参数
                    trade_params = {
                        "trade_type": TradeType.BUY,
                        "input_token_address": SOL_MINT_ADDRESS,
                        "output_token_address": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
                        "amount_input_token": test_amount_sol,
                        "wallet_private_key_b58": wallet_private_key,
                        "wallet_address": wallet_address,
                        "strategy_snapshot": {
                            "buy_slippage_percentage": test_slippage * 100,
                        },
                        "signal_id": PydanticObjectId(ObjectId()),
                        "trade_record_id": PydanticObjectId(ObjectId()),
                        "cached_quote": quote_result  # 使用缓存的报价
                    }
                    
                    # 执行完整交易流程
                    trade_result = await okx_service.execute_trade(**trade_params)
                    
                    if trade_result.status == TradeStatus.SUCCESS:
                        logger.info("🎉 完整交易流程测试成功！")
                        logger.info(f"📋 交易哈希: {trade_result.tx_hash}")
                        logger.info(f"🔗 Solscan链接: https://solscan.io/tx/{trade_result.tx_hash}")
                        logger.info(f"📊 OKX 状态: {trade_result.provider_response_raw}")
                        logger.info("🎯 🎉 恭喜！OKX DEX集成完全可用！ 🎉")
                    else:
                        logger.error(f"❌ 完整交易流程测试失败: {trade_result.error_message}")
                else:
                    logger.warning("❌ 缺少钱包凭证，无法执行完整交易流程测试")
            else:
                logger.info("⏩ 跳过完整交易流程测试")
        else:
            logger.error(f"❌ OKX API 报价获取失败: {quote_result.error_message}")
        
    except Exception as e:
        logger.error(f"❌ 测试过程发生异常: {e}", exc_info=True)
    
    finally:
        # 清理资源
        if 'okx_service' in locals():
            await okx_service.close()
        
        logger.info("🏁 OKX 交易服务测试完成")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())