import logging
import os
from datetime import datetime
from typing import Optional, Dict, Any, List
from beanie import PydanticObjectId

from utils.trading.config_manager import ConfigManager
from utils.trading.channel_registry import ChannelRegistry, ChannelNotFoundError
from utils.trading.channel_selector import ChannelSelector
from utils.trading.trade_orchestrator import TradeOrchestrator, TradeRequest
from utils.trading.trade_record_manager import TradeRecordManager
from utils.trading.solana.trade_interface import TradeType as InterfaceTradeType
from utils.trading.solana.gmgn_trade_service import GmgnTradeService
from models.trade_execution import TradeExecutionResult, TradeStatus
from models.trade_record import TradeRecord
from models.config import TradeChannelConfig, TradingParams, NotificationConfig
from models.dynamic_retry_config import DynamicRetryConfig
from utils.message_sender.message_sender import MessageSender, TelegramMessageSender
from utils.trading.slippage_retry import ParameterMerger

logger = logging.getLogger(__name__)

# SOL代币地址常量
SOL_MINT_ADDRESS = "So11111111111111111111111111111111111111112"


class AutoTradeManager:
    """
    统一自动交易管理器 - 提供简化的交易接口，协调所有子组件
    """
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.channel_registry = ChannelRegistry()
        self.channel_selector = ChannelSelector(self.channel_registry)
        self.trade_orchestrator = TradeOrchestrator(self.channel_registry, self.channel_selector)
        self.trade_record_manager = TradeRecordManager()
        self.message_sender: Optional[MessageSender] = None
        self._initialized = False
    
    async def initialize(self) -> None:
        """
        初始化AutoTradeManager - 加载配置并注册渠道
        """
        if self._initialized:
            logger.warning("AutoTradeManager 已经初始化过，跳过重复初始化")
            return
        
        logger.info("开始初始化 AutoTradeManager")
        
        try:
            # 加载配置
            config = await self.config_manager.get_config()
            logger.info(f"加载配置完成：enabled={config.enabled}, channels={len(config.channels)}")
            
            # 初始化消息发送器（如果配置了通知）
            await self._initialize_message_sender()
            
            # 注册所有启用的渠道
            await self._register_channels(config.channels)
            
            self._initialized = True
            logger.info("AutoTradeManager 初始化成功")
            
        except Exception as e:
            logger.error(f"AutoTradeManager 初始化失败: {e}", exc_info=True)
            raise
    
    async def _initialize_message_sender(self) -> None:
        """
        初始化消息发送器
        """
        try:
            notification_config = await self.config_manager.get_notification_config()
            if notification_config.admin_chat_ids:
                self.message_sender = TelegramMessageSender()
                logger.info(f"消息发送器初始化成功，配置了 {len(notification_config.admin_chat_ids)} 个管理员")
            else:
                logger.info("未配置管理员Chat ID，不初始化消息发送器")
        except Exception as e:
            logger.error(f"初始化消息发送器失败: {e}")
            self.message_sender = None
    
    async def _register_channels(self, channel_configs: List[TradeChannelConfig]) -> None:
        """
        注册交易渠道
        
        Args:
            channel_configs: 渠道配置列表
        """
        logger.info(f"开始注册 {len(channel_configs)} 个交易渠道")
        
        for channel_config in channel_configs:
            try:
                # 创建渠道实例
                channel_instance = await self._create_channel_instance(channel_config)
                
                if channel_instance:
                    # 注册到注册表
                    self.channel_registry.register_channel(
                        channel_type=channel_config.channel_type,
                        channel_instance=channel_instance,
                        channel_config=channel_config
                    )
                    logger.info(f"渠道 '{channel_config.channel_type}' 注册成功")
                else:
                    logger.warning(f"渠道 '{channel_config.channel_type}' 实例创建失败，跳过注册")
                    
            except Exception as e:
                logger.error(f"注册渠道 '{channel_config.channel_type}' 失败: {e}")
        
        registered_channels = self.channel_registry.list_channels()
        logger.info(f"渠道注册完成，共注册 {len(registered_channels)} 个渠道: {registered_channels}")
    
    async def _create_channel_instance(self, channel_config: TradeChannelConfig):
        """
        根据配置创建渠道实例
        
        Args:
            channel_config: 渠道配置
            
        Returns:
            TradeInterface: 渠道实例
        """
        channel_type = channel_config.channel_type
        channel_params = channel_config.channel_params
        
        try:
            if channel_type == "gmgn":
                # 创建GMGN渠道实例
                gmgn_api_host = channel_params.get("api_host") or channel_params.get("gmgn_api_host") or os.getenv("GMGN_API_HOST")
                if not gmgn_api_host:
                    logger.error("GMGN渠道缺少必要参数：api_host")
                    return None
                
                return GmgnTradeService(gmgn_api_host=gmgn_api_host)
                
            elif channel_type == "jupiter":
                # 创建Solana直接交易渠道实例
                from utils.trading.solana.jupiter_trade_service import create_jupiter_trade_service
                # 这里传递一个包含所有必要参数的字典
                solana_params = {
                    "jupiter_api_host": channel_params.get("jupiter_api_host") or os.getenv("JUPITER_API_HOST"),
                    "rpc_endpoint": channel_params.get("rpc_endpoint") or os.getenv("SOLANA_RPC_URL"),
                    "commitment": channel_params.get("commitment", "confirmed"),
                    # 添加其他必要参数
                }
                return create_jupiter_trade_service(solana_params)
                
            elif channel_type == "gmgn_v2":
                # 创建GMGN V2渠道实例
                from utils.trading.solana.gmgn_trade_service_v2 import GmgnTradeServiceV2
                gmgn_api_host = channel_params.get("api_host") or os.getenv("GMGN_API_HOST")
                if not gmgn_api_host:
                    logger.error("GMGN V2渠道缺少必要参数：api_host")
                    return None
                
                return GmgnTradeServiceV2(gmgn_api_host=gmgn_api_host)
                
            elif channel_type == "okx":
                # 创建OKX DEX渠道实例
                from utils.trading.solana.okx_trade_service import OkxTradeService
                
                # 验证必要参数
                api_host = channel_params.get("api_host", "https://www.okx.com")
                api_key_env = channel_params.get("api_key_env", "OKX_API_KEY")
                api_secret_env = channel_params.get("api_secret_env", "OKX_API_SECRET")
                api_passphrase_env = channel_params.get("api_passphrase_env", "OKX_API_PASSPHRASE")
                
                # 检查环境变量是否存在
                if not all([os.getenv(api_key_env), os.getenv(api_secret_env), os.getenv(api_passphrase_env)]):
                    missing = []
                    if not os.getenv(api_key_env):
                        missing.append(api_key_env)
                    if not os.getenv(api_secret_env):
                        missing.append(api_secret_env)
                    if not os.getenv(api_passphrase_env):
                        missing.append(api_passphrase_env)
                    logger.error(f"OKX渠道缺少必要的环境变量: {', '.join(missing)}")
                    return None
                
                # 构建完整的渠道配置
                okx_config = {
                    "api_host": api_host,
                    "api_key_env": api_key_env,
                    "api_secret_env": api_secret_env,
                    "api_passphrase_env": api_passphrase_env,
                    "rpc_endpoint": channel_params.get("rpc_endpoint") or os.getenv("SOLANA_RPC_URL")
                }
                
                return OkxTradeService(channel_config=okx_config)
                
            else:
                logger.error(f"不支持的渠道类型: {channel_type}")
                return None
                
        except Exception as e:
            logger.error(f"创建渠道实例 '{channel_type}' 失败: {e}")
            return None
    
    async def execute_trade(
        self,
        trade_type: str,  # "buy" or "sell"
        token_in_address: str,
        token_out_address: str,
        amount: Optional[float] = None,
        wallet_private_key_env_var: Optional[str] = None,
        wallet_address: Optional[str] = None,
        strategy_trading_overrides: Optional[Dict[str, Any]] = None,
        dynamic_retry_config: Optional[DynamicRetryConfig] = None,  # 新增动态重试配置
        signal_id: Optional[PydanticObjectId] = None,
        strategy_name: Optional[str] = None
    ) -> TradeExecutionResult:
        """
        执行交易 - 统一交易入口
        
        Args:
            trade_type: 交易类型 ("buy" or "sell")
            token_in_address: 输入代币地址
            token_out_address: 输出代币地址
            amount: 可选，交易金额（如果不提供，使用全局默认值）
            wallet_private_key_env_var: 可选，钱包私钥环境变量名（覆盖全局默认）
            wallet_address: 可选，钱包地址（覆盖全局默认）
            strategy_trading_overrides: 可选，策略级别的交易参数覆盖
            signal_id: 可选，关联的信号ID
            strategy_name: 可选，策略名称
            
        Returns:
            TradeExecutionResult: 交易执行结果
        """
        if not self._initialized:
            await self.initialize()
        
        # 检查自动交易是否启用
        if not await self.config_manager.is_enabled():
            logger.warning("自动交易已禁用，跳过交易")
            return TradeExecutionResult(
                final_status=TradeStatus.SKIPPED,
                successful_channel=None,
                final_trade_record_id=None,
                channel_attempts=[],
                total_execution_time=0.0,
                error_summary="Auto trading is disabled",
                started_at=datetime.now(),
                completed_at=datetime.now()
            )
        
        start_time = datetime.now()
        logger.info(f"开始执行交易：{trade_type} {token_in_address} -> {token_out_address}")
        
        try:
            # 准备交易参数
            trade_params = await self._prepare_trade_parameters(
                trade_type=trade_type,
                amount=amount,
                wallet_private_key_env_var=wallet_private_key_env_var,
                wallet_address=wallet_address,
                strategy_trading_overrides=strategy_trading_overrides,
                dynamic_retry_config=dynamic_retry_config
            )
            
            # 从环境变量获取私钥
            final_private_key_env_var = trade_params["wallet_private_key_env_var"]
            private_key = os.getenv(final_private_key_env_var)
            if not private_key:
                raise ValueError(f"无法从环境变量 '{final_private_key_env_var}' 获取钱包私钥")
            
            # 构建策略快照（用于交易记录）
            strategy_snapshot = {
                "trade_type": trade_type,
                "amount": trade_params["amount"],
                "wallet_address": trade_params["wallet_address"],
                "timestamp": datetime.now().isoformat(),
                **(strategy_trading_overrides or {})  # 包含所有策略级别的覆盖参数
            }
            
            # 处理signal_id：如果没有提供，创建一个临时的ObjectId
            final_signal_id = signal_id
            if final_signal_id is None:
                from bson import ObjectId
                final_signal_id = PydanticObjectId(ObjectId())
                logger.debug(f"未提供signal_id，生成临时ID: {final_signal_id}")
            
            # 创建交易记录
            trade_record = await self.trade_record_manager.create_trade_record(
                signal_id=final_signal_id,  # 传递信号ID（确保非空）
                strategy_name=strategy_name or "auto_trade",  # 传递策略名称，如果没有则使用默认值
                trade_type=trade_type,
                token_in_address=token_in_address,
                token_out_address=token_out_address,
                amount=trade_params["amount"],
                wallet_address=trade_params["wallet_address"]
            )
            
            # 准备TradeOrchestrator的参数
            from utils.trading.trade_orchestrator import TradeRequest
            from utils.trading.solana.trade_interface import TradeType as InterfaceTradeType
            
            trade_request = TradeRequest(
                trade_type=InterfaceTradeType(trade_type),
                token_in_address=token_in_address,
                token_out_address=token_out_address,
                amount=trade_params["amount"],
                wallet_private_key_b58=private_key,
                wallet_address=trade_params["wallet_address"],
                strategy_snapshot=strategy_snapshot,
                signal_id=str(final_signal_id),
                trade_record_id=str(trade_record.id),
                merged_trading_params=trade_params["trading_params"],
                dynamic_retry_config=dynamic_retry_config
            )
            
            # 执行交易
            execution_result = await self.trade_orchestrator.execute_trade(trade_request)
            execution_result.final_trade_record_id = trade_record.id
            
            # 更新交易记录
            await self.trade_record_manager.update_trade_record_from_execution_result(
                trade_record, execution_result
            )
            
            # 保存渠道尝试记录
            if execution_result.channel_attempts:
                await self.trade_record_manager.save_channel_attempt_records(
                    trade_record=trade_record,
                    channel_attempts=execution_result.channel_attempts,
                    trade_type=trade_type,
                    token_in_address=token_in_address,
                    token_out_address=token_out_address,
                    amount=trade_params["amount"],
                    signal_id=final_signal_id
                )
            
            logger.info(f"交易执行完成：状态={execution_result.final_status.value}, "
                       f"耗时={execution_result.total_execution_time:.2f}秒")
            
            # 调用通知功能
            await self._send_notification(
                execution_result, 
                signal_id=final_signal_id,
                strategy_name=strategy_name,
                token_address=token_out_address if trade_type == "buy" else token_in_address
            )
            
            return execution_result
            
        except Exception as e:
            logger.error(f"交易执行异常: {e}", exc_info=True)
            end_time = datetime.now()
            
            execution_result = TradeExecutionResult(
                final_status=TradeStatus.FAILED,
                successful_channel=None,
                final_trade_record_id=None,
                channel_attempts=[],
                total_execution_time=(end_time - start_time).total_seconds(),
                error_summary=f"Execution exception: {str(e)}",
                started_at=start_time,
                completed_at=end_time
            )
            
            # 发送异常通知
            await self._send_notification(
                execution_result,
                signal_id=final_signal_id,
                strategy_name=strategy_name,
                token_address=token_out_address if trade_type == "buy" else token_in_address
            )
            
            return execution_result
    
    async def _prepare_trade_parameters(
        self,
        trade_type: str,
        amount: Optional[float],
        wallet_private_key_env_var: Optional[str],
        wallet_address: Optional[str],
        strategy_trading_overrides: Optional[Dict[str, Any]],
        dynamic_retry_config: Optional[DynamicRetryConfig] = None
    ) -> Dict[str, Any]:
        """
        准备交易参数，处理默认值和覆盖逻辑，执行参数合并
        
        Args:
            trade_type: 交易类型
            amount: 可选交易金额
            wallet_private_key_env_var: 可选私钥环境变量名
            wallet_address: 可选钱包地址
            strategy_trading_overrides: 可选策略级别覆盖参数
            dynamic_retry_config: 可选动态重试配置
            
        Returns:
            Dict[str, Any]: 合并后的交易参数
        """
        # 获取全局配置
        enabled_channels = await self.config_manager.get_enabled_channels()
        global_trading_params = None
        if enabled_channels:
            # 使用优先级最高的渠道作为全局默认值
            global_trading_params = enabled_channels[0].trading_params
        
        # 如果没有渠道配置，创建默认的交易参数
        if global_trading_params is None:
            from models.config import TradingParams
            global_trading_params = TradingParams()
            logger.warning("未找到渠道配置，使用默认交易参数")
        
        # 获取钱包配置
        config = await self.config_manager.get_config()
        wallet_config = config.wallet_config
        
        # 准备策略配置对象（如果有策略覆盖参数的话）
        strategy_config = None
        if strategy_trading_overrides:
            # 将策略覆盖参数转换为策略配置对象
            # 这里可以根据需要创建一个简单的配置对象
            from models.config import SingleKolStrategyConfig
            strategy_config = SingleKolStrategyConfig(
                strategy_name="runtime_strategy",
                **strategy_trading_overrides
            )
        
        # 执行参数合并 - 传递各层级参数给ParameterMerger处理
        # ParameterMerger会确保运行时配置(dynamic_retry_config)优先级最高
        parameter_merger = ParameterMerger()
        
        # 转换动态重试配置为运行时覆盖字典
        runtime_overrides = None
        if dynamic_retry_config and dynamic_retry_config.is_effective():
            runtime_overrides = dynamic_retry_config.to_override_dict()
            logger.info(f"应用动态重试配置 '{dynamic_retry_config.config_id}': {runtime_overrides}")
        
        merged_trading_params = parameter_merger.merge_trading_params_with_slippage_retry(
            global_params=global_trading_params,
            channel_params=None,  # 在TradeOrchestrator中会针对具体渠道再次合并
            strategy_config=strategy_config,  # 策略级别配置
            runtime_overrides=runtime_overrides  # 运行时配置，优先级最高
        )
        
        # 构建最终参数字典
        trade_params = {
            "trade_type": trade_type,
            "wallet_private_key_env_var": wallet_private_key_env_var or wallet_config.default_private_key_env_var,
            "wallet_address": wallet_address or wallet_config.default_wallet_address,
            "amount": amount,  # 如果为None，将在渠道执行时使用默认值
            "trading_params": merged_trading_params
        }
        
        return trade_params
    
    async def get_status(self) -> Dict[str, Any]:
        """
        获取AutoTradeManager状态信息
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        try:
            config = await self.config_manager.get_config()
            registry_stats = self.channel_registry.get_registry_stats()
            selector_stats = self.channel_selector.get_selector_stats()
            execution_stats = self.trade_orchestrator.get_execution_stats()
            cache_info = self.config_manager.get_cache_info()
            
            return {
                "initialized": self._initialized,
                "auto_trade_enabled": config.enabled,
                "config_cache_info": cache_info,
                "channel_registry": registry_stats,
                "channel_selector": selector_stats,
                "trade_execution": execution_stats,
                "total_channels": len(config.channels),
                "enabled_channels": len([ch for ch in config.channels if ch.enabled])
            }
            
        except Exception as e:
            logger.error(f"获取状态信息失败: {e}")
            return {
                "initialized": self._initialized,
                "error": str(e)
            }
    
    async def reload_config(self) -> bool:
        """
        重新加载配置
        
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("开始重新加载配置")
            
            # 强制重新加载配置
            await self.config_manager.force_reload()
            
            # 清除健康检查缓存
            self.channel_selector.clear_health_cache()
            
            # 重新注册渠道
            config = await self.config_manager.get_config()
            await self.channel_registry.close_all_channels()
            await self._register_channels(config.channels)
            
            logger.info("配置重新加载成功")
            return True
            
        except Exception as e:
            logger.error(f"重新加载配置失败: {e}")
            return False
    
    async def get_trade_stats(self, days: int = 7) -> Dict[str, Any]:
        """
        获取交易统计信息
        
        Args:
            days: 统计天数
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        return await self.trade_record_manager.get_trade_stats(days=days)
    
    @property
    def is_initialized(self) -> bool:
        """检查是否已初始化"""
        return self._initialized
    
    async def close(self) -> None:
        """关闭AutoTradeManager，清理资源"""
        try:
            logger.info("开始关闭 AutoTradeManager")
            await self.channel_registry.close_all_channels()
            self._initialized = False
            logger.info("AutoTradeManager 已关闭")
        except Exception as e:
            logger.error(f"关闭 AutoTradeManager 时出错: {e}")
    
    async def _send_notification(
        self, 
        execution_result: TradeExecutionResult,
        signal_id: Optional[PydanticObjectId] = None,
        strategy_name: Optional[str] = None,
        token_address: Optional[str] = None
    ) -> None:
        """
        发送通知
        
        Args:
            execution_result: 交易执行结果
            signal_id: 可选，信号ID
            strategy_name: 可选，策略名称
            token_address: 可选，代币地址
        """
        if not self.message_sender:
            return
        
        try:
            notification_config = await self.config_manager.get_notification_config()
            
            # 检查是否需要发送通知
            should_notify = False
            if execution_result.final_status == TradeStatus.FAILED and notification_config.notify_on_failure:
                should_notify = True
            elif execution_result.final_status == TradeStatus.SUCCESS and len(execution_result.channel_attempts) > 1 and notification_config.notify_on_fallback:
                # 成功但使用了故障转移
                should_notify = True
            
            if not should_notify or not notification_config.admin_chat_ids:
                return
            
            # 构建通知消息
            message = await self._build_notification_message(
                execution_result, 
                notification_config, 
                signal_id=signal_id,
                strategy_name=strategy_name,
                token_address=token_address
            )
            
            # 发送给所有管理员
            for admin_chat_id in notification_config.admin_chat_ids:
                try:
                    await self.message_sender.send_message_to_user(message, admin_chat_id)
                    logger.info(f"通知已发送给管理员 {admin_chat_id}")
                except Exception as e:
                    logger.error(f"发送通知给管理员 {admin_chat_id} 失败: {e}")
                    
        except Exception as e:
            logger.error(f"发送通知时出错: {e}")
    
    async def _build_notification_message(
        self, 
        execution_result: TradeExecutionResult, 
        notification_config: NotificationConfig,
        signal_id: Optional[PydanticObjectId] = None,
        strategy_name: Optional[str] = None,
        token_address: Optional[str] = None
    ) -> str:
        """
        构建通知消息
        
        Args:
            execution_result: 交易执行结果
            notification_config: 通知配置
            signal_id: 可选，信号ID
            strategy_name: 可选，策略名称
            token_address: 可选，代币地址
            
        Returns:
            str: 通知消息内容
        """
        # 基础信息头部
        header_info = []
        if signal_id:
            header_info.append(f"Signal: {str(signal_id)}")
        if strategy_name:
            header_info.append(f"Strategy: {strategy_name}")
        if token_address:
            header_info.append(f"Token: {token_address}")
        
        header = " | ".join(header_info) if header_info else ""
        
        if execution_result.final_status == TradeStatus.FAILED:
            # 交易失败通知
            message = f"🚨 Auto-Trade FAILED 🚨\n"
            if header:
                message += f"{header}\n"
            
            if notification_config.include_trade_details:
                message += f"Reason: {execution_result.error_summary or 'Unknown'}\n"
                message += f"Channels attempted: {len(execution_result.channel_attempts)}\n"
                message += f"Total time: {execution_result.total_execution_time:.2f}s\n"
                
                if execution_result.channel_attempts:
                    message += f"Channel attempts:\n"
                    for i, attempt in enumerate(execution_result.channel_attempts, 1):
                        message += f"  {i}. {attempt.channel_type}: {attempt.status.value}"
                        if attempt.error_message:
                            message += f" ({attempt.error_message})"
                        message += f"\n"
            
        elif execution_result.final_status == TradeStatus.SUCCESS and len(execution_result.channel_attempts) > 1:
            # 故障转移成功通知
            failed_channels = [attempt.channel_type for attempt in execution_result.channel_attempts[:-1]]
            message = f"⚠️ Auto-Trade: Fallback Success ⚠️\n"
            if header:
                message += f"{header}\n"
            message += f"Failed channels: {', '.join(failed_channels)}\n"
            message += f"Successful channel: {execution_result.successful_channel}\n"
            
            if notification_config.include_trade_details:
                message += f"Total time: {execution_result.total_execution_time:.2f}s\n"
                
        else:
            # 其他情况的通知
            message = f"ℹ️ Auto-Trade: {execution_result.final_status.value}\n"
            if header:
                message += f"{header}\n"
            
        return message


# 全局实例（单例模式）
_auto_trade_manager_instance: Optional[AutoTradeManager] = None


async def get_auto_trade_manager() -> AutoTradeManager:
    """
    获取AutoTradeManager全局实例（单例模式）
    
    Returns:
        AutoTradeManager: 全局实例
    """
    global _auto_trade_manager_instance
    
    if _auto_trade_manager_instance is None:
        _auto_trade_manager_instance = AutoTradeManager()
        await _auto_trade_manager_instance.initialize()
    
    return _auto_trade_manager_instance 