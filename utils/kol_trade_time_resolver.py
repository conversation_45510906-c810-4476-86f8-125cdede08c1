"""
KOL交易时间解析器

本模块实现FR003需求中的KOL交易时间解析算法，负责从交易记录中
查找对应的KOL最后交易时间，为延迟计算提供关键数据。

@技术实现方案: docs/features/0.1.0/workflows/trading_delay_monitor_dev_plan_ai.md
"""

import logging
from datetime import datetime, timedelta, timezone
from typing import List, Optional, Tuple, NamedTuple
from dataclasses import dataclass

from dao.signal_dao import SignalDAO
from dao.kol_wallet_activity_dao import KOLWalletActivityDAO
from models.trade_record import TradeRecord

logger = logging.getLogger(__name__)


# 常量定义
class ResolverConstants:
    """KOL交易时间解析器常量"""
    SUCCESS_STATUS = "success"
    KOL_EVENT_TYPES = ['buy', 'sell']
    MAX_KOL_ACTIVITIES_LIMIT = 1


# 错误代码定义
class ErrorCodes:
    """标准化的错误代码"""
    TRADE_NOT_SUCCESSFUL = "trade_not_successful"
    NO_SIGNAL_ID = "no_signal_id"
    SIGNAL_NOT_FOUND = "signal_not_found"
    NO_KOL_WALLETS = "no_kol_wallets"
    NO_LOOKBACK_HOURS = "no_lookback_hours"
    NO_MIN_AMOUNT_CONFIG = "no_min_amount_config"  # 缺少最小金额配置（已废弃，保留向后兼容）
    NO_QUALIFIED_KOL_ACTIVITIES_FOUND = "no_qualified_kol_activities_found"  # 未找到符合金额条件的KOL活动
    NO_KOL_ACTIVITIES_FOUND = "no_kol_activities_found"


@dataclass
class TimeRange:
    """时间范围数据类"""
    start_time: datetime
    end_time: datetime
    start_timestamp: int
    end_timestamp: int
    min_amount: Optional[float] = None  # 最小交易金额过滤条件


class KOLTradeTimeResult(NamedTuple):
    """KOL交易时间解析结果"""
    kol_last_trade_time: Optional[datetime]
    hit_kol_wallets: List[str]
    error_code: Optional[str]


class KOLTradeTimeResolver:
    """
    KOL交易时间解析器
    
    负责查找KOL最后交易时间，实现需求文档7.1节定义的算法步骤：
    1. 验证交易记录状态为成功
    2. 从trade_record.signal_id获取Signal记录
    3. 从Signal中提取KOL钱包和查询参数
    4. 查询KOL活动获取最后交易时间
    """
    
    def __init__(self):
        """初始化解析器，注入必要的DAO依赖"""
        self.signal_dao = SignalDAO()
        self.kol_activity_dao = KOLWalletActivityDAO()
    
    async def resolve_kol_last_trade_time(
        self, 
        trade_record: TradeRecord
    ) -> Tuple[Optional[datetime], List[str], Optional[str]]:
        """
        解析KOL最后交易时间
        
        遵循需求文档7.1节定义的算法步骤，从交易记录解析出对应的
        KOL最后交易时间，用于后续的延迟计算。
        
        Args:
            trade_record: 交易记录对象，包含signal_id等关键信息
            
        Returns:
            Tuple包含三个元素：
            - Optional[datetime]: KOL最后交易时间（UTC时区）
            - List[str]: 命中的KOL钱包地址列表
            - Optional[str]: 错误信息（如果有）
            
        Raises:
            Exception: 数据库查询或数据处理异常
        """
        try:
            result = await self._resolve_kol_last_trade_time_internal(trade_record)
            return result.kol_last_trade_time, result.hit_kol_wallets, result.error_code
        except Exception as e:
            logger.error(f"解析KOL交易时间时发生未预期错误: {e}", exc_info=True)
            raise
    
    async def _resolve_kol_last_trade_time_internal(
        self, 
        trade_record: TradeRecord
    ) -> KOLTradeTimeResult:
        """内部解析方法，返回结构化结果"""
        
        # 步骤1: 验证交易记录状态
        validation_result = self._validate_trade_record(trade_record)
        if validation_result:
            return validation_result
        
        # 步骤2: 获取并验证信号信息
        signal_result = await self._get_and_validate_signal(trade_record)
        if signal_result.error_result.error_code:
            return signal_result.error_result
        
        signal = signal_result.signal
        hit_kol_wallets = signal.hit_kol_wallets or []
        
        # 步骤3: 验证必要字段并计算时间范围
        time_range_result = self._calculate_time_range(signal, hit_kol_wallets)
        if time_range_result.error_result.error_code:
            return time_range_result.error_result
        
        # 步骤4: 查询KOL活动并转换结果
        kol_time_result = await self._query_kol_activities(
            time_range_result.time_range, 
            hit_kol_wallets
        )
        
        return kol_time_result
    
    def _validate_trade_record(self, trade_record: TradeRecord) -> Optional[KOLTradeTimeResult]:
        """验证交易记录状态"""
        if trade_record.status != ResolverConstants.SUCCESS_STATUS:
            logger.debug(f"交易记录 {trade_record.id} 状态为 {trade_record.status}，跳过处理")
            return KOLTradeTimeResult(None, [], ErrorCodes.TRADE_NOT_SUCCESSFUL)
        
        if not trade_record.signal_id:
            logger.debug(f"交易记录 {trade_record.id} 缺少signal_id")
            return KOLTradeTimeResult(None, [], ErrorCodes.NO_SIGNAL_ID)
            
        return None  # 验证通过
    
    async def _get_and_validate_signal(self, trade_record: TradeRecord) -> 'SignalResult':
        """获取并验证信号信息"""
        try:
            signal = await self.signal_dao.get_signal(trade_record.signal_id)
            if not signal:
                logger.warning(f"未找到信号记录，signal_id: {trade_record.signal_id}")
                return SignalResult(None, KOLTradeTimeResult(None, [], ErrorCodes.SIGNAL_NOT_FOUND))
            
            return SignalResult(signal, KOLTradeTimeResult(None, [], None))
        except Exception as e:
            logger.error(f"查询信号记录失败，signal_id: {trade_record.signal_id}, 错误: {e}")
            raise
    
    def _calculate_time_range(self, signal, hit_kol_wallets: List[str]) -> 'TimeRangeResult':
        """计算查询时间范围"""
        if not hit_kol_wallets:
            logger.debug(f"信号记录 {signal.id} 缺少hit_kol_wallets")
            return TimeRangeResult(None, KOLTradeTimeResult(None, hit_kol_wallets, ErrorCodes.NO_KOL_WALLETS))
        
        trigger_conditions = signal.trigger_conditions or {}
        lookback_hours = trigger_conditions.get('transaction_lookback_hours')
        if not lookback_hours:
            logger.debug(f"信号记录 {signal.id} 缺少transaction_lookback_hours配置")
            return TimeRangeResult(None, KOLTradeTimeResult(None, hit_kol_wallets, ErrorCodes.NO_LOOKBACK_HOURS))
        
        # 获取最小交易金额限制（向后兼容：如果没有配置则不进行金额过滤）
        min_amount = trigger_conditions.get('transaction_min_amount')
        if min_amount is None:
            logger.debug(f"信号记录 {signal.id} 未配置transaction_min_amount，跳过金额过滤")
        
        # 计算UTC时间范围
        trigger_time_utc = signal.trigger_timestamp
        start_time_utc = trigger_time_utc - timedelta(hours=lookback_hours)

        # 转换为用于查询的东八区时间戳
        # 我们要查找的KOL时间戳是东八区的，所以查询范围也要是东八区的
        # E.g., to find events at 10:00 UTC, we must look for timestamps representing 18:00 GMT+8
        start_timestamp_for_query = int((start_time_utc + timedelta(hours=8)).timestamp())
        end_timestamp_for_query = int((trigger_time_utc + timedelta(hours=8)).timestamp())
        
        time_range = TimeRange(
            start_time=start_time_utc,
            end_time=trigger_time_utc,
            start_timestamp=start_timestamp_for_query,
            end_timestamp=end_timestamp_for_query,
            min_amount=min_amount  # 包含最小金额用于后续查询
        )
        
        logger.debug(
            f"查询KOL活动, UTC范围: {start_time_utc} - {trigger_time_utc}, "
            f"查询使用东八区时间戳范围: {time_range.start_timestamp} - {time_range.end_timestamp}"
        )
        
        return TimeRangeResult(time_range, KOLTradeTimeResult(None, hit_kol_wallets, None))
    
    async def _query_kol_activities(
        self, 
        time_range: TimeRange, 
        hit_kol_wallets: List[str]
    ) -> KOLTradeTimeResult:
        """查询KOL活动并转换结果 - 使用高性能数据库层过滤"""
        try:
            # 使用高性能的数据库层过滤，避免应用层手动过滤
            kol_activities = await self.kol_activity_dao.find_activities_in_time_range(
                wallet_addresses=hit_kol_wallets,
                start_timestamp=time_range.start_timestamp,
                end_timestamp=time_range.end_timestamp,
                event_types=ResolverConstants.KOL_EVENT_TYPES,  # 在数据库层过滤事件类型
                min_cost_usd=time_range.min_amount,  # 金额过滤参数
                sort_by_timestamp=True,  # 按时间戳降序排序
                limit=ResolverConstants.MAX_KOL_ACTIVITIES_LIMIT
            )
        except Exception as e:
            logger.error(f"查询KOL活动失败: {e}")
            raise
        
        if not kol_activities:
            if time_range.min_amount is not None:
                logger.debug(
                    f"未找到符合金额条件的KOL活动记录，钱包: {hit_kol_wallets}, "
                    f"时间范围: {time_range.start_timestamp} - {time_range.end_timestamp}, "
                    f"最小金额: {time_range.min_amount}"
                )
                return KOLTradeTimeResult(None, hit_kol_wallets, ErrorCodes.NO_QUALIFIED_KOL_ACTIVITIES_FOUND)
            else:
                logger.debug(
                    f"未找到KOL活动记录，钱包: {hit_kol_wallets}, "
                    f"时间范围: {time_range.start_timestamp} - {time_range.end_timestamp}"
                )
                return KOLTradeTimeResult(None, hit_kol_wallets, ErrorCodes.NO_KOL_ACTIVITIES_FOUND)
        
        # 转换时间戳格式
        kol_last_trade_time = self._convert_timestamp_to_datetime(kol_activities[0]['timestamp'])
        
        logger.debug(
            f"找到KOL最后交易时间: {kol_last_trade_time}, "
            f"活动ID: {kol_activities[0].get('id', kol_activities[0].get('_id'))}"
        )
        
        return KOLTradeTimeResult(kol_last_trade_time, hit_kol_wallets, None)
    
    def _convert_timestamp_to_datetime(self, timestamp: int) -> datetime:
        """
        将代表东八区本地时间的Unix时间戳转换为UTC datetime对象
        
        KOL活动数据的timestamp字段是基于东八区时间的
        """
        # fromtimestamp 默认使用本地时区，这可能导致双重偏移。
        # 因此，我们先将时间戳显式地视为UTC，以获得一个标准化的datetime对象。
        # 经过验证，`fromtimestamp` 结合 `tz=timezone.utc` 可以正确处理
        # 由一个naive的东八区时间生成的timestamp。
        corrected_utc_time = datetime.fromtimestamp(timestamp, tz=timezone.utc)
        
        logger.debug(f"转换东八区时间戳 {timestamp} -> {corrected_utc_time} (真实UTC)")
        
        return corrected_utc_time


# 辅助数据结构
@dataclass
class SignalResult:
    """信号查询结果"""
    signal: Optional[object]
    error_result: KOLTradeTimeResult


@dataclass  
class TimeRangeResult:
    """时间范围计算结果"""
    time_range: Optional[TimeRange]
    error_result: KOLTradeTimeResult 