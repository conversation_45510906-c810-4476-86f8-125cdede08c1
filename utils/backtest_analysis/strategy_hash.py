"""
策略哈希生成工具
为回测策略参数生成稳定的哈希ID，用于跨报告的策略识别
"""

import json
import hashlib
from typing import Dict, Any


def generate_strategy_hash(params_dict: Dict[str, Any]) -> str:
    """生成策略参数的稳定哈希ID
    
    Args:
        params_dict: 策略参数字典
        
    Returns:
        str: 8位十六进制策略ID
    """
    # 过滤掉非策略相关参数（时间范围、内部技术参数、通知间隔等）
    # 修复：与报告生成器的过滤逻辑保持一致
    excluded_params = {
        # 时间相关参数
        'start_date', 'end_date', 'backtest_start_date', 'backtest_end_date',
        'backtest_start_time', 'backtest_end_time',
        # 内部技术参数
        'use_real_price', 'skip_price_api_query', 'processing_interval',
        # 通知间隔参数（Bug修复的关键）
        'same_token_notification_interval_minutes'
    }
    
    strategy_params = {
        k: v for k, v in params_dict.items() 
        if k not in excluded_params
    }
    
    # 对参数字典按键排序，确保一致性
    sorted_params = dict(sorted(strategy_params.items()))
    
    # 转换为JSON字符串（确保数值类型一致）
    params_str = json.dumps(sorted_params, sort_keys=True, separators=(',', ':'))
    
    # 生成SHA256哈希的前8位作为策略ID
    hash_obj = hashlib.sha256(params_str.encode('utf-8'))
    strategy_id = hash_obj.hexdigest()[:8]
    
    return strategy_id


def generate_strategy_id_from_config(config_data: Dict[str, Any]) -> str:
    """从配置数据生成策略ID
    
    Args:
        config_data: 配置数据字典
        
    Returns:
        str: 策略ID
    """
    return generate_strategy_hash(config_data)


def format_strategy_display_id(strategy_id: str) -> str:
    """格式化策略ID用于显示
    
    Args:
        strategy_id: 策略ID
        
    Returns:
        str: 格式化后的显示ID
    """
    return f"策略ID: {strategy_id}" 