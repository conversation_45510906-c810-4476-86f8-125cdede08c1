"""
交易延迟计算器 - 核心业务逻辑

基于技术实现方案 @trading_delay_monitor_dev_plan_ai.md
实现单个交易和批量交易的延迟计算功能

主要功能：
- 单个交易延迟计算：基于KOL活动计算延迟时间
- 批量延迟计算：高效处理多个交易记录  
- 防重复处理：避免对同一交易重复计算
- 状态分类：区分正常、异常、缺失等不同状态

测试覆盖：TC-201~TC-206 (6个测试用例)
"""

import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any

from models.trading_delay_record import TradingDelayRecord, DelayStatus
from dao.trading_delay_record_dao import TradingDelayRecordDAO
from utils.kol_trade_time_resolver import KOLTradeTimeResolver

# 配置日志
logger = logging.getLogger(__name__)


class TradingDelayCalculator:
    """
    交易延迟计算器
    
    负责计算KOL交易时间与实际交易时间的延迟，并根据延迟情况
    标记相应的状态（正常、过长、异常等）。
    
    遵循FR005需求中定义的延迟计算算法。
    """
    
    def __init__(self):
        """初始化延迟计算器"""
        self.kol_resolver = KOLTradeTimeResolver()
        self.dao = TradingDelayRecordDAO()
        
    async def _calculate_single_trade_delay(
        self, 
        trade_record: 'TradeRecord'
    ) -> Optional['TradingDelayRecord']:
        """
        计算单个交易的延迟
        
        Args:
            trade_record: 交易记录对象
            
        Returns:
            Optional[TradingDelayRecord]: 延迟记录，如果重复处理则返回None
        """
        
        # 检查是否已处理过（防重复）
        existing_record = await self.dao.find_by_trade_record_id(trade_record.id)
        if existing_record:
            logger.debug(f"交易记录 {trade_record.id} 已存在延迟计算，跳过处理")
            return None
        
        # 处理无信号的情况
        if not trade_record.signal_id:
            delay_record = self._create_delay_record(
                trade_record=trade_record,
                delay_status=DelayStatus.SIGNAL_MISSING
            )
            return delay_record
        
        # 解析KOL交易时间
        kol_time, hit_wallets, error_msg = await self.kol_resolver.resolve_kol_last_trade_time(
            trade_record
        )
        
        if error_msg or not kol_time:
            delay_record = self._create_delay_record(
                trade_record=trade_record,
                delay_status=DelayStatus.KOL_ACTIVITY_MISSING,
                hit_kol_wallets=hit_wallets,
                calculation_metadata={"error": error_msg}
            )
            return delay_record
        
        # 计算延迟（确保时区一致性）
        trade_execution_time = self._ensure_utc_timezone(trade_record.created_at)
        kol_trade_time = self._ensure_utc_timezone(kol_time)
        
        delay_seconds = (
            trade_execution_time - kol_trade_time
        ).total_seconds()
        
        # 判断延迟状态
        if delay_seconds < 0:
            delay_status = DelayStatus.TIMESTAMP_ANOMALY
        elif delay_seconds > 600:  # 10分钟
            delay_status = DelayStatus.EXCESSIVE_DELAY
        else:
            delay_status = DelayStatus.CALCULATED
        
        delay_record = self._create_delay_record(
            trade_record=trade_record,
            delay_status=delay_status,
            delay_seconds=round(delay_seconds, 2),
            hit_kol_wallets=hit_wallets,
            kol_last_trade_timestamp=kol_time
        )
        
        return delay_record
    
    def _ensure_utc_timezone(self, dt: datetime) -> datetime:
        """
        确保datetime对象有UTC时区信息
        
        Args:
            dt: 待处理的datetime对象
            
        Returns:
            datetime: 具有UTC时区信息的datetime对象
        """
        if dt.tzinfo is None:
            # 如果没有时区信息，假设为UTC
            return dt.replace(tzinfo=timezone.utc)
        elif dt.tzinfo != timezone.utc:
            # 如果有时区信息但不是UTC，转换为UTC
            return dt.astimezone(timezone.utc)
        else:
            # 已经是UTC时区，直接返回
            return dt
    
    def _extract_token_address(self, trade_record: 'TradeRecord') -> Optional[str]:
        """
        从交易记录中提取代币地址
        
        Args:
            trade_record: 交易记录
            
        Returns:
            代币地址，优先使用token_out_address，其次使用token_in_address
        """
        if hasattr(trade_record, 'token_out_address') and trade_record.token_out_address:
            return trade_record.token_out_address
        elif hasattr(trade_record, 'token_in_address') and trade_record.token_in_address:
            return trade_record.token_in_address
        else:
            return None
    
    def _extract_trade_amount(self, trade_record: 'TradeRecord') -> Optional[float]:
        """
        从交易记录中提取交易金额
        
        Args:
            trade_record: 交易记录
            
        Returns:
            交易金额，优先使用amount_out，其次使用amount_in
        """
        if hasattr(trade_record, 'amount_out') and trade_record.amount_out:
            return float(trade_record.amount_out)
        elif hasattr(trade_record, 'amount_in') and trade_record.amount_in:
            return float(trade_record.amount_in)
        else:
            return None

    def _create_delay_record(
        self, 
        trade_record: 'TradeRecord', 
        delay_status: DelayStatus = DelayStatus.CALCULATED,
        delay_seconds: Optional[float] = None,
        **kwargs
    ) -> 'TradingDelayRecord':
        """
        创建延迟记录实例的工厂方法
        
        Args:
            trade_record: 交易记录
            delay_status: 延迟状态
            delay_seconds: 延迟秒数
            **kwargs: 其他字段值
            
        Returns:
            TradingDelayRecord: 创建的延迟记录实例
        """
        record_data = {
            "trade_record_id": trade_record.id,
            "signal_id": trade_record.signal_id,
            "strategy_name": trade_record.strategy_name,
            "token_address": self._extract_token_address(trade_record),
            "trade_type": trade_record.trade_type.value if hasattr(trade_record.trade_type, 'value') else str(trade_record.trade_type),
            "trade_amount": self._extract_trade_amount(trade_record),
            "trade_execution_timestamp": trade_record.created_at,
            "delay_status": delay_status,
            "delay_seconds": delay_seconds,
            **kwargs
        }
        
        # 过滤掉None值
        filtered_data = {k: v for k, v in record_data.items() if v is not None}
        
        return TradingDelayRecord(**filtered_data)
    
    async def calculate_batch_delays(
        self,
        trade_records: List['TradeRecord']
    ) -> Dict[str, Any]:
        """
        批量计算多个交易记录的延迟
        
        Args:
            trade_records: 交易记录列表
            
        Returns:
            Dict: 处理统计信息
            {
                'total_processed': int,      # 总处理数
                'successful_calculations': int,  # 成功计算数
                'skipped_duplicates': int,   # 跳过重复数
                'saved_records': int         # 保存记录数
            }
        """
        logger.info(f"开始批量处理 {len(trade_records)} 个交易记录的延迟计算")
        
        statistics = {
            'total_processed': len(trade_records),
            'successful_calculations': 0,
            'skipped_duplicates': 0,
            'saved_records': 0
        }
        
        for trade_record in trade_records:
            try:
                delay_record = await self._calculate_single_trade_delay(trade_record)
                
                if delay_record is None:
                    # 重复记录，跳过
                    statistics['skipped_duplicates'] += 1
                else:
                    # 有计算结果，保存到数据库
                    statistics['successful_calculations'] += 1
                    await self.dao.save(delay_record)
                    statistics['saved_records'] += 1
                    
            except Exception as e:
                logger.error(f"处理交易记录 {trade_record.id} 时发生异常: {str(e)}")
                # 创建错误记录
                try:
                    error_record = self._create_delay_record(
                        trade_record=trade_record,
                        delay_status=DelayStatus.SIGNAL_MISSING,  # 使用一个默认状态
                        calculation_metadata={"error": str(e)}
                    )
                    await self.dao.save(error_record)
                    statistics['saved_records'] += 1
                except Exception as save_error:
                    logger.error(f"保存错误记录失败: {str(save_error)}")
        
        logger.info(f"批量处理完成: {statistics}")
        return statistics
    
    async def calculate_delays_for_unprocessed_trades(
        self, 
        batch_size: int = 50
    ) -> Dict[str, int]:
        """
        批量计算未处理交易的延迟
        
        遵循技术实现方案，查询未处理的交易记录并计算延迟
        
        Args:
            batch_size: 批量处理大小，默认50条
            
        Returns:
            Dict包含处理统计信息
            {
                'processed': int,     # 处理的记录数
                'calculated': int,    # 成功计算的记录数  
                'errors': int,        # 错误的记录数
                'skipped': int        # 跳过的记录数
            }
        """
        try:
            from dao.trade_record_dao import TradeRecordDAO
            
            # 获取未处理的交易记录
            trade_dao = TradeRecordDAO()
            unprocessed_trades = await trade_dao.find_unprocessed_trades(limit=batch_size)
            
            # 统计信息初始化
            stats = {
                'processed': 0,
                'calculated': 0,
                'errors': 0,
                'skipped': 0
            }
            
            logger.info(f"找到 {len(unprocessed_trades)} 个未处理的交易记录")
            
            for trade_record in unprocessed_trades:
                try:
                    delay_record = await self._calculate_single_trade_delay(trade_record)
                    
                    if delay_record:
                        await self.dao.save(delay_record)
                        if delay_record.delay_status == DelayStatus.CALCULATED:
                            stats['calculated'] += 1
                        else:
                            stats['skipped'] += 1
                    else:
                        stats['errors'] += 1
                        
                    stats['processed'] += 1
                    
                except Exception as e:
                    logger.error(f"处理交易记录 {trade_record.id} 时发生错误: {str(e)}")
                    stats['errors'] += 1
                    
            logger.info(f"延迟计算完成: {stats}")
            return stats
        
        except Exception as e:
            # 处理数据库未初始化或其他系统级异常
            logger.error(f"批量计算未处理交易延迟时发生错误: {str(e)}")
            return {
                'processed': 0,
                'calculated': 0,
                'errors': 1,
                'skipped': 0
            } 