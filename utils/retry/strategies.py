"""重试策略实现

实现了策略器模式的重试策略，包括：
- 抽象策略接口
- 指数退避策略
- 线性退避策略  
- 随机延迟策略
"""

import asyncio
import random
from abc import ABC, abstractmethod
from typing import Optional, Type, Union
import logging


class RetryStrategy(ABC):
    """重试策略抽象基类
    
    定义了所有重试策略必须实现的接口。
    """
    
    def __init__(self, max_retries: int = 3):
        """初始化重试策略
        
        Args:
            max_retries: 最大重试次数
        """
        self.max_retries = max_retries
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    async def calculate_delay(self, attempt: int, last_exception: Optional[Exception] = None) -> float:
        """计算重试延迟时间
        
        Args:
            attempt: 当前重试次数 (从1开始)
            last_exception: 上次执行的异常
            
        Returns:
            延迟时间（秒）
        """
        pass
    
    def should_retry(self, attempt: int, last_exception: Optional[Exception] = None) -> bool:
        """判断是否应该重试
        
        Args:
            attempt: 当前重试次数 (从1开始)
            last_exception: 上次执行的异常
            
        Returns:
            是否应该重试
        """
        # 默认实现：未超过最大重试次数就继续重试
        return attempt < self.max_retries
    
    def is_retryable_exception(self, exception: Exception) -> bool:
        """判断异常是否可重试
        
        Args:
            exception: 异常对象
            
        Returns:
            是否可重试
        """
        # 默认实现：大部分异常都可重试，除了一些明确不应重试的
        non_retryable_exceptions = (
            ValueError,  # 参数错误
            TypeError,   # 类型错误
            KeyboardInterrupt,  # 用户中断
        )
        return not isinstance(exception, non_retryable_exceptions)


class ExponentialBackoffStrategy(RetryStrategy):
    """指数退避策略
    
    重试延迟按指数增长：delay = base_delay * (multiplier ^ (attempt - 1))
    """
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0, 
                 multiplier: float = 2.0, max_delay: float = 60.0, jitter: bool = True):
        """初始化指数退避策略
        
        Args:
            max_retries: 最大重试次数
            base_delay: 基础延迟时间（秒）
            multiplier: 乘数因子
            max_delay: 最大延迟时间（秒）
            jitter: 是否添加随机抖动
        """
        super().__init__(max_retries)
        self.base_delay = base_delay
        self.multiplier = multiplier
        self.max_delay = max_delay
        self.jitter = jitter
    
    async def calculate_delay(self, attempt: int, last_exception: Optional[Exception] = None) -> float:
        """计算指数退避延迟"""
        # 计算基础延迟：base_delay * (multiplier ^ (attempt - 1))
        delay = self.base_delay * (self.multiplier ** (attempt - 1))
        
        # 限制最大延迟
        delay = min(delay, self.max_delay)
        
        # 添加随机抖动以避免"惊群效应"
        if self.jitter:
            # 添加±25%的随机抖动
            jitter_range = delay * 0.25
            delay += random.uniform(-jitter_range, jitter_range)
            delay = max(0, delay)  # 确保延迟不为负数
        
        self.logger.debug(f"指数退避策略: 第{attempt}次重试，延迟{delay:.2f}秒")
        return delay


class LinearBackoffStrategy(RetryStrategy):
    """线性退避策略
    
    重试延迟线性增长：delay = base_delay + (increment * (attempt - 1))
    """
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0,
                 increment: float = 1.0, max_delay: float = 60.0):
        """初始化线性退避策略
        
        Args:
            max_retries: 最大重试次数
            base_delay: 基础延迟时间（秒）
            increment: 每次增加的延迟时间（秒）
            max_delay: 最大延迟时间（秒）
        """
        super().__init__(max_retries)
        self.base_delay = base_delay
        self.increment = increment
        self.max_delay = max_delay
    
    async def calculate_delay(self, attempt: int, last_exception: Optional[Exception] = None) -> float:
        """计算线性退避延迟"""
        # 计算线性延迟：base_delay + (increment * (attempt - 1))
        delay = self.base_delay + (self.increment * (attempt - 1))
        
        # 限制最大延迟
        delay = min(delay, self.max_delay)
        
        self.logger.debug(f"线性退避策略: 第{attempt}次重试，延迟{delay:.2f}秒")
        return delay


class RandomDelayStrategy(RetryStrategy):
    """随机延迟策略
    
    重试延迟在指定范围内随机选择
    """
    
    def __init__(self, max_retries: int = 3, min_delay: float = 0.5, max_delay: float = 5.0):
        """初始化随机延迟策略
        
        Args:
            max_retries: 最大重试次数
            min_delay: 最小延迟时间（秒）
            max_delay: 最大延迟时间（秒）
        """
        super().__init__(max_retries)
        self.min_delay = min_delay
        self.max_delay = max_delay
        
        if min_delay >= max_delay:
            raise ValueError("min_delay must be less than max_delay")
    
    async def calculate_delay(self, attempt: int, last_exception: Optional[Exception] = None) -> float:
        """计算随机延迟"""
        # 在指定范围内随机选择延迟时间
        delay = random.uniform(self.min_delay, self.max_delay)
        
        self.logger.debug(f"随机延迟策略: 第{attempt}次重试，延迟{delay:.2f}秒")
        return delay


class FixedDelayStrategy(RetryStrategy):
    """固定延迟策略
    
    每次重试使用固定的延迟时间
    """
    
    def __init__(self, max_retries: int = 3, delay: float = 1.0):
        """初始化固定延迟策略
        
        Args:
            max_retries: 最大重试次数
            delay: 固定延迟时间（秒）
        """
        super().__init__(max_retries)
        self.delay = delay
    
    async def calculate_delay(self, attempt: int, last_exception: Optional[Exception] = None) -> float:
        """返回固定延迟"""
        self.logger.debug(f"固定延迟策略: 第{attempt}次重试，延迟{self.delay:.2f}秒")
        return self.delay


class NoDelayStrategy(RetryStrategy):
    """无延迟策略
    
    立即重试，不等待
    """
    
    def __init__(self, max_retries: int = 3):
        """初始化无延迟策略
        
        Args:
            max_retries: 最大重试次数
        """
        super().__init__(max_retries)
    
    async def calculate_delay(self, attempt: int, last_exception: Optional[Exception] = None) -> float:
        """返回0延迟"""
        self.logger.debug(f"无延迟策略: 第{attempt}次重试，立即执行")
        return 0.0 