"""重试模块

提供策略器+断路器模式的重试机制，支持多种重试策略和系统过载保护。

主要组件：
- RetryStrategy: 重试策略接口
- CircuitBreaker: 断路器实现
- RetryManager: 重试管理器
- 装饰器: 便捷的重试装饰器

使用示例：
    from utils.retry import ExponentialBackoffStrategy, RetryManager
    
    strategy = ExponentialBackoffStrategy()
    manager = RetryManager(strategy)
    result = await manager.execute_with_retry(some_async_function)
"""

from .strategies import (
    RetryStrategy,
    ExponentialBackoffStrategy, 
    LinearBackoffStrategy,
    RandomDelayStrategy,
    FixedDelayStrategy,
    NoDelayStrategy,
)
from .circuit_breaker import CircuitBreaker, CircuitBreakerState
from .retry_manager import RetryManager
from .decorators import retry_with_strategy, async_retry_with_strategy

__all__ = [
    # 策略类
    'RetryStrategy',
    'ExponentialBackoffStrategy',
    'LinearBackoffStrategy', 
    'RandomDelayStrategy',
    'FixedDelayStrategy',
    'NoDelayStrategy',
    # 断路器
    'CircuitBreaker',
    'CircuitBreakerState',
    # 管理器
    'RetryManager',
    # 装饰器
    'retry_with_strategy',
    'async_retry_with_strategy',
] 