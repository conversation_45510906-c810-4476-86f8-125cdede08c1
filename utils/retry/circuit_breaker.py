"""断路器实现

实现断路器模式来防止系统过载，支持：
- 三种状态：Closed（正常）、Open（断开）、Half-Open（半开）
- 失败计数和阈值判断
- 自动恢复机制
"""

import asyncio
import time
from enum import Enum
from typing import Optional, Callable, Any
import logging


class CircuitBreakerState(Enum):
    """断路器状态枚举"""
    CLOSED = "closed"        # 正常状态，允许请求通过
    OPEN = "open"           # 断开状态，拒绝所有请求
    HALF_OPEN = "half_open"  # 半开状态，允许少量请求测试服务是否恢复


class CircuitBreakerException(Exception):
    """断路器异常
    
    当断路器处于开启状态时抛出此异常
    """
    pass


class CircuitBreaker:
    """断路器实现
    
    实现了断路器模式来防止系统过载和级联故障。
    """
    
    def __init__(self, 
                 failure_threshold: int = 5,
                 recovery_timeout: float = 30.0,
                 expected_exception: type = Exception,
                 success_threshold: int = 1):
        """初始化断路器
        
        Args:
            failure_threshold: 失败阈值，超过此值会打开断路器
            recovery_timeout: 恢复超时时间（秒），断路器打开后等待多久尝试恢复
            expected_exception: 期待的异常类型，只有这类异常才会计入失败次数
            success_threshold: 半开状态下需要连续成功的次数才能关闭断路器
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        self.success_threshold = success_threshold
        
        # 状态管理
        self._state = CircuitBreakerState.CLOSED
        self._failure_count = 0
        self._success_count = 0
        self._last_failure_time: Optional[float] = None
        
        # 统计信息
        self._total_calls = 0
        self._total_failures = 0
        self._total_successes = 0
        
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @property
    def state(self) -> CircuitBreakerState:
        """获取当前状态"""
        return self._state
    
    @property
    def failure_count(self) -> int:
        """获取失败计数"""
        return self._failure_count
    
    @property
    def success_count(self) -> int:
        """获取成功计数"""
        return self._success_count
    
    def get_stats(self) -> dict:
        """获取统计信息"""
        return {
            'state': self._state.value,
            'failure_count': self._failure_count,
            'success_count': self._success_count,
            'total_calls': self._total_calls,
            'total_failures': self._total_failures,
            'total_successes': self._total_successes,
            'failure_rate': self._total_failures / max(self._total_calls, 1),
            'last_failure_time': self._last_failure_time,
        }
    
    def reset(self):
        """重置断路器到初始状态"""
        self._state = CircuitBreakerState.CLOSED
        self._failure_count = 0
        self._success_count = 0
        self._last_failure_time = None
        self.logger.info("断路器已重置")
    
    def _should_attempt_reset(self) -> bool:
        """判断是否应该尝试重置（从OPEN转到HALF_OPEN）"""
        if self._state != CircuitBreakerState.OPEN:
            return False
        
        if self._last_failure_time is None:
            return False
        
        # 检查是否超过了恢复超时时间
        return time.time() - self._last_failure_time >= self.recovery_timeout
    
    def _record_success(self):
        """记录成功执行"""
        self._total_calls += 1
        self._total_successes += 1
        
        if self._state == CircuitBreakerState.HALF_OPEN:
            self._success_count += 1
            self.logger.debug(f"半开状态成功计数: {self._success_count}/{self.success_threshold}")
            
            # 在半开状态下，如果连续成功次数达到阈值，则关闭断路器
            if self._success_count >= self.success_threshold:
                self._state = CircuitBreakerState.CLOSED
                self._failure_count = 0
                self._success_count = 0
                self.logger.info("断路器已关闭，服务恢复正常")
        
        elif self._state == CircuitBreakerState.CLOSED:
            # 在关闭状态下成功，重置失败计数
            self._failure_count = 0
    
    def _record_failure(self, exception: Exception):
        """记录失败执行"""
        self._total_calls += 1
        self._total_failures += 1
        self._last_failure_time = time.time()
        
        # 只有期待的异常类型才计入失败
        if not isinstance(exception, self.expected_exception):
            self.logger.debug(f"异常类型 {type(exception)} 不计入失败统计")
            return
        
        if self._state == CircuitBreakerState.CLOSED:
            self._failure_count += 1
            self.logger.debug(f"失败计数: {self._failure_count}/{self.failure_threshold}")
            
            # 如果失败次数达到阈值，打开断路器
            if self._failure_count >= self.failure_threshold:
                self._state = CircuitBreakerState.OPEN
                self.logger.warning(f"断路器已打开，失败次数: {self._failure_count}")
        
        elif self._state == CircuitBreakerState.HALF_OPEN:
            # 在半开状态下失败，立即返回开启状态
            self._state = CircuitBreakerState.OPEN
            self._success_count = 0
            self.logger.warning("半开状态下执行失败，断路器重新打开")
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """通过断路器执行函数
        
        Args:
            func: 要执行的函数
            *args: 函数位置参数
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果
            
        Raises:
            CircuitBreakerException: 当断路器处于开启状态时
        """
        # 检查是否需要从OPEN转到HALF_OPEN
        if self._should_attempt_reset():
            self._state = CircuitBreakerState.HALF_OPEN
            self._success_count = 0
            self.logger.info("断路器进入半开状态，开始测试服务")
        
        # 如果断路器是开启状态，直接拒绝请求
        if self._state == CircuitBreakerState.OPEN:
            self.logger.debug("断路器开启，拒绝请求")
            raise CircuitBreakerException("断路器开启，服务暂时不可用")
        
        # 执行函数
        try:
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            # 记录成功
            self._record_success()
            return result
            
        except Exception as e:
            # 记录失败
            self._record_failure(e)
            raise
    
    def __call__(self, func: Callable) -> Callable:
        """装饰器形式使用断路器"""
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                return await self.call(func, *args, **kwargs)
            return async_wrapper
        else:
            def sync_wrapper(*args, **kwargs):
                return asyncio.run(self.call(func, *args, **kwargs))
            return sync_wrapper
    
    def __repr__(self) -> str:
        """字符串表示"""
        return (f"CircuitBreaker(state={self._state.value}, "
                f"failures={self._failure_count}/{self.failure_threshold}, "
                f"successes={self._success_count})")


class AsyncCircuitBreaker(CircuitBreaker):
    """异步专用断路器
    
    针对异步函数优化的断路器实现
    """
    
    async def __call__(self, func: Callable, *args, **kwargs) -> Any:
        """异步调用"""
        return await self.call(func, *args, **kwargs) 