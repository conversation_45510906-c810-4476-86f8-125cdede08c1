"""重试装饰器

提供便捷的装饰器接口，可以直接在函数上使用：
- @retry_with_strategy: 函数/方法装饰器
- @async_retry_with_strategy: 异步函数装饰器
"""

import asyncio
import functools
from typing import Any, Callable, Optional, Union, Tuple, Type

from .strategies import RetryStrategy, ExponentialBackoffStrategy
from .circuit_breaker import CircuitBreaker
from .retry_manager import RetryManager


def retry_with_strategy(strategy: Optional[RetryStrategy] = None,
                       max_retries: int = 3,
                       enable_circuit_breaker: bool = False,
                       circuit_breaker: Optional[CircuitBreaker] = None,
                       retry_on: Optional[Union[Type[Exception], Tuple[Type[Exception], ...]]] = None,
                       stop_on: Optional[Union[Type[Exception], Tuple[Type[Exception], ...]]] = None):
    """重试装饰器（同步函数）
    
    Args:
        strategy: 重试策略，默认使用指数退避策略
        max_retries: 最大重试次数
        enable_circuit_breaker: 是否启用断路器
        circuit_breaker: 断路器实例
        retry_on: 遇到这些异常时重试
        stop_on: 遇到这些异常时停止重试
    
    Returns:
        装饰器函数
    
    Examples:
        @retry_with_strategy(max_retries=5)
        def unstable_function():
            # 可能失败的函数
            pass
        
        @retry_with_strategy(
            strategy=ExponentialBackoffStrategy(base_delay=2.0),
            enable_circuit_breaker=True
        )
        def api_call():
            # API调用
            pass
    """
    def decorator(func: Callable) -> Callable:
        # 创建重试管理器
        retry_strategy = strategy or ExponentialBackoffStrategy(max_retries=max_retries)
        if strategy is None:
            retry_strategy.max_retries = max_retries
        
        manager = RetryManager(
            strategy=retry_strategy,
            circuit_breaker=circuit_breaker,
            enable_circuit_breaker=enable_circuit_breaker
        )
        
        if asyncio.iscoroutinefunction(func):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                return await manager.execute_with_retry(
                    func, *args, 
                    retry_on=retry_on,
                    stop_on=stop_on,
                    **kwargs
                )
            return async_wrapper
        else:
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                # 对于同步函数，需要在事件循环中运行
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        # 如果已有事件循环在运行，直接传递coroutine给run_coroutine_threadsafe
                        # 注意：不要先创建task，直接传递coroutine
                        return asyncio.run_coroutine_threadsafe(
                            manager.execute_with_retry(
                                func, *args,
                                retry_on=retry_on,
                                stop_on=stop_on,
                                **kwargs
                            ), loop
                        ).result()
                    else:
                        # 如果没有事件循环，创建新的
                        return loop.run_until_complete(
                            manager.execute_with_retry(
                                func, *args,
                                retry_on=retry_on,
                                stop_on=stop_on,
                                **kwargs
                            )
                        )
                except RuntimeError:
                    # 如果获取事件循环失败，创建新的
                    return asyncio.run(
                        manager.execute_with_retry(
                            func, *args,
                            retry_on=retry_on,
                            stop_on=stop_on,
                            **kwargs
                        )
                    )
            
            return sync_wrapper
    
    return decorator


def async_retry_with_strategy(strategy: Optional[RetryStrategy] = None,
                             max_retries: int = 3,
                             enable_circuit_breaker: bool = False,
                             circuit_breaker: Optional[CircuitBreaker] = None,
                             retry_on: Optional[Union[Type[Exception], Tuple[Type[Exception], ...]]] = None,
                             stop_on: Optional[Union[Type[Exception], Tuple[Type[Exception], ...]]] = None):
    """异步重试装饰器
    
    专用于异步函数的重试装饰器，使用更简单。
    
    Args:
        strategy: 重试策略，默认使用指数退避策略
        max_retries: 最大重试次数
        enable_circuit_breaker: 是否启用断路器
        circuit_breaker: 断路器实例
        retry_on: 遇到这些异常时重试
        stop_on: 遇到这些异常时停止重试
    
    Returns:
        装饰器函数
    
    Examples:
        @async_retry_with_strategy(max_retries=5)
        async def unstable_async_function():
            # 可能失败的异步函数
            pass
        
        @async_retry_with_strategy(
            strategy=LinearBackoffStrategy(increment=0.5),
            enable_circuit_breaker=True
        )
        async def async_api_call():
            # 异步API调用
            pass
    """
    def decorator(func: Callable) -> Callable:
        if not asyncio.iscoroutinefunction(func):
            raise TypeError(f"async_retry_with_strategy only supports async functions, "
                          f"but {func.__name__} is not async")
        
        # 创建重试管理器
        retry_strategy = strategy or ExponentialBackoffStrategy(max_retries=max_retries)
        if strategy is None:
            retry_strategy.max_retries = max_retries
        
        manager = RetryManager(
            strategy=retry_strategy,
            circuit_breaker=circuit_breaker,
            enable_circuit_breaker=enable_circuit_breaker
        )
        
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            return await manager.execute_with_retry(
                func, *args,
                retry_on=retry_on,
                stop_on=stop_on,
                **kwargs
            )
        
        return wrapper
    
    return decorator


def circuit_breaker_only(circuit_breaker: Optional[CircuitBreaker] = None,
                        failure_threshold: int = 5,
                        recovery_timeout: float = 30.0):
    """仅使用断路器的装饰器（不重试）
    
    Args:
        circuit_breaker: 断路器实例
        failure_threshold: 失败阈值
        recovery_timeout: 恢复超时时间
    
    Returns:
        装饰器函数
    
    Examples:
        @circuit_breaker_only(failure_threshold=3)
        async def critical_service():
            # 关键服务，失败时快速熔断
            pass
    """
    def decorator(func: Callable) -> Callable:
        # 创建断路器
        cb = circuit_breaker or CircuitBreaker(
            failure_threshold=failure_threshold,
            recovery_timeout=recovery_timeout
        )
        
        manager = RetryManager(
            strategy=None,
            circuit_breaker=cb,
            enable_circuit_breaker=True
        )
        
        if asyncio.iscoroutinefunction(func):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                return await manager.execute_with_circuit_breaker_only(func, *args, **kwargs)
            return async_wrapper
        else:
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                try:
                    loop = asyncio.get_event_loop()
                    return loop.run_until_complete(
                        manager.execute_with_circuit_breaker_only(func, *args, **kwargs)
                    )
                except RuntimeError:
                    return asyncio.run(
                        manager.execute_with_circuit_breaker_only(func, *args, **kwargs)
                    )
            return sync_wrapper
    
    return decorator


# 便捷别名
retry = retry_with_strategy
async_retry = async_retry_with_strategy 