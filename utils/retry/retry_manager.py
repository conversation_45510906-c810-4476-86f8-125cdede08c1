"""重试管理器

统一管理重试逻辑，整合重试策略和断路器：
- 执行重试逻辑
- 条件判断（异常类型、HTTP状态码等）
- 统计信息和日志记录
"""

import asyncio
import time
from typing import Any, Callable, Optional, Union, Tuple, Type
import logging
import traceback

from .strategies import RetryStrategy, ExponentialBackoffStrategy
from .circuit_breaker import CircuitBreaker, CircuitBreakerException


class RetryManager:
    """重试管理器
    
    整合重试策略和断路器，提供统一的重试执行接口。
    """
    
    def __init__(self, 
                 strategy: Optional[RetryStrategy] = None,
                 circuit_breaker: Optional[CircuitBreaker] = None,
                 enable_circuit_breaker: bool = True):
        """初始化重试管理器
        
        Args:
            strategy: 重试策略，默认使用指数退避策略
            circuit_breaker: 断路器实例，默认创建一个新的断路器
            enable_circuit_breaker: 是否启用断路器
        """
        self.strategy = strategy or ExponentialBackoffStrategy()
        self.enable_circuit_breaker = enable_circuit_breaker
        
        if enable_circuit_breaker:
            self.circuit_breaker = circuit_breaker or CircuitBreaker()
        else:
            self.circuit_breaker = None
        
        # 统计信息
        self._total_executions = 0
        self._total_retries = 0
        self._total_failures = 0
        self._total_successes = 0
        
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def get_stats(self) -> dict:
        """获取统计信息"""
        stats = {
            'total_executions': self._total_executions,
            'total_retries': self._total_retries,
            'total_failures': self._total_failures,
            'total_successes': self._total_successes,
            'success_rate': self._total_successes / max(self._total_executions, 1),
            'strategy': self.strategy.__class__.__name__,
        }
        
        if self.circuit_breaker:
            stats['circuit_breaker'] = self.circuit_breaker.get_stats()
        
        return stats
    
    def reset_stats(self):
        """重置统计信息"""
        self._total_executions = 0
        self._total_retries = 0
        self._total_failures = 0
        self._total_successes = 0
        
        if self.circuit_breaker:
            self.circuit_breaker.reset()
    
    async def execute_with_retry(self, 
                                func: Callable,
                                *args,
                                retry_on: Optional[Union[Type[Exception], Tuple[Type[Exception], ...]]] = None,
                                stop_on: Optional[Union[Type[Exception], Tuple[Type[Exception], ...]]] = None,
                                before_retry: Optional[Callable] = None,
                                after_retry: Optional[Callable] = None,
                                **kwargs) -> Any:
        """执行带重试的函数调用
        
        Args:
            func: 要执行的函数
            *args: 函数位置参数
            retry_on: 遇到这些异常时重试，默认为所有异常
            stop_on: 遇到这些异常时停止重试，优先级高于retry_on
            before_retry: 重试前的回调函数
            after_retry: 重试后的回调函数
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果
            
        Raises:
            最后一次执行的异常
        """
        self._total_executions += 1
        start_time = time.time()
        attempt = 0
        last_exception = None
        
        self.logger.debug(f"开始执行 {func.__name__}，策略: {self.strategy.__class__.__name__}")
        
        while attempt <= self.strategy.max_retries:
            attempt += 1
            
            try:
                # 通过断路器执行
                if self.circuit_breaker:
                    result = await self.circuit_breaker.call(func, *args, **kwargs)
                else:
                    if asyncio.iscoroutinefunction(func):
                        result = await func(*args, **kwargs)
                    else:
                        result = func(*args, **kwargs)
                
                # 执行成功
                execution_time = time.time() - start_time
                self._total_successes += 1
                
                if attempt > 1:
                    self._total_retries += attempt - 1
                    self.logger.info(f"{func.__name__} 在第{attempt}次尝试后成功，耗时{execution_time:.2f}秒")
                else:
                    self.logger.debug(f"{func.__name__} 首次执行成功，耗时{execution_time:.2f}秒")
                
                # 执行成功后的回调
                if after_retry and attempt > 1:
                    try:
                        if asyncio.iscoroutinefunction(after_retry):
                            await after_retry(attempt, None, result)
                        else:
                            after_retry(attempt, None, result)
                    except Exception as e:
                        self.logger.warning(f"after_retry回调执行失败: {e}")
                
                return result
                
            except CircuitBreakerException:
                # 断路器开启，直接失败
                self.logger.warning(f"断路器开启，{func.__name__} 执行被拒绝")
                self._total_failures += 1
                raise
                
            except Exception as e:
                last_exception = e
                
                # 检查是否应该停止重试
                if stop_on and isinstance(e, stop_on):
                    self.logger.info(f"遇到停止异常 {type(e).__name__}，停止重试")
                    break
                
                # 检查是否应该重试
                if retry_on and not isinstance(e, retry_on):
                    self.logger.info(f"异常 {type(e).__name__} 不在重试列表中，停止重试")
                    break
                
                # 检查策略是否允许重试
                if not self.strategy.should_retry(attempt, e):
                    self.logger.info(f"策略不允许第{attempt}次重试")
                    break
                
                # 检查异常是否可重试
                if not self.strategy.is_retryable_exception(e):
                    self.logger.info(f"异常 {type(e).__name__} 不可重试")
                    break
                
                # 如果还能重试，计算延迟时间
                if attempt <= self.strategy.max_retries:
                    delay = await self.strategy.calculate_delay(attempt, e)
                    
                    self.logger.info(f"{func.__name__} 第{attempt}次执行失败: {str(e)}, "
                                   f"将在{delay:.2f}秒后重试")
                    
                    # 执行重试前的回调
                    if before_retry:
                        try:
                            if asyncio.iscoroutinefunction(before_retry):
                                await before_retry(attempt, e, delay)
                            else:
                                before_retry(attempt, e, delay)
                        except Exception as callback_error:
                            self.logger.warning(f"before_retry回调执行失败: {callback_error}")
                    
                    # 等待延迟时间
                    if delay > 0:
                        await asyncio.sleep(delay)
                else:
                    self.logger.warning(f"{func.__name__} 第{attempt}次执行失败: {str(e)}")
        
        # 所有重试都失败
        execution_time = time.time() - start_time
        self._total_failures += 1
        self._total_retries += attempt - 1
        
        self.logger.error(f"{func.__name__} 所有重试都失败，总计{attempt}次尝试，"
                         f"耗时{execution_time:.2f}秒，最后异常: {str(last_exception)}")
        
        # 执行失败后的回调
        if after_retry:
            try:
                if asyncio.iscoroutinefunction(after_retry):
                    await after_retry(attempt, last_exception, None)
                else:
                    after_retry(attempt, last_exception, None)
            except Exception as callback_error:
                self.logger.warning(f"after_retry回调执行失败: {callback_error}")
        
        # 抛出最后一个异常
        if last_exception:
            raise last_exception
        else:
            raise Exception("所有重试都失败，但没有捕获到异常")
    
    async def execute_with_circuit_breaker_only(self, func: Callable, *args, **kwargs) -> Any:
        """仅使用断路器执行（不重试）
        
        Args:
            func: 要执行的函数
            *args: 函数位置参数
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果
        """
        if not self.circuit_breaker:
            if asyncio.iscoroutinefunction(func):
                return await func(*args, **kwargs)
            else:
                return func(*args, **kwargs)
        
        return await self.circuit_breaker.call(func, *args, **kwargs)
    
    def configure_strategy(self, **strategy_params):
        """配置重试策略参数
        
        Args:
            **strategy_params: 策略参数
        """
        # 更新策略参数
        for param, value in strategy_params.items():
            if hasattr(self.strategy, param):
                setattr(self.strategy, param, value)
                self.logger.debug(f"更新策略参数 {param} = {value}")
    
    def configure_circuit_breaker(self, **breaker_params):
        """配置断路器参数
        
        Args:
            **breaker_params: 断路器参数
        """
        if not self.circuit_breaker:
            self.logger.warning("断路器未启用，无法配置参数")
            return
        
        # 更新断路器参数
        for param, value in breaker_params.items():
            if hasattr(self.circuit_breaker, param):
                setattr(self.circuit_breaker, param, value)
                self.logger.debug(f"更新断路器参数 {param} = {value}")
    
    def __repr__(self) -> str:
        """字符串表示"""
        cb_info = f", circuit_breaker={self.circuit_breaker}" if self.circuit_breaker else ""
        return f"RetryManager(strategy={self.strategy}{cb_info})" 