"""
工作流模块

提供工作流的抽象接口和基本实现
"""

import asyncio
import logging
import traceback
import signal
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from .nodes import Node, NodeStatus, InputNode, ProcessNode, OutputNode, StorageNode
from utils.workflows.message_queue.message_queue import create_message_queue, MessageQueue

class Workflow:
    """工作流类"""
    
    def __init__(self, name: str = None):
        """初始化工作流
        
        Args:
            name: 工作流名称
        """
        self.name = name or self.__class__.__name__
        self.logger = logging.getLogger(self.name)
        self.nodes: Dict[str, Node] = {}  # 节点字典，键为节点名称
        self.tasks: Dict[str, asyncio.Task] = {}  # 任务字典，键为节点名称
        self._stop = False
        self.node_connections: List[Tuple[str, str, str]] = []  # 节点连接列表，元素为(源节点名称, 目标节点名称, 队列名称)
    
    def add_node(self, node: Node, connect_to: Optional[Node] = None, queue_name: Optional[str] = None) -> Node:
        """添加节点到工作流
        
        Args:
            node: 要添加的节点
            connect_to: 要连接到的上游节点，如果为None则不连接
            queue_name: 连接队列名称，如果为None则自动生成
            
        Returns:
            Node: 添加的节点
        """
        self.nodes[node.name] = node
        self.logger.debug(f"节点 {node.name} 已添加到工作流")
        
        # 如果指定了上游节点，则自动连接
        if connect_to is not None:
            self.connect_nodes(connect_to, node, queue_name)
        
        return node
    
    def connect_nodes(self, source_node: Node, target_node: Node, queue_name: Optional[str] = None) -> MessageQueue:
        """连接两个节点，自动创建消息队列
        
        Args:
            source_node: 源节点
            target_node: 目标节点
            queue_name: 队列名称，如果不指定则自动生成
            
        Returns:
            MessageQueue: 创建的消息队列
        """
        # 获取节点名称
        source_name = source_node.name if isinstance(source_node, Node) else source_node
        target_name = target_node.name if isinstance(target_node, Node) else target_node
        
        # 如果传入的是节点名称，获取对应的节点对象
        if isinstance(source_node, str):
            source_node = self.nodes.get(source_node)
            if not source_node:
                raise ValueError(f"找不到名为 {source_name} 的源节点")
        
        if isinstance(target_node, str):
            target_node = self.nodes.get(target_node)
            if not target_node:
                raise ValueError(f"找不到名为 {target_name} 的目标节点")
        
        # 生成队列名称
        if queue_name is None:
            queue_name = f"{source_name}_to_{target_name}"
        
        # 创建消息队列
        queue = create_message_queue("kafka", queue_name)
        
        # 设置源节点的输出队列
        if isinstance(source_node, InputNode) or isinstance(source_node, ProcessNode):
            source_node.output_queues.append(queue)
            # 只有在节点没有标记为待处理流量控制器时才立即初始化
            # 如果有待处理标记，说明会在工作流配置完成后统一初始化
            if not hasattr(source_node, '_flow_controller_pending') or not source_node._flow_controller_pending:
                source_node.setup_flow_controller()
        else:
            self.logger.warning(f"节点 {source_name} 不是输入节点或处理节点，无法设置输出队列")
        
        # 设置目标节点的输入队列
        if isinstance(target_node, ProcessNode) or isinstance(target_node, OutputNode) or isinstance(target_node, StorageNode):
            target_node.input_queue = queue
        else:
            self.logger.warning(f"节点 {target_name} 不是处理节点或输出节点，无法设置输入队列")
        
        # 记录连接关系
        self.node_connections.append((source_name, target_name, queue_name))
        
        self.logger.info(f"已连接节点: {source_name} -> {queue_name} -> {target_name}")
        
        return queue
    
    def get_node_connections(self) -> List[Tuple[str, str, str]]:
        """获取节点连接关系
        
        Returns:
            List[Tuple[str, str, str]]: 节点连接列表，元素为(源节点名称, 目标节点名称, 队列名称)
        """
        return self.node_connections
    
    def stop(self):
        """停止工作流"""
        self._stop = True
        self.logger.info(f"工作流 {self.name} 停止中...")
        
        # 停止所有节点
        for node in self.nodes.values():
            node.stop()
        
        # 取消所有任务
        for task_name, task in self.tasks.items():
            if not task.done():
                task.cancel()
                self.logger.debug(f"任务 {task_name} 已取消")
        
        self.logger.info(f"工作流 {self.name} 已停止")
    
    async def run(self):
        """运行工作流"""
        self.logger.info(f"开始执行工作流 {self.name}")
        self._stop = False
        
        try:
            # 创建并启动所有节点的任务
            for node_name, node in self.nodes.items():
                task = asyncio.create_task(node.run(), name=node_name)
                self.tasks[node_name] = task
                self.logger.debug(f"节点 {node_name} 任务已创建")
            
            # 等待所有任务完成或工作流停止
            pending = list(self.tasks.values())
            while pending and not self._stop:
                done, pending = await asyncio.wait(
                    pending,
                    timeout=1.0,
                    return_when=asyncio.FIRST_COMPLETED
                )
                
                # 检查已完成的任务
                for task in done:
                    node_name = task.get_name()
                    try:
                        result = task.result()
                        self.logger.info(f"节点 {node_name} 任务已完成")
                    except asyncio.CancelledError:
                        self.logger.info(f"节点 {node_name} 任务已取消")
                    except Exception as e:
                        self.logger.error(f"节点 {node_name} 任务出错: {str(e)}\n{traceback.format_exc()}")
                        # 如果有节点出错，停止工作流
                        self.stop()
            
            self.logger.info(f"工作流 {self.name} 执行完成")
        except asyncio.CancelledError:
            self.logger.info(f"工作流 {self.name} 被取消")
            self.stop()
        except Exception as e:
            self.logger.error(f"工作流 {self.name} 执行出错: {str(e)}\n{traceback.format_exc()}")
            self.stop()
            raise

class WorkflowManager:
    """工作流管理器
    
    负责管理和运行多个工作流
    """
    
    def __init__(self):
        """初始化工作流管理器"""
        self.logger = logging.getLogger("WorkflowManager")
        self.workflows: Dict[str, Workflow] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self._stop = False
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, sig, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {sig}，准备停止所有工作流")
        self.stop_all()
    
    def register_workflow(self, workflow: Workflow) -> None:
        """注册工作流
        
        Args:
            workflow: 要注册的工作流
        """
        self.workflows[workflow.name] = workflow
        self.logger.info(f"工作流 {workflow.name} 已注册")
    
    def unregister_workflow(self, workflow_name: str) -> None:
        """注销工作流
        
        Args:
            workflow_name: 要注销的工作流名称
        """
        if workflow_name in self.workflows:
            # 如果工作流正在运行，先停止它
            if workflow_name in self.running_tasks:
                self.stop_workflow(workflow_name)
            
            # 从字典中删除工作流
            del self.workflows[workflow_name]
            self.logger.info(f"工作流 {workflow_name} 已注销")
        else:
            self.logger.warning(f"工作流 {workflow_name} 不存在，无法注销")
    
    async def start_workflow(self, workflow_name: str) -> None:
        """启动工作流
        
        Args:
            workflow_name: 要启动的工作流名称
        """
        if workflow_name not in self.workflows:
            self.logger.warning(f"工作流 {workflow_name} 不存在，无法启动")
            return
        
        if workflow_name in self.running_tasks:
            self.logger.warning(f"工作流 {workflow_name} 已经在运行中")
            return
        
        # 获取工作流
        workflow = self.workflows[workflow_name]
        
        # 创建并启动工作流任务
        task = asyncio.create_task(workflow.run(), name=workflow_name)
        self.running_tasks[workflow_name] = task
        self.logger.info(f"工作流 {workflow_name} 已启动")
    
    def stop_workflow(self, workflow_name: str) -> None:
        """停止工作流
        
        Args:
            workflow_name: 要停止的工作流名称
        """
        if workflow_name not in self.workflows:
            self.logger.warning(f"工作流 {workflow_name} 不存在，无法停止")
            return
        
        if workflow_name not in self.running_tasks:
            self.logger.warning(f"工作流 {workflow_name} 未在运行中，无法停止")
            return
        
        # 获取工作流和任务
        workflow = self.workflows[workflow_name]
        task = self.running_tasks[workflow_name]
        
        # 停止工作流
        workflow.stop()
        
        # 取消任务
        if not task.done():
            task.cancel()
        
        # 从运行中的任务字典中删除
        del self.running_tasks[workflow_name]
        self.logger.info(f"工作流 {workflow_name} 已停止")
    
    async def start_all(self) -> None:
        """启动所有工作流"""
        self.logger.info("开始启动所有工作流")
        for workflow_name in self.workflows:
            await self.start_workflow(workflow_name)
    
    def stop_all(self) -> None:
        """停止所有工作流"""
        self.logger.info("开始停止所有工作流")
        self._stop = True
        
        # 复制一份工作流名称列表，避免在迭代过程中修改字典
        workflow_names = list(self.running_tasks.keys())
        for workflow_name in workflow_names:
            self.stop_workflow(workflow_name)
    
    async def run(self) -> None:
        """运行工作流管理器"""
        self.logger.info("工作流管理器开始运行")
        self._stop = False
        
        try:
            # 启动所有工作流
            await self.start_all()
            
            # 等待所有工作流完成或管理器停止
            while self.running_tasks and not self._stop:
                # 获取所有运行中的任务
                tasks = list(self.running_tasks.values())
                
                # 等待任务完成或超时
                done, pending = await asyncio.wait(
                    tasks,
                    timeout=1.0,
                    return_when=asyncio.FIRST_COMPLETED
                )
                
                # 处理已完成的任务
                for task in done:
                    workflow_name = task.get_name()
                    try:
                        result = task.result()
                        self.logger.info(f"工作流 {workflow_name} 已完成")
                    except asyncio.CancelledError:
                        self.logger.info(f"工作流 {workflow_name} 已取消")
                    except Exception as e:
                        self.logger.error(f"工作流 {workflow_name} 出错: {str(e)}\n{traceback.format_exc()}")
                    
                    # 从运行中的任务字典中删除
                    if workflow_name in self.running_tasks:
                        del self.running_tasks[workflow_name]
            
            self.logger.info("工作流管理器运行完成")
        except asyncio.CancelledError:
            self.logger.info("工作流管理器被取消")
            self.stop_all()
        except Exception as e:
            self.logger.error(f"工作流管理器运行出错: {str(e)}\n{traceback.format_exc()}")
            self.stop_all()
            raise

# 创建全局工作流管理器实例
workflow_manager = WorkflowManager()