"""
基本节点模块

提供节点的抽象基类和状态定义
"""

import asyncio
import logging
import traceback
from abc import ABC, abstractmethod
from typing import List, Any, Optional

from utils.workflows.message_queue.message_queue import Message
from utils.workflows.message_queue.message_manager import KafkaMessageManager, MessageManager
from utils.workflows.parallel_control import IParallelControl, ParallelControl


def show_memory_usage(logger: logging.Logger):
    """显示内存使用情况"""
    import gc
    import sys
    logger.info("*" * 60)
    object_list = []
    for obj in gc.get_objects():
        size = sys.getsizeof(obj)
        object_list.append((obj, size))
    sorted_object_list = sorted(object_list, key=lambda x: x[1], reverse=True)
    for obj, size in sorted_object_list[:10]:
        logger.info(f"OBJECT: {obj.__class__.__name__}, TYPE: {type(obj)}, SIZE: {size/1024/1024:.2f} MB, REPR: {str(obj)[:100]}")
    logger.info("*" * 60)


class NodeStatus:
    """节点状态"""
    PENDING = "pending"      # 等待运行
    RUNNING = "running"      # 正在运行
    SUCCESS = "success"      # 运行成功
    ERROR = "error"          # 运行出错
    STOPPED = "stopped"      # 已停止

class Node(ABC):
    """节点抽象基类"""

    def __init__(self, name: str = None, concurrency: int = 1):
        """初始化节点

        Args:
            name: 节点名称
            concurrency: 并发度
        """
        self.name = name or self.__class__.__name__
        self.logger = logging.getLogger(self.name)
        self.status = NodeStatus.PENDING
        self.error = None
        self._stop = False
        self.output_queues: List[MessageQueue] = []

        # 节点配置
        self.retry_count = 0
        self.max_retry_count = 3
        self.retry_interval = 1  # 重试间隔（秒）
        self.timeout = 60  # 超时时间（秒）
        self.interval = 1  # 处理间隔（秒）
        self.concurrency = concurrency  # 并发度
        self.parallel_control: IParallelControl = None  # 并行控制器
        self.message_manager: MessageManager = None  # 消息管理器

        self.task_check_interval = 0.5  # 任务检查间隔（秒）

    def stop(self):
        """停止节点"""
        self._stop = True
        self.status = NodeStatus.STOPPED
        self.logger.info(f"节点 {self.name} 已停止")

    @abstractmethod
    async def process(self):
        """处理数据"""
        pass

    def setup_node_status(self, status: NodeStatus):
        """设置节点状态"""
        self.status = status

    def setup_flow_controller(self):
        """设置流量控制器"""
        pass

    def setup_parallel_control(self):
        """设置并行控制器"""
        self.parallel_control = ParallelControl(self.concurrency)

    def setup_message_manager(self):
        """设置消息管理器"""
        if hasattr(self, 'input_queue') and self.input_queue is not None:
            self.message_manager = KafkaMessageManager(
                self.input_queue,
                self.parallel_control,
                kafka_receive_count=1000,
                kafka_receive_timeout=1000,
                local_queue_length=1000,
                interval_to_ack=1000,
                interval_to_put_message=1000,
            )

    async def setup(self):
        """设置节点"""
        self.logger.info(f"节点 {self.name} 开始运行")
        self.setup_node_status(NodeStatus.RUNNING)
        self.setup_flow_controller()
        self.setup_parallel_control()
        self.setup_message_manager()

    async def _retry_on_error(self, error: Exception) -> bool:
        """重试逻辑

        Args:
            error: 错误信息

        Returns:
            bool: 是否重试
        """
        self.error = str(error)
        self.retry_count += 1
        self.logger.error(f"节点 {self.name} 处理数据时发生错误: {self.error}\n{traceback.format_exc()}")

        if self.retry_count > self.max_retry_count:
            self.logger.error(f"节点 {self.name} 重试次数超过限制，停止运行")
            self.status = NodeStatus.ERROR
            return False

        self.logger.info(f"节点 {self.name} 将在 {self.retry_interval} 秒后重试 ({self.retry_count}/{self.max_retry_count})")
        await asyncio.sleep(self.retry_interval)
        return True

    def _finalize(self):
        """完成处理"""
        if self.status not in (NodeStatus.ERROR, NodeStatus.STOPPED):
            self.status = NodeStatus.SUCCESS
        self.logger.info(f"节点 {self.name} 运行完成，状态: {self.status}")

    async def wrap_process(self):
        """包装处理"""
        while True:
            await self.process()
            await asyncio.sleep(self.interval)

    async def _process_loop(self):
        """处理数据循环"""

        try:
            # 创建多个协程实例，数量为并发度
            process_coroutines = [self.wrap_process() for _ in range(self.concurrency)]
            self.logger.info(f"为节点 {self.name} 创建了 {self.concurrency} 个处理协程")

            if hasattr(self, 'input_queue') and self.input_queue is not None:
                self.logger.info(f"节点 {self.name} 有输入队列，启动消息管理器")
                message_manager_coroutines = await self.message_manager.start()
                self.logger.info(f"节点 {self.name} 的消息管理器启动了 {len(message_manager_coroutines)} 个协程")
                process_coroutines += message_manager_coroutines

            # 使用并行控制器执行这些协程
            self.logger.info(f"节点 {self.name} 开始并行执行 {len(process_coroutines)} 个协程")
            await self.parallel_control.execute_parallel(process_coroutines)

            self.logger.info(f"节点 {self.name} 的所有协程已启动，进入任务监控循环")
            while not self._stop:
                # 检查任务状态并重启任务
                await self.parallel_control.check_and_restart_task()
                await asyncio.sleep(self.task_check_interval * 10)

        except asyncio.CancelledError:
            self.logger.info(f"节点 {self.name} 被取消")
            self.setup_node_status(NodeStatus.STOPPED)
        except Exception as e:
            self.logger.error(f"节点 {self.name} 处理循环中发生异常: {str(e)}")
            self.logger.error(traceback.format_exc())
            self._handle_fatal_error(e)
        finally:
            self._cleanup()

        self._finalize()

    def _handle_fatal_error(self, error):
        """处理致命错误"""
        self.error = str(error)
        self.status = NodeStatus.ERROR
        self.logger.error(f"节点 {self.name} 运行时发生致命错误: {self.error}\n{traceback.format_exc()}")

    def _cleanup(self):
        """清理资源"""
        self.logger.info(f"节点 {self.name} 正在清理资源")

    async def run(self):
        """运行节点"""
        try:
            await self.setup()
            await self._process_loop()
        except Exception as e:
            self._handle_fatal_error(e)
        finally:
            self._cleanup()

    async def send_data_to_output_queue(self, data: Any):
        """发送数据到输出队列

        Args:
            data: 要发送的数据。可以是一个单独的项（将被包装成一个消息），或者一个列表（其中每个元素将被包装成一个消息）。
        """
        # 如果节点没有设置输出队列，则无法发送数据
        if not self.output_queues:
            self.logger.warning(f"节点 {self.name} 没有设置输出队列，无法发送数据")
            return
        
        # 检查数据是否有效 (允许 0, 0.0, False 作为有效数据)
        if not data and not isinstance(data, (int, float, bool)):
            self.logger.warning(f"节点 {self.name} 尝试发送的单个数据项为空或无效: {data}")
            return

        # _payloads_for_individual_messages 将包含应为每个单独消息创建的内容列表。
        # InputNode.process 调用此函数时，data 参数是单个项 (如一个钱包地址字符串)。
        # 在这种情况下，_payloads_for_individual_messages 将是包含该单个项的列表。
        _payloads_for_individual_messages: list

        if isinstance(data, str):  # 如果 data 是字符串，它是一个单独的消息内容
            _payloads_for_individual_messages = [data]
        elif isinstance(data, dict): # 如果 data 是字典，它是一个单独的消息内容
            _payloads_for_individual_messages = [data]
        elif isinstance(data, list): # 如果 data 是列表，假定它是消息内容的列表
            _payloads_for_individual_messages = data
        else: # 其他类型（如 int, float, bool, 或其他自定义对象）被视为单独的消息内容
            _payloads_for_individual_messages = [data]
            
        if not _payloads_for_individual_messages: # 如果处理后列表为空（例如，传入一个空列表）
            self.logger.warning(f"节点 {self.name} 没有要发送的数据（处理后的payload列表为空）")
            return

        for queue in self.output_queues:
            for i, payload_item in enumerate(_payloads_for_individual_messages):
                message = Message(data=payload_item)
                await queue.send(message)
                # 为了日志清晰，截断较长的payload
                log_payload_str = str(payload_item)
                if len(log_payload_str) > 100:
                    log_payload_str = log_payload_str[:97] + "..."
                self.logger.debug(f"发送第 {i+1}/{len(_payloads_for_individual_messages)} 条数据 '{log_payload_str}' 到输出队列: {queue.name}")

    async def receive_message(self, timeout: Optional[float] = None) -> Optional[Message]:
        """接收消息"""
        return await self.message_manager.get_message(timeout=timeout)

    async def receive_messages(self) -> List[Message]:
        """接收消息"""
        return await self.message_manager.get_messages(count=self.batch_size, timeout=1000)