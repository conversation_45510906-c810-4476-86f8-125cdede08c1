"""
处理节点模块

提供处理节点的抽象接口和基本实现
"""

from typing import Any, Optional
import traceback

from utils.workflows.message_queue.dead_letter_queue import DeadLetterType

from .base_node import Node
from utils.workflows.message_queue.message_queue import KafkaOffsetFlowController, KafkaQueue, Message, MessageQueue, FlowController

class ProcessNode(Node):
    """处理节点，有输入队列和输出队列"""
    
    def __init__(self, name: str = None, input_queue: MessageQueue = None, 
                 max_pending_messages: int = 100, check_interval: float = 30.0, 
                 enable_flow_control: bool = True):
        """初始化处理节点
        
        Args:
            name: 节点名称
            input_queue: 输入队列
            # output_queue: 输出队列 (已从基类继承 output_queues)
            max_pending_messages: 输出队列中最大待处理消息数
            check_interval: 流量控制检查间隔（秒）
            enable_flow_control: 是否启用流量控制
        """
        super().__init__(name)
        self.input_queue = input_queue
        # self.output_queue = output_queue # 已移除, 使用 self.output_queues
        self.batch_size = 10  # 默认批处理大小
        self.enable_streaming = False  # 是否启用流式处理（批次处理完立即发送）
        
        # 流量控制参数
        self.max_pending_messages = max_pending_messages
        self.check_interval = check_interval
        self.enable_flow_control = enable_flow_control
        
        # 流量控制器将在连接输出队列后初始化
        self.flow_controller = None
    
    def setup_flow_controller(self):
        """设置流量控制器"""
        if self.output_queues: # 修改: 检查 self.output_queues
            # 暂时只基于第一个输出队列创建流量控制器
            # TODO: 后续可以优化为支持基于多个队列的复杂流量控制策略
            primary_output_queue = self.output_queues[0]
            if isinstance(primary_output_queue, KafkaQueue):
                self.flow_controller = KafkaOffsetFlowController(
                    queue=primary_output_queue, # 修改: 使用 primary_output_queue
                    max_lag=self.max_pending_messages,
                    check_interval=self.check_interval,
                    enable_flow_control=self.enable_flow_control
                )
            else:
                self.flow_controller = FlowController(
                    queue=primary_output_queue, # 修改: 使用 primary_output_queue
                    max_pending_messages=self.max_pending_messages,
                    check_interval=self.check_interval,
                    enable_flow_control=self.enable_flow_control
            )
        elif self.enable_flow_control: # 如果启用了流控但没有输出队列，则警告
            self.logger.warning(f"节点 {self.name} 启用了流量控制，但没有配置输出队列。")
    
    async def process_item(self, item: Any):
        """处理单个数据项的抽象方法，子类必须实现
        
        现在改为异步生成器方法，可以逐个yield处理结果
        
        Args:
            item: 单个数据项
            
        Yields:
            处理后的数据项，如果不yield则表示该项处理失败或被过滤
        """
        # 默认实现，子类应该重写此方法
        # 如果子类未实现为生成器，则返回None表示没有结果
        yield  # 此行永远不会执行，只是为了使方法成为生成器
    
    async def process(self):
        """处理数据"""
        
        self.logger.info(f"开始处理数据")
        
        # 接收消息，添加超时机制避免无限阻塞
        message = await self.receive_message(timeout=1.0)
        if message is None:
            self.logger.debug("未获取到消息，可能本地队列为空，跳过本次处理")
            return
        
        message_id = message.metadata.get("message_id")
        
        # 添加详细日志，打印消息ID和内容
        self.logger.info(f"接收到消息: ID={message_id}, Offset={message.offset if hasattr(message, 'offset') else 'N/A'}")
        self.logger.info(f"消息内容: {message.data}")
        
        # 处理消息数据，使用异步生成器
        result_count = 0
        try:
            async for result in self.process_item(message.data):
                if result:
                    # 创建新消息并发送到输出队列 (此方法已更新为发送到所有队列)
                    # new_message = Message(data=result) # 此行移至 send_data_to_output_queue 内部
                    # await self.output_queue.send(new_message) # 旧代码，直接发送到单个队列
                    await self.send_data_to_output_queue(result) # 新代码: 使用基类方法发送到所有输出队列
                    result_count += 1
            self.logger.info(f"已处理的消息数量: {result_count}")
            if result_count > 0:
                self.logger.info(f"消息 {message_id} 处理完成，已发送 {result_count} 条结果到输出队列")
                await self.message_manager.solved_message_id(message_id)
            else:
                self.logger.warning(f"消息 {message_id} 没有处理结果")
                try:
                    # 然后尝试发送到死信队列，如果失败也不影响主流程
                    await self.message_manager.send_to_dead_letter_queue(
                        message=message, 
                        error=Exception("没有处理结果"), 
                        node_name=self.name, 
                        dlq_type=DeadLetterType.PROCESS_FAILURE
                    )
                    self.logger.info(f"消息 {message_id} 已发送到死信队列")
                except Exception as e:
                    self.message_manager.solved_message_id(message_id)
                    self.logger.error(f"发送消息 {message_id} 到死信队列失败: {str(e)}\n{traceback.format_exc()}")
        except Exception as e:
            # 发送消息到死信队列
            await self.message_manager.send_to_dead_letter_queue(message, traceback.format_exc(), self.name, DeadLetterType.PROCESS_FAILURE)
            self.logger.error(f"处理消息 {message_id} 时发生异常: {str(e)}\n{traceback.format_exc()}")
            # 异常情况下，不标记消息为已解决，以便后续重试
