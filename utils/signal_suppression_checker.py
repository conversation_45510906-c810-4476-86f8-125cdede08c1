"""
信号抑制检查器 (Signal Suppression Checker)

负责检查是否应该抑制某个交易信号的延迟计算，基于以下逻辑：
1. 查找相同策略-代币组合的历史延迟记录
2. 比较当前KOL交易时间与历史KOL交易时间的间隔
3. 如果间隔小于等于配置的same_token_notification_interval，则抑制延迟计算

创建日期：2025-06-19
更新日期：2025-06-19

@技术实现方案: docs/features/0.1.0/workflows/trading_delay_monitor_dev_plan_ai.md
@测试用例: test/utils/test_signal_suppression_checker.py
"""

import logging
from datetime import datetime, timezone
from dataclasses import dataclass
from typing import Optional, List

from dao.trading_delay_record_dao import TradingDelayRecordDAO
from dao.signal_dao import SignalDAO
from dao.config_dao import ConfigDAO
from utils.kol_trade_time_resolver import KOLTradeTimeResolver
from models.trade_record import TradeRecord
from models.trading_delay_record import TradingDelayRecord

logger = logging.getLogger(__name__)


@dataclass
class SuppressionCheckResult:
    """信号抑制检查结果"""
    is_suppressed: bool  # 是否被抑制
    status: str  # 检查状态：'passed', 'suppressed', 'no_history', 'config_missing', 'error'
    reason: str  # 详细原因说明
    time_interval_minutes: Optional[float] = None  # KOL交易时间间隔（分钟）
    historical_kol_time: Optional[datetime] = None  # 历史KOL交易时间
    current_kol_time: Optional[datetime] = None  # 当前KOL交易时间
    threshold_minutes: Optional[int] = None  # 抑制阈值（分钟）


class SignalSuppressionChecker:
    """
    信号抑制检查器
    
    负责判断是否应该抑制某个交易信号的延迟计算，避免对同一代币的重复计算。
    
    核心逻辑：
    1. 获取当前信号和策略配置
    2. 查找相同策略-代币组合的最近历史记录
    3. 解析当前信号的KOL交易时间
    4. 计算时间间隔并与配置阈值比较
    5. 返回是否抑制的决策结果
    """
    
    def __init__(
        self,
        delay_dao: TradingDelayRecordDAO,
        signal_dao: SignalDAO,
        config_dao: ConfigDAO,
        kol_resolver: KOLTradeTimeResolver
    ):
        """
        初始化信号抑制检查器
        
        Args:
            delay_dao: 延迟记录数据访问对象
            signal_dao: 信号数据访问对象
            config_dao: 配置数据访问对象
            kol_resolver: KOL交易时间解析器
        """
        self.delay_dao = delay_dao
        self.signal_dao = signal_dao
        self.config_dao = config_dao
        self.kol_resolver = kol_resolver
    
    async def check_signal_suppression(self, trade_record: TradeRecord) -> SuppressionCheckResult:
        """
        检查交易信号是否应该被抑制
        
        Args:
            trade_record: 交易记录
            
        Returns:
            SuppressionCheckResult: 抑制检查结果
        """
        try:
            # 1. 获取信号信息
            signal = await self.signal_dao.get_signal(trade_record.signal_id)
            if not signal:
                return SuppressionCheckResult(
                    is_suppressed=False,
                    status='error',
                    reason='signal_not_found'
                )
            
            # 2. 获取策略配置
            strategy_config = await self._get_strategy_config(trade_record.strategy_name)
            if not strategy_config:
                return SuppressionCheckResult(
                    is_suppressed=False,
                    status='config_missing',
                    reason='strategy_config_not_found'
                )
            
            # 3. 获取same_token_notification_interval配置
            threshold_minutes = strategy_config.get('same_token_notification_interval')
            if threshold_minutes is None:
                return SuppressionCheckResult(
                    is_suppressed=False,
                    status='config_missing',
                    reason='same_token_notification_interval_not_configured'
                )
            
            # 4. 查找历史延迟记录
            historical_records = await self.delay_dao.find_recent_records_by_strategy_token(
                strategy_name=trade_record.strategy_name,
                token_address=trade_record.token_out_address,
                before_timestamp=signal.trigger_timestamp,
                limit=1
            )
            
            # 5. 如果没有历史记录，允许计算
            if not historical_records:
                return SuppressionCheckResult(
                    is_suppressed=False,
                    status='no_history',
                    reason='first_signal_for_strategy_token_combination'
                )
            
            historical_record = historical_records[0]
            
            # 6. 检查历史记录的KOL时间
            if not historical_record.kol_last_trade_timestamp:
                return SuppressionCheckResult(
                    is_suppressed=False,
                    status='error',
                    reason='historical_kol_time_missing'
                )
            
            # 7. 解析当前信号的KOL交易时间
            current_kol_time, hit_wallets, error_msg = await self.kol_resolver.resolve_kol_last_trade_time(
                trade_record
            )
            
            if current_kol_time is None:
                return SuppressionCheckResult(
                    is_suppressed=False,
                    status='error',
                    reason=f'kol_time_resolution_failed: {error_msg}'
                )
            
            # 8. 计算时间间隔
            time_interval = current_kol_time - historical_record.kol_last_trade_timestamp
            time_interval_minutes = time_interval.total_seconds() / 60.0
            
            # 9. 判断是否抑制
            is_suppressed = time_interval_minutes <= threshold_minutes
            
            # 10. 构建结果
            if is_suppressed:
                status = 'suppressed'
                reason = f'time_interval_{time_interval_minutes:.2f}min_vs_threshold_{threshold_minutes}min'
            else:
                status = 'passed'
                reason = f'time_interval_{time_interval_minutes:.2f}min_vs_threshold_{threshold_minutes}min'
            
            return SuppressionCheckResult(
                is_suppressed=is_suppressed,
                status=status,
                reason=reason,
                time_interval_minutes=time_interval_minutes,
                historical_kol_time=historical_record.kol_last_trade_timestamp,
                current_kol_time=current_kol_time,
                threshold_minutes=threshold_minutes
            )
            
        except Exception as e:
            logger.exception(f"信号抑制检查出现意外错误: {e}")
            return SuppressionCheckResult(
                is_suppressed=False,
                status='error',
                reason=f'unexpected_error: {str(e)}'
            )
    
    async def _get_strategy_config(self, strategy_name: str) -> Optional[dict]:
        """
        获取策略配置
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            dict: 策略配置字典，如果未找到返回None
        """
        try:
            config = await self.config_dao.get_by_type("auto_trade")
            if not config or not hasattr(config.data, 'buy_strategies'):
                return None
            
            # 查找匹配的策略配置
            for strategy_config in config.data.buy_strategies:
                if hasattr(strategy_config, 'strategy_name') and strategy_config.strategy_name == strategy_name:
                    # 将配置对象转换为字典
                    if hasattr(strategy_config, '__dict__'):
                        return strategy_config.__dict__
                    else:
                        # 如果是MagicMock或其他对象，尝试获取属性
                        config_dict = {}
                        for attr in dir(strategy_config):
                            if not attr.startswith('_'):
                                try:
                                    config_dict[attr] = getattr(strategy_config, attr)
                                except (AttributeError, TypeError):
                                    continue
                        return config_dict
            
            return None
            
        except Exception as e:
            logger.exception(f"获取策略配置失败: {e}")
            return None 