from datetime import datetime, timedelta
from typing import Any, Dict, List, Tuple, Optional
from zoneinfo import ZoneInfo
import logging
from dao.kol_wallet_activity_dao import KOLWalletActivityDAO


def check_message_id_continuous(start_message_id: int, message_ids: List[int]) -> Tu<PERSON>[List[int], int]:
    """检查连续的message_id，支持offset跳跃和重置场景
    
    Args:
        start_message_id (int): 期望的开始消息ID
        message_ids (List[int]): 消息ID列表（需要先排序）
        
    Returns:
        Tuple[List[int], int]: 连续的消息ID列表, 下一个消息ID
        
    注意:
        当检测到offset跳跃时（如Kafka offset重置），会从当前最小消息ID开始
        重新建立连续性，并记录警告日志。
    """
    
    if len(message_ids) == 0:
        return [], start_message_id
    
    # 确保消息ID列表已排序
    sorted_message_ids = sorted(message_ids)
    min_message_id = sorted_message_ids[0]
    
    # 检测offset跳跃：当期望的start_message_id与实际最小消息ID不匹配时
    if start_message_id != min_message_id:
        import logging
        logger = logging.getLogger(__name__)
        
        if start_message_id < min_message_id:
            # offset重置场景：消费者offset被重置到更高的位置
            logger.warning(
                f"Detected Kafka offset reset: expected start_message_id={start_message_id}, "
                f"but got min_message_id={min_message_id}. Reestablishing continuity from {min_message_id}."
            )
            # 从新的起点重新建立连续性
            start_message_id = min_message_id
        else:
            # offset回退场景：期望的ID大于实际最小ID
            logger.warning(
                f"Detected offset rollback: expected start_message_id={start_message_id}, "
                f"but got min_message_id={min_message_id}. This may indicate message redelivery."
            )
            # 仍然从期望的ID开始，但检查实际消息
            pass

    continuous_message_ids = []
    current_expected_id = start_message_id
    
    for message_id in sorted_message_ids:
        if message_id == current_expected_id:
            continuous_message_ids.append(message_id)
            current_expected_id += 1
        elif message_id > current_expected_id:
            # 发现gap，停止收集连续消息
            break
        # 如果message_id < current_expected_id，跳过该消息（可能是重复或乱序）
        
    next_message_id = current_expected_id
    return continuous_message_ids, next_message_id


def convert_list_to_dict(l: List[Dict], key_field: str) -> Dict[str, Any]:
    """将列表转换为字典
    
    Args:
        l (List[Dict]): 字典列表
        key_field (str): 用作字典键的字段名
        
    Returns:
        Dict[str, Any]: 转换后的字典，键为key_field指定的字段值，值为原始字典
    """
    result = {}
    for item in l:
        if key_field in item:
            result[item[key_field]] = item
    return result


def find_index_from_list_by_ratio(l: list, ratio: float) -> int:
    """按比例从列表中选择元素的索引
    
    Args:
        l (list): 输入列表
        ratio (float): 比例值，范围在[0, 1]之间
        
    Returns:
        int: 对应比例位置的元素索引
    """
    if not l:
        raise ValueError("列表不能为空")
    
    if ratio < 0 or ratio > 1:
        raise ValueError("比例必须在0到1之间")
    
    if len(l) == 1:
        return 0
    
    # 计算索引位置（比例乘以列表长度并减1，确保在0到len(l)-1范围内）
    index = len(l) * ratio
    
    # 向下取整并确保索引在有效范围内
    index = int(index)
    if index >= len(l):
        index = len(l) - 1
    
    return index


if __name__ == "__main__":
    # message_ids = [1, 2, 3, 4, 5, 6, 8, 9, 10]
    # start_message_id = 1
    # print(check_message_id_continuous(start_message_id, message_ids))
    # l = [{"key": "a"}, {"key": "b"}]
    # print(convert_list_to_dict(l, "key"))
    l = [0, 1, 2]
    print(find_index_from_list_by_ratio(l, 0.3333))
    

def check_token_data(token_data: Dict, required_fields: List[str]) -> Tuple[bool, str]:
    """检查代币数据是否包含所有必需字段
    
    Args:
        token_data (Dict): 代币数据字典
        required_fields (List[str]): 必需字段列表
    """
    for field in required_fields:
        if field not in token_data:
            return False, f"代币数据缺少必要字段: {field}"
    return True, ""


async def check_kol_sell_ratio(
    kol_activity_dao: KOLWalletActivityDAO,
    token_address: str,
    hit_kol_wallets: List[str],
    buy_signal_time_dt: datetime,
    evaluation_time_dt: datetime,
    sell_ratio_threshold: float,
    sell_activity_lookback_hours: int,
    logger: Optional[logging.Logger] = None
) -> Tuple[bool, float, int]:
    """
    检查KOL卖出比例是否达到阈值。

    Args:
        kol_activity_dao: KOLWalletActivityDAO 实例。
        token_address: 代币地址。
        hit_kol_wallets: 触发买入信号的KOL钱包列表。
        buy_signal_time_dt: 买入信号的创建/触发时间，用于计算卖出活动检查的起始点。
                           必须是时区感知 (offset-aware) 的 datetime 对象。
        evaluation_time_dt: 评估卖出条件的时间点 (即KOL卖出活动检查的结束时间)。
                           必须是时区感知 (offset-aware) 的 datetime 对象。
        sell_ratio_threshold: 策略配置的卖出KOL比例阈值。
        sell_activity_lookback_hours: 从 buy_signal_time_dt 回溯的小时数，
                                      用以确定检查KOL卖出活动的开始时间。
        logger: 可选的日志记录器。

    Returns:
        Tuple[bool, float, int]: (是否达到阈值, 实际计算的比例, 卖出的KOL数量)
    """
    if not hit_kol_wallets:
        if logger:
            logger.info(f"Token {token_address}: No hit KOL wallets provided, cannot check sell ratio. Returning (False, 0.0, 0).")
        return False, 0.0, 0

    if buy_signal_time_dt.tzinfo is None or buy_signal_time_dt.tzinfo.utcoffset(buy_signal_time_dt) is None:
        raise ValueError("buy_signal_time_dt must be timezone-aware.")
    if evaluation_time_dt.tzinfo is None or evaluation_time_dt.tzinfo.utcoffset(evaluation_time_dt) is None:
        raise ValueError("evaluation_time_dt must be timezone-aware.")

    start_time_kol_check_dt = buy_signal_time_dt - timedelta(hours=sell_activity_lookback_hours)
    start_time_kol_check_ts = int(start_time_kol_check_dt.timestamp())
    evaluation_time_ts = int(evaluation_time_dt.timestamp())

    selling_kols_pipeline = [
        {
            '$match': {
                'wallet': {'$in': hit_kol_wallets},
                'token.address': token_address,
                'event_type': 'sell',
                'timestamp': {'$gte': start_time_kol_check_ts, '$lt': evaluation_time_ts}
            }
        },
        {
            '$group': {
                '_id': '$wallet'
            }
        },
        {
            '$count': 'selling_kol_count'
        }
    ]
    
    if logger:
        logger.debug(f"Token {token_address}: Executing sell ratio check pipeline. "
                     f"Hit KOLs: {len(hit_kol_wallets)}, Lookback: {sell_activity_lookback_hours}h from {buy_signal_time_dt.isoformat()}. "
                     f"Check window: {start_time_kol_check_dt.isoformat()} to {evaluation_time_dt.isoformat()}.")

    sell_result = await kol_activity_dao.aggregate(selling_kols_pipeline)
    selling_kol_count = sell_result[0]['selling_kol_count'] if sell_result and sell_result[0] else 0

    calculated_ratio = 0.0
    # len(hit_kol_wallets) > 0 is implied if we reach here due to early exit from 'if not hit_kol_wallets:'
    calculated_ratio = selling_kol_count / len(hit_kol_wallets) 
    
    is_threshold_reached = calculated_ratio >= sell_ratio_threshold

    if logger:
        log_message_parts = [
            f"Token {token_address}: Sell ratio check result:",
            f"Selling KOLs: {selling_kol_count}/{len(hit_kol_wallets)},",
            f"Calculated Ratio: {calculated_ratio:.4f}, Threshold: {sell_ratio_threshold:.4f},",
            f"Reached: {is_threshold_reached}."
        ]
        logger.info(" ".join(log_message_parts))
                    
    return is_threshold_reached, calculated_ratio, selling_kol_count

