"""信号分析组件 - 回测模块V2

基于滑动窗口算法检测买入信号，支持买入即卖出过滤，
使用pandas进行向量化计算提升性能，精确记录信号触发时间戳。
"""

import logging
import pandas as pd
from typing import Dict, List, Any, Optional, Set
from datetime import datetime

from utils.backtest_v2.config_manager import BacktestConfigV2

logger = logging.getLogger("SignalAnalyzerV2")


class SignalAnalyzer:
    """信号分析组件"""
    
    def __init__(self, config: BacktestConfigV2, sell_data_cache: Dict[str, Any]):
        """初始化信号分析器
        
        Args:
            config: 回测配置对象
            sell_data_cache: 预加载的卖出数据缓存
        """
        self.config = config
        self.sell_data_cache = sell_data_cache
        
        # 从配置中提取参数
        self.transaction_lookback_hours = config.transaction_lookback_hours
        self.kol_account_min_count = config.kol_account_min_count
        self.same_token_notification_interval_minutes = config.same_token_notification_interval_minutes
        self.sell_kol_ratio = config.sell_kol_ratio
        
        # 记录每个代币的最近信号时间
        self.recent_signals = {}
        
        logger.info(f"信号分析器初始化完成，参数: lookback={self.transaction_lookback_hours}h, "
                   f"min_kol={self.kol_account_min_count}, interval={self.same_token_notification_interval_minutes}min")
    
    def analyze_token_signals(self, token_address: str, token_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """对单个token的数据进行信号分析
        
        Args:
            token_address: token地址
            token_data: token数据，包含records和kol_wallets
            
        Returns:
            List[Dict[str, Any]]: 检测到的买入信号列表
        """
        try:
            records = token_data['records']
            if not records:
                return []
            
            logger.debug(f"分析token {token_address} 的信号，共 {len(records)} 条记录")
            
            # 转换为DataFrame进行向量化计算
            df = pd.DataFrame(records)
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
            df = df.sort_values('timestamp')
            
            # 执行滑动窗口分析
            signals = self._sliding_window_analysis(token_address, df)
            
            logger.debug(f"Token {token_address} 检测到 {len(signals)} 个信号")
            return signals
            
        except Exception as e:
            logger.error(f"分析token {token_address} 信号失败: {e}")
            return []
    
    def _sliding_window_analysis(self, token_address: str, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """滑动窗口算法实现
        
        Args:
            token_address: token地址
            df: 交易记录DataFrame
            
        Returns:
            List[Dict[str, Any]]: 检测到的信号列表
        """
        signals = []
        lookback_seconds = self.transaction_lookback_hours * 3600
        
        # 遍历每个时间点作为窗口结束点
        for i, row in df.iterrows():
            window_end_time = row['timestamp']
            window_start_time = window_end_time - pd.Timedelta(seconds=lookback_seconds)
            
            # 获取时间窗口内的记录
            window_mask = (df['timestamp'] >= window_start_time) & (df['timestamp'] <= window_end_time)
            window_records = df[window_mask]
            
            if len(window_records) == 0:
                continue
            
            # 统计窗口内唯一KOL数量
            unique_kols = window_records['wallet'].unique()
            kol_count = len(unique_kols)
            
            # 检查是否达到最小KOL数量阈值
            if kol_count >= self.kol_account_min_count:
                # 计算精确的信号时间戳（最后一个交易时间）
                signal_timestamp = int(window_end_time.timestamp())
                
                # 创建候选信号
                candidate_signal = {
                    'token_address': token_address,
                    'signal_timestamp': signal_timestamp,
                    'trigger_kol_count': kol_count,
                    'window_start': int(window_start_time.timestamp()),
                    'window_end': signal_timestamp,
                    'kol_wallets': unique_kols.tolist(),
                    'total_volume_usd': self.config.fixed_trade_amount,  # 使用固定买入金额
                    'avg_price_usd': float(window_records['price_usd'].mean()),
                    # 保留历史数据用于分析参考
                    'historical_volume_usd': float(window_records['cost_usd'].sum())
                }
                
                # 买入即卖出过滤
                if self._check_buy_sell_immediately(candidate_signal, token_address):
                    logger.debug(f"Token {token_address} 信号 {signal_timestamp} 被买入即卖出过滤器过滤")
                    continue
                
                # 检查信号间隔限制
                if not self._check_signal_interval(token_address, signal_timestamp):
                    logger.debug(f"Token {token_address} 信号 {signal_timestamp} 被间隔限制过滤")
                    continue
                
                # 通过所有检查，添加信号
                signals.append(candidate_signal)
                logger.debug(f"Token {token_address} 生成信号: 时间={signal_timestamp}, KOL数量={kol_count}")
        
        return signals
    
    def _check_buy_sell_immediately(self, candidate_signal: Dict[str, Any], token_address: str) -> bool:
        """买入即卖出判断
        
        Args:
            candidate_signal: 候选买入信号
            token_address: token地址
            
        Returns:
            bool: True表示应该跳过该信号，False表示通过检查
        """
        try:
            # 获取该token的卖出数据
            token_sell_data = self.sell_data_cache.get(token_address)
            if not token_sell_data or not token_sell_data.get('records'):
                # 无卖出数据时通过检查
                return False
            
            sell_records = token_sell_data['records']
            signal_kol_wallets = set(candidate_signal['kol_wallets'])
            window_start = candidate_signal['window_start']
            window_end = candidate_signal['window_end']
            
            # 统计在买入信号时间窗口内卖出的KOL数量
            sell_kols_in_window = set()
            for sell_record in sell_records:
                sell_timestamp = sell_record['timestamp']
                sell_wallet = sell_record['wallet']
                
                # 检查是否在时间窗口内且是买入信号的KOL
                if (window_start <= sell_timestamp <= window_end and 
                    sell_wallet in signal_kol_wallets):
                    sell_kols_in_window.add(sell_wallet)
            
            # 计算卖出比例
            if len(signal_kol_wallets) == 0:
                return False
            
            sell_ratio = len(sell_kols_in_window) / len(signal_kol_wallets)
            
            # 如果卖出比例达到阈值，跳过该信号
            should_skip = sell_ratio >= self.sell_kol_ratio
            
            if should_skip:
                logger.info(f"Token {token_address} 信号 {candidate_signal['signal_timestamp']} "
                           f"被买入即卖出过滤: 卖出KOL {len(sell_kols_in_window)}/{len(signal_kol_wallets)} "
                           f"= {sell_ratio:.2%} >= {self.sell_kol_ratio:.2%}")
            else:
                logger.debug(f"Token {token_address} 信号 {candidate_signal['signal_timestamp']} "
                            f"通过买入即卖出检查: 卖出KOL {len(sell_kols_in_window)}/{len(signal_kol_wallets)} "
                            f"= {sell_ratio:.2%} < {self.sell_kol_ratio:.2%}")
            
            return should_skip
            
        except Exception as e:
            logger.error(f"买入即卖出检查失败: {e}")
            # 出错时保守处理，通过检查
            return False
    
    def _check_signal_interval(self, token_address: str, signal_timestamp: int) -> bool:
        """检查信号间隔限制
        
        Args:
            token_address: token地址
            signal_timestamp: 信号时间戳
            
        Returns:
            bool: True表示通过检查，False表示被间隔限制过滤
        """
        interval_seconds = self.same_token_notification_interval_minutes * 60
        
        # 检查该token是否有历史信号
        if token_address not in self.recent_signals:
            # 没有历史信号，直接通过并记录
            self.recent_signals[token_address] = signal_timestamp
            return True
        
        last_signal_time = self.recent_signals[token_address]
        time_diff = signal_timestamp - last_signal_time
        
        # 检查时间间隔
        if time_diff >= interval_seconds:
            # 满足间隔要求，更新记录
            self.recent_signals[token_address] = signal_timestamp
            return True
        else:
            # 不满足间隔要求，过滤该信号
            return False
