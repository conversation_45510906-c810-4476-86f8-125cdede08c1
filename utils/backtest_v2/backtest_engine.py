"""回测执行引擎 - 回测模块V2

统筹整个回测流程，集成各个组件，处理组件间的数据流转，
提供进度监控和错误处理。
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta

from utils.backtest_v2.config_manager import BacktestConfigV2, ConfigManagerV2
from utils.backtest_v2.data_query import DataQuery
from utils.backtest_v2.signal_analyzer import SignalAnalyzer
from utils.backtest_v2.sell_strategy import SellStrategy
from utils.backtest_v2.result_analyzer import ResultAnalyzer
from utils.backtest_v2.delay_simulator import DelaySimulator

logger = logging.getLogger("BacktestEngineV2")


class BacktestEngineV2:
    """回测执行引擎V2"""
    
    def __init__(self, config: BacktestConfigV2):
        """初始化回测引擎
        
        Args:
            config: 回测配置对象
        """
        self.config = config
        self.result_dir = None  # 结果输出目录，由外部设置
        
        # 初始化数据查询和卖出策略组件
        self.data_query = DataQuery(config)
        self.sell_strategy = SellStrategy(config)
        
        # 信号分析器和结果分析器需要在预加载卖出数据后初始化
        self.signal_analyzer = None
        self.result_analyzer = None
        
        # 初始化延迟模拟器
        self.delay_simulator = None
        if config.delay_simulation.enabled:
            delay_strategy = ConfigManagerV2.create_delay_strategy(config.delay_simulation)
            self.delay_simulator = DelaySimulator(delay_strategy)
            logger.info(f"延迟模拟器已启用: {config.delay_simulation.strategy}, "
                       f"延迟时间: {config.delay_simulation.fixed_delay_seconds}s")
        else:
            logger.info("延迟模拟器已禁用")
        
        logger.info("回测引擎V2初始化完成")
    
    @staticmethod
    def _normalize_timestamp(timestamp: Union[datetime, int, float, None]) -> float:
        """将各种时间戳格式统一转换为float类型的Unix时间戳
        
        Args:
            timestamp: 待转换的时间戳，可能是datetime对象、int或float类型的Unix时间戳
            
        Returns:
            float: Unix时间戳
            
        Raises:
            ValueError: 当时间戳为None或不支持的类型时
        """
        if timestamp is None:
            raise ValueError("时间戳不能为None")
        
        if isinstance(timestamp, datetime):
            return timestamp.timestamp()
        elif isinstance(timestamp, (int, float)):
            return float(timestamp)
        else:
            raise ValueError(f"不支持的时间戳类型: {type(timestamp)}, 值: {timestamp}")
    
    async def run_backtest(self) -> Dict[str, Any]:
        """执行完整的回测流程
        
        Returns:
            Dict[str, Any]: 回测结果
        """
        try:
            logger.info("开始执行回测模块V2流程")
            start_time = time.time()

            # 步骤1: 执行买入数据聚合查询（先过滤出符合条件的token）
            logger.info("步骤1: 执行买入数据聚合查询")
            buy_pipeline = await self.data_query.build_buy_data_aggregation_pipeline(
                self.config.backtest_start_time,
                self.config.backtest_end_time
            )
            raw_token_data = await self.data_query.execute_aggregation_query(buy_pipeline)
            logger.info(f"买入数据查询完成，获得 {len(raw_token_data)} 个token")

            # 步骤2: 获取token信息（mint时间等）
            logger.info("步骤2: 获取token基础信息")
            token_addresses = list(raw_token_data.keys())
            token_info_map = await self.data_query.get_token_info(token_addresses)

            # 步骤3: 新代币记录过滤 + KOL数量预筛选
            logger.info("步骤3: 新代币记录过滤和KOL数量预筛选")
            filtered_token_data = await self.data_query.filter_new_token_records(raw_token_data, token_info_map)
            logger.info(f"过滤后剩余 {len(filtered_token_data)} 个token进入信号分析")

            # 步骤4: 预加载卖出数据（为所有通过预筛选的token）
            logger.info("步骤4: 预加载卖出数据")
            if filtered_token_data:
                token_addresses = list(filtered_token_data.keys())
                logger.info(f"需要预加载 {len(token_addresses)} 个token的卖出数据")
                
                # 扩展时间范围以包含卖出策略时间窗口
                extended_end_time = self.config.backtest_end_time + self.config.sell_strategy_hours * 3600
                await self.sell_strategy.preload_sell_data_for_tokens(
                    token_addresses,
                    self.config.backtest_start_time,
                    extended_end_time
                )
            else:
                logger.info("没有token通过预筛选，跳过卖出数据预加载")

            # 步骤5: 初始化信号分析器（传入正确的卖出数据缓存）
            logger.info("步骤5: 初始化信号分析器")
            self.signal_analyzer = SignalAnalyzer(self.config, self.sell_strategy.sell_data_cache)
            self.result_analyzer = ResultAnalyzer(self.config)

            # 步骤6: 信号检测（包含买入即卖出过滤）- 并发处理
            logger.info("步骤6: 并发执行信号检测")
            buy_signals = await self._analyze_signals_concurrent(filtered_token_data)
            logger.info(f"信号检测完成，生成 {len(buy_signals)} 个买入信号")

            # 步骤7: 应用延迟模拟到买入信号（如果启用）
            if self.delay_simulator:
                logger.info("步骤7: 应用延迟模拟到买入信号")
                buy_signals = self._apply_delay_simulation(buy_signals)
                logger.info(f"延迟模拟完成，处理了 {len(buy_signals)} 个信号")
            else:
                logger.info("步骤7: 跳过延迟模拟（未启用）")

            # 步骤8: 卖出策略（基于延迟后的买入信号）
            logger.info("步骤8: 执行卖出策略")
            sell_signals = await self.sell_strategy.determine_sell_signals(buy_signals)
            
            # 步骤8.1: 应用延迟模拟到卖出信号（如果启用）
            if self.delay_simulator:
                logger.info("步骤8.1: 应用延迟模拟到卖出信号")
                sell_signals = self._apply_delay_simulation_to_sell_signals(sell_signals)
                logger.info(f"卖出信号延迟模拟完成，处理了 {len(sell_signals)} 个信号")

            # 步骤9: 收益计算
            logger.info("步骤9: 计算交易收益")
            trades = self._calculate_trades(buy_signals, sell_signals)

            # 步骤10: 结果分析
            logger.info("步骤10: 生成结果分析")
            results = self.result_analyzer.analyze(trades)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 添加执行时间信息
            results['execution_time'] = execution_time
            results['execution_time_formatted'] = str(timedelta(seconds=execution_time))
            results['backtest_version'] = 'v2'
            
            # 添加延迟模拟统计信息
            if self.delay_simulator:
                delay_stats = self.delay_simulator.get_simulation_summary()
                results['delay_simulation'] = {
                    'enabled': True,
                    'statistics': delay_stats.get_summary()
                }
                logger.info(f"延迟模拟统计: {delay_stats.get_summary()}")
            else:
                results['delay_simulation'] = {'enabled': False}
            
            logger.info(f"回测流程执行完成，用时: {timedelta(seconds=execution_time)}")
            return results
            
        except Exception as e:
            logger.error(f"回测执行失败: {e}")
            raise
    
    async def _analyze_signals_concurrent(self, filtered_token_data: Dict[str, Any], 
                                        max_concurrent_tokens: int = 10) -> List[Dict[str, Any]]:
        """并发分析多个token的信号，提升性能
        
        Args:
            filtered_token_data: 过滤后的token数据
            max_concurrent_tokens: 最大并发token数量
            
        Returns:
            List[Dict[str, Any]]: 所有买入信号列表
        """
        token_items = list(filtered_token_data.items())
        
        # 创建信号量控制并发数量
        semaphore = asyncio.Semaphore(max_concurrent_tokens)
        
        async def analyze_single_token(token_address: str, token_data: Dict[str, Any]) -> List[Dict[str, Any]]:
            """分析单个token的信号"""
            async with semaphore:
                logger.debug(f"分析token {token_address} 的信号")
                if self.signal_analyzer is None:
                    raise RuntimeError("信号分析器未初始化")
                return self.signal_analyzer.analyze_token_signals(token_address, token_data)
        
        # 并发执行所有token的信号分析
        tasks = [
            analyze_single_token(token_address, token_data) 
            for token_address, token_data in token_items
        ]
        
        # 等待所有任务完成并收集结果
        token_signals_list = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 合并所有token的信号结果
        all_buy_signals = []
        for i, signals in enumerate(token_signals_list):
            if isinstance(signals, Exception):
                token_address = token_items[i][0]
                logger.error(f"Token {token_address} 信号分析失败: {signals}")
                continue
            if signals and isinstance(signals, list):  # 如果该token有信号且是列表类型
                all_buy_signals.extend(signals)
        
        return all_buy_signals
    
    def _calculate_trades(self, buy_signals: List[Dict[str, Any]], 
                         sell_signals: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """根据买入卖出信号计算具体交易
        
        Args:
            buy_signals: 买入信号列表
            sell_signals: 卖出信号列表
            
        Returns:
            List[Dict[str, Any]]: 交易列表
        """
        logger.info(f"计算 {len(buy_signals)} 个买入信号对应的交易")
        trades = []
        
        for buy_signal in buy_signals:
            # 使用原始时间戳进行买卖匹配（如果延迟模拟被应用）
            matching_timestamp = buy_signal.get('original_signal_timestamp', buy_signal.get('signal_timestamp'))
            
            # 查找对应的卖出信号
            sell_signal = next(
                (s for s in sell_signals if s['token_address'] == buy_signal['token_address'] 
                 and s['buy_signal_timestamp'] == matching_timestamp), 
                None
            )
            
            if sell_signal:
                try:
                    # 使用延迟后的时间戳作为实际买入时间，统一转换为float类型
                    actual_buy_timestamp = self._normalize_timestamp(buy_signal['signal_timestamp'])
                    actual_sell_timestamp = self._normalize_timestamp(sell_signal['sell_timestamp'])
                    matching_timestamp_normalized = self._normalize_timestamp(matching_timestamp)
                    
                    # 验证时间逻辑：确保卖出时间晚于买入时间
                    if actual_sell_timestamp <= actual_buy_timestamp:
                        logger.warning(f"时间逻辑错误: token={buy_signal['token_address']}, "
                                     f"卖出时间({actual_sell_timestamp}) <= 买入时间({actual_buy_timestamp}), "
                                     f"跳过此交易")
                        continue
                    
                    # 修复：使用统一的float类型时间戳计算持有时间（单位：小时）
                    holding_hours = (actual_sell_timestamp - actual_buy_timestamp) / 3600.0
                    
                    # 验证持有时间合理性（例如：不能小于0，不能超过策略设定的最大时间）
                    if holding_hours <= 0:
                        logger.warning(f"持有时间异常: token={buy_signal['token_address']}, "
                                     f"持有时间={holding_hours:.2f}小时, 跳过此交易")
                        continue
                    
                    if holding_hours > self.config.sell_strategy_hours * 2:  # 超过策略时间的2倍认为异常
                        logger.warning(f"持有时间过长: token={buy_signal['token_address']}, "
                                     f"持有时间={holding_hours:.2f}小时, 策略上限={self.config.sell_strategy_hours}小时")
                    
                    # 修复：计算买入延迟时间（使用统一的float类型时间戳）
                    buy_delay_seconds = float(actual_buy_timestamp - matching_timestamp_normalized) if matching_timestamp_normalized != actual_buy_timestamp else 0.0
                    
                    trade = {
                        'token_address': buy_signal['token_address'],
                        'buy_timestamp': actual_buy_timestamp,  # 使用延迟后的时间戳
                        'sell_timestamp': actual_sell_timestamp,  # 使用延迟后的时间戳
                        'buy_price': buy_signal.get('avg_price_usd', 0),
                        'sell_price': sell_signal.get('avg_price_usd', 0),
                        'quantity': buy_signal.get('total_volume_usd', 0),
                        'buy_reason': 'kol_signal',
                        'sell_reason': sell_signal.get('sell_reason', 'unknown'),
                        'kol_count': buy_signal.get('trigger_kol_count', 0),
                        'holding_hours': holding_hours,
                        # 添加延迟信息到交易记录中
                        'original_buy_timestamp': matching_timestamp_normalized,  # 原始买入时间戳
                        'original_sell_timestamp': sell_signal.get('original_sell_timestamp', actual_sell_timestamp),  # 原始卖出时间戳
                        'buy_delay_applied_seconds': buy_delay_seconds,
                        'sell_delay_applied_seconds': sell_signal.get('sell_delay_applied_seconds', 0)
                    }
                    
                    # 计算收益率（考虑手续费和滑点）
                    commission = self.config.commission_pct
                    slippage = self.config.slippage_pct
                    
                    if trade['buy_price'] > 0 and trade['sell_price'] > 0:
                        effective_buy_price = trade['buy_price'] * (1 + commission + slippage)
                        effective_sell_price = trade['sell_price'] * (1 - commission - slippage)
                        
                        trade['return_rate'] = (effective_sell_price - effective_buy_price) / effective_buy_price
                        trade['profit_usd'] = trade['quantity'] * trade['return_rate']
                    else:
                        # 价格数据不可用时，使用模拟收益
                        trade['return_rate'] = 0.0
                        trade['profit_usd'] = 0.0
                        trade['price_data_available'] = False
                    
                    trades.append(trade)
                    
                    logger.debug(f"成功匹配交易: token={buy_signal['token_address']}, "
                               f"原始时间戳={matching_timestamp_normalized}, 延迟后时间戳={actual_buy_timestamp}, "
                               f"延迟={buy_delay_seconds:.2f}s, 持有时间={holding_hours:.2f}小时")
                
                except (ValueError, TypeError) as e:
                    logger.error(f"时间戳处理错误: token={buy_signal['token_address']}, 错误: {e}, "
                               f"买入时间戳={buy_signal.get('signal_timestamp')}, "
                               f"卖出时间戳={sell_signal.get('sell_timestamp')}")
                    continue
                except Exception as e:
                    logger.error(f"交易计算异常: token={buy_signal['token_address']}, 错误: {e}")
                    continue
            else:
                logger.warning(f"买入信号 {buy_signal['token_address']}:{matching_timestamp} "
                             f"未找到对应卖出信号")
        
        logger.info(f"交易计算完成，生成 {len(trades)} 笔交易")
        return trades
    
    def _apply_delay_simulation(self, signals: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """应用延迟模拟到信号列表
        
        Args:
            signals: 原始信号列表
            
        Returns:
            List[Dict[str, Any]]: 应用延迟后的信号列表
        """
        if not self.delay_simulator:
            return signals
        
        delayed_signals = []
        # 增加性能监控
        delay_stats = {
            'total_signals': len(signals),
            'success_count': 0,
            'failure_count': 0,
            'total_delay_applied': 0.0,
            'min_delay': float('inf'),
            'max_delay': 0.0
        }
        
        for signal in signals:
            try:
                # 获取原始时间戳
                original_timestamp = signal.get('signal_timestamp')
                if original_timestamp is None:
                    logger.warning(f"信号缺少signal_timestamp字段: {signal}")
                    delayed_signals.append(signal)
                    delay_stats['failure_count'] += 1
                    continue
                
                delay_result = self.delay_simulator.apply_delay_to_signal(signal)
                if delay_result and hasattr(delay_result, 'delay_seconds'):
                    # 计算延迟后的时间戳，正确处理不同的时间戳类型
                    if isinstance(original_timestamp, datetime):
                        # 如果原始时间戳是datetime对象，计算延迟后的datetime
                        delayed_timestamp = original_timestamp + timedelta(seconds=delay_result.delay_seconds)
                    else:
                        # 如果原始时间戳是int，直接相加
                        delayed_timestamp = original_timestamp + int(delay_result.delay_seconds)
                    
                    # 保存原始时间戳用于买卖匹配，更新延迟后时间戳用于实际交易
                    signal['original_signal_timestamp'] = original_timestamp  # 新增：保存原始时间戳
                    signal['signal_timestamp'] = delayed_timestamp  # 更新：延迟后的时间戳
                    
                    delayed_signals.append(signal)
                    
                    # 更新统计信息
                    delay_stats['success_count'] += 1
                    delay_stats['total_delay_applied'] += delay_result.delay_seconds
                    delay_stats['min_delay'] = min(delay_stats['min_delay'], delay_result.delay_seconds)
                    delay_stats['max_delay'] = max(delay_stats['max_delay'], delay_result.delay_seconds)
                    
                    logger.debug(f"信号延迟应用成功: token={signal.get('token_address', 'unknown')}, "
                               f"原始时间戳={original_timestamp}, 延迟后时间戳={delayed_timestamp}, "
                               f"延迟={delay_result.delay_seconds:.2f}s, 策略={delay_result.strategy_used}")
                else:
                    # 延迟应用失败，也要保存原始时间戳以保持一致性
                    signal['original_signal_timestamp'] = original_timestamp
                    delayed_signals.append(signal)
                    delay_stats['failure_count'] += 1
                    logger.warning(f"信号延迟应用失败，使用原始信号: {signal.get('token_address', 'unknown')}")
                    
            except Exception as e:
                # 异常情况，也要保存原始时间戳
                original_timestamp = signal.get('signal_timestamp')
                if original_timestamp:
                    signal['original_signal_timestamp'] = original_timestamp
                delayed_signals.append(signal)
                delay_stats['failure_count'] += 1
                logger.error(f"延迟模拟过程中发生异常: {e}, 信号: {signal.get('token_address', 'unknown')}")
        
        # 记录总体统计信息
        if delay_stats['success_count'] > 0:
            avg_delay = delay_stats['total_delay_applied'] / delay_stats['success_count']
            delay_stats['min_delay'] = delay_stats['min_delay'] if delay_stats['min_delay'] != float('inf') else 0.0
            
            logger.info(f"延迟模拟完成统计: 总信号={delay_stats['total_signals']}, "
                       f"成功={delay_stats['success_count']}, 失败={delay_stats['failure_count']}, "
                       f"平均延迟={avg_delay:.2f}s, 延迟范围=[{delay_stats['min_delay']:.2f}, {delay_stats['max_delay']:.2f}]s")
        
        # 将统计信息存储到实例中，供后续报告使用
        self.delay_simulation_stats = delay_stats
        
        return delayed_signals

    def _print_delay_simulation_summary(self):
        """打印延迟模拟摘要报告"""
        stats = self.delay_simulation_stats
        if not stats or stats['total_signals'] == 0:
            return
            
        print("\n" + "="*60)
        print("📊 延迟模拟执行摘要")
        print("="*60)
        print(f"总处理信号数: {stats['total_signals']}")
        print(f"成功应用延迟: {stats['success_count']}")
        print(f"应用失败: {stats['failure_count']}")
        print(f"成功率: {(stats['success_count']/stats['total_signals']*100):.2f}%")
        
        if stats['success_count'] > 0:
            avg_delay = stats['total_delay_applied'] / stats['success_count']
            print(f"平均延迟: {avg_delay:.2f} 秒")
            print(f"延迟范围: [{stats['min_delay']:.2f}, {stats['max_delay']:.2f}] 秒")
            print(f"总延迟时间: {stats['total_delay_applied']:.2f} 秒")
        
        print("="*60)

    def _apply_delay_simulation_to_sell_signals(self, sell_signals: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """应用延迟模拟到卖出信号列表
        
        Args:
            sell_signals: 原始卖出信号列表
            
        Returns:
            List[Dict[str, Any]]: 应用延迟后的卖出信号列表
        """
        if not self.delay_simulator:
            return sell_signals
        
        delayed_sell_signals = []
        
        for sell_signal in sell_signals:
            try:
                # 获取原始卖出时间戳
                original_sell_timestamp = sell_signal.get('sell_timestamp')
                if original_sell_timestamp is None:
                    logger.warning(f"卖出信号缺少sell_timestamp字段，跳过延迟应用")
                    delayed_sell_signals.append(sell_signal)
                    continue
                
                # 应用延迟模拟
                delay_result = self.delay_simulator.apply_delay_to_signal({
                    'signal_timestamp': original_sell_timestamp,
                    'token_address': sell_signal['token_address']
                })
                
                if delay_result:
                    # 创建延迟后的卖出信号
                    delayed_sell_signal = sell_signal.copy()
                    
                    # 计算延迟后的时间戳
                    if hasattr(delay_result, 'delayed_timestamp') and delay_result.delayed_timestamp:
                        delayed_sell_timestamp = delay_result.delayed_timestamp.timestamp()
                    else:
                        delayed_sell_timestamp = original_sell_timestamp + delay_result.delay_seconds
                    
                    # 更新卖出信号的时间戳
                    delayed_sell_signal['sell_timestamp'] = delayed_sell_timestamp
                    delayed_sell_signal['original_sell_timestamp'] = original_sell_timestamp
                    delayed_sell_signal['sell_delay_applied_seconds'] = delay_result.delay_seconds
                    
                    # 关键修复：buy_signal_timestamp 保持不变，用于与买入信号的 original_signal_timestamp 匹配
                    # 不修改 buy_signal_timestamp，因为它用于买卖匹配
                    
                    delayed_sell_signals.append(delayed_sell_signal)
                    
                    logger.debug(f"卖出信号延迟应用成功: {sell_signal['token_address']}, "
                               f"原始卖出时间: {original_sell_timestamp}, "
                               f"延迟后卖出时间: {delayed_sell_timestamp}, "
                               f"延迟: {delay_result.delay_seconds}s, "
                               f"匹配时间戳: {sell_signal.get('buy_signal_timestamp')} (保持不变)")
                else:
                    logger.warning(f"卖出信号延迟应用失败: {sell_signal['token_address']}")
                    delayed_sell_signals.append(sell_signal)
                    
            except Exception as e:
                logger.error(f"处理卖出信号延迟时出错: {e}")
                delayed_sell_signals.append(sell_signal)
        
        logger.info(f"卖出信号延迟模拟完成，成功处理 {len(delayed_sell_signals)} 个信号")
        return delayed_sell_signals
