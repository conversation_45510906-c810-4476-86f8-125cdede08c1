"""
延迟感知价格查询模块

创建日期：2025-01-20
更新日期：2025-01-20
版本：v1.0.0

功能概述：
- 延迟感知价格查询和影响分析
- 价格数据获取失败处理
- 价格影响统计摘要生成
- 异常处理和数据验证

对应技术方案: @backtest_v2_delay_simulation_dev_plan_ai.md DelayAwarePriceQuery设计
"""

import logging
from dataclasses import dataclass, field
from datetime import datetime, timezone
from decimal import Decimal, ROUND_HALF_UP
from typing import Optional, Dict, Any, List, Union, Protocol
from collections import defaultdict

logger = logging.getLogger(__name__)


# ================== 异常定义 ==================

class PriceQueryError(Exception):
    """价格查询基础异常"""
    pass


class InvalidPriceDataError(PriceQueryError):
    """无效价格数据异常"""
    pass


class PriceDataNotFoundError(PriceQueryError):
    """价格数据未找到异常"""
    pass


# ================== 数据模型 ==================

@dataclass
class PriceImpactResult:
    """价格影响结果数据模型"""
    original_price: Decimal
    delayed_price: Decimal
    price_change_percent: Decimal
    delay_seconds: float
    is_favorable: bool
    timestamp: datetime
    
    def __post_init__(self):
        """数据验证"""
        if self.original_price <= 0:
            raise ValueError("原始价格必须大于0")
        if self.delayed_price <= 0:
            raise ValueError("延迟价格必须大于0")
        if self.delay_seconds < 0:
            raise ValueError("延迟秒数不能为负数")


@dataclass
class PriceImpactSummary:
    """价格影响统计摘要"""
    total_queries: int = 0
    successful_queries: int = 0
    failed_queries: int = 0
    success_rate: float = 0.0
    average_price_change_percent: Decimal = Decimal("0.0")
    favorable_impact_count: int = 0
    unfavorable_impact_count: int = 0
    neutral_impact_count: int = 0
    total_delay_seconds: float = 0.0
    average_delay_seconds: float = 0.0
    failure_reasons: Dict[str, int] = field(default_factory=lambda: defaultdict(int))
    
    def add_successful_query(
        self, 
        price_change_percent: Decimal, 
        delay_seconds: float, 
        is_favorable: bool
    ) -> None:
        """添加成功查询的统计数据"""
        self.total_queries += 1
        self.successful_queries += 1
        self.total_delay_seconds += delay_seconds
        
        # 更新价格变化统计
        if self.successful_queries == 1:
            self.average_price_change_percent = price_change_percent
        else:
            # 增量计算平均值
            total_change = (
                self.average_price_change_percent * (self.successful_queries - 1) + 
                price_change_percent
            )
            self.average_price_change_percent = total_change / self.successful_queries
        
        # 更新延迟统计
        self.average_delay_seconds = self.total_delay_seconds / self.successful_queries
        
        # 更新影响分类统计
        if is_favorable:
            self.favorable_impact_count += 1
        elif price_change_percent == Decimal("0.0"):
            self.favorable_impact_count += 1  # 零变化也算有利
        else:
            self.unfavorable_impact_count += 1
        
        # 更新成功率
        self.success_rate = self.successful_queries / self.total_queries
    
    def add_failed_query(self, reason: str = "unknown") -> None:
        """添加失败查询的统计数据"""
        self.total_queries += 1
        self.failed_queries += 1
        self.failure_reasons[reason] += 1
        
        # 更新成功率
        self.success_rate = self.successful_queries / self.total_queries


# ================== 协议定义 ==================

class PriceDataSource(Protocol):
    """价格数据源协议"""
    
    def get_price_at_time(self, token_address: str, timestamp: datetime) -> Optional[Decimal]:
        """在指定时间获取代币价格"""
        ...


# ================== 主要类实现 ==================

class DelayAwarePriceQuery:
    """延迟感知价格查询器
    
    负责在应用延迟后查询对应时间点的价格，并分析延迟期间的价格影响。
    """
    
    def __init__(self, price_data_source: PriceDataSource, query_timeout: float = 10.0):
        """初始化延迟感知价格查询器
        
        Args:
            price_data_source: 价格数据源
            query_timeout: 查询超时时间（秒）
        
        Raises:
            ValueError: 参数无效
        """
        if price_data_source is None:
            raise ValueError("价格数据源不能为None")
        
        self.price_data_source = price_data_source
        self.query_timeout = query_timeout
        self._summary = PriceImpactSummary()
        
        logger.info(f"DelayAwarePriceQuery初始化完成，查询超时: {query_timeout}秒")
    
    def get_price_with_delay_impact(
        self,
        token_address: str,
        original_time: datetime,
        delayed_time: datetime
    ) -> Optional[PriceImpactResult]:
        """获取延迟价格影响分析
        
        Args:
            token_address: 代币地址
            original_time: 原始信号时间
            delayed_time: 延迟后时间
            
        Returns:
            PriceImpactResult: 价格影响分析结果，失败时返回None
            
        Raises:
            ValueError: 参数无效
            InvalidPriceDataError: 价格数据无效
        """
        # 参数验证
        if not token_address:
            raise ValueError("代币地址不能为空")
        if token_address is None:
            raise ValueError("代币地址不能为None")
        if original_time is None or delayed_time is None:
            raise ValueError("时间参数不能为None")
        
        try:
            # 计算延迟秒数
            delay_seconds = (delayed_time - original_time).total_seconds()
            
            # 零延迟处理 - 只查询一次价格
            if delay_seconds == 0:
                original_price = self.price_data_source.get_price_at_time(token_address, original_time)
                if original_price is None or original_price <= 0:
                    raise InvalidPriceDataError(f"无效的价格数据: {original_price}")
                
                result = PriceImpactResult(
                    original_price=original_price,
                    delayed_price=original_price,
                    price_change_percent=Decimal("0.0"),
                    delay_seconds=0.0,
                    is_favorable=True,  # 零延迟总是有利的
                    timestamp=datetime.now(timezone.utc)
                )
                
                # 更新统计
                self._summary.add_successful_query(
                    price_change_percent=Decimal("0.0"),
                    delay_seconds=0.0,
                    is_favorable=True
                )
                
                return result
            
            # 获取原始时间和延迟时间的价格
            original_price = self.price_data_source.get_price_at_time(token_address, original_time)
            delayed_price = self.price_data_source.get_price_at_time(token_address, delayed_time)
            
            # 验证价格数据
            if original_price is None or original_price <= 0:
                raise InvalidPriceDataError(f"无效的原始价格数据: {original_price}")
            if delayed_price is None or delayed_price <= 0:
                raise InvalidPriceDataError(f"无效的延迟价格数据: {delayed_price}")
            
            # 计算价格变化百分比
            price_change_percent = ((delayed_price - original_price) / original_price * 100).quantize(
                Decimal("0.1"), rounding=ROUND_HALF_UP
            )
            
            # 判断是否有利（对买入信号而言，价格下跌有利，价格上涨不利）
            is_favorable = price_change_percent <= 0
            
            result = PriceImpactResult(
                original_price=original_price,
                delayed_price=delayed_price,
                price_change_percent=price_change_percent,
                delay_seconds=delay_seconds,
                is_favorable=is_favorable,
                timestamp=datetime.now(timezone.utc)
            )
            
            # 更新统计
            self._summary.add_successful_query(
                price_change_percent=price_change_percent,
                delay_seconds=delay_seconds,
                is_favorable=is_favorable
            )
            
            logger.debug(f"价格影响分析完成: {token_address}, 变化: {price_change_percent}%, 延迟: {delay_seconds}s")
            return result
            
        except InvalidPriceDataError:
            # 重新抛出数据验证异常
            raise
        except TimeoutError as e:
            logger.warning(f"价格查询超时: {token_address}, 错误: {e}")
            self._summary.add_failed_query("timeout")
            return None
        except Exception as e:
            logger.error(f"价格查询失败: {token_address}, 错误: {e}")
            self._summary.add_failed_query("general_error")
            return None
    
    def get_price_impact_summary(self) -> PriceImpactSummary:
        """获取价格影响统计摘要
        
        Returns:
            PriceImpactSummary: 统计摘要
        """
        return self._summary
    
    def reset_statistics(self) -> None:
        """重置统计数据"""
        self._summary = PriceImpactSummary()
        logger.info("价格影响统计数据已重置")


# ================== 工具函数 ==================

def calculate_price_change_percentage(original_price: Decimal, new_price: Decimal) -> Decimal:
    """计算价格变化百分比
    
    Args:
        original_price: 原始价格
        new_price: 新价格
        
    Returns:
        Decimal: 价格变化百分比
        
    Raises:
        ValueError: 价格无效
    """
    if original_price <= 0:
        raise ValueError("原始价格必须大于0")
    if new_price <= 0:
        raise ValueError("新价格必须大于0")
    
    return ((new_price - original_price) / original_price * 100).quantize(
        Decimal("0.1"), rounding=ROUND_HALF_UP
    )


def is_favorable_price_change(price_change_percent: Decimal, is_buy_signal: bool = True) -> bool:
    """判断价格变化是否有利
    
    Args:
        price_change_percent: 价格变化百分比
        is_buy_signal: 是否为买入信号
        
    Returns:
        bool: 是否有利
    """
    if is_buy_signal:
        # 对买入信号，价格下跌有利
        return price_change_percent <= 0
    else:
        # 对卖出信号，价格上涨有利
        return price_change_percent >= 0


# ================== 版本信息 ==================

__version__ = "1.0.0"
__author__ = "memeMonitor Development Team"
__description__ = "延迟感知价格查询模块"

# ================== 模块导出 ==================

__all__ = [
    # 主要类
    "DelayAwarePriceQuery",
    
    # 数据模型
    "PriceImpactResult",
    "PriceImpactSummary",
    
    # 异常类
    "PriceQueryError",
    "InvalidPriceDataError", 
    "PriceDataNotFoundError",
    
    # 协议
    "PriceDataSource",
    
    # 工具函数
    "calculate_price_change_percentage",
    "is_favorable_price_change"
] 