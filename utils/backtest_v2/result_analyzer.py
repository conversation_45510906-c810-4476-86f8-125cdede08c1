"""结果分析组件 - 回测模块V2

保持与现有回测模块的输出兼容性，提供丰富的统计指标和可视化，
支持结果导出和报告生成。
"""

import os
import json
import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime

from utils.backtest_v2.config_manager import BacktestConfigV2
from utils.backtest_analysis.kelly_calculator import calculate_kelly

logger = logging.getLogger("ResultAnalyzerV2")


class ResultAnalyzer:
    """结果分析组件"""
    
    def __init__(self, config: BacktestConfigV2):
        """初始化结果分析器
        
        Args:
            config: 回测配置对象
        """
        self.config = config
        logger.info("结果分析器V2初始化完成")
    
    def analyze(self, trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析交易结果并生成统计指标
        
        Args:
            trades: 交易列表
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        try:
            logger.info(f"开始分析 {len(trades)} 笔交易的结果")
            
            if not trades:
                return self._generate_empty_result()
            
            # 计算性能指标
            statistics = self.calculate_performance_metrics(trades)
            
            # 生成资金曲线数据
            equity_curve = self.generate_equity_curve(trades)
            
            # 构建完整结果
            result = {
                'statistics': statistics,
                'trades': trades,
                'equity_curve': equity_curve,
                'config': self.config.__dict__,
                'analysis_timestamp': datetime.now().isoformat()
            }
            
            logger.info("结果分析完成")
            return result
            
        except Exception as e:
            logger.error(f"结果分析失败: {e}")
            raise
    
    def calculate_performance_metrics(self, trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算回测统计指标
        
        Args:
            trades: 交易列表
            
        Returns:
            Dict[str, Any]: 统计指标
        """
        if not trades:
            return {}
        
        df = pd.DataFrame(trades)
        
        # 基础统计
        total_trades = len(trades)
        winning_trades = len(df[df['return_rate'] > 0])
        losing_trades = len(df[df['return_rate'] < 0])
        
        # 胜率
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # 收益统计
        returns = df['return_rate'].values
        
        # 整体收益率：总盈利 / 总投资 (适合固定金额投资策略)
        total_invested = total_trades * self.config.fixed_trade_amount  # 总投资金额
        total_profit = df['profit_usd'].sum()  # 总盈利金额
        total_return = total_profit / total_invested if total_invested > 0 else 0  # 整体收益率
        
        # 注意：在固定金额投资策略下，总收益率 = 平均收益率，故不重复计算
        std_return = np.std(returns)
        
        # 盈亏统计
        winning_returns = df[df['return_rate'] > 0]['return_rate']
        losing_returns = df[df['return_rate'] < 0]['return_rate']
        
        avg_winning_return = np.mean(winning_returns) if len(winning_returns) > 0 else 0
        avg_losing_return = np.mean(losing_returns) if len(losing_returns) > 0 else 0
        
        # 最大回撤
        cumulative_returns = (1 + df['return_rate']).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = drawdown.min()
        
        # 夏普比率（假设无风险利率为0）
        sharpe_ratio = total_return / std_return if std_return > 0 else 0
        
        # 盈亏比
        profit_loss_ratio = abs(avg_winning_return / avg_losing_return) if avg_losing_return != 0 else 0
        
        # 计算凯利分数
        kelly_fraction = 0
        if winning_trades > 0 and losing_trades > 0:
            # 计算平均盈利和平均亏损（使用美元金额）
            winning_trades_df = df[df['profit_usd'] > 0]
            losing_trades_df = df[df['profit_usd'] <= 0]
            
            if len(winning_trades_df) > 0 and len(losing_trades_df) > 0:
                avg_win = winning_trades_df['profit_usd'].mean()
                avg_loss = abs(losing_trades_df['profit_usd'].mean())
                
                # 避免除以零
                if avg_loss > 0:
                    # 计算赔率 (avg_win / avg_loss)
                    payoff_ratio = avg_win / avg_loss
                    
                    # 使用标准凯利计算函数
                    raw_f, capped_f, status = calculate_kelly(win_rate, payoff_ratio)
                    
                    if capped_f is not None:
                        kelly_fraction = capped_f
                        logger.info(f"V2回测凯利分数计算 (原始值): {raw_f:.4f} (状态: {status}) - 已保存约束值: {capped_f:.4f}")
                    else:
                        logger.warning(f"V2回测凯利分数计算失败: {status}")
                    
                    # 记录胜率是否达标
                    if win_rate >= 0.5:
                        logger.info(f"V2回测胜率 {win_rate:.2%} 达到或超过 50%")
                    else:
                        logger.info(f"V2回测胜率 {win_rate:.2%} 未超过 50%")
        
        # 持仓时间统计（转换为分钟）
        holding_hours = df['holding_hours'].values
        holding_minutes = holding_hours * 60  # 转换为分钟
        avg_holding_minutes = np.mean(holding_minutes)
        max_holding_minutes = np.max(holding_minutes)
        min_holding_minutes = np.min(holding_minutes)
        
        # 按卖出原因分组统计
        sell_reason_stats = df.groupby('sell_reason').agg({
            'return_rate': ['count', 'mean'],
            'holding_hours': 'mean'
        }).round(4)

        # KOL数量统计
        kol_count_stats = df.groupby('kol_count').agg({
            'return_rate': ['count', 'mean']
        }).round(4)

        # 转换为可序列化的格式
        def convert_stats_to_dict(stats_df):
            """将pandas统计结果转换为可JSON序列化的字典"""
            if stats_df.empty:
                return {}

            result = {}
            for index, row in stats_df.iterrows():
                key = str(index)  # 确保键是字符串
                result[key] = {}
                for col in stats_df.columns:
                    if isinstance(col, tuple):
                        # 处理多级列名
                        col_name = '_'.join(str(c) for c in col)
                    else:
                        col_name = str(col)
                    result[key][col_name] = float(row[col]) if pd.notna(row[col]) else None
            return result
        
        statistics = {
            # 基础统计
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': round(win_rate, 4),
            
            # 收益统计
            'return_rate': round(total_return, 4),  # 整体收益率
            # 'std_return': round(std_return, 4),  # 暂不显示
            'avg_winning_return': round(avg_winning_return, 4),
            'avg_losing_return': round(avg_losing_return, 4),
            
            # 风险指标
            'max_drawdown': round(max_drawdown, 4),
            'max_profit': round(df['return_rate'].max(), 4) if not df.empty else 0.0,  # 最大盈利（单笔最高收益率）
            'sharpe_ratio': round(sharpe_ratio, 4),
            'profit_loss_ratio': round(profit_loss_ratio, 4),
            
            # 凯利分数
            'kelly_fraction_calculated': round(kelly_fraction, 4),
            
            # 持仓时间
            # 时间相关（分钟）
            'avg_holding_minutes': round(avg_holding_minutes, 2),
            'max_holding_minutes': round(max_holding_minutes, 2),
            'min_holding_minutes': round(min_holding_minutes, 2),
            
            # 分组统计
            'sell_reason_stats': convert_stats_to_dict(sell_reason_stats),
            'kol_count_stats': convert_stats_to_dict(kol_count_stats),
            
            # 时间范围
            'backtest_start_time': self.config.backtest_start_time,
            'backtest_end_time': self.config.backtest_end_time,
            'backtest_days': (self.config.backtest_end_time - self.config.backtest_start_time) / 86400,
            
            # 交易参数
            'fixed_trade_amount': self.config.fixed_trade_amount,
            'total_invested': total_invested,
            'total_profit_usd': total_profit
        }
        
        logger.info(f"统计指标计算完成: 总交易={total_trades}, 胜率={win_rate:.2%}, "
                   f"总收益={total_return:.2%}, 最大回撤={max_drawdown:.2%}, 凯利分数={kelly_fraction:.4f}")
        
        return statistics
    
    def generate_equity_curve(self, trades: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """生成资金曲线数据
        
        Args:
            trades: 交易列表
            
        Returns:
            List[Dict[str, Any]]: 资金曲线数据点
        """
        if not trades:
            return []
        
        # 转换为DataFrame并按卖出时间排序
        df = pd.DataFrame(trades)
        df = df.sort_values('sell_timestamp')
        
        # 计算累积收益
        cumulative_capital = self.config.fixed_trade_amount
        equity_curve = []
        
        for _, trade in df.iterrows():
            # V2回测中每笔交易都是完整的买卖对，直接累加盈亏
            cumulative_capital += trade['profit_usd']
            
            equity_curve.append({
                'timestamp': datetime.fromtimestamp(trade['sell_timestamp']).isoformat(),
                'capital': cumulative_capital,
                'return_rate': (cumulative_capital - self.config.fixed_trade_amount) / self.config.fixed_trade_amount,
                'trade_count': len(equity_curve) + 1
            })
        
        return equity_curve
    
    def _generate_empty_result(self) -> Dict[str, Any]:
        """生成空结果的统计数据
        
        Returns:
            Dict[str, Any]: 空结果数据
        """
        return {
            'statistics': {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'win_rate': 0.0,
                'return_rate': 0.0,
                'avg_winning_return': 0.0,
                'avg_losing_return': 0.0,
                'max_drawdown': 0.0,
                'max_profit': 0.0,
                'sharpe_ratio': 0.0,
                'profit_loss_ratio': 0.0,
                'kelly_fraction_calculated': 0.0,
                'avg_holding_minutes': 0.0,
                'max_holding_minutes': 0.0,
                'min_holding_minutes': 0.0,
                'backtest_start_time': self.config.backtest_start_time,
                'backtest_end_time': self.config.backtest_end_time,
                'backtest_days': (self.config.backtest_end_time - self.config.backtest_start_time) / 86400,
                'fixed_trade_amount': self.config.fixed_trade_amount,
                'total_invested': 0.0,
                'total_profit_usd': 0.0
            },
            'trades': [],
            'equity_curve': [],
            'config': self.config.__dict__,
            'analysis_timestamp': datetime.now().isoformat()
        }
    
    def export_results(self, results: Dict[str, Any], output_dir: str):
        """导出回测结果
        
        Args:
            results: 分析结果
            output_dir: 输出目录
        """
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            # 导出JSON文件
            json_path = os.path.join(output_dir, 'backtest_results.json')
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, default=str, ensure_ascii=False)
            
            logger.info(f"回测结果已导出到: {json_path}")
            
        except Exception as e:
            logger.error(f"导出结果失败: {e}")
            raise
