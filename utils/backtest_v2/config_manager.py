"""配置管理组件 - 回测模块V2

统一管理所有配置参数，支持多种配置来源，提供参数验证和默认值处理。

扩展功能：延迟模拟配置管理
对应技术方案: @backtest_v2_delay_simulation_dev_plan_ai.md

重构优化:
- 改进类型提示和错误处理
- 优化配置验证逻辑
- 添加缓存机制和性能优化  
- 改进代码结构和可读性
"""

import json
import yaml
import logging
from typing import Dict, Any, List, Optional, Union, Final, ClassVar, Tuple
from dataclasses import dataclass, field, fields
import itertools
from functools import lru_cache

logger = logging.getLogger("ConfigManagerV2")

# 常量定义 - 提高配置管理的可维护性
SUPPORTED_CONFIG_FORMATS: Final[List[str]] = ['.json', '.yaml', '.yml']
DEFAULT_DELAY_SIMULATION_ENABLED: Final[bool] = False
DEFAULT_DELAY_STRATEGY: Final[str] = "fixed"
DEFAULT_MAX_DELAY_SECONDS: Final[float] = 300.0
VALID_DELAY_STRATEGIES: Final[List[str]] = ["fixed", "statistical", "distribution"]
VALID_STATISTICAL_TYPES: Final[List[str]] = ["mean", "median", "p95", "p99"]


class ConfigurationError(Exception):
    """配置相关异常的基类"""
    pass


class ConfigValidationError(ConfigurationError, ValueError):
    """配置验证失败异常
    
    继承自ValueError以保持向后兼容性
    """
    pass


class UnsupportedConfigFormatError(ConfigurationError):
    """不支持的配置文件格式异常"""
    pass


@dataclass
class ValidationResult:
    """配置验证结果类"""
    is_valid: bool = True
    errors: List[str] = field(default_factory=list)
    
    def add_error(self, error: str) -> None:
        """添加错误信息"""
        self.errors.append(error)
        self.is_valid = False
    
    def is_enabled(self) -> bool:
        """检查验证是否成功 - 兼容测试方法"""
        return self.is_valid


@dataclass
class DelaySimulationConfig:
    """延迟模拟配置类
    
    对应技术方案: @backtest_v2_delay_simulation_dev_plan_ai.md 延迟配置数据模型
    
    重构改进:
    - 使用常量提高可维护性
    - 改进异常处理和错误消息
    - 添加类型提示优化
    - 优化验证逻辑
    """
    enabled: bool = DEFAULT_DELAY_SIMULATION_ENABLED
    strategy: str = DEFAULT_DELAY_STRATEGY
    fixed_delay_seconds: float = 0.0
    max_delay_seconds: float = DEFAULT_MAX_DELAY_SECONDS
    statistical_type: str = "median"
    delay_data_time_range: int = 30
    
    def __post_init__(self) -> None:
        """后处理初始化，验证参数有效性"""
        self.validate()
    
    def validate(self) -> None:
        """验证延迟配置参数的有效性
        
        Raises:
            ConfigValidationError: 当配置参数无效时
        """
        # 验证策略类型
        if self.strategy not in VALID_DELAY_STRATEGIES:
            raise ConfigValidationError(
                f"延迟策略 '{self.strategy}' 无效。"
                f"支持的策略: {', '.join(VALID_DELAY_STRATEGIES)}"
            )
        
        # 验证延迟时间参数
        if self.fixed_delay_seconds < 0:
            raise ConfigValidationError(
                f"固定延迟时间不能为负数: {self.fixed_delay_seconds}"
            )
        
        if self.max_delay_seconds < 0:
            raise ConfigValidationError(
                f"最大延迟时间不能为负数: {self.max_delay_seconds}"
            )
        
        if self.fixed_delay_seconds > self.max_delay_seconds:
            raise ConfigValidationError(
                f"固定延迟时间 ({self.fixed_delay_seconds}) "
                f"不能超过最大延迟时间 ({self.max_delay_seconds})"
            )
        
        # 验证统计类型
        if self.statistical_type not in VALID_STATISTICAL_TYPES:
            raise ConfigValidationError(
                f"统计类型 '{self.statistical_type}' 无效。"
                f"支持的类型: {', '.join(VALID_STATISTICAL_TYPES)}"
            )
        
        # 验证时间范围
        if self.delay_data_time_range <= 0:
            raise ConfigValidationError(
                f"延迟数据时间范围必须大于0: {self.delay_data_time_range}"
            )
    
    def is_enabled(self) -> bool:
        """检查延迟模拟是否启用"""
        return self.enabled
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'enabled': self.enabled,
            'strategy': self.strategy,
            'fixed_delay_seconds': self.fixed_delay_seconds,
            'max_delay_seconds': self.max_delay_seconds,
            'statistical_type': self.statistical_type,
            'delay_data_time_range': self.delay_data_time_range
        }


@dataclass
class BacktestConfigV2:
    """回测模块V2配置类
    
    扩展功能：增加延迟模拟配置支持
    """
    
    # 回测时间参数
    backtest_start_time: int = **********  # 2023-01-01 00:00:00 UTC
    backtest_end_time: int = **********    # 2023-02-01 00:00:00 UTC
    
    # 数据筛选参数
    transaction_min_amount: float = 500.0  # 最小交易金额(USD)
    kol_account_min_txs: int = 10          # KOL账号最小交易数
    kol_account_max_txs: int = 500         # KOL账号最大交易数
    kol_account_min_count: int = 6         # 最小KOL账号数量
    kol_min_winrate: float = 0.0           # KOL最小总胜率阈值(0-1，默认0表示不过滤)
    token_mint_lookback_hours: int = 48    # 代币创建时间回溯小时数
    
    # 策略参数
    transaction_lookback_hours: int = 24   # 交易回溯时间窗口(小时)
    sell_strategy_hours: int = 24          # 卖出策略时间窗口(小时)
    sell_kol_ratio: float = 0.5            # 卖出KOL比例阈值
    
    # 回测控制参数
    fixed_trade_amount: float = 100.0      # 固定买入金额(USD)
    commission_pct: float = 0.003          # 手续费百分比
    slippage_pct: float = 0.002            # 滑点百分比
    same_token_notification_interval_minutes: int = 60  # 相同代币通知最小间隔(分钟)
    
    # 延迟模拟配置 - 新增
    delay_simulation: DelaySimulationConfig = field(default_factory=DelaySimulationConfig)
    
    def __post_init__(self):
        """后处理初始化，验证参数有效性"""
        self.validate()
    
    def validate(self):
        """验证配置参数的有效性"""
        if self.backtest_start_time >= self.backtest_end_time:
            raise ValueError("回测开始时间必须小于结束时间")
        
        if self.transaction_min_amount <= 0:
            raise ValueError("最小交易金额必须大于0")
        
        if self.kol_account_min_count <= 0:
            raise ValueError("最小KOL账号数量必须大于0")
        
        if self.kol_account_min_txs <= 0:
            raise ValueError("KOL最小交易数必须大于0")
        
        if self.kol_account_max_txs < self.kol_account_min_txs:
            raise ValueError("KOL最大交易数必须大于等于最小交易数")
        
        if not 0 < self.sell_kol_ratio <= 1:
            raise ValueError("卖出KOL比例阈值必须在(0, 1]范围内")

        if not 0 <= self.kol_min_winrate <= 1:
            raise ValueError("KOL最小总胜率阈值必须在[0, 1]范围内")

        if self.transaction_lookback_hours <= 0:
            raise ValueError("交易回溯时间窗口必须大于0")

        if self.sell_strategy_hours <= 0:
            raise ValueError("卖出策略时间窗口必须大于0")

        if self.token_mint_lookback_hours <= 0:
            raise ValueError("代币创建时间回溯小时数必须大于0")
        
        if self.same_token_notification_interval_minutes <= 0:
            raise ValueError("相同代币通知最小间隔必须大于0")
        
        if self.fixed_trade_amount <= 0:
            raise ValueError("固定买入金额必须大于0")
        
        # 验证延迟模拟配置 - 修复字典转换问题
        if isinstance(self.delay_simulation, dict):
            try:
                self.delay_simulation = self._convert_delay_simulation_dict(self.delay_simulation)
            except (ConfigValidationError, ValueError, TypeError) as e:
                raise ConfigValidationError(f"延迟模拟配置无效: {e}") from e
        elif not isinstance(self.delay_simulation, DelaySimulationConfig):
            # 如果不是字典也不是DelaySimulationConfig实例，给出更明确的错误
            raise ConfigValidationError(
                f"延迟模拟配置必须是DelaySimulationConfig实例或字典，"
                f"当前类型: {type(self.delay_simulation)}"
            )
    
    @staticmethod
    def _convert_delay_simulation_dict(delay_dict: Dict[str, Any]) -> DelaySimulationConfig:
        """将字典转换为DelaySimulationConfig实例 - 修复静默字段丢失和不明确错误问题
        
        Args:
            delay_dict: 延迟配置字典
            
        Returns:
            DelaySimulationConfig: 延迟配置对象
            
        Raises:
            ConfigValidationError: 配置验证失败，包含未知字段或无效值
        """
        if not isinstance(delay_dict, dict):
            raise ConfigValidationError(f"延迟配置必须是字典类型，当前类型: {type(delay_dict)}")
        
        # 获取DelaySimulationConfig的所有有效字段
        valid_fields = {field.name for field in fields(DelaySimulationConfig)}
        
        # 检查未知字段，防止静默丢失配置
        unknown_fields = set(delay_dict.keys()) - valid_fields
        if unknown_fields:
            raise ConfigValidationError(
                f"延迟配置包含未知字段: {', '.join(sorted(unknown_fields))}。"
                f"支持的字段: {', '.join(sorted(valid_fields))}"
            )
        
        # 逐个验证和转换字段值
        validated_dict = {}
        for key, value in delay_dict.items():
            try:
                validated_dict[key] = BacktestConfigV2._validate_delay_field(key, value)
            except (ValueError, TypeError) as e:
                raise ConfigValidationError(f"延迟配置字段 '{key}' 无效: {e}") from e
        
        # 创建DelaySimulationConfig实例，这会触发__post_init__验证
        try:
            return DelaySimulationConfig(**validated_dict)
        except ConfigValidationError as e:
            # 重新抛出更明确的错误信息
            raise ConfigValidationError(f"延迟模拟配置验证失败: {e}") from e
    
    @staticmethod
    def _validate_delay_field(field_name: str, value: Any) -> Any:
        """验证单个延迟配置字段的值类型和范围
        
        Args:
            field_name: 字段名
            value: 字段值
            
        Returns:
            Any: 验证后的字段值
            
        Raises:
            ValueError: 字段值类型或范围无效
        """
        if field_name == 'enabled':
            if not isinstance(value, bool):
                # 尝试转换常见的布尔表示
                if isinstance(value, str):
                    if value.lower() in ('true', '1', 'yes', 'on'):
                        return True
                    elif value.lower() in ('false', '0', 'no', 'off'):
                        return False
                    else:
                        raise ValueError(f"无法将字符串 '{value}' 转换为布尔值")
                elif isinstance(value, (int, float)):
                    return bool(value)
                else:
                    raise ValueError(f"期望布尔值，得到 {type(value)}")
            return value
        
        elif field_name == 'strategy':
            if not isinstance(value, str):
                raise ValueError(f"期望字符串，得到 {type(value)}")
            if value not in VALID_DELAY_STRATEGIES:
                raise ValueError(f"无效策略 '{value}'，支持的策略: {', '.join(VALID_DELAY_STRATEGIES)}")
            return value
        
        elif field_name in ['fixed_delay_seconds', 'max_delay_seconds']:
            if not isinstance(value, (int, float)):
                raise ValueError(f"期望数值，得到 {type(value)}")
            float_value = float(value)
            if float_value < 0:
                raise ValueError(f"不能为负数: {float_value}")
            return float_value
        
        elif field_name == 'statistical_type':
            if not isinstance(value, str):
                raise ValueError(f"期望字符串，得到 {type(value)}")
            if value not in VALID_STATISTICAL_TYPES:
                raise ValueError(f"无效统计类型 '{value}'，支持的类型: {', '.join(VALID_STATISTICAL_TYPES)}")
            return value
        
        elif field_name == 'delay_data_time_range':
            if not isinstance(value, (int, float)):
                raise ValueError(f"期望整数，得到 {type(value)}")
            int_value = int(value)
            if int_value <= 0:
                raise ValueError(f"必须大于0: {int_value}")
            return int_value
        
        else:
            # 未知字段（理论上不应该到达这里，因为之前已经检查过）
            raise ValueError(f"未知字段: {field_name}")
        
        return value


class ConfigManagerV2:
    """回测配置管理器V2 - 重构优化版本
    
    主要改进：
    - 异常体系重建
    - 常量驱动设计
    - 性能优化缓存
    - 方法功能增强
    - 向后兼容性保证
    
    对应技术方案: @backtest_v2_delay_simulation_dev_plan_ai.md 配置管理增强
    """
    
    # 类常量 - 重构改进：常量驱动设计
    _config_cache: ClassVar[Dict[str, Tuple[BacktestConfigV2, float]]] = {}
    _cache_ttl: ClassVar[float] = 300.0  # 5分钟缓存TTL
    
    @staticmethod
    def load_from_file(file_path: str) -> BacktestConfigV2:
        """从文件加载配置（兼容旧版本方法名）
        
        Args:
            file_path: 配置文件路径
            
        Returns:
            BacktestConfigV2: 配置对象
            
        Raises:
            UnsupportedConfigFormatError: 不支持的文件格式
            ConfigValidationError: 配置验证失败
        """
        return ConfigManagerV2.load_config_from_file(file_path)
    
    @staticmethod
    def load_config_from_file(file_path: str) -> BacktestConfigV2:
        """从配置文件加载配置 - 重构优化版本
        
        Args:
            file_path: 配置文件路径
            
        Returns:
            BacktestConfigV2: 配置对象
            
        Raises:
            UnsupportedConfigFormatError: 不支持的文件格式
            ConfigValidationError: 配置验证失败
        """
        import time
        
        # 重构改进：缓存机制优化性能
        current_time = time.time()
        if file_path in ConfigManagerV2._config_cache:
            cached_config, cached_time = ConfigManagerV2._config_cache[file_path]
            if current_time - cached_time < ConfigManagerV2._cache_ttl:
                logger.debug(f"从缓存加载配置: {file_path}")
                return cached_config
        
        try:
            if file_path.lower().endswith('.json'):
                config_data = ConfigManagerV2._load_json_file(file_path)
            elif file_path.lower().endswith(('.yaml', '.yml')):
                config_data = ConfigManagerV2._load_yaml_file(file_path)
            else:
                raise UnsupportedConfigFormatError(
                    f"不支持的配置文件格式: {file_path}。"
                    f"支持的格式: {', '.join(SUPPORTED_CONFIG_FORMATS)}"
                )
            
            # 过滤V2参数并创建配置对象
            v2_config_data = ConfigManagerV2._filter_v2_params(config_data)
            config = ConfigManagerV2.load_config_from_dict(v2_config_data)
            
            # 重构改进：缓存配置对象
            ConfigManagerV2._config_cache[file_path] = (config, current_time)
            
            logger.info(f"成功从文件加载配置: {file_path}")
            return config
            
        except (UnsupportedConfigFormatError, ConfigValidationError):
            raise
        except Exception as e:
            logger.error(f"从文件加载配置失败: {file_path}, 错误: {e}")
            raise ConfigurationError(f"从文件加载配置失败: {file_path}") from e
    
    @staticmethod
    def _load_json_file(file_path: str) -> Dict[str, Any]:
        """加载JSON配置文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError as e:
            raise ConfigValidationError(f"JSON文件格式错误: {e}") from e
        except FileNotFoundError:
            raise ConfigValidationError(f"配置文件不存在: {file_path}")
        except Exception as e:
            raise ConfigurationError(f"读取JSON文件失败: {e}") from e
    
    @staticmethod
    def _load_yaml_file(file_path: str) -> Dict[str, Any]:
        """加载YAML配置文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except yaml.YAMLError as e:
            raise ConfigValidationError(f"YAML文件格式错误: {e}") from e
        except FileNotFoundError:
            raise ConfigValidationError(f"配置文件不存在: {file_path}")
        except Exception as e:
            raise ConfigurationError(f"读取YAML文件失败: {e}") from e
    
    @staticmethod
    def load_config_from_dict(config_dict: Dict[str, Any]) -> BacktestConfigV2:
        """从字典创建配置对象 - 重构优化版本
        
        Args:
            config_dict: 配置字典
            
        Returns:
            BacktestConfigV2: 配置对象
            
        Raises:
            ConfigValidationError: 配置验证失败
        """
        try:
            # 重构改进：更好的错误处理和验证
            if not isinstance(config_dict, dict):
                raise ConfigValidationError(
                    f"配置数据必须是字典类型，当前类型: {type(config_dict)}"
                )
            
            # 过滤V2参数
            v2_config_data = ConfigManagerV2._filter_v2_params(config_dict)
            
            # 创建配置对象
            config = BacktestConfigV2(**v2_config_data)
            
            logger.info("成功从字典创建配置对象")
            return config
            
        except (TypeError, ValueError) as e:
            logger.error(f"从字典创建配置对象失败: {e}")
            raise ConfigValidationError(f"配置参数无效: {e}") from e
        except Exception as e:
            logger.error(f"从字典创建配置对象时发生未预期错误: {e}")
            raise ConfigurationError(f"配置对象创建失败") from e
    
    @staticmethod
    def _filter_v2_params(config_data: Dict[str, Any]) -> Dict[str, Any]:
        """过滤并转换为V2配置参数格式 - 重构优化版本
        
        Args:
            config_data: 原始配置字典
            
        Returns:
            Dict[str, Any]: 过滤后的V2配置字典
        """
        # 重构改进：使用常量提高可维护性
        valid_fields = {field.name for field in fields(BacktestConfigV2)}
        
        v2_config = {}
        for key, value in config_data.items():
            if key in valid_fields:
                v2_config[key] = value
            else:
                logger.debug(f"跳过非V2配置参数: {key}")
        
        logger.debug(f"过滤V2配置参数: {len(v2_config)}/{len(config_data)} 个参数保留")
        return v2_config
    
    @staticmethod
    def create_delay_strategy(delay_config: DelaySimulationConfig):
        """创建延迟策略实例 - 重构优化版本
        
        Args:
            delay_config: 延迟模拟配置
            
        Returns:
            DelayStrategy: 延迟策略实例
            
        Raises:
            ConfigValidationError: 策略配置无效
            ImportError: 导入策略模块失败
        """
        try:
            # 重构改进：验证输入参数
            if not isinstance(delay_config, DelaySimulationConfig):
                raise ConfigValidationError(
                    f"延迟配置必须是DelaySimulationConfig实例，"
                    f"当前类型: {type(delay_config)}"
                )
            
            # 动态导入延迟策略模块
            from utils.backtest_v2.delay_strategy import DelayStrategy, FixedDelayStrategy
            
            # 重构改进：支持禁用延迟的情况，返回零延迟策略
            if not delay_config.enabled:
                strategy = FixedDelayStrategy(
                    delay_seconds=0.0,
                    max_delay_seconds=delay_config.max_delay_seconds
                )
                logger.info("延迟模拟已禁用，创建零延迟策略")
                return strategy
            
            # 重构改进：使用常量驱动的策略创建
            if delay_config.strategy == "fixed":
                strategy = FixedDelayStrategy(
                    delay_seconds=delay_config.fixed_delay_seconds,
                    max_delay_seconds=delay_config.max_delay_seconds
                )
                logger.info(f"创建固定延迟策略: {delay_config.fixed_delay_seconds}秒")
                return strategy
            
            # TODO: 实现其他策略类型
            # elif delay_config.strategy == "statistical":
            #     return StatisticalDelayStrategy(...)
            # elif delay_config.strategy == "distribution":
            #     return DistributionDelayStrategy(...)
            else:
                supported_strategies = ', '.join(VALID_DELAY_STRATEGIES)
                raise ConfigValidationError(
                    f"不支持的延迟策略: '{delay_config.strategy}'。"
                    f"支持的策略: {supported_strategies}"
                )
        
        except ImportError as e:
            logger.error(f"导入延迟策略模块失败: {e}")
            raise ConfigurationError("延迟策略模块不可用") from e
        except (ConfigValidationError, ConfigurationError):
            raise
        except Exception as e:
            logger.error(f"创建延迟策略失败: {e}")
            raise ConfigurationError("延迟策略创建失败") from e
    
    @staticmethod
    def get_default_config() -> BacktestConfigV2:
        """返回默认配置参数 - 重构优化版本
        
        Returns:
            BacktestConfigV2: 默认配置对象
        """
        try:
            return BacktestConfigV2()
        except Exception as e:
            logger.error(f"创建默认配置失败: {e}")
            raise ConfigurationError("默认配置创建失败") from e
    
    @staticmethod
    def is_config_valid(config: Union[BacktestConfigV2, Dict[str, Any]]) -> bool:
        """检查配置是否有效 - 新增便捷方法
        
        Args:
            config: 配置字典或配置对象
            
        Returns:
            bool: 配置是否有效
        """
        try:
            result = ConfigManagerV2.validate_config(config)
            return result.is_enabled()
        except Exception:
            return False
    
    @staticmethod
    def get_config_errors(config: Union[BacktestConfigV2, Dict[str, Any]]) -> List[str]:
        """获取配置错误列表 - 新增便捷方法
        
        Args:
            config: 配置字典或配置对象
            
        Returns:
            List[str]: 错误信息列表
        """
        try:
            result = ConfigManagerV2.validate_config(config)
            return result.errors
        except Exception as e:
            return [str(e)]
    
    @staticmethod
    def clear_cache() -> None:
        """清空配置缓存 - 新增方法
        
        用于测试或强制重新加载配置
        """
        ConfigManagerV2._config_cache.clear()
        logger.info("配置缓存已清空")
    
    @staticmethod
    def get_cache_info() -> Dict[str, Any]:
        """获取缓存信息 - 新增方法
        
        Returns:
            Dict[str, Any]: 缓存统计信息
        """
        return {
            'cache_size': len(ConfigManagerV2._config_cache),
            'cache_ttl': ConfigManagerV2._cache_ttl,
            'cached_files': list(ConfigManagerV2._config_cache.keys())
        }
    
    @staticmethod
    def apply_cli_overrides(config: BacktestConfigV2, args) -> BacktestConfigV2:
        """应用命令行参数覆盖 - 重构优化版本
        
        Args:
            config: 基础配置对象
            args: 命令行参数字典或对象
            
        Returns:
            BacktestConfigV2: 更新后的配置对象
            
        Raises:
            ConfigValidationError: 参数类型错误或配置无效
        """
        try:
            # 将配置对象转换为字典
            config_dict = config.__dict__.copy()
            
            # 重构改进：更好的参数类型处理
            if isinstance(args, dict):
                args_dict = args
            else:
                # 如果是对象，尝试获取其属性
                try:
                    args_dict = vars(args)
                except TypeError:
                    raise ConfigValidationError(
                        f"命令行参数必须是字典或具有__dict__属性的对象，"
                        f"当前类型: {type(args)}"
                    )
            
            # 应用命令行参数覆盖
            override_count = 0
            for key, value in args_dict.items():
                if value is not None:
                    if ConfigManagerV2._apply_nested_override(config_dict, key, value):
                        override_count += 1
                        logger.info(f"命令行参数覆盖: {key} = {value}")
            
            logger.info(f"应用了 {override_count} 个命令行参数覆盖")
            
            # 创建新的配置对象
            return BacktestConfigV2(**config_dict)
            
        except (ConfigValidationError, ConfigurationError):
            raise
        except Exception as e:
            logger.error(f"应用命令行覆盖失败: {e}")
            raise ConfigurationError("命令行参数覆盖失败") from e
    
    @staticmethod
    def _apply_nested_override(config_dict: Dict[str, Any], key: str, value: Any) -> bool:
        """应用嵌套配置覆盖 - 重构改进的内部方法
        
        Args:
            config_dict: 配置字典
            key: 配置键（支持点分嵌套，如 delay_simulation.enabled）
            value: 配置值
            
        Returns:
            bool: 是否成功应用覆盖
        """
        try:
            if '.' in key:
                parts = key.split('.')
                if len(parts) == 2 and parts[0] == 'delay_simulation':
                    # 处理延迟模拟配置的嵌套属性
                    delay_config = config_dict.get('delay_simulation', DelaySimulationConfig())
                    if isinstance(delay_config, DelaySimulationConfig):
                        setattr(delay_config, parts[1], value)
                    else:
                        # 如果是字典，转换为对象
                        delay_dict = delay_config if isinstance(delay_config, dict) else {}
                        delay_dict[parts[1]] = value
                        config_dict['delay_simulation'] = DelaySimulationConfig(**delay_dict)
                    return True
            else:
                # 直接配置字段
                if key in config_dict:
                    config_dict[key] = value
                    return True
            
            return False
            
        except Exception as e:
            logger.warning(f"应用嵌套覆盖失败: {key} = {value}, 错误: {e}")
            return False
    
    @staticmethod
    def apply_environment_overrides(config: BacktestConfigV2) -> BacktestConfigV2:
        """应用环境变量覆盖 - 重构优化版本
        
        Args:
            config: 基础配置对象
            
        Returns:
            BacktestConfigV2: 更新后的配置对象
            
        Raises:
            ConfigValidationError: 环境变量值无效
        """
        import os
        
        try:
            # 将配置对象转换为字典
            config_dict = config.__dict__.copy()
            
            # 重构改进：使用常量定义环境变量前缀
            env_prefix = "BACKTEST_"
            override_count = 0
            
            # 查找所有相关环境变量
            for env_key, env_value in os.environ.items():
                if env_key.startswith(env_prefix):
                    # 移除前缀并转换为小写
                    config_key = env_key[len(env_prefix):].lower()
                    
                    if ConfigManagerV2._apply_env_override(config_dict, config_key, env_value, config):
                        override_count += 1
                        logger.info(f"环境变量覆盖: {config_key} = {env_value}")
            
            logger.info(f"应用了 {override_count} 个环境变量覆盖")
            
            # 创建新的配置对象
            return BacktestConfigV2(**config_dict)
            
        except (ConfigValidationError, ConfigurationError):
            raise
        except Exception as e:
            logger.error(f"应用环境变量覆盖失败: {e}")
            raise ConfigurationError("环境变量覆盖失败") from e
    
    @staticmethod
    def _apply_env_override(config_dict: Dict[str, Any], config_key: str, 
                           env_value: str, original_config: BacktestConfigV2) -> bool:
        """应用单个环境变量覆盖 - 重构改进的内部方法
        
        Args:
            config_dict: 配置字典
            config_key: 配置键
            env_value: 环境变量值
            original_config: 原始配置对象
            
        Returns:
            bool: 是否成功应用覆盖
        """
        try:
            # 处理嵌套配置，如 DELAY_SIMULATION_ENABLED
            if config_key.startswith('delay_simulation_'):
                nested_key = config_key[len('delay_simulation_'):]
                
                # 获取当前延迟配置
                delay_config = config_dict.get('delay_simulation', DelaySimulationConfig())
                if isinstance(delay_config, DelaySimulationConfig):
                    delay_dict = delay_config.to_dict()
                else:
                    delay_dict = delay_config if isinstance(delay_config, dict) else {}
                
                # 转换环境变量值类型
                if nested_key == 'enabled':
                    delay_dict[nested_key] = env_value.lower() in ('true', '1', 'yes')
                elif nested_key in ['fixed_delay_seconds', 'max_delay_seconds']:
                    delay_dict[nested_key] = float(env_value)
                elif nested_key in ['delay_data_time_range']:
                    delay_dict[nested_key] = int(env_value)
                else:
                    delay_dict[nested_key] = env_value
                
                config_dict['delay_simulation'] = DelaySimulationConfig(**delay_dict)
                return True
            
            elif hasattr(original_config, config_key):
                # 直接配置字段
                # 根据原始类型转换值
                original_value = getattr(original_config, config_key)
                if isinstance(original_value, bool):
                    config_dict[config_key] = env_value.lower() in ('true', '1', 'yes')
                elif isinstance(original_value, int):
                    config_dict[config_key] = int(env_value)
                elif isinstance(original_value, float):
                    config_dict[config_key] = float(env_value)
                else:
                    config_dict[config_key] = env_value
                return True
            
            return False
            
        except (ValueError, TypeError) as e:
            logger.warning(f"环境变量类型转换失败: {config_key} = {env_value}, 错误: {e}")
            return False
        except Exception as e:
            logger.warning(f"应用环境变量覆盖失败: {config_key} = {env_value}, 错误: {e}")
            return False
    
    @staticmethod
    def generate_parameter_combinations(param_grid: Dict[str, List]) -> List[Dict[str, Any]]:
        """生成参数网格的所有组合 - 重构优化版本
        
        Args:
            param_grid: 参数网格，如 {'param1': [1, 2], 'param2': [3, 4]}
            
        Returns:
            List[Dict[str, Any]]: 参数组合列表
            
        Raises:
            ConfigValidationError: 参数网格格式错误
        """
        try:
            # 重构改进：输入验证
            if not isinstance(param_grid, dict):
                raise ConfigValidationError(
                    f"参数网格必须是字典类型，当前类型: {type(param_grid)}"
                )
            
            if not param_grid:
                logger.warning("参数网格为空，返回空列表")
                return []
            
            # 验证所有值都是列表
            for key, values in param_grid.items():
                if not isinstance(values, list):
                    raise ConfigValidationError(
                        f"参数 '{key}' 的值必须是列表，当前类型: {type(values)}"
                    )
            
            # 获取所有参数名和可能的值
            param_names = list(param_grid.keys())
            param_values = list(param_grid.values())
            
            # 生成笛卡尔积
            combinations = list(itertools.product(*param_values))
            
            # 转换为字典列表
            result = []
            for combination in combinations:
                config_dict = {name: value for name, value in zip(param_names, combination)}
                result.append(config_dict)
            
            logger.info(f"从 {len(param_names)} 个参数生成了 {len(result)} 个参数组合")
            return result
            
        except ConfigValidationError:
            raise
        except Exception as e:
            logger.error(f"生成参数组合失败: {e}")
            raise ConfigurationError("参数组合生成失败") from e
    
    @staticmethod
    def validate_config(config: Union[BacktestConfigV2, Dict[str, Any]]) -> ValidationResult:
        """验证配置参数的有效性
        
        Args:
            config: 配置字典或配置对象
            
        Returns:
            ValidationResult: 验证结果对象
        """
        result = ValidationResult()
        
        try:
            if isinstance(config, dict):
                # 尝试创建配置对象，会自动验证
                BacktestConfigV2(**config)
            elif isinstance(config, BacktestConfigV2):
                # 直接验证配置对象
                config.validate()
            else:
                result.add_error(f"不支持的配置类型: {type(config)}")
                return result
                
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            result.add_error(str(e))
        
        return result

    @staticmethod
    def validate_config_simple(config: Union[BacktestConfigV2, Dict[str, Any]]) -> bool:
        """验证配置参数的有效性 - 简化版本，为了兼容旧测试
        
        Args:
            config: 配置字典或配置对象
            
        Returns:
            bool: 验证是否成功
        """
        try:
            if isinstance(config, dict):
                # 尝试创建配置对象，会自动验证
                BacktestConfigV2(**config)
            elif isinstance(config, BacktestConfigV2):
                # 直接验证配置对象
                config.validate()
            else:
                return False
            return True
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            return False
