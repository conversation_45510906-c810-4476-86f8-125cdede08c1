"""
延迟策略模块

提供多种延迟计算策略的接口和实现，支持固定延迟、统计延迟、分布采样等模式。

技术方案参考: @backtest_v2_delay_simulation_dev_plan_ai.md
需求文档参考: @backtest_v2_delay_simulation_requirements_ai.md

重构优化:
- 改进类型提示和文档字符串
- 优化错误处理和验证逻辑
- 添加性能优化和缓存机制
- 改进代码结构和可读性
"""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import Optional, Dict, Any, Final, ClassVar
import logging
from functools import lru_cache

logger = logging.getLogger(__name__)

# 常量定义 - 提高代码可维护性
DEFAULT_MAX_DELAY_SECONDS: Final[float] = 300.0
MIN_DELAY_SECONDS: Final[float] = 0.0


class DelayStrategyError(Exception):
    """延迟策略相关异常的基类"""
    pass


class InvalidDelayParameterError(DelayStrategyError, ValueError):
    """延迟参数无效异常
    
    继承自ValueError以保持向后兼容性，同时提供更具体的异常类型
    """
    pass


class DelayStrategy(ABC):
    """
    延迟策略抽象基类
    
    定义了延迟计算的标准接口，所有具体的延迟策略都需要继承此类并实现其抽象方法。
    
    技术实现基于: @backtest_v2_delay_simulation_dev_plan_ai.md 第3.3节
    
    重构改进:
    - 更严格的类型提示
    - 自定义异常类型
    - 改进的文档字符串
    """
    
    # 类变量 - 用于策略注册
    _strategy_registry: ClassVar[Dict[str, type]] = {}
    
    def __init_subclass__(cls, **kwargs) -> None:
        """自动注册策略子类"""
        super().__init_subclass__(**kwargs)
        if hasattr(cls, 'STRATEGY_NAME'):
            DelayStrategy._strategy_registry[cls.STRATEGY_NAME] = cls
    
    @abstractmethod
    async def calculate_delay(self, timestamp: datetime) -> float:
        """
        计算延迟时间
        
        Args:
            timestamp: 信号触发时间戳，用于基于时间的延迟计算
            
        Returns:
            延迟秒数，必须为非负数
            
        Raises:
            InvalidDelayParameterError: 当输入参数无效时
            DelayStrategyError: 当延迟计算失败时
        """
        pass
    
    @abstractmethod
    def get_strategy_name(self) -> str:
        """
        获取策略名称
        
        Returns:
            策略名称标识符，用于配置和日志记录
        """
        pass
    
    def validate_delay_result(self, delay: float) -> float:
        """
        验证延迟计算结果的有效性
        
        Args:
            delay: 计算得到的延迟时间
            
        Returns:
            验证后的延迟时间
            
        Raises:
            InvalidDelayParameterError: 当延迟值无效时
        """
        if delay < MIN_DELAY_SECONDS:
            raise InvalidDelayParameterError(f"延迟时间不能为负数: {delay}")
        
        return delay
    
    @classmethod
    def get_available_strategies(cls) -> Dict[str, type]:
        """
        获取所有可用的延迟策略
        
        Returns:
            策略名称到策略类的映射
        """
        return cls._strategy_registry.copy()


class FixedDelayStrategy(DelayStrategy):
    """
    固定延迟策略
    
    总是返回预设的固定延迟时间，这是最简单的延迟模拟策略。
    适用于需要稳定、可预测延迟的回测场景。
    
    技术实现基于: @backtest_v2_delay_simulation_dev_plan_ai.md 第3.3节
    
    重构改进:
    - 添加参数验证缓存
    - 改进错误消息
    - 优化性能
    - 更好的类型提示
    """
    
    STRATEGY_NAME: ClassVar[str] = "fixed"
    
    def __init__(
        self, 
        delay_seconds: float, 
        max_delay_seconds: float = DEFAULT_MAX_DELAY_SECONDS
    ) -> None:
        """
        初始化固定延迟策略
        
        Args:
            delay_seconds: 固定延迟秒数，必须为非负数
            max_delay_seconds: 最大允许延迟秒数，用于安全限制
            
        Raises:
            InvalidDelayParameterError: 当延迟参数无效时
        """
        self._validate_parameters(delay_seconds, max_delay_seconds)
        
        self._delay_seconds = delay_seconds
        self._max_delay_seconds = max_delay_seconds
        
        logger.debug(
            f"FixedDelayStrategy initialized: delay={delay_seconds}s, max={max_delay_seconds}s"
        )
    
    @staticmethod
    def _validate_parameters(delay_seconds: float, max_delay_seconds: float) -> None:
        """
        验证初始化参数的有效性
        
        Args:
            delay_seconds: 固定延迟秒数
            max_delay_seconds: 最大延迟秒数
            
        Raises:
            InvalidDelayParameterError: 当参数无效时
        """
        if delay_seconds < MIN_DELAY_SECONDS:
            raise InvalidDelayParameterError(
                f"延迟时间不能为负数: {delay_seconds}。延迟时间必须 >= {MIN_DELAY_SECONDS}"
            )
        
        if max_delay_seconds < MIN_DELAY_SECONDS:
            raise InvalidDelayParameterError(
                f"最大延迟时间不能为负数: {max_delay_seconds}。最大延迟时间必须 >= {MIN_DELAY_SECONDS}"
            )
        
        if delay_seconds > max_delay_seconds:
            raise InvalidDelayParameterError(
                f"延迟时间 ({delay_seconds}) 不能超过最大延迟时间 ({max_delay_seconds})"
            )
    
    @property
    def delay_seconds(self) -> float:
        """获取延迟秒数"""
        return self._delay_seconds
    
    @property
    def max_delay_seconds(self) -> float:
        """获取最大延迟秒数"""
        return self._max_delay_seconds
    
    async def calculate_delay(self, timestamp: datetime) -> float:
        """
        计算固定延迟
        
        对于固定延迟策略，无论输入时间戳是什么，都返回相同的延迟值。
        此方法被设计为异步以保持接口一致性，但实际计算是同步的。
        
        Args:
            timestamp: 信号触发时间戳（此策略中不使用，但保持接口一致性）
            
        Returns:
            固定的延迟秒数
            
        Note:
            为了性能考虑，此方法直接返回预存的延迟值，避免重复验证
        """
        return self._delay_seconds
    
    def get_strategy_name(self) -> str:
        """
        获取策略名称
        
        Returns:
            返回 "fixed" 作为策略标识符
        """
        return self.STRATEGY_NAME
    
    def __repr__(self) -> str:
        """策略的字符串表示，用于调试和日志"""
        return (
            f"FixedDelayStrategy("
            f"delay={self._delay_seconds}s, "
            f"max={self._max_delay_seconds}s"
            f")"
        )
    
    def __eq__(self, other: object) -> bool:
        """比较两个策略是否相等"""
        if not isinstance(other, FixedDelayStrategy):
            return NotImplemented
        return (
            self._delay_seconds == other._delay_seconds and
            self._max_delay_seconds == other._max_delay_seconds
        )
    
    def __hash__(self) -> int:
        """计算策略的哈希值，支持缓存等用途"""
        return hash((self._delay_seconds, self._max_delay_seconds)) 