"""
交易延迟记录数据模型

该模型用于存储交易延迟计算结果，包括KOL交易时间与实际交易执行时间的差值。
支持信号抑制检查功能，记录详细的延迟计算和分析元数据。

关联技术方案: @trading_delay_monitor_dev_plan_ai.md
关联需求: @trading_delay_monitor_requirements_ai.md
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from enum import Enum
from beanie import Document, PydanticObjectId, Indexed
from pydantic import Field, ConfigDict
from pymongo import IndexModel

class DelayStatus(str, Enum):
    """延迟状态枚举
    
    用于标识延迟计算的结果状态，区分正常计算、异常情况和信号抑制等。
    
    关联需求:
    - @FR006: 延迟计算状态跟踪
    - @FR009: 信号抑制状态记录
    """
    CALCULATED = "calculated"                   # 正常计算完成
    SIGNAL_MISSING = "signal_missing"           # 信号记录缺失
    KOL_ACTIVITY_MISSING = "kol_activity_missing"  # KOL活动记录缺失
    TIMESTAMP_ANOMALY = "timestamp_anomaly"     # 时间戳异常（如负延迟）
    SIGNAL_SUPPRESSED = "signal_suppressed"     # 信号被抑制（间隔时间过短）
    EXCESSIVE_DELAY = "excessive_delay"         # 异常长延迟（超过合理范围）


class TradingDelayRecord(Document):
    """交易延迟记录模型
    
    记录每笔交易的延迟计算结果，包括：
    - 交易基本信息（记录ID、信号ID、策略等）
    - 时间戳信息（KOL最后交易时间、实际交易执行时间等）
    - 延迟计算结果（延迟秒数、状态等）
    - 信号抑制检查结果和相关元数据
    
    关联需求:
    - @FR004: 延迟计算数据存储
    - @FR005: 延迟数据精度要求
    - @FR007: 数据模型字段定义
    - @FR008: 数据索引和查询性能
    - @FR009: 信号抑制功能集成
    """
    
    # === 核心关联字段 ===
    trade_record_id: PydanticObjectId = Field(
        ..., 
        description="关联的交易记录ID，建立与主交易记录的映射关系",
        json_schema_extra={"unique": True}
    )
    
    signal_id: Optional[PydanticObjectId] = Field(
        None, 
        description="触发交易的信号ID，用于追溯信号来源"
    )
    
    # === 交易基本信息 ===
    strategy_name: Optional[str] = Field(
        None, 
        max_length=100,
        description="交易策略名称，用于延迟分析的策略分组"
    )
    
    token_address: Optional[str] = Field(
        None, 
        max_length=200,
        description="代币合约地址，支持多链代币标识",
        json_schema_extra={"index": True}
    )
    
    trade_type: Optional[str] = Field(
        None, 
        pattern=r"^(buy|sell)$",
        description="交易类型：buy(买入) 或 sell(卖出)"
    )
    
    trade_amount: Optional[float] = Field(
        None, 
        gt=0,
        description="交易金额，用于延迟影响分析"
    )
    
    # === KOL信息 ===
    hit_kol_wallets: List[str] = Field(
        default_factory=list, 
        max_items=50,
        description="触发信号的KOL钱包地址列表，支持多KOL触发分析"
    )
    
    # === 时间戳信息 ===
    kol_last_trade_timestamp: Optional[datetime] = Field(
        None, 
        description="相关KOL的最后交易时间戳，延迟计算的起始时间点"
    )
    
    signal_trigger_timestamp: Optional[datetime] = Field(
        None, 
        description="信号触发时间戳，用于信号处理延迟分析"
    )
    
    trade_execution_timestamp: datetime = Field(
        ..., 
        description="实际交易执行时间戳，延迟计算的结束时间点",
        json_schema_extra={"index": True}
    )
    
    # === 延迟计算结果 ===
    delay_seconds: Optional[float] = Field(
        None, 
        description="计算得出的延迟秒数，保留足够精度进行分析",
        json_schema_extra={"index": True}
    )
    
    delay_status: DelayStatus = Field(
        ..., 
        description="延迟计算状态，标识计算结果的类型和可靠性",
        json_schema_extra={"index": True}
    )
    
    # === 信号抑制相关字段 ===
    suppression_check_result: Optional[str] = Field(
        None, 
        max_length=50,
        description="信号抑制检查结果：suppressed(被抑制) 或 allowed(允许通过)"
    )
    
    # === 计算元数据 ===
    calculation_metadata: Optional[Dict[str, Any]] = Field(
        None, 
        description="延迟计算和抑制检查的详细元数据，包含调试和审计信息"
    )
    
    # === 自动管理字段 ===
    created_at: datetime = Field(
        default_factory=datetime.utcnow, 
        description="记录创建时间"
    )
    
    updated_at: datetime = Field(
        default_factory=datetime.utcnow, 
        description="记录最后更新时间"
    )
    
    # === Beanie配置 ===
    model_config = ConfigDict(
        collection_name="trading_delay_records",
        use_enum_values=True,
        str_strip_whitespace=True,
        validate_assignment=True
    )
    
    class Settings:
        """Beanie设置"""
        name = "trading_delay_records"
        
        # 数据库索引定义
        indexes = [
            # 核心查询索引 - 唯一索引
            IndexModel([("trade_record_id", 1)], unique=True),  # 唯一索引，防止重复处理
            [("strategy_name", 1), ("delay_status", 1)],  # 策略分析索引
            [("token_address", 1), ("trade_execution_timestamp", -1)],  # 代币时序分析索引  
            [("delay_status", 1), ("created_at", -1)],  # 状态时序查询索引
            [("trade_execution_timestamp", -1)],  # 时间序列分析索引
            [("delay_seconds", 1)],  # 延迟范围查询索引
            
            # 信号抑制分析索引
            [("suppression_check_result", 1), ("strategy_name", 1)],  # 抑制效果分析索引
        ]
    
    def update_timestamp(self):
        """更新记录的最后修改时间"""
        self.updated_at = datetime.utcnow()
    
    def format_delay_display(self, precision: int = 2) -> Optional[str]:
        """格式化延迟时间用于显示
        
        Args:
            precision: 小数点精度，默认2位
            
        Returns:
            格式化后的延迟时间字符串，如 "30.50s"
        """
        if self.delay_seconds is None:
            return None
        return f"{self.delay_seconds:.{precision}f}s"
    
    def is_suppressed(self) -> bool:
        """检查此记录是否为信号被抑制的情况"""
        return self.delay_status == DelayStatus.SIGNAL_SUPPRESSED
    
    def get_calculation_summary(self) -> Dict[str, Any]:
        """获取计算结果摘要信息"""
        return {
            "delay_seconds": self.delay_seconds,
            "delay_status": self.delay_status.value,
            "suppression_result": self.suppression_check_result,
            "kol_wallets_count": len(self.hit_kol_wallets),
            "has_metadata": self.calculation_metadata is not None
        } 