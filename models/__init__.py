from motor.motor_asyncio import AsyncIOMotorClient
from beanie import init_beanie
from dotenv import load_dotenv
import os

from models.config import Config, ApplicationConfig, KolActivityMonitorConfig
from models.trade_execution import TradeExecutionResult, ChannelAttemptResult, TradeStatus
from models.channel_attempt import ChannelAttemptRecord
from models.slippage_retry import SlippageAdjustmentRecord, RetryDecision, SlippageRetryConfig
from models.dynamic_retry_config import DynamicRetryConfig, MarketConditionRetryConfig
from models.gmgn_blue_chip_holders import GmgnBlueChipHolders
from models.gmgn_bot_degen_holdings import GmgnBotDegenHoldings
from models.gmgn_bot_degen_traders import GmgnBotDegenTraders
from models.gmgn_dev_activity import GmgnDevActivity
from models.gmgn_developer_holders import GmgnDeveloperHolders
from models.gmgn_developer_traders import GmgnDeveloperTraders
from models.gmgn_following_kol import GmgnFollowingKol
from models.gmgn_fresh_activity import GmgnFreshActivity
from models.gmgn_fresh_wallet_holders import GmgnFreshWalletHolders
from models.gmgn_fresh_wallets import GmgnFreshWallets
from models.gmgn_gas_price import GmgnGasPrice
from models.gmgn_kol_activity import GmgnKolActivity
from models.gmgn_kol_holdings import GmgnKolHoldings
from models.gmgn_kol_traders import GmgnKolTraders
from models.gmgn_phishing_holdings import GmgnPhishingHoldings
from models.gmgn_phishing_wallets import GmgnPhishingWallets
from models.gmgn_rat_trader_activity import GmgnRatTraderActivity
from models.gmgn_rat_traders import GmgnRatTraders
from models.gmgn_smart_activity import GmgnSmartActivity
from models.gmgn_sniper_activity import GmgnSniperActivity
from models.gmgn_token_buyers import GmgnTokenBuyers
from models.gmgn_token_links import GmgnTokenLinks
from models.gmgn_token_stats import GmgnTokenStats
from models.gmgn_token_window import GmgnTokenWindow
from models.gmgn_top_holders import GmgnTopHolders
from models.gmgn_top_holders_activity import GmgnTopHoldersActivity
from models.gmgn_top_traders import GmgnTopTraders
from models.gmgn_whale_activity import GmgnWhaleActivity
from models.smart_money_holdings import SmartMoneyHoldings
from models.kol_wallet import KOLWallet
from models.kol_wallet_activity import KOLWalletActivity, TokenInfo, QuoteToken
from models.solana_monitor_address import SolanaMonitorAddress
from models.telegram_users import TelegramUser
from models.token_message_send_history import TokenMessageSendHistory
from models.tweet_monitor_user import TweetMonitorUser
from models.signal import Signal
from models.gmgn_token_trades import GmgnTokenTrade
from models.token_trade_fetch_status import TokenTradeFetchStatus
from models.trade_record import TradeRecord
from models.kol_activity_filter import KolActivityFilter
from models.monitor_alert_state import MonitorAlertState
from models.alert_event_record import AlertEventRecord
from models.notification_log_record import NotificationLogRecord

# 新增：KOL 打分功能相关模型
from models.kol_strategy_score import KOLStrategyScore
from models.trade_score_log import TradeScoreLog

# GMGN KOL钱包统计数据模型
from models.gmgn_wallet_stats import GmgnWalletStats, GmgnRiskMetrics

# 交易延迟监控模型
from models.trading_delay_record import TradingDelayRecord, DelayStatus

# 加载环境变量
load_dotenv()

# 获取数据库 URL
DATABASE_HOST = os.getenv('MONGODB_HOST', '')
DATABASE_NAME = os.getenv('MONGODB_DB', '')
DATABASE_AUTH_SOURCE = os.getenv('MONGODB_AUTH_SOURCE', '')
DATABASE_USERNAME = os.getenv('MONGODB_USERNAME', '')
DATABASE_PASSWORD = os.getenv('MONGODB_PASSWORD', '')

async def init_db():
    """初始化数据库连接"""
    # 创建数据库客户端
    # connection_string = f"mongodb://{DATABASE_USERNAME}:{DATABASE_PASSWORD}@{DATABASE_HOST.replace('mongodb://', '')}/{DATABASE_NAME}?authSource={DATABASE_AUTH_SOURCE}"
    client = AsyncIOMotorClient(host=DATABASE_HOST, port=27017, authSource=DATABASE_AUTH_SOURCE, username=DATABASE_USERNAME, password=DATABASE_PASSWORD)
    
    # 初始化 Beanie
    from .x_user import XUser
    from .tweet import Tweet
    from .solana_transaction import SolanaTransaction
    from .token import Token
    from .error_log import ErrorLog
    from .smart_money_wallet import SmartMoneyWallet
    from .gmgn_token import (
        GmgnToken1m,
        GmgnToken5m,
        GmgnToken1h,
        GmgnToken6h,
        GmgnToken24h
    )
    await init_beanie(
        database=client[DATABASE_NAME],
        document_models=[
            Config,
            TelegramUser,
            TokenMessageSendHistory,
            Signal,
            XUser,
            Tweet,
            TweetMonitorUser,
            SolanaTransaction,
            SolanaMonitorAddress,
            Token,
            ErrorLog,
            SmartMoneyWallet,
            KOLWallet,
            KOLWalletActivity,
            GmgnFollowingKol,
            GmgnToken1m,
            GmgnToken5m,
            GmgnToken1h,
            GmgnToken6h,
            GmgnToken24h,
            GmgnTokenLinks,
            GmgnTokenBuyers,
            GmgnTokenStats,
            GmgnTokenWindow,
            GmgnGasPrice,
            SmartMoneyHoldings,
            GmgnTopTraders,
            GmgnKolTraders,
            GmgnFreshWallets,
            GmgnDeveloperTraders,
            GmgnRatTraders,
            GmgnPhishingWallets,
            GmgnBotDegenTraders,
            GmgnTopHolders,
            GmgnKolHoldings,
            GmgnBlueChipHolders,
            GmgnFreshWalletHolders,
            GmgnDeveloperHolders,
            GmgnDevActivity,
            GmgnKolActivity,
            GmgnSmartActivity,
            GmgnWhaleActivity,
            GmgnFreshActivity,
            GmgnSniperActivity,
            GmgnTopHoldersActivity,
            GmgnRatTraderActivity,
            GmgnBotDegenHoldings,
            GmgnPhishingHoldings,
            GmgnTokenTrade,
            TokenTradeFetchStatus,
            TradeRecord,
            KolActivityFilter,
            MonitorAlertState,
            AlertEventRecord,
            NotificationLogRecord,
            ChannelAttemptRecord,
            SlippageAdjustmentRecord,
            RetryDecision,
            # 注意：DynamicRetryConfig 和 MarketConditionRetryConfig 是接口模型，不需要注册为文档
            KOLStrategyScore,
            TradeScoreLog,
            # GMGN KOL钱包统计数据模型
            GmgnWalletStats,
            # 交易延迟监控模型
            TradingDelayRecord
        ],
        skip_indexes=True
    ) 