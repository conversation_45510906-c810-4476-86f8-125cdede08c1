# Bug修复文档：Async Decorators Task创建误用

## Bug标识
- **Bug描述**: `retry_with_strategy` 装饰器在处理同步函数时，错误地使用 `asyncio.create_task()` 创建task后传递给 `asyncio.run_coroutine_threadsafe()`
- **发现日期**: 2025-06-13T00:47:18+08:00
- **报告来源**: Cursor代码审查

## 根源分析概要

### 问题所在
在 `utils/retry/decorators.py` 第75-88行，`retry_with_strategy` 装饰器的同步函数包装器中：

```python
# 错误的实现
task = asyncio.create_task(
    manager.execute_with_retry(...)
)
return asyncio.run_coroutine_threadsafe(task, loop).result()
```

### 根本原因
1. **错误的API使用**: `asyncio.run_coroutine_threadsafe()` 是用于从不同线程提交coroutine的，不是用于执行已经在当前事件循环中调度的task
2. **不必要的task创建**: 先用 `asyncio.create_task()` 创建task，然后传递给 `run_coroutine_threadsafe()` 是错误的用法
3. **潜在风险**: 这种误用可能导致 `TypeError`、死锁或其他异步行为异常

## 详细的修复方案

### 修复前的代码
```python
if loop.is_running():
    # 错误：先创建task再传递给run_coroutine_threadsafe
    task = asyncio.create_task(
        manager.execute_with_retry(
            func, *args,
            retry_on=retry_on,
            stop_on=stop_on,
            **kwargs
        )
    )
    return asyncio.run_coroutine_threadsafe(task, loop).result()
```

### 修复后的代码
```python
if loop.is_running():
    # 正确：直接传递coroutine给run_coroutine_threadsafe
    return asyncio.run_coroutine_threadsafe(
        manager.execute_with_retry(
            func, *args,
            retry_on=retry_on,
            stop_on=stop_on,
            **kwargs
        ), loop
    ).result()
```

### 核心修改点
1. **移除不必要的task创建**: 删除 `asyncio.create_task()` 调用
2. **直接传递coroutine**: 将coroutine直接传递给 `asyncio.run_coroutine_threadsafe()`
3. **保持API一致性**: 修复后API行为完全一致，不影响现有代码

## 测试用例验证

修复后运行了完整的重试模块测试套件：
- **测试文件**: `test/utils/retry/`
- **测试结果**: 20个测试全部通过
- **验证范围**: 包括装饰器使用、重试策略、断路器集成等

## 影响范围

### 受影响的组件
- `utils/retry/decorators.py` 中的 `retry_with_strategy` 装饰器
- 所有使用该装饰器修饰同步函数的代码

### 不受影响的组件
- `async_retry_with_strategy` 装饰器（仅支持异步函数）
- `circuit_breaker_only` 装饰器（使用不同的事件循环处理方式）
- 异步函数的重试处理

## 方案提出者/执行者
- **AI助手**: Claude (Sonnet 4)

## 方案审阅者/批准者
- **用户**: gaojunbin

## 方案批准日期
- **批准日期**: 2025-06-13

## 预期的验证方法
1. **单元测试验证**: 运行重试模块的完整测试套件
2. **集成测试**: 验证使用装饰器的现有爬虫代码正常工作
3. **性能测试**: 确认修复没有引入性能回归

## 关联链接 (Related Links)
- **修复文件**: @utils/retry/decorators.py
- **相关测试**: @test/utils/retry/
- **重构方案**: @docs/features/0.1.0/spiders/REFACTOR_PLAN_BasicSpider_RetryStrategyCircuitBreaker_20250612.md 