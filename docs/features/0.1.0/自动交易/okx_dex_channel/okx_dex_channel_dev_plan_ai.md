# 技术实现方案 (Development Plan)

## 1. 概述
本方案旨在为 `AutoTradeManager` 集成 OKX DEX API 作为新的 Solana 交易通道。核心工作是创建一个新的交易服务 `OkxTradeService`，并更新系统配置以支持该通道。`AutoTradeManager` 的核心逻辑保持不变。

## 2. 计划修改的文件

1.  **`utils/trading/okx/okx_trade_service.py` (新建)**:
    -   实现 OKX DEX 交易的核心逻辑。
    -   此类将继承 `TradeInterface` 并实现其所有方法。

2.  **`models/config.py` (修改)**:
    -   在 `AutoTradeConfig` 模型中增加 `"okx"` 作为可选的交易通道。
    -   为 `"okx"` 通道定义所需的 `channel_params`。

3.  **`utils/trading/__init__.py` (修改)**:
    -   导入新的 `OkxTradeService`。
    -   在 `get_trade_service` 函数中注册新的 "okx" 通道，使其能够根据配置被 `AutoTradeManager` 正确初始化。

4.  **`test/utils/trading/okx/test_okx_trade_service.py` (新建)**:
    -   为 `OkxTradeService` 编写单元测试。

## 3. 关键逻辑变更点

### 3.1. `OkxTradeService` 实现

-   **`__init__(self, config)`**:
    -   接收 `AutoTradeConfig` 对象作为配置。
    -   从环境变量中加载 OKX API Key, Secret, 和 Passphrase，环境变量的名称由 `config.channel_params` 指定。
    -   初始化一个 `httpx.AsyncClient` 用于后续的 API 请求。
    -   存储 API host (`config.channel_params['api_host']`)。

-   **`_generate_auth_headers(self, method, request_path, body)`**:
    -   这是一个私有辅助方法，用于生成 OKX API 所需的认证头。
    -   获取当前 UTC 时间戳。
    -   根据 `timestamp + method + request_path + body` 格式创建待签名的字符串。
    -   使用 HMAC-SHA256 和 API Secret 对该字符串进行签名，并进行 Base64 编码。
    -   返回包含 `OK-ACCESS-KEY`, `OK-ACCESS-SIGN`, `OK-ACCESS-TIMESTAMP`, `OK-ACCESS-PASSPHRASE` 的字典。

-   **`async get_quote(self, from_token, to_token, from_amount, slippage)`**:
    -   此方法用于获取报价，但 OKX 的 `swap-instruction` 接口直接返回交易指令，本身已包含报价信息。
    -   因此，此方法将直接调用 `swap-instruction` 接口，但只返回预期的输出金额和价格影响等信息，而不会执行交易。这可以模拟获取报价的行为。

-   **`async trade(self, from_token, to_token, from_amount_wei, slippage, priority_fee, force_legacy)`**:
    -   **构造请求**:
        -   `method`: "GET"
        -   `request_path`: "/api/v5/dex/aggregator/swap-instruction"
        -   `params`: 构建一个包含 `tokenInAddr`, `tokenOutAddr`, `amountIn`, `sl`, `walletAddr` 等参数的字典。
    -   **发送请求**:
        -   调用 `_generate_auth_headers` 生成认证头。
        -   使用 `httpx.AsyncClient` 发送 GET 请求到 `api_host + request_path`。
        -   处理潜在的 `httpx` 异常和 OKX API 返回的错误码。
    -   **处理响应**:
        -   检查响应状态码和 `code` 字段是否为 "0" (成功)。
        -   从响应的 `data` 字段中解析出 `instructions` 和 `addressLookupTableAddresses`。
    -   **构建交易**:
        -   这是一个核心步骤，逻辑将**高度参考**现有的 `SolanaDirectTradeService` 中的交易构建部分。
        -   反序列化 `instructions` 和 `addressLookupTableAddresses`。
        -   加载地址查找表 (Address Lookup Tables)。
        -   使用最新的区块哈希 (`recent_blockhash`) 创建一个 `MessageV0`。
        -   将 `MessageV0` 和 `address_lookup_tables` 组合成一个 `VersionedTransaction`。
    -   **签名与广播**:
        -   使用从配置中获取的 `Keypair` 对 `VersionedTransaction` 进行签名。
        -   使用 `solana.rpc.async_api.AsyncClient` 的 `send_transaction` 方法将交易发送到 Solana 网络。
        -   等待交易确认。
    -   **返回结果**:
        -   返回交易签名 (tx_sig) 作为成功的凭证。

### 3.2. `models/config.py` 修改
-   在 `AutoTradeConfig` 类的 `channel` 字段中，将 `Literal` 的范围扩大，加入 `"okx"`。
-   `channel_params` 将是一个 `Dict`，其结构会根据 `channel` 的值而变化。我们将添加对 "okx" key 的支持，其值为一个包含 `api_host`, `api_key_env` 等字段的 Pydantic model。

### 3.3. `utils/trading/__init__.py` 修改
-   在文件顶部，从 `utils.trading.okx.okx_trade_service` 导入 `OkxTradeService`。
-   在 `get_trade_service` 函数中，增加一个 `elif` 条件判断：
    ```python
    # ... (existing code for 'jupiter' and 'gmgn')
    elif channel_name == "okx":
        return OkxTradeService(config)
    ```

## 4. 数据结构
-   `OkxTradeService` 将主要与 `VersionedTransaction` (from `solders.transaction`) 和 `Keypair` (from `solders.keypair`) 对象打交道。
-   与 OKX API 的交互将使用标准的 JSON (字典) 格式。

## 5. 边界条件与风险
-   **API 速率限制**: 需要注意 OKX API 的调用频率限制，尽管对于单个交易来说通常不是问题。
-   **私钥安全**: 私钥的管理至关重要，必须确保它只在内存中处理，并且永不暴露。
-   **交易失败**: `trade` 方法必须能优雅地处理交易在链上失败的情况（例如，由于滑点过高或网络拥堵），并向上层报告失败。
-   **依赖相似性**: 实现将大量借鉴 `solana_direct_trade_service.py` 的成功实践，这降低了引入全新逻辑的风险。

---
**关联链接 (Related Links):**
- **关联需求 (Related Requirement):** [OKX DEX详细需求规格](mdc:okx_dex_channel_requirements_ai.md)
- **测试用例 (Test Cases):** [OKX DEX测试用例设计](mdc:okx_dex_channel_test_cases_ai.md)
- **跟踪任务 (Tracked by):** [OKX DEX开发任务列表](mdc:okx_dex_channel_todo_list.md)
- **部署指南 (Deployment Guide):** [OKX DEX生产环境部署指南](mdc:production_deployment_guide.md) 