# 测试用例设计 (Test Case Design)

## 1. 概述
本测试计划旨在确保新集成的 `OkxTradeService` 功能正确、稳定，并且能够与 `AutoTradeManager` 无缝协作。测试将主要集中在单元测试层面，通过模拟外部依赖（如 OKX API 和 Solana RPC）来验证服务内部逻辑的正确性。

## 2. 单元测试 (`test/utils/trading/okx/test_okx_trade_service.py`)

### 2.1. `test_initialization`
-   **目的**: 验证 `OkxTradeService` 是否能被正确初始化。
-   **步骤**:
    1.  创建一个 `mock` 的 `AutoTradeConfig` 对象，其中 `channel` 为 "okx"，并提供完整的 `channel_params`。
    2.  设置相应的环境变量 (`MOCK_OKX_API_KEY`, `MOCK_OKX_SECRET`, `MOCK_OKX_PASSPHRASE`)。
    3.  实例化 `OkxTradeService(mock_config)`。
-   **预期结果**:
    -   对象成功创建，没有抛出异常。
    -   对象的 `api_key`, `api_secret`, `passphrase`, `api_host` 属性与预期值相符。

### 2.2. `test_generate_auth_headers`
-   **目的**: 验证 OKX API 认证头的生成逻辑是否正确。
-   **步骤**:
    1.  初始化一个 `OkxTradeService` 实例。
    2.  调用私有方法 `_generate_auth_headers("GET", "/api/v5/dex/aggregator/swap-instruction", "")`。
-   **预期结果**:
    -   返回一个字典。
    -   字典中包含 `OK-ACCESS-KEY`, `OK-ACCESS-SIGN`, `OK-ACCESS-TIMESTAMP`, `OK-ACCESS-PASSPHRASE` 四个键。
    -   `OK-ACCESS-SIGN` 的值是基于预期的签名字符串和密钥通过 HMAC-SHA256 计算得出的正确 Base64 编码字符串。
    -   `OK-ACCESS-TIMESTAMP` 是一个有效的 UTC 时间戳字符串。

### 2.3. `test_trade_success`
-   **目的**: 模拟一次成功的交易流程，验证从 API 请求到交易广播的完整逻辑。
-   **依赖模拟 (Mocks)**:
    -   `httpx.AsyncClient.get`: 模拟 OKX API 的成功响应。返回一个精心构造的 JSON，包含 `code: "0"` 以及 `instructions` 和 `addressLookupTableAddresses` 字段的示例数据。
    -   `solana.rpc.async_api.AsyncClient.get_latest_blockhash`: 模拟获取最新区块哈希。
    -   `solana.rpc.async_api.AsyncClient.get_multiple_accounts`: 模拟加载地址查找表。
    -   `solana.rpc.async_api.AsyncClient.send_transaction`: 模拟发送交易，并返回一个模拟的交易签名。
-   **步骤**:
    1.  初始化 `OkxTradeService`。
    2.  调用 `trade(...)` 方法，传入所有必要的参数。
-   **预期结果**:
    -   `httpx.AsyncClient.get` 被以正确的 URL 和认证头调用。
    -   所有 Solana RPC 的 mock 方法都被正确调用。
    -   `send_transaction` 被一个正确签名后的 `VersionedTransaction` 对象调用。
    -   方法最终返回模拟的交易签名，没有抛出异常。

### 2.4. `test_trade_api_error`
-   **目的**: 验证当 OKX API 返回错误时，服务能正确处理并抛出异常。
-   **依赖模拟 (Mocks)**:
    -   `httpx.AsyncClient.get`: 模拟 OKX API 的失败响应。返回一个包含错误码的 JSON，例如 `code: "51001"`。
-   **步骤**:
    1.  初始化 `OkxTradeService`。
    2.  调用 `trade(...)` 方法。
-   **预期结果**:
    -   捕获到一个自定义的异常 (例如 `OkxApiException`)。
    -   异常消息应包含从 API 响应中提取的错误码和信息。

### 2.5. `test_trade_solana_rpc_error`
-   **目的**: 验证当 Solana RPC 调用失败时，服务能正确处理并向上抛出异常。
-   **依赖模拟 (Mocks)**:
    -   `httpx.AsyncClient.get`: 模拟 OKX API 的成功响应。
    -   `solana.rpc.async_api.AsyncClient.send_transaction`: 模拟交易上链失败，例如抛出一个 `Exception`。
-   **步骤**:
    1.  初始化 `OkxTradeService`。
    2.  调用 `trade(...)` 方法。
-   **预期结果**:
    -   程序捕获到从 `send_transaction` 抛出的原始异常或一个包装后的自定义异常。

### 2.6. `test_get_trade_service_for_okx_channel` (in `test/utils/trading/test_init.py`)
-   **目的**: 验证 `get_trade_service` 工厂函数是否能为 "okx" 通道正确返回 `OkxTradeService` 实例。
-   **依赖模拟 (Mocks)**:
    -   `OkxTradeService` 类本身，用于检查返回实例的类型。
-   **步骤**:
    1.  从 `utils.trading` 导入 `get_trade_service` 和 `OkxTradeService`。
    2.  创建一个 `mock` 的 `AutoTradeConfig` 对象，`channel` 为 "okx"，并提供初始化所需的 `channel_params`。
    3.  设置 `OkxTradeService` 初始化所需的环境变量。
    4.  调用 `get_trade_service("okx", mock_config)`。
-   **预期结果**:
    -   返回的对象是 `OkxTradeService` 的一个实例。
    -   调用没有抛出 `ValueError`。

## 3. 集成测试 (手动)
由于该功能高度依赖外部服务和私钥，自动化集成测试较为复杂。在开发完成后，将进行一次手动集成测试。

-   **目的**: 验证整个流程在真实环境（或接近真实的主网-fork环境）中的可行性。
-   **步骤**:
    1.  在 `.env` 文件中配置真实的 OKX API 凭证和一个测试用的 Solana 私钥。
    2.  在数据库中创建一个 `AutoTradeConfig` 文档，`channel` 设置为 "okx"，并指向测试钱包。
    3.  编写一个简单的脚本，加载此配置，实例化 `AutoTradeManager`，并调用其 `trade` 方法执行一笔小额交易（例如，SOL 换 USDC）。
-   **预期结果**:
    -   脚本执行成功。
    -   在 Solana 区块链浏览器 (如 Solscan) 上可以查询到该笔交易，且交易细节与预期相符。
    -   相关日志被正确记录。

---
**关联链接 (Related Links):**
- **关联需求 (Related Requirement):** [OKX DEX详细需求规格](mdc:okx_dex_channel_requirements_ai.md)
- **实现方案 (Development Plan):** [OKX DEX技术实现方案](mdc:okx_dex_channel_dev_plan_ai.md)
- **跟踪任务 (Tracked by):** [OKX DEX开发任务列表](mdc:okx_dex_channel_todo_list.md)
- **测试代码文件 (Test Files):** 
  - `test/utils/trading/solana/test_okx_trade_service.py` - OKX交易服务单元测试
  - `test/utils/trading/test_auto_trade_manager_channel_creation.py` - 渠道创建测试 