# OKX DEX 生产环境部署指南

## 📋 概述

本文档提供OKX DEX交易渠道在生产环境中的完整部署配置方案，包括环境变量配置、数据库配置、监控设置等。

## 🔧 1. 环境变量配置

### 1.1 创建生产环境配置文件

创建 `.env.production` 文件：

```bash
# ===========================================
# OKX DEX 生产环境配置
# ===========================================

# OKX API 认证信息
OKX_API_KEY=your_production_okx_api_key_here
OKX_API_SECRET=your_production_okx_secret_here
OKX_API_PASSPHRASE=your_production_okx_passphrase_here

# OKX API Host (生产环境)
OKX_API_HOST=https://www.okx.com

# ===========================================
# Solana RPC 配置 (生产环境建议使用付费RPC)
# ===========================================

# 主RPC端点 (建议使用Helius、QuickNode等付费服务)
SOLANA_RPC_URL=https://rpc.helius.xyz/?api-key=your-api-key
# 备用RPC端点
SOLANA_RPC_URL_BACKUP=https://api.mainnet-beta.solana.com
# WebSocket端点 (如需要)
SOLANA_WS_URL=wss://rpc.helius.xyz/?api-key=your-api-key

# ===========================================
# 代理配置 (生产环境可选)
# ===========================================

# 是否启用代理 (true/false)
USE_PROXY=false
# 代理类型 (free/paid)
PROXY_TYPE=paid

# ===========================================
# 钱包配置
# ===========================================

# 默认交易钱包私钥 (Base58格式，64字节完整私钥)
DEFAULT_WALLET_PRIVATE_KEY=your_production_wallet_private_key_here
# 默认钱包地址
DEFAULT_WALLET_ADDRESS=your_production_wallet_address_here

# ===========================================
# MongoDB 配置
# ===========================================

# 生产数据库连接字符串
MONGODB_URL=*******************************************************************
MONGODB_DATABASE=memeMonitor

# ===========================================
# 通知配置
# ===========================================

# Telegram Bot Token (用于交易通知)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
# 管理员Chat ID列表 (逗号分隔)
ADMIN_CHAT_IDS=chat_id_1,chat_id_2

# ===========================================
# 日志配置
# ===========================================

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO
# 日志文件路径
LOG_FILE_PATH=/var/log/memeMonitor/auto_trade.log

# ===========================================
# 性能和监控配置
# ===========================================

# API调用频率限制 (每秒最大请求数)
MAX_API_REQUESTS_PER_SECOND=10
# 缓存TTL (秒)
API_CACHE_TTL=10
# 健康检查间隔 (秒)
HEALTH_CHECK_INTERVAL=30
```

### 1.2 安全注意事项

🔐 **重要安全提醒**：

1. **私钥管理**：生产环境私钥应通过安全的密钥管理系统管理
2. **API凭证**：OKX API凭证应设置适当的权限和IP白名单
3. **环境隔离**：确保生产和测试环境完全隔离
4. **备份策略**：建立完善的私钥和配置备份策略

## 🗄️ 2. 数据库配置

### 2.1 创建AutoTradeManager配置文档

在MongoDB中创建以下配置文档：

```javascript
// 使用MongoDB Shell或客户端工具执行
db.config.insertOne({
  "type": "auto_trade_manager",
  "version": 1,
  "description": "OKX DEX生产环境自动交易配置",
  "created_at": new Date(),
  "updated_at": new Date(),
  "data": {
    "auto_trade": {
      "enabled": true,
      "wallet_config": {
        "default_private_key_env_var": "DEFAULT_WALLET_PRIVATE_KEY",
        "default_wallet_address": "your_production_wallet_address_here"
      },
      "channels": [
        {
          "channel_type": "okx",
          "priority": 1,
          "enabled": true,
          "timeout_seconds": 60,
          "max_retries": 5,
          "trading_params": {
            "default_buy_amount_sol": 0.01,
            "default_buy_slippage_percentage": 1.0,
            "default_buy_priority_fee_sol": 0.0001,
            "default_sell_slippage_percentage": 1.5,
            "default_sell_priority_fee_sol": 0.0001,
            "enable_slippage_retry": true,
            "slippage_increment_percentage": 0.5,
            "max_slippage_percentage": 5.0,
            "retry_delay_seconds": 1.0,
            "retry_delay_strategy": "exponential",
            "max_retry_delay_seconds": 10.0
          },
          "channel_params": {
            "api_host": "https://www.okx.com",
            "api_key_env": "OKX_API_KEY",
            "api_secret_env": "OKX_API_SECRET",
            "api_passphrase_env": "OKX_API_PASSPHRASE",
            "rpc_endpoint": null,  // 使用环境变量 SOLANA_RPC_URL
            "use_proxy": false,
            "proxy_type": "paid",
            "max_retries": 3
          }
        },
        {
          "channel_type": "gmgn",
          "priority": 2,
          "enabled": true,
          "timeout_seconds": 45,
          "max_retries": 3,
          "trading_params": {
            "default_buy_amount_sol": 0.01,
            "default_buy_slippage_percentage": 2.0,
            "default_sell_slippage_percentage": 2.5,
            "enable_slippage_retry": true,
            "max_slippage_percentage": 8.0
          },
          "channel_params": {
            "api_host": "https://gmgn.ai"
          }
        }
      ],
      "default_timeout": 120,
      "max_total_retries": 10,
      "notification_config": {
        "notify_on_failure": true,
        "notify_on_fallback": true,
        "admin_chat_ids": ["your_admin_chat_id_1", "your_admin_chat_id_2"],
        "include_trade_details": true
      }
    }
  }
});
```

### 2.2 索引优化

为了提高查询性能，建议创建以下索引：

```javascript
// 交易记录索引
db.trade_records.createIndex({ "signal_id": 1, "created_at": -1 });
db.trade_records.createIndex({ "status": 1, "created_at": -1 });
db.trade_records.createIndex({ "token_address": 1, "created_at": -1 });

// 配置索引
db.config.createIndex({ "type": 1, "version": -1 });

// 信号索引 (如果使用)
db.signals.createIndex({ "strategy_name": 1, "created_at": -1 });
db.signals.createIndex({ "processed": 1, "created_at": -1 });
```

## 🚀 3. 部署脚本

### 3.1 生产环境启动脚本

创建 `scripts/start_production.sh`：

```bash
#!/bin/bash

# OKX DEX 生产环境启动脚本

set -e

echo "🚀 启动 OKX DEX 生产环境服务..."

# 设置工作目录
cd "$(dirname "$0")/.."

# 加载生产环境变量
if [ -f .env.production ]; then
    echo "📋 加载生产环境配置..."
    export $(cat .env.production | grep -v '^#' | xargs)
else
    echo "❌ 错误：找不到 .env.production 文件"
    exit 1
fi

# 验证关键环境变量
required_vars=(
    "OKX_API_KEY"
    "OKX_API_SECRET" 
    "OKX_API_PASSPHRASE"
    "DEFAULT_WALLET_PRIVATE_KEY"
    "DEFAULT_WALLET_ADDRESS"
    "MONGODB_URL"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ 错误：缺少必要的环境变量 $var"
        exit 1
    fi
done

echo "✅ 环境变量验证通过"

# 创建日志目录
mkdir -p /var/log/memeMonitor

# 启动服务
echo "🎯 启动自动交易服务..."
python -m uvicorn main:app \
    --host 0.0.0.0 \
    --port 8000 \
    --workers 4 \
    --log-level info \
    --access-log \
    --log-config logging.conf

echo "✅ 服务启动完成"
```

### 3.2 健康检查脚本

创建 `scripts/health_check.py`：

```python
#!/usr/bin/env python3
"""
OKX DEX 服务健康检查脚本
"""

import asyncio
import sys
import logging
from utils.trading import get_auto_trade_manager

async def main():
    """健康检查主函数"""
    try:
        # 获取交易管理器
        trade_manager = await get_auto_trade_manager()
        
        # 检查初始化状态
        if not trade_manager.is_initialized:
            print("❌ AutoTradeManager 未初始化")
            return False
        
        # 获取服务状态
        status = await trade_manager.get_status()
        
        # 检查关键指标
        checks = [
            ("配置已加载", status.get("config_loaded", False)),
            ("渠道已注册", len(status.get("registered_channels", [])) > 0),
            ("OKX渠道可用", "okx" in status.get("enabled_channels", [])),
            ("健康渠道数量", len(status.get("healthy_channels", [])) > 0)
        ]
        
        all_healthy = True
        for check_name, check_result in checks:
            status_icon = "✅" if check_result else "❌"
            print(f"{status_icon} {check_name}: {check_result}")
            if not check_result:
                all_healthy = False
        
        if all_healthy:
            print("🎉 所有健康检查通过")
            return True
        else:
            print("⚠️ 部分健康检查失败")
            return False
            
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
```

## 📊 4. 监控配置

### 4.1 日志配置

创建 `logging.conf`：

```ini
[loggers]
keys=root,auto_trade,okx_service

[handlers]
keys=consoleHandler,fileHandler,errorFileHandler

[formatters]
keys=detailed,simple

[logger_root]
level=INFO
handlers=consoleHandler,fileHandler

[logger_auto_trade]
level=INFO
handlers=fileHandler,errorFileHandler
qualname=auto_trade
propagate=0

[logger_okx_service]
level=INFO
handlers=fileHandler,errorFileHandler
qualname=utils.trading.solana.okx_trade_service
propagate=0

[handler_consoleHandler]
class=StreamHandler
level=INFO
formatter=simple
args=(sys.stdout,)

[handler_fileHandler]
class=handlers.RotatingFileHandler
level=INFO
formatter=detailed
args=('/var/log/memeMonitor/auto_trade.log', 'a', 10485760, 5)

[handler_errorFileHandler]
class=handlers.RotatingFileHandler
level=ERROR
formatter=detailed
args=('/var/log/memeMonitor/auto_trade_error.log', 'a', 10485760, 3)

[formatter_detailed]
format=%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s
datefmt=%Y-%m-%d %H:%M:%S

[formatter_simple]
format=%(levelname)s - %(message)s
```

### 4.2 Prometheus 监控指标

创建 `utils/monitoring/metrics.py`：

```python
"""
OKX DEX 服务监控指标
"""

from prometheus_client import Counter, Histogram, Gauge, start_http_server
import time
import logging

logger = logging.getLogger(__name__)

# 交易相关指标
trade_total = Counter('okx_trades_total', 'Total number of trades', ['channel', 'status'])
trade_duration = Histogram('okx_trade_duration_seconds', 'Trade execution duration', ['channel'])
trade_amount = Histogram('okx_trade_amount_sol', 'Trade amount in SOL', ['channel', 'trade_type'])

# API相关指标
api_requests_total = Counter('okx_api_requests_total', 'Total API requests', ['endpoint', 'status'])
api_response_time = Histogram('okx_api_response_time_seconds', 'API response time', ['endpoint'])

# 系统健康指标
healthy_channels = Gauge('okx_healthy_channels', 'Number of healthy channels')
active_connections = Gauge('okx_active_connections', 'Number of active connections')

class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, port: int = 9090):
        self.port = port
        self._started = False
    
    def start_server(self):
        """启动指标服务器"""
        if not self._started:
            start_http_server(self.port)
            self._started = True
            logger.info(f"Prometheus指标服务器已启动，端口: {self.port}")
    
    def record_trade(self, channel: str, status: str, duration: float, amount: float, trade_type: str):
        """记录交易指标"""
        trade_total.labels(channel=channel, status=status).inc()
        trade_duration.labels(channel=channel).observe(duration)
        trade_amount.labels(channel=channel, trade_type=trade_type).observe(amount)
    
    def record_api_request(self, endpoint: str, status: str, response_time: float):
        """记录API请求指标"""
        api_requests_total.labels(endpoint=endpoint, status=status).inc()
        api_response_time.labels(endpoint=endpoint).observe(response_time)
    
    def update_health_metrics(self, healthy_count: int, connection_count: int):
        """更新健康指标"""
        healthy_channels.set(healthy_count)
        active_connections.set(connection_count)

# 全局指标收集器实例
metrics_collector = MetricsCollector()
```

## 🔄 5. CI/CD 部署

### 5.1 Docker 配置

创建 `Dockerfile.production`：

```dockerfile
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制项目文件
COPY pyproject.toml poetry.lock ./
COPY . .

# 安装Python依赖
RUN pip install poetry && \
    poetry config virtualenvs.create false && \
    poetry install --no-dev

# 创建非root用户
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python scripts/health_check.py

# 启动命令
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 5.2 Docker Compose 配置

创建 `docker-compose.production.yml`：

```yaml
version: '3.8'

services:
  okx-trade-service:
    build:
      context: .
      dockerfile: Dockerfile.production
    container_name: okx-trade-service
    restart: unless-stopped
    env_file:
      - .env.production
    ports:
      - "8000:8000"
      - "9090:9090"  # Prometheus指标端口
    volumes:
      - ./logs:/var/log/memeMonitor
    depends_on:
      - mongodb
      - redis
    networks:
      - meme-monitor-network

  mongodb:
    image: mongo:6.0
    container_name: mongo-production
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
      MONGO_INITDB_DATABASE: ${MONGO_DATABASE}
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js
    ports:
      - "27017:27017"
    networks:
      - meme-monitor-network

  redis:
    image: redis:7-alpine
    container_name: redis-production
    restart: unless-stopped
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - meme-monitor-network

  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    restart: unless-stopped
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9091:9090"
    networks:
      - meme-monitor-network

volumes:
  mongodb_data:
  redis_data:
  prometheus_data:

networks:
  meme-monitor-network:
    driver: bridge
```

## 📋 6. 部署检查清单

### 6.1 部署前检查

- [ ] **环境变量配置**
  - [ ] OKX API凭证已设置并验证
  - [ ] 钱包私钥安全存储
  - [ ] RPC端点已配置并测试
  - [ ] 数据库连接字符串正确

- [ ] **安全配置**
  - [ ] API IP白名单已设置
  - [ ] 防火墙规则已配置
  - [ ] SSL证书已安装
  - [ ] 访问权限已限制

- [ ] **资源配置**
  - [ ] 服务器资源充足 (CPU, 内存, 存储)
  - [ ] 网络带宽满足需求
  - [ ] 监控系统已配置

### 6.2 部署后验证

- [ ] **功能验证**
  - [ ] 服务启动正常
  - [ ] API健康检查通过
  - [ ] 数据库连接正常
  - [ ] OKX API调用成功

- [ ] **性能验证**
  - [ ] 响应时间在预期范围内
  - [ ] 内存使用率正常
  - [ ] 无内存泄漏
  - [ ] 错误率在可接受范围

- [ ] **监控验证**
  - [ ] 日志输出正常
  - [ ] 指标收集正常
  - [ ] 告警规则生效
  - [ ] 通知渠道畅通

## 🚨 7. 故障排除

### 7.1 常见问题

**问题**: OKX API认证失败
```bash
# 解决方案
1. 检查API凭证是否正确
2. 验证IP白名单设置
3. 确认API权限配置
4. 检查时间同步
```

**问题**: 交易执行失败
```bash
# 解决方案
1. 检查钱包余额
2. 验证RPC连接
3. 检查滑点设置
4. 查看交易日志
```

**问题**: 服务响应慢
```bash
# 解决方案
1. 检查网络连接
2. 优化数据库查询
3. 调整缓存配置
4. 增加服务器资源
```

### 7.2 日志分析

查看关键日志：

```bash
# 查看交易日志
tail -f /var/log/memeMonitor/auto_trade.log | grep "OKX"

# 查看错误日志
tail -f /var/log/memeMonitor/auto_trade_error.log

# 查看API调用日志
grep "call_okx_api" /var/log/memeMonitor/auto_trade.log
```

## 📞 8. 支持联系

如遇到部署问题，请提供：

1. 环境变量配置 (脱敏后)
2. 错误日志
3. 系统资源使用情况
4. 网络连接状态

---

**部署完成后，请务必进行小额测试交易验证系统正常运行！**

---
**关联链接 (Related Links):**
- **关联需求 (Related Requirement):** [OKX DEX详细需求规格](mdc:okx_dex_channel_requirements_ai.md)
- **实现方案 (Development Plan):** [OKX DEX技术实现方案](mdc:okx_dex_channel_dev_plan_ai.md)
- **测试用例 (Test Cases):** [OKX DEX测试用例设计](mdc:okx_dex_channel_test_cases_ai.md)
- **跟踪任务 (Tracked by):** [OKX DEX开发任务列表](mdc:okx_dex_channel_todo_list.md)
- **核心实现文件 (Core Implementation):**
  - `utils/trading/solana/okx_trade_service.py` - OKX交易服务实现
  - `utils/trading/auto_trade_manager.py` - 自动交易管理器
  - `models/config.py` - 配置模型定义 