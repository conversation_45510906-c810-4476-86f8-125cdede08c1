# 详细需求规格 (Clarified Requirements)

## 1. 概述
本需求旨在为 `AutoTradeManager` 增加一个新的交易通道，利用 OKX DEX API 在 Solana 网络上执行代币交易。这将为系统提供除 Jupiter 之外的另一个交易聚合器选项，以提高交易的灵活性和潜在的更优报价。

## 2. 核心功能需求
1.  **实现 OKX DEX 交易服务**:
    -   创建一个新的交易服务 `OkxTradeService`，该服务需实现 `TradeInterface` 接口。
    -   该服务应能处理 Solana 链上的代币交换（Swap）操作。

2.  **API 集成**:
    -   `OkxTradeService` 需要与 OKX DEX API 的 `GET /api/v5/dex/aggregator/swap-instruction` 端点进行交互。
    -   API 请求应包含执行交易所需的所有参数，例如输入/输出代币地址、数量、滑点等。
    -   API 请求必须包含正确的身份验证头信息 (`OK-ACCESS-KEY`, `OK-ACCESS-SIGN`, `OK-ACCESS-TIMESTAMP`, `OK-ACCESS-PASSPHRASE`)。

3.  **交易构建与执行**:
    -   服务需要解析 OKX API 返回的 `instructions` 和 `addressLookupTableAddresses`。
    -   利用这些数据构建一个标准的 Solana `VersionedTransaction`。
    -   使用项目配置的私钥对交易进行本地签名。
    -   将签名的交易发送到 Solana 网络并等待确认。

4.  **配置管理**:
    -   在 `models/config.py` 中，将 `"okx"` 添加为 `AutoTradeConfig` 下的一个新的有效交易通道 (`channel`)。
    -   在 `channel_params` 中为 `"okx"` 通道定义其特定的配置参数，包括：
        -   `api_host`: OKX API 的基础 URL。
        -   `api_key_env`, `api_secret_env`, `api_passphrase_env`: 用于从环境变量中读取 OKX API 凭证的键名。

5.  **与 `AutoTradeManager` 无缝集成**:
    -   `AutoTradeManager` 本身不需要进行任何代码修改。
    -   通过在数据库中添加一个 `channel` 字段为 `"okx"` 的新 `AutoTradeConfig` 文档，即可激活和使用新的交易通道。

## 3. 非功能性需求
1.  **安全性**: API 密钥和私钥必须通过环境变量安全地加载，绝不能硬编码在代码中。
2.  **错误处理**: `OkxTradeService` 必须实现健壮的错误处理机制，能够捕获并记录 API 调用失败、交易构建失败或交易上链失败等情况，并向上层调用者抛出标准化的异常。
3.  **可测试性**: `OkxTradeService` 的设计应易于进行单元测试，特别是对 OKX API 的调用应使用 `mock` 对象进行模拟。

## 4. 验收标准
1.  成功创建一个 `channel` 为 `"okx"` 的 `AutoTradeConfig` 记录。
2.  `AutoTradeManager` 能够根据此配置正确初始化 `OkxTradeService`。
3.  当触发交易时，系统能够成功通过 OKX DEX API 在 Solana 链上完成一笔代币交换。
4.  交易结果（成功或失败）被正确记录。
5.  相关的单元测试全部通过。

---
**关联链接 (Related Links):**
- **跟踪任务 (Tracked by):** [OKX DEX开发任务列表](mdc:okx_dex_channel_todo_list.md)
- **实现方案 (Development Plan):** [OKX DEX技术实现方案](mdc:okx_dex_channel_dev_plan_ai.md)
- **测试用例 (Test Cases):** [OKX DEX测试用例设计](mdc:okx_dex_channel_test_cases_ai.md)
- **部署指南 (Deployment Guide):** [OKX DEX生产环境部署指南](mdc:production_deployment_guide.md) 