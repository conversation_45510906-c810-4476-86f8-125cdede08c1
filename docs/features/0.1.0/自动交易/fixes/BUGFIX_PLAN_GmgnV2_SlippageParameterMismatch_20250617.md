# Bug修复计划 - GMGN v2代币小数位处理错误

## Bug信息
**Bug ID**: GmgnV2_TokenDecimalsCalculationError  
**发现时间**: 2025-06-17T18:17:49  
**方案更新时间**: 2025-06-17T18:29:43 (改为参考Jupiter实现)  
**报告人员**: gaojerry  
**Bug严重级别**: 🔴 **Critical** (直接影响资金交易)  
**受影响模块**: GMGN v2交易服务 (`utils/trading/solana/gmgn_trade_service_v2.py`)  

## 问题描述

### 现象
- GMGN v2在执行SPL代币卖出操作时，一直报错："Route failed: token balance is less than available balance"
- 同样的交易切换到Jupiter渠道后可以成功执行
- 钱包中确实有足够的代币余额

### 数据库记录
```json
{
  "amount_in": 1241.51066,
  "channel_type": "gmgn_v2",
  "trade_type": "sell",
  "token_in_address": "6xAaY2vJ2nZDCqbhFhPx4L547vyh2gRVn2SwCgEabonk",
  "token_out_address": "So11111111111111111111111111111111111111112",
  "error_message": "滑点重试耗尽: Route failed: token balance is less than available balance",
  "status": "failed"
}
```

### 日志关键信息
连续6次重试都报相同错误，说明问题不是临时性的网络问题，而是参数计算错误。

## 根本原因分析

### 代码对比分析

**GMGN v1** (正确实现):
```python
# 第242-254行：严格的小数位验证
if input_token_address == SOL_MINT_ADDRESS:
    input_decimals = 9  # SOL固定9位
elif input_decimals_from_snapshot is not None:
    # 严格验证input_token_decimals的有效性
    if not isinstance(input_decimals_from_snapshot, int):
        return TradeResult(status=TradeStatus.FAILED, ...)
    if not (0 <= input_decimals <= 18):
        return TradeResult(status=TradeStatus.FAILED, ...)
    input_decimals = input_decimals_from_snapshot
else:
    # SPL代币缺失input_token_decimals时直接失败
    return TradeResult(status=TradeStatus.FAILED, 
        error_message=f"input_token_decimals missing for {input_token_address}")
```

**GMGN v2** (有缺陷的实现):
```python
# 第475行：简单粗暴的默认值处理
decimals = strategy_snapshot.get('input_token_decimals', 9)  # ❌ 错误！
```

### 具体问题

1. **错误的默认值**: SPL代币缺失`input_token_decimals`时，默认使用9位小数（SOL的小数位）
2. **计算错误**: 大多数meme币是6位小数，使用9位计算会导致：
   - 预期量: `1241.51066 * 10^6 = 1,241,510,660` lamports
   - 实际计算: `1241.51066 * 10^9 = 1,241,510,660,000` lamports  
   - **差了1000倍！**
3. **GMGN API反应**: 认为要卖出的数量远超钱包余额，返回余额不足错误

## 修复方案 (基于Jupiter实现)

### 核心修改
参考Jupiter交易服务的实现方式，将GMGN v2的小数位处理逻辑改为动态获取：

1. **SOL地址**: 固定使用9位小数
2. **SPL代币**: 通过TokenInfo类动态获取代币精度信息
3. **获取顺序**: Database -> GMGN -> Solscan (与Jupiter一致)
4. **缺失处理**: 如果所有数据源都无法获取，使用默认值6位小数并记录警告
5. **缓存机制**: 在服务实例中缓存已获取的代币精度信息

### 技术实现细节

#### 1. 添加_get_token_decimals方法
```python
async def _get_token_decimals(self, token_address: str) -> int:
    """获取代币的精度信息 (参考Jupiter实现)"""
    # SOL的精度固定为9
    if token_address == SOL_MINT_ADDRESS:
        return 9
    
    # 检查缓存
    if hasattr(self, '_token_decimals_cache') and token_address in self._token_decimals_cache:
        return self._token_decimals_cache[token_address]
    
    # 使用TokenInfo类获取代币信息
    try:
        from utils.spiders.solana.token_info import TokenInfo
        
        token_info_fetcher = TokenInfo(token_address, chain="sol")
        token_info = await token_info_fetcher.get_token_info()
        
        if token_info and 'decimals' in token_info:
            decimals = int(token_info['decimals'])
            
            # 缓存结果
            if not hasattr(self, '_token_decimals_cache'):
                self._token_decimals_cache = {}
            self._token_decimals_cache[token_address] = decimals
            
            return decimals
    except Exception as e:
        logger.warning(f"Failed to get token decimals for {token_address}: {e}")
    
    # 如果查询失败，返回常见的默认值6位小数
    logger.warning(f"Using default decimals (6) for token {token_address}")
    return 6
```

#### 2. 修改_prepare_route_params方法
```python
def _prepare_route_params(self, ...):
    # 动态获取代币小数位数
    if input_token_address == SOL_MINT_ADDRESS:
        decimals = 9
    else:
        # 使用异步方法获取代币精度，需要在调用处await
        decimals = await self._get_token_decimals(input_token_address)
    
    in_amount = str(int(amount_input_token * (10 ** decimals)))
    # ... 其余逻辑保持不变
```

#### 3. 修改execute_trade方法
由于_prepare_route_params现在需要异步调用，需要相应调整：
```python
# 在execute_trade方法中
route_params = await self._prepare_route_params_async(...)
```

### 修改文件
- **主要文件**: `utils/trading/solana/gmgn_trade_service_v2.py`
- **影响方法**: 
  - 新增`_get_token_decimals` (异步方法)
  - 修改`_prepare_route_params` -> `_prepare_route_params_async` (变为异步)
  - 调整`execute_trade`中的调用方式

### 预期效果
- ✅ SPL代币自动获取正确的小数位数，无需手动配置
- ✅ 消除"token balance is less than available balance"错误
- ✅ 与Jupiter行为保持一致，使用相同的代币信息获取机制
- ✅ 提供缓存机制，提高性能
- ✅ 优雅降级：获取失败时使用6位小数默认值

## 风险评估

### 修复风险: ✅ 低风险
- **影响范围**: 仅限于GMGN v2的参数计算逻辑
- **向后兼容**: 现有正确配置的策略不受影响
- **潜在影响**: 缺失`input_token_decimals`的策略会开始报错（这是期望的行为）

### 测试验证计划
1. **单元测试**: 验证不同小数位的正确计算
2. **缺失配置测试**: 验证错误处理逻辑
3. **集成测试**: 验证修复后的实际交易成功率
4. **对比测试**: 确保与GMGN v1行为一致

## 实施计划 (基于Jupiter实现)

### 步骤1: 实现动态代币信息获取 ✅
- [x] 新增`_get_token_decimals`异步方法
- [x] 实现缓存机制
- [x] 添加详细的错误处理和日志

### 步骤2: 重构核心逻辑 ✅
- [x] 将`_prepare_route_params`改为异步方法`_prepare_route_params_async`
- [x] 修改`execute_trade`中的调用逻辑
- [x] 移除对`strategy_snapshot['input_token_decimals']`的依赖

### 步骤3: 创建测试用例 ✅  
- [x] 测试动态获取代币小数位功能
- [x] 测试缓存机制
- [x] 测试优雅降级(使用默认6位小数)
- [x] 测试SOL固定9位小数

### 步骤4: 验证修复效果 ✅
- [x] 运行测试套件 (9/9测试通过)
- [x] 验证核心Bug修复 (原始Bug数据计算正确)
- [x] 确认不引入新问题

### 步骤5: 更新文档 ✅
- [x] 更新修复记录
- [x] 更新测试文档 (记录所有测试结果)
- [x] 更新API使用说明（不再需要input_token_decimals配置）

## 测试场景

### 场景1: SPL代币卖出 (动态获取小数位)
```python
# 测试代币: 6xAaY2vJ2nZDCqbhFhPx4L547vyh2gRVn2SwCgEabonk
# 期望流程:
# 1. 从数据库查询代币信息
# 2. 如果数据库没有，从GMGN获取
# 3. 如果GMGN失败，从Solscan获取
# 4. 使用获取的decimals计算lamports
# 5. 交易成功
```

### 场景2: 代币信息获取失败 (优雅降级)
```python
# 模拟所有数据源都无法获取代币信息的情况
# 期望: 使用默认6位小数，记录警告，交易继续
```

### 场景3: SOL交易 (固定小数位)
```python
# input_token_address == SOL_MINT_ADDRESS
# 期望: 固定使用9位小数，不查询外部数据源
```

### 场景4: 缓存机制验证
```python
# 同一个代币的多次调用
# 期望: 
# 1. 第一次调用时查询数据源并缓存
# 2. 后续调用直接使用缓存，不再查询
```

---

**关联链接 (Related Links):**
- **Bug复现数据**: 数据库记录 `685140d67952c3c04530651e`
- **相关代码**: `utils/trading/solana/gmgn_trade_service_v2.py:465-520`
- **参考实现**: `utils/trading/solana/gmgn_trade_service.py:242-254`
- **修复跟踪**: 当前文档 