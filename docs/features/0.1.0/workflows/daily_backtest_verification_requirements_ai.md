# 每日回测校验功能 - 产品需求文档 (PRD)

## 1. 产品概述

### 1.1 背景与动机
在 memeMonitor 项目中，交易策略的可靠性和一致性是系统成功的关键。目前系统虽然具备了回测功能和实时交易执行能力，但缺乏对策略执行一致性的日常监控机制。为了确保回测结果与实际交易执行的一致性，需要建立一个自动化的每日校验系统。

### 1.2 产品目标
- **主要目标**: 建立自动化的每日回测校验机制，确保交易策略的执行一致性
- **次要目标**: 提供及时的异常警报，帮助快速发现和解决策略执行问题
- **成功指标**: 
  - 每日自动执行校验，无需人工干预
  - 校验结果准确率 ≥ 95%
  - 异常检测响应时间 < 5 分钟
  - 通知送达率 = 100%

## 2. 用户故事与需求

### 2.1 主要用户故事

**作为系统管理员**，我希望每天自动收到回测校验报告，这样我就能及时了解交易策略的执行状况。

**作为交易策略开发者**，我希望能够对比回测结果和实际交易信号，这样我就能验证策略逻辑的正确性。

**作为风险管理员**，我希望在发现回测与实际交易不一致时立即收到警报，这样我就能及时采取风险控制措施。

### 2.2 功能需求

#### 2.2.1 核心功能需求

**FR-001: 配置获取与文件管理**
- 系统应从 config 表中读取 kol_activity 配置的策略参数
- 系统应将配置参数写入临时 JSON 文件，供回测使用
- 系统应在校验完成后清理临时文件

**FR-002: 回测执行**
- 系统应使用指定命令执行回测：`python run_backtest_ed.py --mode single_v2 --config 临时回测文件`
- 系统应等待回测完成并验证结果文件生成
- 系统应处理回测执行过程中的异常情况

**FR-003: 回测结果解析**
- 系统应读取回测结果文件 result.json
- 系统应提取 trades 部分的交易信息
- 系统应解析交易的买入/卖出时间、币种等关键信息

**FR-004: 实际信号获取**
- 系统应从 signals 表中获取当天的交易信号
- 系统应筛选出成功执行的交易信号
- 系统应提取信号的时间戳、币种等关键信息

**FR-005: 数据比对与验证**
- 系统应比对交易信号数量是否一致
- 系统应比对交易币种是否一致
- 系统应比对交易时间，允许 3 分钟的时间差异
- 系统应生成详细的比对结果报告

**FR-006: 通知发送**
- 系统应从 config 表中读取管理员 Telegram 配置
- 系统应根据预定义模板格式化校验结果
- 系统应通过 Telegram 发送校验结果通知

**FR-007: 策略选择与过滤 (新增)**
- 系统应支持通过命令行参数 `--strategy-name` 指定特定策略名称进行校验
- 系统应支持通过命令行参数 `--all-enabled-strategies` 校验所有已启用的策略
- 当指定策略名称时，系统应只校验该策略的回测结果和信号
- 当选择所有已启用策略时，系统应遍历所有启用状态的策略进行校验
- 系统应在 Telegram 消息中明确标识当前校验的策略范围

**FR-008: 多策略校验报告 (新增)**
- 当校验多个策略时，系统应生成汇总报告
- 报告应包含每个策略的校验状态和详细结果
- 报告应提供整体校验成功率统计
- 系统应支持策略级别的错误隔离，单个策略失败不影响其他策略校验

#### 2.2.2 非功能需求

**NFR-001: 性能要求**
- 整个校验流程应在 10 分钟内完成
- 系统应支持处理至少 100 个交易信号的比对

**NFR-002: 可靠性要求**
- 系统应具备异常处理机制，确保校验流程的鲁棒性
- 网络异常时应支持重试机制
- 校验失败时应记录详细的错误日志

**NFR-003: 可维护性要求**
- 代码应具有良好的模块化设计
- 应提供详细的日志记录功能
- 应支持配置参数的灵活调整

## 3. 功能范围定义

### 3.1 功能范围内 (In Scope)

✅ **包含功能**:
- 每日自动化回测校验
- 回测结果与实际信号的多维度比对
- Telegram 通知发送
- 异常情况处理和日志记录
- Crontab 定时任务部署支持
- **策略选择功能 (新增)**:
  - 通过命令行参数指定特定策略名称
  - 通过命令行参数校验所有已启用策略
  - 多策略校验的汇总报告
  - 策略级别的错误隔离

### 3.2 功能范围外 (Out of Scope)

❌ **不包含功能**:
- 历史数据的批量校验（仅限当天数据）
- 实时监控和即时警报（仅限每日定时校验）
- 策略参数的自动调优建议
- Web 界面展示（仅限 Telegram 通知）
- 策略性能分析和优化建议
- 跨日期的策略比较分析

## 4. 业务逻辑与规则

### 4.1 校验逻辑

**时间匹配规则**:
- 买入时间差异 ≤ 3 分钟视为匹配
- 卖出时间差异 ≤ 3 分钟视为匹配
- 时间比较基于 UTC 时间戳

**币种匹配规则**:
- 完全字符串匹配（区分大小写）
- 支持 Solana 代币地址格式

**数量匹配规则**:
- 回测交易数量 = 实际信号数量视为匹配
- 允许回测交易数量少于实际信号（考虑到策略过滤）
- 实际信号数量少于回测交易视为异常

### 4.2 异常处理规则

**回测执行异常**:
- 回测命令执行失败时，记录错误并发送警报通知
- 回测结果文件不存在时，等待 60 秒后重试一次

**数据获取异常**:
- 数据库连接失败时，重试 3 次
- 信号数据为空时，记录警告但继续执行

**通知发送异常**:
- Telegram 发送失败时，重试 2 次
- 所有重试失败后，记录到错误日志

## 5. 数据模型与接口

### 5.1 输入数据

**配置数据** (来源: config 表):
```json
{
  "strategy_name": "kol_activity",
  "strategy_params": {
    // 策略相关参数
  }
}
```

**信号数据** (来源: signals 表):
```json
{
  "signal_id": "string",
  "symbol": "string", 
  "timestamp": "datetime",
  "signal_type": "buy|sell",
  "status": "success|failed"
}
```

### 5.2 输出数据

**单策略校验结果通知格式**:
```
📊 每日回测校验报告
📅 检验时间：2025-01-12
🎯 校验策略：kol_activity_strategy
🔍 校验结果：✅ 一致 / ❌ 存在差异

📈 交易数量：
- 回测交易数量：3
- 真实信号数量：3

📊 交易信息：
1. 回测交易：
   币种：83mCRQJzvKMeQd9wJbZDUCTPgRbZMDoPdMSx5Sf1pump
   买入：2025-01-12 18:00:01  卖出：2025-01-12 18:01:01

2. 真实交易信号：
   币种：83mCRQJzvKMeQd9wJbZDUCTPgRbZMDoPdMSx5Sf1pump
   买入：2025-01-12 18:00:03  卖出：2025-01-12 18:01:02

✅ 差异说明：
- 买入时间差异：2秒 ✅
- 卖出时间差异：1秒 ✅
```

**多策略校验结果通知格式 (新增)**:
```
📊 每日回测校验汇总报告
📅 检验时间：2025-01-12
🎯 校验模式：所有已启用策略
📈 策略总数：3个

🔍 整体校验结果：
✅ 成功：2个策略
❌ 失败：1个策略
📊 成功率：66.7%

📋 详细结果：
1. ✅ kol_activity_strategy
   - 回测交易：5笔
   - 实际信号：5笔
   - 状态：完全一致

2. ✅ smart_money_strategy
   - 回测交易：3笔
   - 实际信号：3笔
   - 状态：完全一致

3. ❌ trend_following_strategy
   - 回测交易：2笔
   - 实际信号：4笔
   - 状态：信号数量不匹配
   - 详情：缺少2笔实际交易信号

⚠️ 需要关注的策略：
- trend_following_strategy：信号执行异常
```

## 6. 边界条件与异常场景

### 6.1 边界条件

**数据边界**:
- 当天无交易信号时的处理
- 回测结果为空时的处理
- 配置参数缺失时的处理

**时间边界**:
- 跨日期交易的时间比对
- 时区转换的准确性
- 夏令时调整的影响

### 6.2 异常场景处理

**场景一: 回测执行失败**
- 错误信息: "回测执行失败，请检查配置文件"
- 处理方式: 发送错误通知，记录详细日志

**场景二: 数据不一致**
- 错误信息: "发现 X 项差异，请检查策略执行"
- 处理方式: 发送详细差异报告

**场景三: 通知发送失败**
- 错误信息: "Telegram 通知发送失败"
- 处理方式: 记录到错误日志，不影响校验主流程

## 7. 验收标准

### 7.1 功能验收标准

- [ ] 能够成功读取配置并生成临时文件
- [ ] 能够正确执行回测命令并获取结果
- [ ] 能够准确解析回测结果和信号数据
- [ ] 能够执行多维度数据比对
- [ ] 能够发送格式正确的 Telegram 通知
- [ ] 能够处理各种异常情况而不崩溃

### 7.2 性能验收标准

- [ ] 整体执行时间 < 10 分钟
- [ ] 内存使用量 < 500MB
- [ ] 支持处理 100+ 交易信号的比对

### 7.3 质量验收标准

- [ ] 代码测试覆盖率 ≥ 80%
- [ ] 所有异常情况都有对应的测试用例
- [ ] 日志信息完整且可追溯

---

**关联链接 (Related Links):**
- **跟踪任务 (Tracked by):** @daily_backtest_verification_todo_list.md
- **实现方案 (Development Plan):** @daily_backtest_verification_dev_plan_ai.md
- **测试用例 (Test Cases):** @daily_backtest_verification_test_cases_ai.md
- **功能增强记录:** 策略选择器增强需求 (2025-01-12) - 强制指定策略名称，提升系统安全性