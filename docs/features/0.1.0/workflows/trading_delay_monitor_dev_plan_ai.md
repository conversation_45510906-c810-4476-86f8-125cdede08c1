# 交易延迟监控功能技术实现方案

## 1. 文档历史 (Document History)

| 版本 | 日期                    | 修改人       | 修改内容 |
|------|-------------------------|--------------|----------|
| v1.0 | 2025-06-18 22:15:38 CST | AI开发工程师 | 初版创建 |
| v1.1 | 2025-06-18 22:30:00 CST | AI开发工程师 | **[重要修复]** 修正KOL交易时间解析算法：增加transaction_min_amount金额过滤逻辑，修改KOLTradeTimeResolver和KOLWalletActivityDAO实现，确保只查找符合金额条件的KOL活动 |

## 2. 技术方案概述 (Technical Overview)

### 2.1. 架构设计原则

本技术实现方案遵循memeMonitor项目的现有架构模式： - **工作流驱动**: 基于YAML配置的工作流节点系统 - **DAO模式**: 数据访问对象封装数据库操作 - **模块化设计**: 清晰的关注点分离和可测试性 - **异步处理**: 全面采用async/await模式 - **错误恢复**: 内置重试机制和异常处理

### 2.2. 核心技术栈

-   **编程语言**: Python 3.11+
-   **数据库**: MongoDB (Beanie ODM)
-   **工作流引擎**: 项目现有的工作流系统
-   **异步框架**: asyncio
-   **数据处理**: 批量处理和并发优化

## 3. 系统架构设计 (System Architecture)

### 3.1. 总体架构图

```         
工作流调度器
    ↓
交易延迟监控工作流
    ↓
延迟计算处理器 (TradingDelayHandler)
    ↓
┌─────────────────┬──────────────────┬─────────────────┐
│   数据采集模块    │   延迟计算模块     │   数据存储模块   │
│                │                 │                │
│ • 获取未计算     │ • KOL交易时间查询 │ • 延迟记录保存   │
│   的交易记录     │ • 延迟时间计算    │ • 重复检测       │
│ • 信号关联查询   │ • 异常标记        │ • 批量处理       │
└─────────────────┴──────────────────┴─────────────────┘
    ↓              ↓                  ↓
TradeRecordDAO   SignalDAO         TradingDelayRecordDAO
KOLWalletActivityDAO               + TradingDelayRecord Model
```

### 3.2. 模块职责分工

#### 3.2.1. 工作流层 (Workflow Layer)

-   **文件**: `workflows/trading_delay_monitor/handler.py`
-   **职责**: 工作流节点实现和数据流控制
-   **核心函数**: `process_trading_delays()`, `validate()`

#### 3.2.2. 数据模型层 (Data Model Layer)

-   **文件**: `models/trading_delay_record.py`
-   **职责**: 延迟记录的数据结构定义
-   **关键字段**: 交易ID、信号ID、KOL交易时间、延迟计算等

#### 3.2.3. 数据访问层 (DAO Layer)

-   **文件**: `dao/trading_delay_record_dao.py`
-   **职责**: 延迟记录的CRUD操作和复杂查询
-   **关键方法**: 批量插入、重复检测、统计查询

#### 3.2.4. 业务逻辑层 (Business Logic Layer)

-   **文件**: `utils/trading_delay_calculator.py`
-   **职责**: 核心延迟计算算法和KOL交易时间查询
-   **关键类**: `TradingDelayCalculator`, `KOLTradeTimeResolver`

## 4. 详细设计 (Detailed Design)

### 4.1. 数据模型设计

#### 4.1.1. TradingDelayRecord 模型

``` python
from datetime import datetime
from typing import List, Optional
from beanie import Document, PydanticObjectId, Indexed
from pydantic import Field

class DelayStatus(str, Enum):
    """延迟状态枚举"""
    CALCULATED = "calculated"          # 正常计算
    SIGNAL_MISSING = "signal_missing"  # 信号缺失
    KOL_ACTIVITY_MISSING = "kol_activity_missing"  # KOL活动缺失
    TIMESTAMP_ANOMALY = "timestamp_anomaly"        # 时间戳异常
    SIGNAL_SUPPRESSED = "signal_suppressed"  # **新增** 信号被抑制
    EXCESSIVE_DELAY = "excessive_delay"             # 异常长延迟

class TradingDelayRecord(Document):
    """交易延迟记录模型"""
    
    # 关联信息
    trade_record_id: Indexed(PydanticObjectId) = Field(..., unique=True, description="交易记录ID")
    signal_id: Optional[PydanticObjectId] = Field(default=None, description="信号ID")
    
    # 交易基本信息 (用于分析)
    strategy_name: Optional[str] = Field(default=None, description="策略名称")
    token_address: Optional[str] = Field(default=None, description="代币地址")
    trade_type: Optional[str] = Field(default=None, description="交易类型(buy/sell)")
    trade_amount: Optional[float] = Field(default=None, description="交易金额")
    
    # KOL相关信息
    hit_kol_wallets: List[str] = Field(default_factory=list, description="触发信号的KOL钱包地址")
    kol_last_trade_timestamp: Optional[datetime] = Field(default=None, description="KOL最后交易时间")
    kol_trade_lookup_hours: Optional[float] = Field(default=None, description="KOL交易查找回溯小时")
    
    # 时间戳信息
    trade_execution_timestamp: datetime = Field(..., description="实际交易执行时间")
    signal_trigger_timestamp: Optional[datetime] = Field(default=None, description="信号触发时间")
    
    # 延迟计算结果
    delay_seconds: Optional[float] = Field(default=None, description="延迟秒数")
    delay_status: DelayStatus = Field(..., description="延迟状态")
    
    # 元数据
    created_at: datetime = Field(default_factory=datetime.utcnow, description="记录创建时间")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="记录更新时间")
    calculation_metadata: Optional[dict] = Field(default=None, description="计算过程元数据")
    
    # **新增** 信号抑制检查结果
    suppression_check_result: Optional[str] = Field(default=None, description="信号抑制检查状态")
    
    class Settings:
        name = "trading_delay_records"
        indexes = [
            "trade_record_id",  # 唯一索引
            "signal_id",
            "delay_status", 
            "created_at",
            "strategy_name",
            "token_address",
            "trade_type",
            # 复合索引用于分析查询
            ["delay_seconds", "delay_status"],
            ["strategy_name", "delay_status"],
            ["token_address", "created_at"],
            ["trade_type", "delay_seconds"],
            ["created_at", "delay_status"],
            # 统计查询优化索引
            ["strategy_name", "token_address", "delay_status"],
            ["created_at", "strategy_name", "delay_seconds"],
        ]
```

### 4.2. 核心算法设计

#### 4.2.1. KOL交易时间解析器 (KOLTradeTimeResolver)

``` python
class KOLTradeTimeResolver:
    """KOL交易时间解析器 - 负责查找KOL最后交易时间"""
    
    def __init__(self):
        self.signal_dao = SignalDAO()
        self.kol_activity_dao = KOLWalletActivityDAO()
    
    async def resolve_kol_last_trade_time(
        self, 
        trade_record: TradeRecord
    ) -> Tuple[Optional[datetime], List[str], Optional[str]]:
        """
        解析KOL最后交易时间
        
        修正逻辑：遵循需求文档7.1节定义的算法步骤
        1. 验证交易记录状态为成功
        2. 从trade_record.signal_id获取Signal记录
        3. 从Signal中提取KOL钱包和查询参数（包括金额限制）
        4. 查询KOL活动获取最后符合金额条件的交易时间
        
        Args:
            trade_record: 交易记录对象
            
        Returns:
            Tuple[
                Optional[datetime],  # KOL最后交易时间
                List[str],          # 命中的KOL钱包地址列表
                Optional[str]       # 错误信息（如果有）
            ]
        """
        
        # 步骤1: 验证交易记录状态（只处理成功的交易）
        if trade_record.status != "success":
            return None, [], "trade_not_successful"
        
        # 步骤2: 验证信号ID存在
        if not trade_record.signal_id:
            return None, [], "no_signal_id"
            
        # 步骤3: 获取信号信息
        signal = await self.signal_dao.get_by_id(trade_record.signal_id)
        if not signal:
            return None, [], "signal_not_found"
        
        # 步骤4: 验证必要字段
        hit_kol_wallets = signal.hit_kol_wallets or []
        if not hit_kol_wallets:
            return None, [], "no_kol_wallets"
        
        trigger_conditions = signal.trigger_conditions or {}
        lookback_hours = trigger_conditions.get('transaction_lookback_hours')
        if not lookback_hours:
            return None, hit_kol_wallets, "no_lookback_hours"
        
        # 步骤5: **[新增]** 获取最小交易金额限制
        min_amount = trigger_conditions.get('transaction_min_amount')
        if min_amount is None:
            return None, hit_kol_wallets, "no_min_amount_config"
        
        # 步骤6: 计算查询时间范围
        trigger_time = signal.trigger_timestamp
        start_time = trigger_time - timedelta(hours=lookback_hours)
        
        # 转换为Unix时间戳进行查询
        end_timestamp = int(trigger_time.timestamp())
        start_timestamp = int(start_time.timestamp())
        
        # 步骤7: 查询KOL活动（增加金额过滤）
        kol_activities = await self.kol_activity_dao.find_activities_in_time_range(
            wallet_addresses=hit_kol_wallets,
            start_timestamp=start_timestamp,
            end_timestamp=end_timestamp,
            event_types=['buy', 'sell'],  # 只关注买卖活动
            min_cost_usd=min_amount,     # **[新增]** 最小金额过滤
            sort_by_timestamp=True,
            limit=1  # 只需要最后一个符合条件的
        )
        
        if not kol_activities:
            return None, hit_kol_wallets, "no_qualified_kol_activities_found"
        
        # 步骤8: 转换时间戳格式
        last_activity = kol_activities[0]
        kol_last_trade_time = datetime.fromtimestamp(
            last_activity.timestamp, 
            tz=timezone.utc
        )
        
        return kol_last_trade_time, hit_kol_wallets, None


#### 4.2.2. 信号抑制检查器 (SignalSuppressionChecker) **[新增]**

```python
# utils/trading_delay_monitor/signal_suppression_checker.py

from dataclasses import dataclass
from typing import Optional
from datetime import datetime, timedelta
from models.trade_record import TradeRecord
from models.signal import Signal
from dao.trading_delay_record_dao import TradingDelayRecordDAO
from dao.signal_dao import SignalDAO
from dao.config_dao import ConfigDAO

@dataclass
class SuppressionCheckResult:
    """信号抑制检查结果"""
    is_suppressed: bool
    status: str  # 'passed', 'suppressed', 'no_history', 'config_missing', 'error'
    reason: Optional[str] = None
    time_interval_minutes: Optional[float] = None
    historical_kol_time: Optional[datetime] = None

class SignalSuppressionChecker:
    """信号抑制检查器 - 检查信号是否因抑制机制而应被跳过"""
    
    def __init__(self, 
                 delay_dao: TradingDelayRecordDAO, 
                 signal_dao: SignalDAO,
                 config_dao: ConfigDAO,
                 kol_resolver: 'KOLTradeTimeResolver'):
        self.delay_dao = delay_dao
        self.signal_dao = signal_dao
        self.config_dao = config_dao
        self.kol_resolver = kol_resolver
    
    async def check_signal_suppression(self, trade_record: TradeRecord) -> SuppressionCheckResult:
        """
        检查信号是否被抑制机制影响
        
        遵循需求文档FR009和7.2节算法要求
        """
        try:
            # 步骤1: 获取当前信号信息
            signal = await self.signal_dao.get_by_id(trade_record.signal_id)
            if not signal:
                return SuppressionCheckResult(
                    is_suppressed=False, 
                    status='error', 
                    reason='signal_not_found'
                )
            
            # 步骤2: 获取策略配置
            strategy_config = await self._get_strategy_config(signal.strategy_name)
            if not strategy_config:
                return SuppressionCheckResult(
                    is_suppressed=False, 
                    status='config_missing', 
                    reason='strategy_config_not_found'
                )
            
            same_token_interval = strategy_config.get('same_token_notification_interval')
            if same_token_interval is None:
                return SuppressionCheckResult(
                    is_suppressed=False, 
                    status='config_missing', 
                    reason='same_token_notification_interval_not_configured'
                )
            
            # 步骤3: 计算当前信号的KOL交易时间
            current_kol_time, _, error_msg = await self.kol_resolver.resolve_kol_last_trade_time(trade_record)
            if error_msg or not current_kol_time:
                return SuppressionCheckResult(
                    is_suppressed=False, 
                    status='error', 
                    reason=f'kol_time_resolution_failed: {error_msg}'
                )
            
            # 步骤4: 查询历史相关信号
            historical_records = await self.delay_dao.find_recent_records_by_strategy_token(
                strategy_name=signal.strategy_name,
                token_address=signal.token_address,
                before_timestamp=signal.trigger_timestamp,
                limit=1
            )
            
            if not historical_records:
                return SuppressionCheckResult(
                    is_suppressed=False, 
                    status='no_history', 
                    reason='first_signal_for_strategy_token_combination'
                )
            
            # 步骤5: 计算时间间隔并判断抑制
            historical_record = historical_records[0]
            historical_kol_time = historical_record.kol_last_trade_timestamp
            if not historical_kol_time:
                return SuppressionCheckResult(
                    is_suppressed=False, 
                    status='error', 
                    reason='historical_kol_time_missing'
                )
            
            time_interval = current_kol_time - historical_kol_time
            interval_minutes = time_interval.total_seconds() / 60
            
            is_suppressed = interval_minutes <= same_token_interval
            
            return SuppressionCheckResult(
                is_suppressed=is_suppressed,
                status='suppressed' if is_suppressed else 'passed',
                reason=f'time_interval_{interval_minutes:.2f}min_vs_threshold_{same_token_interval}min',
                time_interval_minutes=interval_minutes,
                historical_kol_time=historical_kol_time
            )
            
        except Exception as e:
            return SuppressionCheckResult(
                is_suppressed=False, 
                status='error', 
                reason=f'unexpected_error: {str(e)}'
            )
    
    async def _get_strategy_config(self, strategy_name: str) -> Optional[dict]:
        """获取策略配置"""
        try:
            config = await self.config_dao.get_by_type('kol_activity')
            if not config or not config.data:
                return None
            
            # 查找对应的策略配置
            for strategy in config.data.buy_strategies:
                if strategy.strategy_name == strategy_name:
                    return strategy.model_dump()
            
            return None
        except Exception:
            return None
```

#### 4.2.3. 错误重试机制 (RetryManager)

```python
from utils.retry import RetryManager, ExponentialBackoffStrategy, CircuitBreaker

class TradingDelayRetryManager:
    """交易延迟计算重试管理器"""
    
    def __init__(self):
        # 数据库查询重试策略
        self.db_retry_strategy = ExponentialBackoffStrategy(
            max_retries=3,
            base_delay=1.0,
            max_delay=10.0
        )
        
        # 断路器保护
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=5,
            recovery_timeout=60.0
        )
        
        self.retry_manager = RetryManager(
            strategy=self.db_retry_strategy,
            circuit_breaker=self.circuit_breaker
        )
    
    async def execute_with_retry(self, operation, *args, **kwargs):
        """执行带重试的操作"""
        return await self.retry_manager.execute_async(operation, *args, **kwargs)
    
    async def safe_database_query(self, query_func, *args, **kwargs):
        """安全的数据库查询，带重试和熔断"""
        try:
            return await self.execute_with_retry(query_func, *args, **kwargs)
        except Exception as e:
            logger.error(f"数据库查询失败，已达最大重试次数: {e}")
            raise
```

```         

#### 4.2.4. 延迟计算器 (TradingDelayCalculator) **[已更新]**

```python
class TradingDelayCalculator:
    """交易延迟计算器 - 核心业务逻辑"""
    
    def __init__(self):
        self.kol_resolver = KOLTradeTimeResolver()
        self.trade_dao = TradeRecordDAO()
        self.delay_dao = TradingDelayRecordDAO()
        # 新增：信号抑制检查器
        self.suppression_checker = SignalSuppressionChecker(
            delay_dao=self.delay_dao,
            signal_dao=SignalDAO(),
            config_dao=ConfigDAO(),
            kol_resolver=self.kol_resolver
        )
    
    async def calculate_delays_for_unprocessed_trades(
        self, 
        batch_size: int = 50
    ) -> Dict[str, int]:
        """
        批量计算未处理交易的延迟
        
        Returns:
            Dict包含处理统计信息
        """
        stats = {
            'processed': 0,
            'calculated': 0,
            'errors': 0,
            'skipped': 0
        }
        
        # 获取未处理的交易记录
        unprocessed_trades = await self.trade_dao.find_unprocessed_trades(
            limit=batch_size
        )
        
        for trade_record in unprocessed_trades:
            try:
                delay_record = await self._calculate_single_trade_delay(trade_record)
                if delay_record:
                    await self.delay_dao.save(delay_record)
                    if delay_record.delay_status == DelayStatus.CALCULATED:
                        stats['calculated'] += 1
                    else:
                        stats['skipped'] += 1
                else:
                    stats['errors'] += 1
                    
                stats['processed'] += 1
                
            except Exception as e:
                logger.error(f"处理交易记录 {trade_record.id} 时发生错误: {str(e)}")
                stats['errors'] += 1
                
        return stats
    
    async def _calculate_single_trade_delay(
        self, 
        trade_record: TradeRecord
    ) -> Optional[TradingDelayRecord]:
        """
        计算单个交易的延迟
        
        增强防重复处理机制：程序检查 + 数据库唯一约束双重保护
        """
        
        # 防重复机制1: 程序层面检查
        existing = await self.delay_dao.find_by_trade_record_id(trade_record.id)
        if existing:
            logger.debug(f"交易记录 {trade_record.id} 已存在延迟计算，跳过处理")
            return None  # 已处理过
        
        # 新增：信号抑制检查
        suppression_result = await self.suppression_checker.check_signal_suppression(trade_record)
        if suppression_result.is_suppressed:
            logger.info(f"交易记录 {trade_record.id} 被信号抑制机制过滤，跳过延迟计算")
            # 创建被抑制状态的延迟记录
            suppressed_record = TradingDelayRecord(
                trade_record_id=trade_record.id,
                signal_id=trade_record.signal_id,
                strategy_name=trade_record.strategy_name,
                token_address=trade_record.token_address,
                trade_type=trade_record.trade_type,
                trade_amount=trade_record.trade_amount,
                trade_execution_timestamp=trade_record.created_at,
                delay_status=DelayStatus.SIGNAL_SUPPRESSED,
                suppression_check_result=suppression_result.status,
                calculation_metadata={
                    'suppression_reason': suppression_result.reason,
                    'time_interval_minutes': suppression_result.time_interval_minutes,
                    'processed_at': datetime.utcnow().isoformat()
                }
            )
            try:
                await suppressed_record.save()
                return suppressed_record
            except DuplicateKeyError:
                logger.warning(f"交易记录 {trade_record.id} 延迟计算出现并发重复，已跳过")
                return None
        
        # 创建基础延迟记录
        delay_record = TradingDelayRecord(
            trade_record_id=trade_record.id,
            signal_id=trade_record.signal_id,
            strategy_name=trade_record.strategy_name,  # 添加策略名称
            token_address=trade_record.token_address,  # 添加代币地址
            trade_type=trade_record.trade_type,        # 添加交易类型
            trade_amount=trade_record.trade_amount,    # 添加交易金额
            trade_execution_timestamp=trade_record.created_at,
            calculation_metadata={'processed_at': datetime.utcnow().isoformat()}
        )
        
        # 处理无信号的情况
        if not trade_record.signal_id:
            delay_record.delay_status = DelayStatus.SIGNAL_MISSING
            try:
                await delay_record.save()
                return delay_record
            except DuplicateKeyError:
                logger.warning(f"交易记录 {trade_record.id} 延迟计算出现并发重复，已跳过")
                return None
        
        # 解析KOL交易时间
        kol_time, hit_wallets, error_msg = await self.kol_resolver.resolve_kol_last_trade_time(
            trade_record
        )
        
        delay_record.hit_kol_wallets = hit_wallets
        
        if error_msg:
            delay_record.delay_status = DelayStatus.KOL_ACTIVITY_MISSING
            delay_record.calculation_metadata['error'] = error_msg
            try:
                await delay_record.save()
                return delay_record
            except DuplicateKeyError:
                logger.warning(f"交易记录 {trade_record.id} 延迟计算出现并发重复，已跳过")
                return None
        
        # 计算延迟
        delay_record.kol_last_trade_timestamp = kol_time
        delay_seconds = (
            trade_record.created_at - kol_time
        ).total_seconds()
        
        delay_record.delay_seconds = round(delay_seconds, 2)
        
        # 判断延迟状态
        if delay_seconds < 0:
            delay_record.delay_status = DelayStatus.TIMESTAMP_ANOMALY
        elif delay_seconds > 600:  # 10分钟
            delay_record.delay_status = DelayStatus.EXCESSIVE_DELAY
        else:
            delay_record.delay_status = DelayStatus.CALCULATED
        
        # 防重复机制2: 数据库唯一约束保护（最终保存）
        try:
            await delay_record.save()
            return delay_record
        except DuplicateKeyError:
            logger.warning(f"交易记录 {trade_record.id} 延迟计算出现并发重复，已跳过")
            return None
```

### 4.3. 工作流处理器实现

#### 4.3.1. 主处理器 (TradingDelayHandler)

``` python
# workflows/trading_delay_monitor/handler.py

import logging
import asyncio
from typing import List, Dict, Any, Optional
from utils.trading_delay_calculator import TradingDelayCalculator

logger = logging.getLogger("TradingDelayMonitorHandler")

async def process_trading_delays() -> Optional[List[Dict]]:
    """
    工作流节点主函数 - 处理交易延迟计算
    
    Returns:
        Optional[List[Dict]]: 处理结果摘要
    """
    logger.info("开始处理交易延迟计算")
    
    calculator = TradingDelayCalculator()
    
    try:
        # 批量处理延迟计算
        stats = await calculator.calculate_delays_for_unprocessed_trades(
            batch_size=50  # 可配置
        )
        
        logger.info(f"延迟计算完成: {stats}")
        
        # 返回处理摘要
        return [{
            'processing_summary': stats,
            'timestamp': datetime.utcnow().isoformat(),
            'status': 'success'
        }]
        
    except Exception as e:
        logger.error(f"处理交易延迟时发生错误: {str(e)}")
        return [{
            'status': 'error',
            'error_message': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }]

async def validate(data: List[Dict]) -> bool:
    """验证处理结果"""
    if not data or not isinstance(data, list):
        return False
    
    for item in data:
        if not isinstance(item, dict) or 'status' not in item:
            return False
    
    return True

async def main():
    """工作流主入口函数"""
    result = await process_trading_delays()
    if result:
        logger.info("交易延迟监控工作流执行完成")
    else:
        logger.warning("交易延迟监控工作流未返回结果")
```

### 4.4. 数据访问层实现

#### 4.4.1. TradingDelayRecordDAO

``` python
# dao/trading_delay_record_dao.py

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from beanie import PydanticObjectId
from models.trading_delay_record import TradingDelayRecord, DelayStatus

class TradingDelayRecordDAO:
    """交易延迟记录数据访问对象"""
    
    async def save(self, record: TradingDelayRecord) -> TradingDelayRecord:
        """保存延迟记录"""
        await record.save()
        return record
    
    async def find_by_trade_record_id(
        self, 
        trade_record_id: PydanticObjectId
    ) -> Optional[TradingDelayRecord]:
        """根据交易记录ID查找延迟记录"""
        return await TradingDelayRecord.find_one(
            TradingDelayRecord.trade_record_id == trade_record_id
        )
    
    async def get_delay_statistics(
        self, 
        hours_back: int = 24
    ) -> Dict[str, Any]:
        """获取延迟统计信息"""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours_back)
        
        # 聚合查询统计
        pipeline = [
            {'$match': {'created_at': {'$gte': cutoff_time}}},
            {'$group': {
                '_id': '$delay_status',
                'count': {'$sum': 1},
                'avg_delay': {
                    '$avg': {
                        '$cond': [
                            {'$ne': ['$delay_seconds', None]},
                            '$delay_seconds',
                            0
                        ]
                    }
                },
                'max_delay': {'$max': '$delay_seconds'},
                'min_delay': {'$min': '$delay_seconds'}
            }}
        ]
        
        results = await TradingDelayRecord.aggregate(pipeline).to_list()
        
        # 格式化统计结果
        stats = {
            'total_records': sum(r['count'] for r in results),
            'by_status': {r['_id']: r for r in results},
            'period_hours': hours_back
        }
        
        return stats
    
    async def find_delays_for_analysis(
        self,
        status_filter: Optional[List[DelayStatus]] = None,
        min_delay: Optional[float] = None,
        max_delay: Optional[float] = None,
        limit: int = 1000
    ) -> List[TradingDelayRecord]:
        """查找用于分析的延迟记录"""
        query = {}
        
        if status_filter:
            query['delay_status'] = {'$in': status_filter}
        
        if min_delay is not None or max_delay is not None:
            delay_query = {}
            if min_delay is not None:
                delay_query['$gte'] = min_delay
            if max_delay is not None:
                delay_query['$lte'] = max_delay
            query['delay_seconds'] = delay_query
        
        return await TradingDelayRecord.find(query).limit(limit).to_list()
```

### 4.5. 工作流配置

#### 4.5.1. YAML配置文件

``` yaml
# workflows/trading_delay_monitor/trading_delay_monitor_workflow.yaml

name: "交易延迟监控工作流"
description: "定期计算交易延迟并存储到数据库，为回测系统提供真实延迟数据"

nodes:
  - name: "TradingDelayProcessorNode"
    node_type: "input"
    interval: 10  # 每10秒运行一次
    generate_data: workflows.trading_delay_monitor.handler.process_trading_delays
    flow_control:
      max_pending_messages: 5
      check_interval: 2
      enable_flow_control: true

  - name: "TradingDelayValidatorNode"
    node_type: "storage"
    depend_ons: ["TradingDelayProcessorNode"]
    store_data: workflows.trading_delay_monitor.handler.validate
    validate: workflows.trading_delay_monitor.handler.validate
```

## 5. 性能优化设计 (Performance Optimization)

### 5.1. 数据库优化

#### 5.1.1. 索引策略

-   `trade_record_id`: 唯一索引，防重复处理
-   `(delay_seconds, delay_status)`: 复合索引，支持分析查询
-   `created_at`: 时间索引，支持时间范围查询

#### 5.1.2. 查询优化

-   批量处理：一次处理50个交易记录
-   预聚合：使用MongoDB聚合管道减少内存使用
-   连接池：复用数据库连接

### 5.2. 算法优化

#### 5.2.1. 缓存策略

-   信号查询缓存：相同signal_id的查询结果临时缓存
-   KOL活动缓存：相同时间窗口的KOL活动查询缓存

#### 5.2.2. 并发控制

-   异步批处理：并发处理多个交易记录
-   流量控制：通过工作流flow_control限制并发度

## 6. 错误处理与监控 (Error Handling & Monitoring)

### 6.1. 异常分类处理

``` python
class TradingDelayError(Exception):
    """交易延迟计算基础异常"""
    pass

class SignalNotFoundError(TradingDelayError):
    """信号未找到异常"""
    pass

class KOLActivityNotFoundError(TradingDelayError):
    """KOL活动未找到异常"""
    pass

class TimestampFormatError(TradingDelayError):
    """时间戳格式异常"""
    pass
```

### 6.2. 日志记录策略

-   **INFO级别**: 正常处理流程和统计信息
-   **WARNING级别**: 数据缺失但可继续处理的情况
-   **ERROR级别**: 处理失败需要人工介入的情况
-   **DEBUG级别**: 详细的查询和计算过程

### 6.3. 监控指标

-   处理成功率
-   平均延迟计算时间
-   各状态分布统计
-   数据库查询性能

## 7. 部署与配置 (Deployment & Configuration)

### 7.1. 环境变量配置

``` bash
# 交易延迟监控相关配置
TRADING_DELAY_BATCH_SIZE=50
TRADING_DELAY_LOG_LEVEL=INFO
TRADING_DELAY_ENABLE_CACHE=true
TRADING_DELAY_CACHE_TTL=300  # 5分钟
```

### 7.2. 启动命令

``` bash
# 启动交易延迟监控工作流
python run_workflow.py --file workflows/trading_delay_monitor/trading_delay_monitor_workflow.yaml
```

## 8. 测试策略 (Testing Strategy)

### 8.1. 单元测试覆盖

-   `TradingDelayCalculator`: 延迟计算逻辑
-   `KOLTradeTimeResolver`: KOL交易时间查询
-   `TradingDelayRecordDAO`: 数据访问操作
-   数据模型验证

### 8.2. 集成测试

-   完整工作流执行测试
-   数据库操作集成测试
-   异常场景端到端测试

### 8.3. 性能测试

-   大批量数据处理性能
-   数据库查询性能
-   内存使用情况

## 9. 计划创建/修改的文件 (Planned Files)

### 9.1. 新增文件

1.  **数据模型**: `models/trading_delay_record.py`
2.  **DAO层**: `dao/trading_delay_record_dao.py`
3.  **业务逻辑**: `utils/trading_delay_calculator.py`
4.  **工作流处理器**: `workflows/trading_delay_monitor/handler.py`
5.  **工作流配置**: `workflows/trading_delay_monitor/trading_delay_monitor_workflow.yaml`

### 9.2. 修改文件

1.  **模型初始化**: `models/__init__.py` - 添加TradingDelayRecord注册
2.  **交易记录DAO**: `dao/trade_record_dao.py` - 添加查询未处理交易的方法
    ```python
    async def find_unprocessed_trades(self, limit: int = 50) -> List[TradeRecord]:
        """
        查询未处理的交易记录
        
        只返回状态为'success'且在延迟记录表中不存在对应记录的交易
        遵循需求文档FR002和7.1节算法要求
        """
        # 1. 查询状态为成功的交易记录
        # 2. 排除已有延迟记录的交易  
        # 3. 限制返回数量
        pass
    ```

3.  **延迟记录DAO**: `dao/trading_delay_record_dao.py` - 添加信号抑制相关查询方法 **[新增]**
    ```python
    async def find_recent_records_by_strategy_token(
        self, 
        strategy_name: str, 
        token_address: str, 
        before_timestamp: datetime, 
        limit: int = 1
    ) -> List[TradingDelayRecord]:
        """
        查询相同策略和代币的最近延迟记录
        
        用于信号抑制检查，查询在指定时间戳之前的最近记录
        遵循需求文档FR009要求
        """
        # 1. 按策略名称和代币地址过滤
        # 2. 时间范围：创建时间在before_timestamp之前
        # 3. 按创建时间降序排序
        # 4. 限制返回数量
        pass
    ```
4.  **KOL活动DAO**: `dao/kol_wallet_activity_dao.py` - **[重要修改]** 添加金额过滤的时间范围查询方法
    ```python
    async def find_activities_in_time_range(
        self, 
        wallet_addresses: List[str],
        start_timestamp: int,
        end_timestamp: int,
        event_types: List[str] = None,
        min_cost_usd: float = None,  # **[新增]** 最小金额过滤
        sort_by_timestamp: bool = True,
        limit: int = None
    ) -> List[dict]:
        """
        基于钱包地址、时间范围和金额过滤查询KOL活动
        
        新增金额过滤功能：只返回cost_usd >= min_cost_usd的活动
        遵循需求文档FR003的金额过滤要求
        """
        # 1. 按钱包地址列表过滤
        # 2. 按时间范围过滤 
        # 3. **[新增]** 按cost_usd金额过滤
        # 4. 按事件类型过滤（可选）
        # 5. 按时间戳排序和限制返回数量
        pass
    ```

### 9.3. 测试文件

1.  `test/models/test_trading_delay_record.py`
2.  `test/dao/test_trading_delay_record_dao.py`
3.  `test/utils/test_trading_delay_calculator.py`
4.  `test/workflows/trading_delay_monitor/test_handler.py`
5.  `test/workflows/trading_delay_monitor/test_integration.py`

## 10. 实施计划 (Implementation Plan)

### 10.1. 第一阶段：核心功能

1.  创建数据模型和DAO
2.  实现核心延迟计算算法
3.  基础工作流处理器

### 10.2. 第二阶段：优化与完善

1.  性能优化和缓存
2.  错误处理和监控
3.  完整测试覆盖

### 10.3. 第三阶段：集成与部署

1.  工作流配置和测试
2.  生产环境部署
3.  监控和维护

------------------------------------------------------------------------

**关联链接 (Related Links):** - **关联需求 (Related Requirement)**: @trading_delay_monitor_requirements_ai.md - **测试用例 (Test Cases)**: (待创建) - **跟踪任务 (Tracked by)**: @trading_delay_monitor_todo_list.md