# 每日回测校验功能 - 技术实现方案

## 1. 技术架构概述

### 1.1 系统架构

每日回测校验功能采用**单体式工作流**设计，基于 memeMonitor 现有的工作流框架构建，确保与系统的无缝集成。支持单策略和多策略校验模式。

```
┌─────────────────────────────────────────────────────────────┐
│                   每日回测校验工作流                          │
├─────────────────────────────────────────────────────────────┤
│ [参数解析器] → [策略选择器] → [配置读取器] → [文件管理器]       │
│      ↓              ↓              ↓              ↓         │
│ [回测执行器] → [结果解析器] → [信号获取器] → [比对分析器]       │
│      ↓              ↓              ↓              ↓         │
│ [报告生成器] → [通知发送器] → [多策略汇总] → [错误隔离器]       │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                   外部依赖和存储                              │
├─────────────────────────────────────────────────────────────┤
│ MongoDB (config, signals) | 文件系统 | Telegram API         │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 技术选型

| 组件类型 | 技术选择 | 原因 |
|---------|---------|------|
| **主程序框架** | Python 3.11 + asyncio | 与现有系统一致，支持异步处理 |
| **数据库 ORM** | Beanie ODM | 项目标准，支持 MongoDB 异步操作 |
| **进程执行** | subprocess + asyncio | 原生支持，无额外依赖 |
| **文件处理** | Python 标准库 json, tempfile | 轻量级，稳定可靠 |
| **通知发送** | 现有 TelegramMessageSender | 复用项目组件 |
| **任务调度** | Crontab | 系统级调度，稳定可靠 |
| **日志系统** | Python logging | 项目标准，支持多级别日志 |

## 2. 核心模块设计

### 2.1 模块结构

```
workflows/
└── daily_backtest_verification/
    ├── __init__.py                     # 包初始化
    ├── main.py                         # 主处理逻辑（核心业务逻辑）
    ├── components/                     # 核心组件模块
    │   ├── __init__.py
    │   ├── strategy_selector.py       # 策略选择器 (新增)
    │   ├── config_reader.py           # 配置读取器
    │   ├── backtest_executor.py       # 回测执行器  
    │   ├── result_parser.py           # 结果解析器
    │   ├── signal_fetcher.py          # 信号获取器
    │   ├── comparison_analyzer.py     # 比对分析器
    │   ├── report_generator.py        # 报告生成器
    │   ├── multi_strategy_aggregator.py # 多策略汇总器 (新增)
    │   └── notification_sender.py     # 通知发送器
    └── models/                        # 数据模型
        ├── __init__.py
        ├── verification_result.py     # 校验结果模型
        ├── strategy_config.py         # 策略配置模型 (新增)
        └── comparison_item.py         # 比对项模型

scripts/
└── daily_backtest_verification.py    # Crontab 入口脚本（调用main.py）

test/
└── workflows/
    └── daily_backtest_verification/
        ├── __init__.py
        ├── test_handler.py            # 集成测试
        └── components/
            ├── test_strategy_selector.py      # 策略选择器测试 (新增)
            ├── test_config_reader.py
            ├── test_backtest_executor.py
            ├── test_result_parser.py
            ├── test_signal_fetcher.py
            ├── test_comparison_analyzer.py
            ├── test_report_generator.py
            ├── test_multi_strategy_aggregator.py # 多策略汇总器测试 (新增)
            └── test_notification_sender.py
```

### 2.2 数据模型设计

#### 2.2.1 校验结果模型

```python
# workflows/daily_backtest_verification/models/verification_result.py
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from pydantic import BaseModel, Field
from enum import Enum

class VerificationStatus(str, Enum):
    """校验状态枚举"""
    SUCCESS = "success"           # 校验成功，无差异
    WARNING = "warning"           # 校验成功，有可接受差异  
    FAILURE = "failure"           # 校验失败，有严重差异
    ERROR = "error"               # 校验过程出错

class ComparisonItem(BaseModel):
    """单个比对项"""
    symbol: str = Field(..., description="币种符号")
    backtest_buy_time: Optional[datetime] = Field(default=None, description="回测买入时间")
    backtest_sell_time: Optional[datetime] = Field(default=None, description="回测卖出时间")
    signal_buy_time: Optional[datetime] = Field(default=None, description="实际买入信号时间")
    signal_sell_time: Optional[datetime] = Field(default=None, description="实际卖出信号时间")
    buy_time_diff_seconds: Optional[float] = Field(default=None, description="买入时间差异（秒）")
    sell_time_diff_seconds: Optional[float] = Field(default=None, description="卖出时间差异（秒）")
    is_symbol_match: bool = Field(..., description="币种是否匹配")
    is_buy_time_match: bool = Field(..., description="买入时间是否匹配（3分钟内）")
    is_sell_time_match: bool = Field(..., description="卖出时间是否匹配（3分钟内）")
    issues: List[str] = Field(default_factory=list, description="发现的问题列表")

class DailyVerificationResult(BaseModel):
    """每日校验结果"""
    verification_date: date = Field(..., description="校验日期")
    verification_timestamp: datetime = Field(..., description="校验执行时间")
    status: VerificationStatus = Field(..., description="校验状态")
    strategy_name: Optional[str] = Field(default=None, description="策略名称")
    
    # 数量统计
    backtest_trade_count: int = Field(..., description="回测交易数量")
    signal_count: int = Field(..., description="实际信号数量")
    matched_count: int = Field(default=0, description="匹配的交易数量")
    
    # 详细比对结果
    comparison_items: List[ComparisonItem] = Field(default_factory=list, description="详细比对项列表")
    
    # 问题汇总
    issues_summary: List[str] = Field(default_factory=list, description="问题汇总")
    
    # 执行信息
    execution_time_seconds: float = Field(..., description="执行耗时（秒）")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    
    # 元数据
    config_used: Dict[str, Any] = Field(default_factory=dict, description="使用的配置信息")
    backtest_result_path: Optional[str] = Field(default=None, description="回测结果文件路径")

class MultiStrategyVerificationResult(BaseModel):
    """多策略校验汇总结果 (新增)"""
    verification_date: date = Field(..., description="校验日期")
    verification_timestamp: datetime = Field(..., description="校验执行时间")
    verification_mode: str = Field(..., description="校验模式")
    
    # 策略统计
    total_strategies: int = Field(..., description="总策略数量")
    success_strategies: int = Field(default=0, description="成功策略数量")
    failed_strategies: int = Field(default=0, description="失败策略数量")
    success_rate: float = Field(default=0.0, description="成功率")
    
    # 详细结果
    strategy_results: List[DailyVerificationResult] = Field(default_factory=list, description="各策略详细结果")
    
    # 汇总信息
    total_execution_time_seconds: float = Field(..., description="总执行耗时（秒）")
    failed_strategy_names: List[str] = Field(default_factory=list, description="失败策略名称列表")
    overall_issues: List[str] = Field(default_factory=list, description="整体问题汇总")
```

#### 2.2.2 策略配置模型 (新增)

```python
# workflows/daily_backtest_verification/models/strategy_config.py
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from enum import Enum

class StrategySelectionMode(str, Enum):
    """策略选择模式"""
    SINGLE_STRATEGY = "single_strategy"           # 单个策略（必须指定策略名称）
    ALL_ENABLED_STRATEGIES = "all_enabled"        # 所有已启用策略

class StrategyConfig(BaseModel):
    """策略配置模型"""
    strategy_name: str = Field(..., description="策略名称")
    enabled: bool = Field(default=True, description="是否启用")
    buy_strategies: List[Dict[str, Any]] = Field(default_factory=list, description="买入策略配置")
    config_data: Dict[str, Any] = Field(default_factory=dict, description="完整配置数据")
    
class StrategySelectionConfig(BaseModel):
    """策略选择配置"""
    mode: StrategySelectionMode = Field(..., description="选择模式")
    specific_strategy_name: Optional[str] = Field(default=None, description="指定的策略名称（SINGLE_STRATEGY模式下必须提供）")
    selected_strategies: List[StrategyConfig] = Field(default_factory=list, description="选中的策略列表")
```

#### 2.2.3 工作流数据传递模型

```python
# workflows/daily_backtest_verification/models/workflow_data.py
from typing import Optional, Dict, Any, List
from datetime import date
from pydantic import BaseModel

class WorkflowDataContext(BaseModel):
    """工作流数据传递上下文"""
    verification_date: date
    temp_config_path: Optional[str] = None
    backtest_result_path: Optional[str] = None
    parsed_backtest_trades: List[Dict[str, Any]] = []
    fetched_signals: List[Dict[str, Any]] = []
    verification_result: Optional[DailyVerificationResult] = None
    config_data: Optional[Dict[str, Any]] = None
    admin_chat_ids: List[str] = []
```

### 2.3 核心组件详细设计

#### 2.3.1 策略选择器 (StrategySelector) (新增)

**职责**: 根据指定模式选择和验证策略配置

```python
# workflows/daily_backtest_verification/components/strategy_selector.py
import logging
from typing import List, Optional, Dict, Any
from models.config import Config
from dao.config_dao import ConfigDAO
from ..models.strategy_config import StrategyConfig, StrategySelectionConfig, StrategySelectionMode

class StrategyNotSpecifiedError(Exception):
    """策略未指定异常"""
    pass

class StrategyNotFoundError(Exception):
    """策略未找到异常"""
    pass

class StrategyConfigInvalidError(Exception):
    """策略配置无效异常"""
    pass

class StrategySelector:
    """策略选择器"""
    
    def __init__(self):
        self.config_dao = ConfigDAO()
        self.logger = logging.getLogger(__name__)
    
    async def select_strategies(self, 
                              mode: StrategySelectionMode,
                              specific_strategy_name: Optional[str] = None) -> StrategySelectionConfig:
        """
        根据模式选择策略
        
        Args:
            mode: 策略选择模式
            specific_strategy_name: 指定的策略名称（SINGLE_STRATEGY模式下必须提供）
            
        Returns:
            StrategySelectionConfig: 策略选择配置
            
        Raises:
            StrategyNotSpecifiedError: 单策略模式下未指定策略名称
            StrategyNotFoundError: 指定的策略不存在
            StrategyConfigInvalidError: 策略配置无效
        """
        
        if mode == StrategySelectionMode.SINGLE_STRATEGY:
            if not specific_strategy_name or not specific_strategy_name.strip():
                raise StrategyNotSpecifiedError(
                    "SINGLE_STRATEGY模式下必须指定策略名称，请使用 --strategy 参数指定策略"
                )
            return await self._select_single_strategy(specific_strategy_name.strip())
        
        elif mode == StrategySelectionMode.ALL_ENABLED_STRATEGIES:
            return await self._select_all_enabled_strategies()
        
        else:
            raise ValueError(f"不支持的策略选择模式: {mode}")
    
    async def _select_single_strategy(self, strategy_name: str) -> StrategySelectionConfig:
        """选择单个策略"""
        strategy_config = await self._load_strategy_config(strategy_name)
        
        return StrategySelectionConfig(
            mode=StrategySelectionMode.SINGLE_STRATEGY,
            specific_strategy_name=strategy_name,
            selected_strategies=[strategy_config]
        )
    
    async def _select_all_enabled_strategies(self) -> StrategySelectionConfig:
        """选择所有已启用的策略"""
        all_strategies = await self._load_all_strategies()
        enabled_strategies = [s for s in all_strategies if s.enabled]
        
        if not enabled_strategies:
            raise StrategyNotFoundError("未找到任何已启用的策略")
        
        return StrategySelectionConfig(
            mode=StrategySelectionMode.ALL_ENABLED_STRATEGIES,
            selected_strategies=enabled_strategies
        )
    
    async def _load_strategy_config(self, strategy_name: str) -> StrategyConfig:
        """加载指定策略配置"""
        config = await self.config_dao.get_config_by_name(strategy_name)
        if not config:
            raise StrategyNotFoundError(f"策略 '{strategy_name}' 不存在")
        
        return self._convert_to_strategy_config(config)
    
    async def _load_all_strategies(self) -> List[StrategyConfig]:
        """加载所有策略配置"""
        configs = await self.config_dao.get_all_configs()
        return [self._convert_to_strategy_config(config) for config in configs]
    
    def _convert_to_strategy_config(self, config: Config) -> StrategyConfig:
        """转换配置为策略配置模型"""
        try:
            config_data = config.config_data if hasattr(config, 'config_data') else {}
            buy_strategies = config_data.get('buy_strategies', [])
            
            # 验证配置有效性
            if not buy_strategies:
                raise StrategyConfigInvalidError(f"策略 '{config.name}' 缺少买入策略配置")
            
            return StrategyConfig(
                strategy_name=config.name,
                enabled=getattr(config, 'enabled', True),
                buy_strategies=buy_strategies,
                config_data=config_data
            )
        except Exception as e:
            raise StrategyConfigInvalidError(f"策略 '{config.name}' 配置格式无效: {str(e)}")
```

#### 2.3.2 配置读取器 (ConfigReader)

**职责**: 从 MongoDB config 表中读取 kol_activity 配置并生成临时文件

```python
# workflows/daily_backtest_verification/components/config_reader.py
import json
import tempfile
import os
from typing import Tuple, Dict, Any, List
from models.config import Config
from dao.config_dao import ConfigDAO

class ConfigReader:
    """配置读取器"""
    
    def __init__(self):
        self.config_dao = ConfigDAO()
        self.logger = logging.getLogger(__name__)
    
    async def read_kol_activity_config(self) -> Tuple[Dict[str, Any], str]:
        """
        读取kol_activity配置并生成临时文件
        
        Returns:
            Tuple[Dict[str, Any], str]: (配置数据, 临时文件路径)
            
        Raises:
            ConfigNotFoundError: 配置不存在
            ConfigInvalidError: 配置格式无效
        """
        
    async def read_admin_telegram_config(self) -> List[str]:
        """
        读取管理员Telegram配置
        
        Returns:
            List[str]: 管理员Chat ID列表
        """
    
    def create_temp_config_file(self, config_data: Dict[str, Any]) -> str:
        """
        创建临时配置文件
        
        Args:
            config_data: 配置数据
            
        Returns:
            str: 临时文件路径
        """
    
    def cleanup_temp_file(self, file_path: str) -> None:
        """清理临时文件"""
```

#### 2.3.2 回测执行器 (BacktestExecutor)

**职责**: 执行回测命令并等待结果

```python
# workflows/daily_backtest_verification/components/backtest_executor.py
import asyncio
import subprocess
import os
import time
from typing import Tuple, Optional

class BacktestExecutionError(Exception):
    """回测执行异常"""
    pass

class BacktestExecutor:
    """回测执行器"""
    
    def __init__(self, timeout_seconds: int = 600):
        self.timeout_seconds = timeout_seconds
        self.logger = logging.getLogger(__name__)
    
    async def execute_backtest(self, config_file_path: str) -> Tuple[str, bool]:
        """
        执行回测命令
        
        Args:
            config_file_path: 配置文件路径
            
        Returns:
            Tuple[str, bool]: (结果文件路径, 是否成功)
            
        Raises:
            BacktestExecutionError: 回测执行失败
        """
    
    def _build_command(self, config_file_path: str) -> List[str]:
        """构建回测命令"""
        return [
            "python", "run_backtest_ed.py", 
            "--mode", "single_v2",
            "--config", config_file_path
        ]
    
    async def _wait_for_result_file(self, expected_path: str, 
                                   max_wait_seconds: int = 120) -> bool:
        """等待结果文件生成"""
```

#### 2.3.3 结果解析器 (ResultParser)

**职责**: 解析回测结果文件，提取交易信息

```python
# workflows/daily_backtest_verification/components/result_parser.py
import json
from typing import List, Dict, Any, Optional
from datetime import datetime

class ResultParseError(Exception):
    """结果解析异常"""
    pass

class BacktestTrade(BaseModel):
    """回测交易记录"""
    symbol: str
    buy_time: datetime
    sell_time: Optional[datetime] = None
    action: str  # 'buy' or 'sell'

class ResultParser:
    """回测结果解析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    async def parse_backtest_result(self, result_file_path: str) -> List[BacktestTrade]:
        """
        解析回测结果文件
        
        Args:
            result_file_path: 结果文件路径 (result.json)
            
        Returns:
            List[BacktestTrade]: 解析后的交易列表
            
        Raises:
            ResultParseError: 解析失败
        """
    
    def _extract_trades_from_json(self, result_data: Dict[str, Any]) -> List[BacktestTrade]:
        """从JSON数据中提取交易信息"""
    
    def _parse_datetime(self, datetime_str: str) -> datetime:
        """解析时间字符串"""
```

#### 2.3.4 信号获取器 (SignalFetcher)

**职责**: 从数据库获取当天的交易信号

```python
# workflows/daily_backtest_verification/components/signal_fetcher.py
from datetime import date, datetime, timedelta
from typing import List, Dict, Any
from models.signal import Signal
from dao.signal_dao import SignalDAO

class SignalData(BaseModel):
    """信号数据"""
    signal_id: str
    symbol: str
    signal_type: str  # 'kol_buy', 'sell'
    timestamp: datetime
    status: str

class SignalFetcher:
    """信号获取器"""
    
    def __init__(self):
        self.signal_dao = SignalDAO()
        self.logger = logging.getLogger(__name__)
    
    async def fetch_daily_signals(self, target_date: date) -> List[SignalData]:
        """
        获取指定日期的交易信号
        
        Args:
            target_date: 目标日期
            
        Returns:
            List[SignalData]: 信号数据列表
        """
    
    def _convert_signal_to_data(self, signal: Signal) -> SignalData:
        """转换Signal模型为SignalData"""
    
    def _get_date_range(self, target_date: date) -> Tuple[datetime, datetime]:
        """获取日期范围（UTC时间）"""
```

#### 2.3.5 比对分析器 (ComparisonAnalyzer)

**职责**: 执行多维度数据比对分析

```python
# workflows/daily_backtest_verification/components/comparison_analyzer.py
from datetime import timedelta
from typing import List, Tuple
from ..models.verification_result import ComparisonItem, VerificationStatus

class ComparisonAnalyzer:
    """比对分析器"""
    
    TIME_DIFF_THRESHOLD = timedelta(minutes=3)  # 3分钟阈值
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def analyze_comparison(self, 
                         backtest_trades: List[BacktestTrade],
                         signals: List[SignalData]) -> Tuple[List[ComparisonItem], VerificationStatus]:
        """
        执行比对分析
        
        Args:
            backtest_trades: 回测交易列表
            signals: 实际信号列表
            
        Returns:
            Tuple[List[ComparisonItem], VerificationStatus]: (比对结果, 整体状态)
        """
    
    def _match_trades_and_signals(self, 
                                trades: List[BacktestTrade], 
                                signals: List[SignalData]) -> List[ComparisonItem]:
        """匹配交易和信号"""
    
    def _analyze_time_difference(self, time1: datetime, time2: datetime) -> Tuple[float, bool]:
        """分析时间差异"""
    
    def _determine_overall_status(self, comparison_items: List[ComparisonItem]) -> VerificationStatus:
        """确定整体状态"""
```

#### 2.3.6 报告生成器 (ReportGenerator)

**职责**: 根据预定义模板生成格式化报告

```python
# workflows/daily_backtest_verification/components/report_generator.py
from datetime import date
from typing import List
from ..models.verification_result import DailyVerificationResult, VerificationStatus

class ReportGenerator:
    """报告生成器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def generate_verification_report(self, result: DailyVerificationResult) -> str:
        """
        生成校验报告
        
        Args:
            result: 校验结果
            
        Returns:
            str: 格式化的报告文本
        """
    
    def _format_status_emoji(self, status: VerificationStatus) -> str:
        """格式化状态表情"""
    
    def _format_comparison_details(self, result: DailyVerificationResult) -> str:
        """格式化比对详情"""
    
    def _format_time_difference(self, diff_seconds: Optional[float]) -> str:
        """格式化时间差异"""
```

#### 2.3.7 通知发送器 (NotificationSender)

**职责**: 发送 Telegram 通知

```python
# workflows/daily_backtest_verification/components/notification_sender.py
from typing import List
from utils.message_sender.message_sender import TelegramMessageSender

class NotificationSender:
    """通知发送器"""
    
    def __init__(self):
        self.telegram_sender = TelegramMessageSender()
        self.logger = logging.getLogger(__name__)
    
    async def send_verification_report(self, 
                                     report_text: str, 
                                     admin_chat_ids: List[str]) -> bool:
        """
        发送校验报告
        
        Args:
            report_text: 报告文本
            admin_chat_ids: 管理员Chat ID列表
            
        Returns:
            bool: 是否全部发送成功
        """
    
    async def send_error_notification(self, 
                                    error_message: str, 
                                    admin_chat_ids: List[str]) -> bool:
        """发送错误通知"""
```

## 3. 核心业务逻辑实现

### 3.1 主处理逻辑

```python
# workflows/daily_backtest_verification/main.py
import asyncio
import logging
from datetime import date, datetime
from typing import Dict, Any

from .components.config_reader import ConfigReader
from .components.backtest_executor import BacktestExecutor
from .components.result_parser import ResultParser
from .components.signal_fetcher import SignalFetcher
from .components.comparison_analyzer import ComparisonAnalyzer
from .components.report_generator import ReportGenerator
from .components.notification_sender import NotificationSender
from .models.verification_result import DailyVerificationResult, VerificationStatus

logger = logging.getLogger(__name__)

class DailyBacktestVerificationProcessor:
    """每日回测校验处理器"""
    
    def __init__(self):
        self.config_reader = ConfigReader()
        self.backtest_executor = BacktestExecutor()
        self.result_parser = ResultParser()
        self.signal_fetcher = SignalFetcher()
        self.comparison_analyzer = ComparisonAnalyzer()
        self.report_generator = ReportGenerator()
        self.notification_sender = NotificationSender()
        
    async def run_daily_verification(self, target_date: date = None) -> Dict[str, Any]:
        """
        执行每日回测校验的主流程
        
        Args:
            target_date: 目标日期，默认为今天
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        start_time = datetime.utcnow()
        verification_date = target_date or date.today()
        temp_config_path = None
        
        try:
            logger.info(f"开始执行每日回测校验 - {verification_date}")
            
            # 1. 读取配置和管理员信息
            config_data, temp_config_path = await self.config_reader.read_kol_activity_config()
            admin_chat_ids = await self.config_reader.read_admin_telegram_config()
            
            # 2. 执行回测
            result_file_path, success = await self.backtest_executor.execute_backtest(temp_config_path)
            if not success:
                raise Exception("回测执行失败")
            
            # 3. 解析回测结果
            backtest_trades = await self.result_parser.parse_backtest_result(result_file_path)
            
            # 4. 获取实际信号
            signals = await self.signal_fetcher.fetch_daily_signals(verification_date)
            
            # 5. 执行比对分析
            comparison_items, status = self.comparison_analyzer.analyze_comparison(
                backtest_trades, signals
            )
            
            # 6. 构建校验结果
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            verification_result = DailyVerificationResult(
                verification_date=verification_date,
                verification_timestamp=datetime.utcnow(),
                status=status,
                backtest_trade_count=len(backtest_trades),
                signal_count=len(signals),
                matched_count=len([item for item in comparison_items if item.is_symbol_match]),
                comparison_items=comparison_items,
                execution_time_seconds=execution_time,
                config_used=config_data,
                backtest_result_path=result_file_path
            )
            
            # 7. 生成报告
            report_text = self.report_generator.generate_verification_report(verification_result)
            
            # 8. 发送通知
            await self.notification_sender.send_verification_report(report_text, admin_chat_ids)
            
            logger.info(f"每日回测校验完成 - 状态: {status}")
            return {
                "status": "success",
                "verification_result": verification_result.dict(),
                "report_sent": True
            }
            
        except Exception as e:
            logger.error(f"每日回测校验失败: {e}", exc_info=True)
            
            # 发送错误通知
            try:
                admin_chat_ids = await self.config_reader.read_admin_telegram_config()
                error_message = f"每日回测校验失败\n日期: {verification_date}\n错误: {str(e)}"
                await self.notification_sender.send_error_notification(error_message, admin_chat_ids)
            except Exception as notify_error:
                logger.error(f"发送错误通知失败: {notify_error}")
            
            return {
                "status": "error",
                "error_message": str(e),
                "verification_date": str(verification_date)
            }
            
        finally:
            # 清理临时文件
            if temp_config_path:
                self.config_reader.cleanup_temp_file(temp_config_path)


async def main():
    """
    主入口函数，供外部调用
    """
    processor = DailyBacktestVerificationProcessor()
    return await processor.run_daily_verification()


if __name__ == "__main__":
    # 直接运行时的入口
    import sys
    from pathlib import Path
    
    # 添加项目根目录到路径
    project_root = Path(__file__).parent.parent.parent
    sys.path.insert(0, str(project_root))
    
    # 设置基本日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行校验
    result = asyncio.run(main())
    print(f"校验结果: {result}")
```

## 4. 部署方案

### 4.1 Crontab 入口脚本

```python
# scripts/daily_backtest_verification.py
#!/usr/bin/env python3
"""
每日回测校验 - Crontab 入口脚本

使用方式:
    python scripts/daily_backtest_verification.py
    
    # 指定日期运行（可选）
    python scripts/daily_backtest_verification.py --date 2025-01-12

Crontab 配置示例:
    # 每天上午9点执行每日回测校验
    0 9 * * * cd /path/to/meme_monitor && python scripts/daily_backtest_verification.py >> logs/daily_verification.log 2>&1
"""

import asyncio
import os
import sys
import logging
import argparse
from datetime import datetime, date
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv
from models import init_db

# 加载环境变量
load_dotenv()

# 确保日志目录存在
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/daily_backtest_verification.log')
    ]
)

logger = logging.getLogger(__name__)

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='每日回测校验工具')
    parser.add_argument(
        '--date', 
        type=str, 
        help='指定校验日期 (格式: YYYY-MM-DD)，默认为今天'
    )
    parser.add_argument(
        '--dry-run', 
        action='store_true', 
        help='干运行模式，不发送通知'
    )
    return parser.parse_args()

async def main():
    """主函数"""
    try:
        # 解析参数
        args = parse_arguments()
        
        # 解析日期
        target_date = None
        if args.date:
            try:
                target_date = datetime.strptime(args.date, '%Y-%m-%d').date()
            except ValueError:
                logger.error(f"无效的日期格式: {args.date}，请使用 YYYY-MM-DD 格式")
                return 1
        else:
            target_date = date.today()
        
        logger.info("=" * 60)
        logger.info(f"每日回测校验开始 - {datetime.now()}")
        logger.info(f"目标日期: {target_date}")
        if args.dry_run:
            logger.info("运行模式: 干运行（不发送通知）")
        logger.info("=" * 60)
        
        # 初始化数据库连接
        await init_db()
        logger.info("数据库连接已初始化")
        
        # 导入并执行校验逻辑
        from workflows.daily_backtest_verification.main import DailyBacktestVerificationProcessor
        
        processor = DailyBacktestVerificationProcessor()
        
        # 如果是干运行模式，可以在这里设置特殊标志
        if args.dry_run:
            # 可以通过环境变量或其他方式传递给处理器
            os.environ['DRY_RUN_MODE'] = 'true'
        
        result = await processor.run_daily_verification(target_date)
        
        if result.get("status") == "success":
            logger.info("每日回测校验成功完成")
            logger.info(f"处理结果概要: 回测交易数={result['verification_result']['backtest_trade_count']}, "
                       f"实际信号数={result['verification_result']['signal_count']}, "
                       f"状态={result['verification_result']['status']}")
            return 0
        else:
            logger.error(f"每日回测校验失败: {result.get('error_message', '未知错误')}")
            return 1
            
    except ImportError as e:
        logger.error(f"导入模块失败: {e}")
        logger.error("请确保项目依赖已正确安装")
        return 1
    except Exception as e:
        logger.error(f"每日回测校验执行异常: {e}", exc_info=True)
        return 1
    finally:
        # 清理环境变量
        if 'DRY_RUN_MODE' in os.environ:
            del os.environ['DRY_RUN_MODE']
            
        logger.info("=" * 60)
        logger.info(f"每日回测校验结束 - {datetime.now()}")
        logger.info("=" * 60)

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
```

### 4.2 部署配置

#### Crontab 配置

```bash
# 编辑 crontab
crontab -e

# 添加以下配置（每天上午9点执行）
0 9 * * * cd /path/to/meme_monitor && /usr/bin/python3 scripts/daily_backtest_verification.py >> logs/daily_verification.log 2>&1

# 可选：每天下午3点再执行一次作为备份校验
0 15 * * * cd /path/to/meme_monitor && /usr/bin/python3 scripts/daily_backtest_verification.py >> logs/daily_verification_backup.log 2>&1
```

#### 日志轮转配置

```bash
# /etc/logrotate.d/meme-monitor-verification
/path/to/meme_monitor/logs/daily_verification*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    copytruncate
}
```

## 5. 错误处理与监控

### 5.1 异常分类与处理策略

| 异常类型 | 处理策略 | 恢复机制 |
|---------|---------|---------|
| **配置读取失败** | 记录错误日志，发送通知，退出 | 人工检查配置 |
| **回测执行超时** | 等待60秒后重试一次，失败则退出 | 检查系统资源 |
| **结果文件不存在** | 等待2分钟后重试，失败则退出 | 检查回测进程 |
| **数据库连接失败** | 重试3次，每次间隔5秒 | 自动恢复 |
| **Telegram发送失败** | 重试2次，记录日志但不影响主流程 | 自动恢复 |

### 5.2 监控指标

- **执行成功率**: 每日执行成功/失败统计
- **执行耗时**: 平均执行时间趋势监控
- **比对一致性**: 匹配率趋势分析
- **通知送达率**: Telegram 通知成功率

## 6. 性能优化

### 6.1 执行效率优化

- **并发执行**: 信号获取和回测执行可并行处理
- **数据缓存**: 重复配置读取使用内存缓存
- **索引优化**: 确保 signals 表的时间和状态字段有合适索引
- **分页处理**: 大量信号数据分批处理，避免内存溢出

### 6.2 资源使用优化

- **内存管理**: 及时释放大对象，使用生成器处理大数据集
- **文件清理**: 及时清理临时文件和旧的回测结果
- **连接池**: 复用数据库连接，避免频繁建立连接

## 7. 扩展性设计

### 7.1 多策略支持预留

当前版本仅支持 kol_activity 策略，但架构设计支持未来扩展：

- **配置读取器**: 增加策略类型参数
- **比对分析器**: 支持不同策略的比对逻辑
- **报告生成器**: 支持多策略报告模板

### 7.2 通知渠道扩展

- **飞书通知**: 利用现有 FeishuMessageSender
- **邮件通知**: 添加邮件发送支持
- **企业微信**: 添加企业微信机器人支持

---

**关联链接 (Related Links):**
- **关联需求 (Related Requirement):** @daily_backtest_verification_requirements_ai.md
- **测试用例 (Test Cases):** @daily_backtest_verification_test_cases_ai.md
- **跟踪任务 (Tracked by):** @daily_backtest_verification_todo_list.md
- **功能增强记录:** 策略选择器增强 (2025-01-12) - 移除默认策略选项，强制指定策略名称