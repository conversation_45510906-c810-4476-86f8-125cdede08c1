# 交易延迟监控功能需求规格文档

## 1. 文档历史 (Document History)

| 版本 | 日期 | 修改人 | 修改内容 |
|-----------------|-----------------|------------------|----------------------|
| v1.0 | 2025-06-18 21:36:44 CST | AI产品经理 | 初版创建 |
| v1.1 | 2025-06-18 21:38:45 CST | AI产品经理 | 修正技术细节：KOL交易时间获取逻辑、数据采集范围 |
| v1.2 | 2025-06-18 22:30:00 CST | AI产品经理 | **[重要修复]** 添加交易金额过滤功能：修正KOL交易时间查找逻辑，增加transaction_min_amount与cost_usd的金额过滤机制，确保只有符合金额条件的KOL交易才能用于延迟计算 |

## 2. 功能概述 (Feature Overview)

### 2.1. 背景与问题陈述 (Background & Problem Statement)

在当前的memeMonitor系统中，我们能够监控KOL的交易活动并生成交易信号，也能够基于这些信号执行自动交易。然而，我们缺乏一个关键的性能指标：**从KOL交易发生到我们系统实际执行交易之间的延迟时间**。

这个延迟直接影响交易的盈利能力，特别是在高频交易场景下，即使几秒钟的延迟也可能导致显著的滑点和收益损失。目前，我们的回测系统无法准确模拟这种现实延迟，导致回测结果过于乐观，与实际交易表现存在差距。

### 2.2. 目标与成功指标 (Goals & Success Metrics)

**主要目标:** - 建立实时交易延迟监控体系，为每笔交易记录精确的延迟时间 - 为回测系统提供真实的延迟数据，提高回测准确性 - 为系统性能优化提供数据支撑

**成功指标:** - 系统能够捕获100%的交易延迟数据（覆盖率100%） - 延迟计算精度达到秒级 - 数据采集频率满足每10秒一次的要求 - 历史延迟数据可用于回测系统，提高回测结果的真实性

### 2.3. 核心价值 (Core Value Proposition)

通过准确测量和记录交易延迟，使memeMonitor系统能够更真实地评估和优化交易策略表现，为用户提供更可靠的投资决策依据。

## 3. 用户故事与范围 (User Stories & Scope)

### 3.1. 用户画像 (User Personas)

-   **策略研发人员**: 需要了解真实的交易延迟以优化策略参数
-   **回测分析师**: 需要在回测中使用真实延迟数据以获得准确结果
-   **系统运维人员**: 需要监控系统性能，识别延迟异常

### 3.2. 用户故事 (User Stories)

**US001**: 作为策略研发人员，我希望能够查看每笔交易的具体延迟时间，以便我能够了解策略的实际执行效率。

**US002**: 作为回测分析师，我希望回测系统能够使用历史真实延迟数据，以便我能够获得更准确的回测结果。

**US003**: 作为系统运维人员，我希望能够监控交易延迟的趋势变化，以便我能够及时发现和解决系统性能问题。

**US004**: 作为数据分析师，我希望能够导出延迟统计数据，以便我能够进行深度分析和报告生成。

### 3.3. 范围定义 (Scope Definition)

#### 范围内 (In Scope)

-   实时采集最新交易记录和对应的触发信号
-   计算并存储KOL交易时间与实际交易时间的延迟
-   每10秒运行一次的自动化数据采集工作流
-   延迟数据的持久化存储
-   基础的数据查询接口

#### 范围外 (Out of Scope)

-   延迟数据的可视化界面（留待后续迭代）
-   延迟异常的主动告警功能（留待后续迭代）
-   延迟优化的自动调整机制（留待后续迭代）
-   与第三方监控系统的集成（留待后续迭代）

## 4. 详细功能需求 (Functional Requirements)

### 4.1. 数据采集需求

**FR001**: 系统应每10秒执行一次数据采集任务 - 数据源：`trade_records` 集合中所有还未计算延迟的记录 - 筛选条件：在延迟记录表中不存在对应记录的交易 - 确保历史交易和新交易都能被处理

**FR002**: 系统应识别每笔交易对应的触发信号 - 通过交易记录中的 `signal_id` 字段关联对应的信号 - 从 `signals` 集合中获取信号的详细信息 - 特别关注信号中的 `hit_kol_wallets`、`trigger_timestamp` 和 `trigger_conditions.transaction_lookback_hours` 字段 - **DAO扩展需求**: TradeRecordDAO需要添加 `find_unprocessed_trades()` 方法，查询没有对应延迟记录的交易

**FR003**: 系统应通过复杂查询获取KOL最后交易时间 - 从信号的 `hit_kol_wallets` 字段获取相关KOL钱包地址列表 - 从 `kol_wallet_activities` 集合中查询这些钱包的活动记录 - 查询条件：
  - 钱包地址在 `hit_kol_wallets` 中
  - 交易时间在 `signal.trigger_timestamp` 之前的 `transaction_lookback_hours` 小时内
  - **[新增]** 交易金额过滤：`cost_usd` ≥ `signal.trigger_conditions.transaction_min_amount`
- 按时间戳排序，获取最后一个**符合金额条件**的交易时间戳作为KOL交易时间 - **DAO扩展需求**: KOLWalletActivityDAO需要添加以下方法： - `find_activities_in_time_range()`: 基于钱包地址、时间范围**和金额过滤**查询活动 - `find_last_activity_for_wallets()`: 查询指定钱包的最后活动

**FR004**: 系统应处理无法匹配信号或KOL活动的交易记录 - 对于无 `signal_id` 或信号已被删除的交易，记录为"信号缺失" - 对于无法找到对应KOL活动的交易，记录为"KOL活动缺失" - 在延迟记录中标记为特殊状态，便于后续分析

### 4.2. 延迟计算需求

**FR005**: 系统应准确计算交易延迟 - 延迟 = 交易记录的 `created_at` - 从KOL活动中获取的最后交易时间戳 - KOL活动时间戳需要从Unix时间戳转换为datetime格式 - 延迟值以秒为单位，保留到小数点后2位 - 负延迟值（理论上不应出现）应被标记为异常数据

**FR006**: 系统应识别和标记异常延迟数据 - 延迟超过600秒（10分钟）的记录标记为"异常长延迟" - 负延迟的记录标记为"时间戳异常" - 无法获取KOL交易时间戳的记录标记为"KOL活动缺失" - 无法获取信号信息的记录标记为"信号缺失"

### 4.3. 数据存储需求

**FR007**: 系统应创建专门的延迟记录数据模型 - 包含字段：交易ID、信号ID、KOL钱包地址列表、KOL最后交易时间戳、实际交易时间戳、延迟秒数、状态标记、创建时间 - 支持基于时间范围的高效查询 - 支持基于交易对、策略类型等维度的分析查询 - 建立与trade_record的唯一约束，防止重复计算

**FR008**: 系统应确保数据存储的可靠性 - 使用数据库事务确保数据一致性 - 实现重复数据检测，避免同一交易的重复延迟记录 - 提供数据修复机制处理异常中断的情况 - 支持批量处理，提高处理历史数据的效率

### 4.4. 工作流集成需求

**FR009**: 信号抑制检查功能 **[新增]**
- **需求描述**: 在计算延迟前检查信号是否被策略抑制机制影响，确保延迟计算的准确性
- **业务背景**: 
  - 策略配置中的 `same_token_notification_interval` 会抑制相同代币的重复信号
  - 当 `transaction_lookback_hours > same_token_notification_interval` 时，可能出现重复KOL买入记录触发的信号
  - 这类重复信号的延迟计算结果不准确，需要被过滤
- **检查逻辑**:
  1. 获取当前信号的策略名称、代币地址、KOL最后交易时间
  2. 查询已计算过延迟的相同策略、相同代币的历史信号
  3. 比较当前信号与最近历史信号的KOL交易时间间隔
  4. 如果间隔 ≤ `same_token_notification_interval`，判定为被抑制信号，跳过计算
  5. 如果间隔 > `same_token_notification_interval`，判定为独立信号，正常计算
- **预期效果**: 提高延迟计算的准确性，避免重复信号干扰
- **DAO扩展需求**: TradingDelayRecordDAO需要添加 `find_recent_records_by_strategy_token()` 方法，查询相同策略和代币的最近延迟记录

**FR010**: 系统应作为独立工作流运行 - 创建名为 `trading_delay_monitor` 的工作流 - 支持通过 `run_workflow.py` 脚本启动 - 提供标准的工作流配置文件（YAML格式）

**FR011**: 系统应提供灵活的调度配置 - 支持配置采集频率（默认10秒，可调整） - 支持配置数据保留周期（避免数据无限增长） - 支持运行时间窗口配置（例如只在交易时间运行） - 支持批处理模式，一次性处理大量历史数据

## 5. 非功能性需求 (Non-Functional Requirements)

### 5.1. 性能 (Performance)

-   单次数据采集任务应在3秒内完成
-   数据库查询响应时间应小于1秒
-   系统应能处理每分钟最多1000笔交易的延迟计算

### 5.2. 安全性 (Security)

-   延迟数据不包含敏感的交易金额信息
-   遵循现有的数据库访问权限控制
-   日志记录不应包含敏感的API密钥或凭据

### 5.3. 可用性 (Usability)

-   提供清晰的错误日志，便于问题诊断
-   支持优雅停机，避免数据采集中断造成数据不一致
-   提供基础的运行状态检查机制

### 5.4. 可维护性 (Maintainability)

-   代码结构清晰，遵循项目现有的架构模式
-   提供充分的单元测试覆盖
-   文档完整，便于后续开发人员理解和扩展

## 6. 假设与依赖 (Assumptions & Dependencies)

### 6.1. 假设 (Assumptions)

-   交易记录和信号记录的时间戳字段是准确可靠的
-   系统时钟与数据库时钟保持同步
-   现有的数据库性能足以支持新增的查询负载
-   KOL交易时间戳在信号记录中是可用且格式正确的

### 6.2. 依赖 (Dependencies)

-   **数据依赖**: 依赖现有的 `trade_records`、`signals` 和 `kol_wallet_activities` 集合数据
-   **技术依赖**: 依赖现有的工作流引擎和数据库连接框架
-   **模块依赖**: 依赖现有的DAO层和数据模型结构
-   **运行依赖**: 依赖MongoDB数据库和工作流调度系统的正常运行
-   **查询依赖**: 依赖复杂的跨集合关联查询能力

## 7. 技术实现细节 (Technical Implementation Details)

### 7.1. KOL最后交易时间获取算法

1.  **步骤1**: 从 `trade_record.signal_id` 获取对应的 `Signal` 记录
2.  **步骤2**: 从 `Signal.hit_kol_wallets` 获取KOL钱包地址列表
3.  **步骤3**: 从 `Signal.trigger_conditions.transaction_lookback_hours` 获取回溯时间窗口
4.  **步骤4**: **[新增]** 从 `Signal.trigger_conditions.transaction_min_amount` 获取最小交易金额限制
5.  **步骤5**: 计算查询时间范围：
    -   结束时间：`Signal.trigger_timestamp`
    -   开始时间：`Signal.trigger_timestamp - transaction_lookback_hours * 3600` 秒
6.  **步骤6**: 在 `kol_wallet_activities` 集合中查询：
    -   `wallet` 在 `hit_kol_wallets` 列表中
    -   `timestamp` 在计算的时间范围内
    -   **[新增]** `cost_usd`（字符串） ≥ `transaction_min_amount`（需转换为数字比较）
    -   按 `timestamp` 降序排序，取第一条**符合所有条件**的记录
7.  **步骤7**: 将Unix时间戳转换为datetime，作为KOL最后交易时间

**金额过滤逻辑说明**:
- `cost_usd` 字段存储为字符串格式，需要转换为浮点数进行比较
- 只有 `float(cost_usd) >= transaction_min_amount` 的KOL活动才符合触发条件
- 如果所有KOL活动都不满足金额条件，返回"无符合条件的KOL活动"错误

### 7.2. 信号抑制检查算法 **[新增]**

**目标**: 过滤因抑制机制产生的重复信号，确保延迟计算准确性

**算法流程**:
1.  **步骤1**: 获取当前信号的关键信息
    -   策略名称：`Signal.strategy_name`  
    -   代币地址：`Signal.token_address`
    -   same_token_notification_interval：从策略配置中获取
2.  **步骤2**: 计算当前信号的KOL最后交易时间（使用7.1算法）
3.  **步骤3**: 查询历史相关信号
    -   查询条件：相同 `strategy_name` 和 `token_address` 的已计算延迟记录
    -   时间范围：在当前信号时间戳之前的合理范围内
    -   按创建时间降序排序，获取最近的一条记录
4.  **步骤4**: 执行抑制检查
    -   计算时间间隔：`当前信号KOL时间 - 历史信号KOL时间`
    -   如果间隔 ≤ `same_token_notification_interval` 分钟：标记为抑制信号，跳过计算
    -   如果间隔 > `same_token_notification_interval` 分钟：判定为独立信号，继续计算
5.  **步骤5**: 记录检查结果
    -   在延迟记录中添加 `suppression_check_result` 字段
    -   记录检查状态：`passed`（通过）、`suppressed`（被抑制）、`no_history`（无历史记录）

**特殊情况处理**:
-   **无历史记录**: 首次出现的策略-代币组合，直接通过检查
-   **配置缺失**: 无法获取 `same_token_notification_interval` 时，记录警告但继续计算
-   **时间异常**: KOL交易时间异常时，跳过抑制检查，按原有逻辑处理

### 7.3. 数据处理优化策略

-   **批量处理**: 一次查询多个交易记录，减少数据库往返次数
-   **缓存机制**: 对相同signal_id的查询结果进行临时缓存
-   **并发控制**: 限制同时处理的交易记录数量，避免数据库过载

## 8. 边界条件与异常处理 (Edge Cases & Exception Handling)

### 8.1. 边界条件

-   **冷启动场景**: 系统首次运行时，可能有大量历史交易需要处理
-   **KOL活动缺失**: 在指定时间窗口内找不到对应的KOL钱包活动
-   **时间戳格式**: 处理不同格式的时间戳（Unix时间戳 vs datetime）
-   **空数据处理**: hit_kol_wallets为空或transaction_lookback_hours未设置的情况

### 8.2. 异常处理

-   **数据库连接失败**: 实现重试机制，记录错误日志
-   **数据格式异常**: 对不符合预期格式的时间戳进行容错处理
-   **工作流中断**: 支持从中断点恢复，避免数据重复或丢失
-   **关联数据缺失**: 优雅处理signal或KOL活动不存在的情况

------------------------------------------------------------------------

**关联链接 (Related Links):** - **跟踪任务 (Tracked by)**: @trading_delay_monitor_todo_list.md - **实现方案 (Development Plan)**: (待创建) - **测试用例 (Test Cases)**: (待创建)