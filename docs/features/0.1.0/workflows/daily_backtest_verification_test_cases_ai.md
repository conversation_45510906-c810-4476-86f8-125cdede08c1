# 每日回测校验功能 - 测试用例设计

## 1. 测试策略概述

### 1.1 测试目标

确保每日回测校验功能能够：
- 准确读取和解析配置数据
- **正确解析命令行参数并选择策略 (新增)**
- **支持单策略和多策略校验模式 (新增)**
- 正确执行回测命令并等待结果
- 精确解析回测结果并提取交易信息
- 可靠获取当天的交易信号数据
- 准确执行多维度数据比对分析
- 正确生成格式化的校验报告
- **生成多策略汇总报告 (新增)**
- **实现策略级别的错误隔离 (新增)**
- 成功发送 Telegram 通知

### 1.2 测试范围

**包含范围 (In Scope)**:
- 所有核心组件的功能测试
- 组件间集成测试
- 端到端工作流测试
- 异常处理和错误恢复测试
- 性能和超时测试
- 数据一致性验证测试

**排除范围 (Out of Scope)**:
- 外部系统（MongoDB、Telegram API）的内部实现测试
- 回测引擎本身的算法正确性测试
- 大规模并发性能测试
- 生产环境部署测试

### 1.3 测试方法

- **单元测试**: 测试各个组件的独立功能
- **集成测试**: 测试组件间的协作和数据传递
- **端到端测试**: 测试完整的工作流执行
- **模拟测试**: 使用 Mock 对象模拟外部依赖
- **边界测试**: 测试极端情况和边界条件

## 2. 测试环境与前置条件

### 2.1 测试环境配置

```yaml
测试环境:
  数据库: MongoDB (测试专用实例)
  Python版本: 3.11+
  依赖库: pytest, pytest-asyncio, unittest.mock
  模拟服务: MockTelegramAPI, MockFileSystem
  测试数据: 预定义的配置和信号数据集
```

### 2.2 测试数据准备

```python
# 测试配置数据
TEST_KOL_ACTIVITY_CONFIG = {
    "type": "kol_activity",
    "data": {
        "buy_strategies": [{
            "strategy_name": "test_strategy",
            "transaction_lookback_hours": 24,
            "transaction_min_amount": 1000.0,
            "kol_account_min_count": 3,
            "sell_strategy_hours": 24,
            "sell_kol_ratio": 0.5
        }]
    }
}

# 测试信号数据
TEST_SIGNALS = [
    {
        "token_address": "83mCRQJzvKMeQd9wJbZDUCTPgRbZMDoPdMSx5Sf1pump",
        "signal_type": "kol_buy",
        "trigger_timestamp": datetime(2025, 6, 24, 18, 0, 1),
        "status": "open"
    },
    {
        "token_address": "83mCRQJzvKMeQd9wJbZDUCTPgRbZMDoPdMSx5Sf1pump", 
        "signal_type": "sell",
        "trigger_timestamp": datetime(2025, 6, 24, 18, 1, 1),
        "status": "sold"
    }
]

# 测试回测结果数据
TEST_BACKTEST_RESULT = {
    "trades": [
        {
            "symbol": "83mCRQJzvKMeQd9wJbZDUCTPgRbZMDoPdMSx5Sf1pump",
            "buy_time": "2025-06-24T18:00:01Z",
            "sell_time": "2025-06-24T18:01:01Z",
            "action": "complete"
        }
    ],
    "statistics": {
        "total_trades": 1,
        "win_rate": 0.6,
        "return_rate": 0.15
    }
}
```

## 3. 单元测试用例

### 3.1 策略选择器 (StrategySelector) 测试 (新增)

#### 测试用例 3.1.1: 单策略选择
```python
async def test_select_single_strategy_success():
    """测试成功选择单个策略"""
    # Given: 数据库中存在指定名称的策略配置
    # When: 调用 select_strategies(strategy_name="kol_activity")
    # Then: 返回包含该策略的 StrategySelectionConfig
    # And: 选择模式为 SINGLE_STRATEGY
```

#### 测试用例 3.1.2: 单策略不存在
```python
async def test_select_single_strategy_not_found():
    """测试选择不存在的策略"""
    # Given: 数据库中不存在指定名称的策略
    # When: 调用 select_strategies(strategy_name="non_existent")
    # Then: 抛出 StrategyNotFoundError 异常
```

#### 测试用例 3.1.3: 所有已启用策略选择
```python
async def test_select_all_enabled_strategies():
    """测试选择所有已启用策略"""
    # Given: 数据库中存在多个已启用的策略配置
    # When: 调用 select_strategies(all_enabled=True)
    # Then: 返回包含所有已启用策略的配置
    # And: 选择模式为 ALL_ENABLED_STRATEGIES
```

#### 测试用例 3.1.4: 无已启用策略
```python
async def test_select_all_enabled_strategies_none_found():
    """测试没有已启用策略的情况"""
    # Given: 数据库中所有策略都被禁用
    # When: 调用 select_strategies(all_enabled=True)
    # Then: 抛出 NoEnabledStrategiesError 异常
```

#### 测试用例 3.1.5: 强制指定策略验证
```python
async def test_require_strategy_specification():
    """测试强制指定策略验证"""
    # Given: 策略选择器实例
    strategy_selector = StrategySelector()
    
    # When: 调用 select_strategies() 且模式为 SINGLE_STRATEGY 但未指定策略名称
    with pytest.raises(StrategyNotSpecifiedError) as exc_info:
        await strategy_selector.select_strategies(
            mode=StrategySelectionMode.SINGLE_STRATEGY,
            specific_strategy_name=None
        )
    
    # Then: 抛出 StrategyNotSpecifiedError 异常
    # And: 错误信息提示必须指定策略名称
    assert "SINGLE_STRATEGY模式下必须指定策略名称" in str(exc_info.value)
    assert "--strategy 参数" in str(exc_info.value)
```

#### 测试用例 3.1.6: 空字符串策略名称验证
```python
async def test_empty_strategy_name_validation():
    """测试空字符串策略名称验证"""
    # Given: 策略选择器实例
    strategy_selector = StrategySelector()
    
    # When: 调用 select_strategies() 且策略名称为空字符串
    with pytest.raises(StrategyNotSpecifiedError) as exc_info:
        await strategy_selector.select_strategies(
            mode=StrategySelectionMode.SINGLE_STRATEGY,
            specific_strategy_name=""
        )
    
    # Then: 抛出 StrategyNotSpecifiedError 异常
    assert "SINGLE_STRATEGY模式下必须指定策略名称" in str(exc_info.value)
```

#### 测试用例 3.1.7: 仅空白字符策略名称验证
```python
async def test_whitespace_strategy_name_validation():
    """测试仅包含空白字符的策略名称验证"""
    # Given: 策略选择器实例
    strategy_selector = StrategySelector()
    
    # When: 调用 select_strategies() 且策略名称仅包含空白字符
    with pytest.raises(StrategyNotSpecifiedError) as exc_info:
        await strategy_selector.select_strategies(
            mode=StrategySelectionMode.SINGLE_STRATEGY,
            specific_strategy_name="   "
        )
    
    # Then: 抛出 StrategyNotSpecifiedError 异常
    assert "SINGLE_STRATEGY模式下必须指定策略名称" in str(exc_info.value)
```

#### 测试用例 3.1.6: 策略配置验证
```python
async def test_validate_strategy_config():
    """测试策略配置验证"""
    # Given: 包含无效买入策略的配置
    # When: 调用策略选择方法
    # Then: 抛出 StrategyConfigInvalidError 异常
```

### 3.2 配置读取器 (ConfigReader) 测试

#### 测试用例 3.2.1: 正常配置读取
```python
async def test_read_kol_activity_config_success():
    """测试成功读取 kol_activity 配置"""
    # Given: 数据库中存在有效的 kol_activity 配置
    # When: 调用 read_kol_activity_config()
    # Then: 返回配置数据和临时文件路径
    # And: 临时文件内容正确
```

#### 测试用例 3.2.2: 配置不存在
```python
async def test_read_kol_activity_config_not_found():
    """测试配置不存在的情况"""
    # Given: 数据库中不存在 kol_activity 配置
    # When: 调用 read_kol_activity_config()
    # Then: 抛出 ConfigNotFoundError 异常
```

#### 测试用例 3.2.3: 配置格式无效
```python
async def test_read_kol_activity_config_invalid_format():
    """测试配置格式无效的情况"""
    # Given: 数据库中存在格式错误的配置
    # When: 调用 read_kol_activity_config()  
    # Then: 抛出 ConfigInvalidError 异常
```

#### 测试用例 3.2.4: 管理员配置读取
```python
async def test_read_admin_telegram_config():
    """测试读取管理员 Telegram 配置"""
    # Given: 数据库中存在 application_config 配置
    # When: 调用 read_admin_telegram_config()
    # Then: 返回正确的 Chat ID 列表
```

#### 测试用例 3.2.5: 临时文件创建和清理
```python
def test_create_and_cleanup_temp_file():
    """测试临时文件创建和清理"""
    # Given: 有效的配置数据
    # When: 创建临时文件并写入数据
    # Then: 文件被正确创建且内容正确
    # When: 调用清理方法
    # Then: 文件被正确删除
```

#### 测试用例 3.2.6: 多策略配置读取 (新增)
```python
async def test_read_multiple_strategy_configs():
    """测试读取多个策略配置"""
    # Given: 数据库中存在多个策略配置
    # When: 调用 read_strategy_configs(strategy_names=["strategy1", "strategy2"])
    # Then: 返回所有策略的配置数据和临时文件路径
    # And: 每个策略都有对应的临时文件
```

### 3.2 回测执行器 (BacktestExecutor) 测试

#### 测试用例 3.2.1: 回测执行成功
```python
async def test_execute_backtest_success():
    """测试回测成功执行"""
    # Given: 有效的配置文件路径
    # When: 调用 execute_backtest()
    # Then: 返回结果文件路径和成功状态
    # And: 结果文件存在且可访问
```

#### 测试用例 3.2.2: 回测执行超时
```python
async def test_execute_backtest_timeout():
    """测试回测执行超时"""
    # Given: 配置了短超时时间的执行器
    # When: 执行一个长时间运行的回测
    # Then: 抛出 BacktestExecutionError 异常
    # And: 异常信息包含超时描述
```

#### 测试用例 3.2.3: 回测命令失败
```python
async def test_execute_backtest_command_failure():
    """测试回测命令执行失败"""
    # Given: 无效的配置文件路径
    # When: 调用 execute_backtest()
    # Then: 抛出 BacktestExecutionError 异常
```

#### 测试用例 3.2.4: 结果文件等待机制
```python
async def test_wait_for_result_file():
    """测试结果文件等待机制"""
    # Given: 预期的结果文件路径
    # When: 文件在等待期间创建
    # Then: 方法返回 True
    # When: 文件在超时期间未创建
    # Then: 方法返回 False
```

### 3.3 结果解析器 (ResultParser) 测试

#### 测试用例 3.3.1: 正常结果解析
```python
async def test_parse_backtest_result_success():
    """测试成功解析回测结果"""
    # Given: 包含有效交易数据的结果文件
    # When: 调用 parse_backtest_result()
    # Then: 返回正确的交易列表
    # And: 交易时间正确解析
```

#### 测试用例 3.3.2: 文件不存在
```python
async def test_parse_backtest_result_file_not_found():
    """测试结果文件不存在"""
    # Given: 不存在的文件路径
    # When: 调用 parse_backtest_result()
    # Then: 抛出 ResultParseError 异常
```

#### 测试用例 3.3.3: JSON 格式错误
```python
async def test_parse_backtest_result_invalid_json():
    """测试 JSON 格式错误"""
    # Given: 包含无效 JSON 的文件
    # When: 调用 parse_backtest_result()
    # Then: 抛出 ResultParseError 异常
```

#### 测试用例 3.3.4: 缺少必要字段
```python
async def test_parse_backtest_result_missing_fields():
    """测试缺少必要字段"""
    # Given: 缺少 trades 字段的结果文件
    # When: 调用 parse_backtest_result()
    # Then: 抛出 ResultParseError 异常
```

#### 测试用例 3.3.5: 时间格式解析
```python
def test_parse_datetime_formats():
    """测试多种时间格式解析"""
    # Given: 不同格式的时间字符串
    # When: 调用 _parse_datetime()
    # Then: 正确解析为 datetime 对象
    # Test: ISO格式、带毫秒格式、UTC格式等
```

### 3.4 信号获取器 (SignalFetcher) 测试

#### 测试用例 3.4.1: 正常信号获取
```python
async def test_fetch_daily_signals_success():
    """测试成功获取当天信号"""
    # Given: 数据库中存在当天的信号数据
    # When: 调用 fetch_daily_signals()
    # Then: 返回正确的信号列表
    # And: 时间范围过滤正确
```

#### 测试用例 3.4.2: 无信号数据
```python
async def test_fetch_daily_signals_empty():
    """测试无信号数据的情况"""
    # Given: 数据库中不存在当天的信号数据
    # When: 调用 fetch_daily_signals()
    # Then: 返回空列表
```

#### 测试用例 3.4.3: 时间范围边界测试
```python
async def test_fetch_daily_signals_time_boundary():
    """测试时间范围边界"""
    # Given: 跨越日期边界的信号数据
    # When: 调用 fetch_daily_signals()
    # Then: 只返回指定日期范围内的信号
```

#### 测试用例 3.4.4: 信号状态过滤
```python
async def test_fetch_daily_signals_status_filter():
    """测试信号状态过滤"""
    # Given: 不同状态的信号数据
    # When: 调用 fetch_daily_signals()
    # Then: 正确过滤出需要的状态信号
```

### 3.5 比对分析器 (ComparisonAnalyzer) 测试

#### 测试用例 3.5.1: 完全匹配场景
```python
def test_analyze_comparison_perfect_match():
    """测试完全匹配的比对分析"""
    # Given: 回测交易和信号完全匹配
    # When: 调用 analyze_comparison()
    # Then: 返回 SUCCESS 状态
    # And: 所有比对项均匹配
```

#### 测试用例 3.5.2: 数量不匹配
```python
def test_analyze_comparison_count_mismatch():
    """测试数量不匹配的比对分析"""
    # Given: 回测交易数量与信号数量不同
    # When: 调用 analyze_comparison()
    # Then: 返回 WARNING 或 FAILURE 状态
    # And: 记录数量差异问题
```

#### 测试用例 3.5.3: 币种不匹配
```python
def test_analyze_comparison_symbol_mismatch():
    """测试币种不匹配的比对分析"""
    # Given: 回测交易币种与信号币种不同
    # When: 调用 analyze_comparison()
    # Then: 返回 FAILURE 状态
    # And: 记录币种不匹配问题
```

#### 测试用例 3.5.4: 时间差异测试
```python
def test_analyze_comparison_time_difference():
    """测试时间差异分析"""
    # Given: 不同时间差异的交易对
    # When: 调用 analyze_comparison()
    # Then: 正确计算时间差异
    # And: 根据3分钟阈值判断匹配状态
    
    # Test Case 1: 1分钟差异 - 应该匹配
    # Test Case 2: 5分钟差异 - 应该不匹配
    # Test Case 3: 恰好3分钟差异 - 边界测试
```

#### 测试用例 3.5.5: 复杂匹配场景
```python
def test_analyze_comparison_complex_matching():
    """测试复杂的匹配场景"""
    # Given: 多个交易和信号的复杂匹配
    # When: 调用 analyze_comparison()
    # Then: 正确执行最优匹配算法
    # And: 产生合理的比对结果
```

### 3.6 报告生成器 (ReportGenerator) 测试

#### 测试用例 3.6.1: 成功状态报告
```python
def test_generate_verification_report_success():
    """测试生成成功状态报告"""
    # Given: SUCCESS 状态的校验结果
    # When: 调用 generate_verification_report()
    # Then: 生成包含 ✅ 的格式化报告
    # And: 包含正确的统计信息
```

#### 测试用例 3.6.2: 失败状态报告
```python
def test_generate_verification_report_failure():
    """测试生成失败状态报告"""
    # Given: FAILURE 状态的校验结果
    # When: 调用 generate_verification_report()
    # Then: 生成包含 ❌ 的格式化报告
    # And: 包含详细的问题描述
```

#### 测试用例 3.6.3: 报告格式验证
```python
def test_generate_verification_report_format():
    """测试报告格式正确性"""
    # Given: 标准的校验结果数据
    # When: 调用 generate_verification_report()
    # Then: 报告包含所有必要的章节
    # And: 格式符合模板要求
```

#### 测试用例 3.6.4: 时间格式化测试
```python
def test_format_time_difference():
    """测试时间差异格式化"""
    # Given: 不同的时间差异值
    # When: 调用 _format_time_difference()
    # Then: 返回易读的格式化字符串
    # Test: 秒、分钟、小时的格式化
```

### 3.7 多策略汇总器 (MultiStrategyAggregator) 测试 (新增)

#### 测试用例 3.7.1: 多策略结果汇总
```python
async def test_aggregate_multiple_strategy_results():
    """测试汇总多个策略的校验结果"""
    # Given: 多个策略的校验结果
    # When: 调用 aggregate_results(strategy_results)
    # Then: 返回 MultiStrategyVerificationResult
    # And: 包含正确的统计信息和成功率
```

#### 测试用例 3.7.2: 策略错误隔离
```python
async def test_strategy_error_isolation():
    """测试策略级别的错误隔离"""
    # Given: 一个策略失败，其他策略成功
    # When: 调用汇总方法
    # Then: 失败策略不影响其他策略的结果
    # And: 汇总报告正确标识失败策略
```

#### 测试用例 3.7.3: 成功率计算
```python
def test_calculate_success_rate():
    """测试成功率计算"""
    # Given: 不同数量的成功和失败策略
    # When: 计算成功率
    # Then: 返回正确的百分比
    # Test: 边界情况（全成功、全失败、部分成功）
```

#### 测试用例 3.7.4: 失败策略汇总
```python
def test_summarize_failed_strategies():
    """测试失败策略汇总"""
    # Given: 包含失败策略的结果列表
    # When: 调用失败策略汇总方法
    # Then: 返回失败策略名称和错误原因
```

#### 测试用例 3.7.5: 空结果处理
```python
def test_handle_empty_results():
    """测试空结果列表处理"""
    # Given: 空的策略结果列表
    # When: 调用汇总方法
    # Then: 抛出 NoStrategyResultsError 异常
```

### 3.8 通知发送器 (NotificationSender) 测试

#### 测试用例 3.8.1: 成功发送通知
```python
async def test_send_verification_report_success():
    """测试成功发送校验报告"""
    # Given: 有效的报告文本和 Chat ID 列表
    # When: 调用 send_verification_report()
    # Then: 返回 True
    # And: 所有 Chat ID 都收到消息
```

#### 测试用例 3.8.2: 部分发送失败
```python
async def test_send_verification_report_partial_failure():
    """测试部分发送失败"""
    # Given: 部分无效的 Chat ID
    # When: 调用 send_verification_report()
    # Then: 返回 False
    # And: 记录发送失败的详细信息
```

#### 测试用例 3.8.3: 错误通知发送
```python
async def test_send_error_notification():
    """测试错误通知发送"""
    # Given: 错误信息和 Chat ID 列表
    # When: 调用 send_error_notification()
    # Then: 成功发送错误通知
    # And: 消息格式正确
```

#### 测试用例 3.8.4: Telegram API 异常
```python
async def test_send_notification_api_exception():
    """测试 Telegram API 异常处理"""
    # Given: Mock 的 TelegramMessageSender 抛出异常
    # When: 调用发送方法
    # Then: 正确处理异常并记录日志
    # And: 返回适当的失败状态
```

## 4. 集成测试用例

### 4.1 组件集成测试

#### 测试用例 4.1.1: 配置读取到回测执行
```python
async def test_config_to_backtest_integration():
    """测试配置读取到回测执行的集成"""
    # Given: 完整的配置数据
    # When: 依次调用配置读取器和回测执行器
    # Then: 数据正确传递，回测成功执行
```

#### 测试用例 4.1.2: 结果解析到比对分析
```python
async def test_parse_to_analysis_integration():
    """测试结果解析到比对分析的集成"""
    # Given: 回测结果文件和信号数据
    # When: 依次调用结果解析器和比对分析器
    # Then: 数据正确传递，分析结果准确
```

#### 测试用例 4.1.3: 比对分析到报告生成
```python
def test_analysis_to_report_integration():
    """测试比对分析到报告生成的集成"""
    # Given: 比对分析结果
    # When: 调用报告生成器
    # Then: 生成符合格式的报告文本
```

#### 测试用例 4.1.4: 多策略选择到汇总集成 (新增)
```python
async def test_multi_strategy_selection_to_aggregation():
    """测试多策略选择到汇总的集成"""
    # Given: 多个策略配置和校验结果
    # When: 依次调用策略选择器和多策略汇总器
    # Then: 正确选择策略并生成汇总报告
    # And: 策略级别错误隔离正常工作
```

### 4.2 数据传递测试

#### 测试用例 4.2.1: 工作流数据上下文
```python
async def test_workflow_data_context_flow():
    """测试工作流数据上下文传递"""
    # Given: 初始的工作流数据上下文
    # When: 数据在各组件间传递
    # Then: 每个阶段的数据状态正确
    # And: 无数据丢失或污染
```

## 5. 端到端测试用例

### 5.1 完整工作流测试

#### 测试用例 5.1.1: 成功执行完整工作流
```python
async def test_complete_workflow_success():
    """测试成功执行完整工作流"""
    # Given: 完整的测试环境和数据
    # When: 执行完整的校验工作流
    # Then: 所有步骤成功完成
    # And: 生成正确的校验报告
    # And: 发送成功的通知
```

#### 测试用例 5.1.2: 工作流异常恢复
```python
async def test_workflow_exception_recovery():
    """测试工作流异常恢复"""
    # Given: 模拟各种异常情况
    # When: 执行工作流
    # Then: 正确处理异常并发送错误通知
    # And: 清理临时资源
```

#### 测试用例 5.1.3: 多策略校验端到端测试 (新增)
```python
async def test_multi_strategy_verification_end_to_end():
    """测试多策略校验的端到端流程"""
    # Given: 多个策略配置和对应的信号数据
    # When: 执行多策略校验工作流
    # Then: 所有策略都被正确校验
    # And: 生成多策略汇总报告
    # And: 发送包含策略统计的通知
    # And: 策略级别错误不影响其他策略
```

### 5.2 真实场景模拟

#### 测试用例 5.2.1: 典型工作日场景
```python
async def test_typical_workday_scenario():
    """测试典型工作日场景"""
    # Given: 模拟真实的交易日数据
    # When: 执行每日校验
    # Then: 处理结果符合预期
    # And: 报告内容准确反映实际情况
```

#### 测试用例 5.2.2: 无交易日场景
```python
async def test_no_trading_day_scenario():
    """测试无交易日场景"""
    # Given: 当天无回测交易和信号
    # When: 执行每日校验
    # Then: 正确处理空数据情况
    # And: 生成相应的报告
```

#### 测试用例 5.2.3: 大量数据场景
```python
async def test_high_volume_scenario():
    """测试大量数据场景"""
    # Given: 大量的交易和信号数据
    # When: 执行每日校验
    # Then: 在合理时间内完成处理
    # And: 内存使用保持在正常范围
```

#### 测试用例 5.2.4: 多策略混合场景 (新增)
```python
async def test_multi_strategy_mixed_scenario():
    """测试多策略混合场景"""
    # Given: 部分策略成功、部分失败、部分无数据
    # When: 执行多策略校验
    # Then: 正确处理各种策略状态
    # And: 生成准确的汇总统计
    # And: 突出显示需要关注的策略
```

## 6. 边界和异常测试用例

### 6.1 边界条件测试

#### 测试用例 6.1.1: 时间边界测试
```python
async def test_time_boundary_conditions():
    """测试时间边界条件"""
    # Test Case 1: 恰好跨越午夜的信号
    # Test Case 2: 恰好3分钟时间差异
    # Test Case 3: 时区边界测试
```

#### 测试用例 6.1.2: 数据大小边界
```python
async def test_data_size_boundaries():
    """测试数据大小边界"""
    # Test Case 1: 空数据集
    # Test Case 2: 单个数据项
    # Test Case 3: 最大数据集
```

### 6.2 异常情况测试

#### 测试用例 6.2.1: 网络异常
```python
async def test_network_exceptions():
    """测试网络异常情况"""
    # Given: 模拟网络连接问题
    # When: 执行需要网络的操作
    # Then: 正确处理超时和重试
```

#### 测试用例 6.2.2: 文件系统异常
```python
async def test_filesystem_exceptions():
    """测试文件系统异常"""
    # Given: 模拟磁盘满、权限不足等问题
    # When: 执行文件操作
    # Then: 正确处理并报告错误
```

#### 测试用例 6.2.3: 数据库异常
```python
async def test_database_exceptions():
    """测试数据库异常"""
    # Given: 模拟数据库连接问题
    # When: 执行数据库查询
    # Then: 正确处理连接重试和故障转移
```

#### 测试用例 6.2.4: 多策略部分失败异常 (新增)
```python
async def test_multi_strategy_partial_failure():
    """测试多策略部分失败异常"""
    # Given: 部分策略配置错误或数据缺失
    # When: 执行多策略校验
    # Then: 失败策略被正确隔离
    # And: 成功策略继续执行
    # And: 生成包含失败信息的汇总报告
```

## 7. 性能测试用例

### 7.1 执行时间测试

#### 测试用例 7.1.1: 正常负载性能
```python
async def test_normal_load_performance():
    """测试正常负载下的性能"""
    # Given: 正常数量的交易和信号数据
    # When: 执行完整工作流
    # Then: 在10分钟内完成
    # And: 资源使用合理
```

#### 测试用例 7.1.2: 重负载性能
```python
async def test_heavy_load_performance():
    """测试重负载下的性能"""
    # Given: 大量的交易和信号数据
    # When: 执行完整工作流
    # Then: 在合理时间内完成
    # And: 不出现内存泄漏
```

#### 测试用例 7.1.3: 多策略并行性能 (新增)
```python
async def test_multi_strategy_parallel_performance():
    """测试多策略并行执行性能"""
    # Given: 多个策略配置
    # When: 并行执行多策略校验
    # Then: 总执行时间优于串行执行
    # And: 资源使用效率合理
    # And: 策略间不相互影响性能
```

### 7.2 资源使用测试

#### 测试用例 7.2.1: 内存使用测试
```python
async def test_memory_usage():
    """测试内存使用情况"""
    # Given: 执行完整工作流
    # When: 监控内存使用
    # Then: 内存使用保持在合理范围
    # And: 执行完成后正确释放内存
```

#### 测试用例 7.2.2: 文件句柄测试
```python
async def test_file_handle_usage():
    """测试文件句柄使用"""
    # Given: 执行多次工作流
    # When: 监控文件句柄
    # Then: 不出现文件句柄泄漏
```

## 8. 安全性测试用例

### 8.1 输入验证测试

#### 测试用例 8.1.1: 恶意配置数据
```python
async def test_malicious_config_data():
    """测试恶意配置数据处理"""
    # Given: 包含恶意内容的配置
    # When: 尝试解析配置
    # Then: 正确拒绝并记录安全事件
```

#### 测试用例 8.1.2: SQL 注入测试
```python
async def test_sql_injection_protection():
    """测试 SQL 注入保护"""
    # Given: 包含 SQL 注入尝试的输入
    # When: 执行数据库查询
    # Then: 查询安全执行，无注入风险
```

#### 测试用例 8.1.3: 多策略配置安全验证 (新增)
```python
async def test_multi_strategy_config_security():
    """测试多策略配置安全验证"""
    # Given: 包含恶意策略名称或路径的配置
    # When: 执行策略选择和校验
    # Then: 正确拒绝恶意配置
    # And: 记录安全事件
    # And: 不影响其他合法策略的执行
```

### 8.2 敏感信息保护

#### 测试用例 8.2.1: 日志敏感信息过滤
```python
def test_sensitive_info_filtering():
    """测试日志中敏感信息过滤"""
    # Given: 包含敏感信息的操作
    # When: 记录日志
    # Then: 敏感信息被正确脱敏
```

## 9. 兼容性测试用例

### 9.1 数据格式兼容性

#### 测试用例 9.1.1: 旧版本配置格式
```python
async def test_legacy_config_format():
    """测试旧版本配置格式兼容性"""
    # Given: 旧格式的配置数据
    # When: 尝试读取配置
    # Then: 正确处理或给出清晰的升级指导
```

#### 测试用例 9.1.2: 不同回测结果格式
```python
async def test_different_backtest_formats():
    """测试不同回测结果格式兼容性"""
    # Given: 不同版本回测引擎的结果格式
    # When: 解析结果文件
    # Then: 正确识别并解析格式
```

#### 测试用例 9.1.3: 多策略配置向后兼容 (新增)
```python
async def test_multi_strategy_backward_compatibility():
    """测试多策略配置向后兼容性"""
    # Given: 单策略模式的旧配置
    # When: 在多策略模式下执行
    # Then: 正确识别为单策略模式
    # And: 生成兼容的单策略报告格式
    # And: 提供升级到多策略的建议
```

## 10. 测试执行策略

### 10.1 测试优先级

**高优先级 (P0)**:
- 核心功能单元测试
- 端到端成功场景测试
- 关键异常处理测试

**中优先级 (P1)**:
- 边界条件测试
- 集成测试
- 性能基准测试

**低优先级 (P2)**:
- 兼容性测试
- 压力测试
- 安全性测试

### 10.2 测试自动化

```bash
# 执行所有测试
pytest test/workflows/daily_backtest_verification/ -v

# 执行单元测试
pytest test/workflows/daily_backtest_verification/components/ -v

# 执行集成测试
pytest test/workflows/daily_backtest_verification/test_main.py -v

# 执行性能测试
pytest test/workflows/daily_backtest_verification/ -v -m performance

# 执行多策略相关测试 (新增)
pytest test/workflows/daily_backtest_verification/ -v -k "multi_strategy"

# 执行策略选择器测试 (新增)
pytest test/workflows/daily_backtest_verification/components/test_strategy_selector.py -v

# 执行多策略汇总器测试 (新增)
pytest test/workflows/daily_backtest_verification/components/test_multi_strategy_aggregator.py -v

# 生成覆盖率报告
pytest test/workflows/daily_backtest_verification/ --cov=workflows.daily_backtest_verification --cov-report=html
```

### 10.3 持续集成配置

```yaml
# .github/workflows/daily_verification_tests.yml
name: Daily Backtest Verification Tests

on:
  pull_request:
    paths:
      - 'workflows/daily_backtest_verification/**'
      - 'test/workflows/daily_backtest_verification/**'
  
jobs:
  test:
    runs-on: ubuntu-latest
    services:
      mongodb:
        image: mongo:4.4
        ports:
          - 27017:27017
    
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.11'
      
      - name: Install dependencies
        run: |
          pip install poetry
          poetry install
      
      - name: Run tests
        run: |
          poetry run pytest test/workflows/daily_backtest_verification/ -v --cov
      
      - name: Upload coverage
        run: |
          poetry run codecov
```

## 11. 验收标准

### 11.1 功能验收标准

- ✅ 所有 P0 和 P1 测试用例通过
- ✅ 测试覆盖率达到 85% 以上
- ✅ 端到端测试在正常环境下执行成功
- ✅ 异常处理测试验证系统健壮性

### 11.2 性能验收标准

- ✅ 正常负载下，完整执行时间 < 10 分钟
- ✅ 内存使用峰值 < 512MB
- ✅ 无内存泄漏和资源泄漏

### 11.3 安全验收标准

- ✅ 输入验证测试全部通过
- ✅ 敏感信息保护测试通过
- ✅ 无已知安全漏洞

---

**关联链接 (Related Links):**
- **关联需求 (Related Requirement):** @daily_backtest_verification_requirements_ai.md
- **技术实现方案 (Development Plan):** @daily_backtest_verification_dev_plan_ai.md
- **跟踪任务 (Tracked by):** @daily_backtest_verification_todo_list.md
- **功能增强记录:** 策略选择器增强测试用例 (2025-01-12) - 新增3个异常处理测试用例