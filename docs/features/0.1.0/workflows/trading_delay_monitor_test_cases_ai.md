# 交易延迟监控功能测试用例设计

## 1. 文档历史 (Document History)

| 版本 | 日期                    | 修改人           | 修改内容 |
|------|-------------------------|------------------|----------|
| v1.0 | 2025-06-18 22:15:38 CST | AI质量保障工程师 | 初版创建 |
| v1.1 | 2025-06-18 22:30:00 CST | AI质量保障工程师 | **[重要扩展]** 添加交易金额过滤测试用例：新增TC012-TC015测试transaction_min_amount与cost_usd金额过滤功能，确保只有符合金额条件的KOL活动被用于延迟计算 |

## 2. 测试概述 (Test Overview)

### 2.1. 测试范围 (Test Scope)

本测试计划覆盖交易延迟监控功能的所有核心组件： - **数据模型层**: TradingDelayRecord模型验证 - **数据访问层**: TradingDelayRecordDAO的CRUD操作 - **业务逻辑层**: TradingDelayCalculator和KOLTradeTimeResolver - **工作流层**: 工作流处理器的端到端流程 - **集成测试**: 跨模块协作和数据库集成

### 2.2. 测试策略 (Test Strategy)

#### 2.2.1. 测试层次

-   **单元测试**: 独立模块和函数的功能验证
-   **集成测试**: 模块间协作和数据库操作验证\
-   **端到端测试**: 完整工作流场景验证
-   **性能测试**: 批量处理和查询性能验证

#### 2.2.2. 测试原则

-   **数据驱动**: 使用多样化的测试数据覆盖各种场景
-   **边界值测试**: 重点测试时间边界和异常值
-   **异常路径**: 充分测试错误处理和恢复机制
-   **可追溯性**: 每个测试用例关联具体需求点

### 2.3. 测试环境 (Test Environment)

-   **测试框架**: pytest
-   **模拟框架**: unittest.mock, pytest-mock
-   **数据库**: 本地开发环境MongoDB (dev-local-mongo)
-   **异步测试**: pytest-asyncio
-   **覆盖率工具**: pytest-cov

## 3. 测试用例设计 (Test Case Design)

### 3.0. DAO扩展方法测试 (DAO Extension Tests)

#### 3.0.1. TradeRecordDAO扩展方法测试

| 用例ID | 关联需求 | 用例标题 | 前置条件 | 测试步骤 | 预期结果 | 用例类型 | 优先级 |
|---------|---------|---------|---------|---------|---------|---------|---------|
| TC-DAO-001 | @FR002 | 测试find_unprocessed_trades方法 | 数据库中有3笔交易记录，其中1笔已有延迟记录 | 1\. 调用TradeRecordDAO.find_unprocessed_trades()<br>2. 验证返回结果数量<br>3. 验证返回的记录确实没有对应的延迟记录 | 返回2笔未处理的交易记录 | 集成测试 | 高 |
| TC-DAO-002 | @FR002 | 测试find_unprocessed_trades批量查询 | 数据库中有1000笔交易记录，其中500笔未处理 | 1\. 调用find_unprocessed_trades(limit=100)<br>2. 验证返回100条记录<br>3. 验证查询时间小于1秒 | 正确返回指定数量的未处理记录 | 性能测试 | 中 |

#### 3.0.2. KOLWalletActivityDAO扩展方法测试

| 用例ID | 关联需求 | 用例标题 | 前置条件 | 测试步骤 | 预期结果 | 用例类型 | 优先级 |
|---------|---------|---------|---------|---------|---------|---------|---------|
| TC-DAO-003 | @FR003 | 测试find_activities_in_time_range方法 | KOL钱包活动数据：wallet1在时间T1、T2、T3有活动，查询时间范围：T1.5到T2.5 | 1\. 调用find_activities_in_time_range(\["wallet1"\], T1.5, T2.5)<br>2. 验证返回1条记录（T2时间的活动） | 返回时间范围内的活动记录 | 集成测试 | 高 |
| TC-DAO-004 | @FR003 | 测试find_last_activity_for_wallets方法 | wallet1有3个活动，时间分别为T1、T2、T3 | 1\. 调用find_last_activity_for_wallets(\["wallet1"\])<br>2. 验证返回T3时间的活动 | 返回最新的活动记录 | 集成测试 | 高 |
| TC-DAO-005 | @FR003 | 测试多钱包查询 | wallet1、wallet2、wallet3都有活动记录 | 1\. 调用find_activities_in_time_range(\[wallet1,wallet2,wallet3\], start, end)<br>2. 验证返回所有相关钱包的活动 | 正确返回多个钱包的活动记录 | 集成测试 | 中 |

#### 3.0.3. 并发处理测试 (Concurrency Tests)

| 用例ID | 关联需求 | 用例标题 | 前置条件 | 测试步骤 | 预期结果 | 用例类型 | 优先级 |
|---------|---------|---------|---------|---------|---------|---------|---------|
| TC-CONC-001 | @FR001 | 测试工作流并发执行 | 工作流配置为10秒间隔 | 1\. 同时启动2个工作流实例<br>2. 观察并发处理行为<br>3. 验证数据一致性 | 无重复处理，数据一致 | 集成测试 | 中 |
| TC-CONC-002 | @FR008 | 测试数据库并发写入 | 多个线程同时写入延迟记录 | 1\. 创建10个并发线程<br>2. 同时插入延迟记录<br>3. 验证唯一性约束 | 唯一性约束正常工作，无数据冲突 | 集成测试 | 中 |

#### 3.0.4. 数据一致性验证测试 (Data Consistency Tests)

| 用例ID | 关联需求 | 用例标题 | 前置条件 | 测试步骤 | 预期结果 | 用例类型 | 优先级 |
|---------|---------|---------|---------|---------|---------|---------|---------|
| TC-CONS-001 | @FR008 | 验证延迟记录与交易记录一致性 | 有延迟记录和对应的交易记录 | 1\. 查询所有延迟记录<br>2. 验证每个延迟记录的trade_record_id都能找到对应交易<br>3. 验证字段值一致性 | 所有延迟记录都有对应的有效交易记录 | 集成测试 | 中 |
| TC-CONS-002 | @FR007 | 验证延迟记录字段完整性 | 各种状态的延迟记录 | 1\. 查询不同状态的延迟记录<br>2. 验证必填字段都有值<br>3. 验证可选字段的逻辑正确性 | 字段值符合业务逻辑要求 | 单元测试 | 中 |

### 3.1. 数据模型层测试

#### 3.1.1. TradingDelayRecord模型测试

| 用例ID | 关联需求 | 用例标题 | 前置条件 | 测试步骤 | 预期结果 | 用例类型 | 优先级 |
|---------|---------|---------|---------|---------|---------|---------|---------|
| TC-001 | @FR007 | 验证TradingDelayRecord模型基本字段创建 | 无 | 1\. 使用有效数据创建TradingDelayRecord实例<br>2. 验证所有必填字段 | 模型实例创建成功，字段值正确 | 单元测试 | 高 |
| TC-002 | @FR007 | 验证trade_record_id唯一性约束 | 数据库已有一条延迟记录 | 1\. 尝试创建相同trade_record_id的记录<br>2. 保存到数据库 | 抛出唯一性约束异常 | 单元测试 | 高 |
| TC-003 | @FR006 | 验证DelayStatus枚举值 | 无 | 1\. 测试所有有效的DelayStatus值<br>2. 测试无效的状态值 | 有效值正常，无效值抛出验证错误 | 单元测试 | 中 |
| TC-004 | @FR005 | 验证延迟秒数精度 | 无 | 1\. 设置delay_seconds为3.1415926<br>2. 验证保存和读取的精度 | 精度保持到小数点后2位 | 单元测试 | 中 |

### 3.2. 业务逻辑层测试

#### 3.2.1. KOLTradeTimeResolver测试

| 用例ID | 关联需求 | 用例标题 | 前置条件 | 测试步骤 | 预期结果 | 用例类型 | 优先级 |
|---------|---------|---------|---------|---------|---------|---------|---------|
| TC-101 | @FR003 | 正常解析KOL最后交易时间 | 1\. 有效的Signal记录<br>2. 有匹配的KOL活动记录 | 1\. 调用resolve_kol_last_trade_time<br>2. 传入有效signal_id | 返回正确的KOL交易时间，无错误 | 单元测试 | 高 |
| TC-102 | @FR004 | 处理信号不存在的情况 | 数据库中无对应Signal | 1\. 传入不存在的signal_id<br>2. 调用resolve_kol_last_trade_time | 返回None和"signal_not_found"错误 | 单元测试 | 高 |
| TC-103 | @FR004 | 处理hit_kol_wallets为空的情况 | Signal存在但hit_kol_wallets为空 | 1\. 创建无KOL钱包的Signal<br>2. 调用解析方法 | 返回None和"no_kol_wallets"错误 | 单元测试 | 高 |
| TC-104 | @FR003 | 处理缺少transaction_lookback_hours的情况 | Signal存在但缺少回溯时间配置 | 1\. 创建缺少lookback_hours的Signal<br>2. 调用解析方法 | 返回None和"no_lookback_hours"错误 | 单元测试 | 中 |
| TC-105 | @FR003 | 处理时间范围内无KOL活动的情况 | Signal存在但时间范围内无KOL活动 | 1\. 创建有效Signal<br>2. 时间范围内无对应KOL活动 | 返回None和"no_kol_activities_found"错误 | 单元测试 | 高 |
| TC-106 | @FR003 | 验证时间戳转换准确性 | 有Unix时间戳的KOL活动 | 1\. 准备Unix时间戳KOL活动<br>2. 解析并验证转换结果 | datetime转换准确，时区正确(UTC) | 单元测试 | 中 |

#### 3.2.2. TradingDelayCalculator测试

| 用例ID | 关联需求 | 用例标题 | 前置条件 | 测试步骤 | 预期结果 | 用例类型 | 优先级 |
|---------|---------|---------|---------|---------|---------|---------|---------|
| TC-201 | @FR005 | 正常计算交易延迟 | 1\. 有效的TradeRecord<br>2. 可解析的KOL交易时间 | 1\. 调用_calculate_single_trade_delay<br>2. 验证延迟计算结果 | 延迟秒数计算正确，状态为CALCULATED | 单元测试 | 高 |
| TC-202 | @FR004 | 处理无signal_id的交易 | TradeRecord的signal_id为None | 1\. 传入无signal_id的交易记录<br>2. 计算延迟 | 状态标记为SIGNAL_MISSING | 单元测试 | 高 |
| TC-203 | @FR006 | 检测负延迟异常 | KOL交易时间晚于实际交易时间 | 1\. 准备时间戳倒置的数据<br>2. 计算延迟 | 状态标记为TIMESTAMP_ANOMALY | 单元测试 | 高 |
| TC-204 | @FR006 | 检测过长延迟异常 | 延迟超过10分钟(600秒) | 1\. 准备延迟超过600秒的数据<br>2. 计算延迟 | 状态标记为EXCESSIVE_DELAY | 单元测试 | 中 |
| TC-205 | @FR001 | 防止重复处理交易 | 延迟记录已存在 | 1\. 创建已存在的延迟记录<br>2. 再次计算同一交易 | 返回None，不重复处理 | 单元测试 | 中 |
| TC-206 | @FR001 | 批量处理多个交易记录 | 多个未处理的交易记录 | 1\. 准备50个交易记录<br>2. 调用批量处理方法 | 所有记录被处理，返回正确统计 | 单元测试 | 高 |

### 3.3. 数据访问层测试

#### 3.3.1. TradingDelayRecordDAO测试

| 用例ID | 关联需求 | 用例标题 | 前置条件 | 测试步骤 | 预期结果 | 用例类型 | 优先级 |
|---------|---------|---------|---------|---------|---------|---------|---------|
| TC-301 | @FR008 | 保存延迟记录 | 有效的TradingDelayRecord实例 | 1\. 调用save方法<br>2. 验证数据库中的记录 | 记录成功保存，字段值正确 | 集成测试 | 高 |
| TC-302 | @FR008 | 根据trade_record_id查找记录 | 数据库中已有延迟记录 | 1\. 调用find_by_trade_record_id<br>2. 验证返回结果 | 返回正确的延迟记录 | 集成测试 | 高 |
| TC-303 | @FR008 | 查找不存在的记录 | 数据库中无对应记录 | 1\. 查找不存在的trade_record_id | 返回None | 集成测试 | 中 |
| TC-304 | 性能需求 | 获取延迟统计信息 | 数据库中有多条不同状态的记录 | 1\. 调用get_delay_statistics<br>2. 验证统计结果 | 返回正确的聚合统计数据 | 集成测试 | 中 |
| TC-305 | 性能需求 | 分析查询性能测试 | 数据库中有1000+记录 | 1. 调用find_delays_for_analysis<br>2. 测量查询时间 | 查询时间<1秒，结果正确 | 性能测试 | 低 |
| TC-306 | @FR009 | 查询相同策略和代币的最近记录 | 数据库中有多条相同策略-代币的延迟记录 | 1. 调用find_recent_records_by_strategy_token<br>2. 传入策略名、代币地址、时间戳 | 返回指定时间前的最近记录，按时间降序 | 集成测试 | 高 |
| TC-307 | @FR009 | 无匹配策略-代币记录查询 | 数据库中无对应策略-代币组合的记录 | 1. 传入不存在的策略名和代币地址<br>2. 调用查询方法 | 返回空列表 | 集成测试 | 中 |
| TC-308 | @FR009 | 时间范围边界测试 | 数据库中有边界时间的记录 | 1. 创建时间戳恰好等于before_timestamp的记录<br>2. 调用查询方法 | 不返回等于时间戳的记录（严格小于） | 集成测试 | 中 |
| TC-309 | @FR009 | 限制返回数量测试 | 数据库中有多条符合条件的记录 | 1. 设置limit=1<br>2. 调用查询方法 | 只返回最近的1条记录 | 集成测试 | 中 |

### 3.4. 工作流层测试

#### 3.4.1. 工作流处理器测试

| 用例ID | 关联需求 | 用例标题 | 前置条件 | 测试步骤 | 预期结果 | 用例类型 | 优先级 |
|---------|---------|---------|---------|---------|---------|---------|---------|
| TC-401 | @FR009 | 工作流正常处理流程 | 有未处理的交易记录 | 1\. 调用process_trading_delays<br>2. 验证返回结果和日志 | 返回成功状态和处理统计 | 集成测试 | 高 |
| TC-402 | @FR009 | 工作流异常处理 | 数据库连接异常 | 1\. 模拟数据库异常<br>2. 调用工作流处理器 | 返回错误状态，记录错误日志 | 集成测试 | 高 |
| TC-403 | @FR009 | 验证函数测试 | 有效和无效的处理结果 | 1\. 测试validate函数<br>2. 传入各种格式数据 | 正确验证数据格式 | 单元测试 | 中 |
| TC-404 | @FR010 | 空数据处理 | 无未处理的交易记录 | 1\. 清空未处理记录<br>2. 运行工作流 | 正常完成，统计显示0处理 | 集成测试 | 中 |

### 3.4.2. 工作流集成专项测试 **[新增]**

| 用例ID | 关联需求 | 用例标题 | 前置条件 | 测试步骤 | 预期结果 | 用例类型 | 优先级 |
|---------|---------|---------|---------|---------|---------|---------|---------|
| TC-WF-001 | @FR009 | 工作流处理器初始化测试 | 无 | 1\. 导入工作流处理器模块<br>2. 验证主要函数存在 | 成功导入，函数可调用 | 单元测试 | 高 |
| TC-WF-002 | @FR009 | process_trading_delays成功场景 | 有5个未处理的交易记录 | 1\. 调用process_trading_delays<br>2. 验证返回结果格式 | 返回包含processing_summary和status的字典 | 单元测试 | 高 |
| TC-WF-003 | @FR009 | process_trading_delays异常处理 | 模拟TradingDelayCalculator异常 | 1\. Mock计算器抛出异常<br>2. 调用工作流处理器 | 返回error状态，包含错误信息 | 单元测试 | 高 |
| TC-WF-004 | @FR009 | validate函数正确验证 | 有效和无效的数据格式 | 1\. 传入正确格式数据<br>2. 传入错误格式数据<br>3. 验证返回值 | 正确数据返回True，错误数据返回False | 单元测试 | 中 |
| TC-WF-005 | @FR009 | main函数完整流程 | 正常的工作流环境 | 1\. 调用main函数<br>2. 验证日志输出 | 成功执行，输出完成日志 | 单元测试 | 中 |
| TC-WF-006 | @FR010 | 工作流YAML配置验证 | 工作流配置文件存在 | 1\. 读取YAML配置<br>2. 验证节点配置<br>3. 验证间隔设置 | 配置格式正确，节点设置为10秒间隔 | 配置测试 | 中 |
| TC-WF-007 | @FR010 | 工作流节点依赖关系 | 完整的工作流配置 | 1\. 解析工作流配置<br>2. 验证节点间依赖 | 依赖关系正确，处理器->验证器 | 配置测试 | 中 |
| TC-WF-008 | @FR010 | 工作流流量控制配置 | 工作流配置包含流量控制 | 1\. 检查flow_control配置<br>2. 验证参数设置 | max_pending_messages=5，enable_flow_control=true | 配置测试 | 低 |
| TC-WF-009 | @FR009 | 端到端工作流执行 | 完整的测试环境 | 1\. 准备测试数据<br>2. 运行完整工作流<br>3. 验证处理结果 | 所有交易被正确处理，延迟记录保存到数据库 | E2E测试 | 高 |
| TC-WF-010 | @FR008 | 工作流重复执行测试 | 相同的交易数据 | 1\. 第一次运行工作流<br>2. 第二次运行工作流<br>3. 验证防重复机制 | 第二次运行跳过已处理的记录 | E2E测试 | 高 |
| TC-WF-011 | @FR009 | 工作流批量处理限制 | 100个未处理交易记录 | 1\. 创建大量测试数据<br>2. 运行工作流<br>3. 验证批量处理 | 按batch_size分批处理，不超过限制 | E2E测试 | 中 |
| TC-WF-012 | @FR010 | 工作流定时调度模拟 | 工作流调度环境 | 1\. 模拟多次定时执行<br>2. 验证执行间隔<br>3. 检查资源使用 | 每次执行间隔正确，无资源泄漏 | E2E测试 | 低 |

### 3.5. 端到端测试

#### 3.5.1. 完整业务流程测试

| 用例ID | 关联需求 | 用例标题 | 前置条件 | 测试步骤 | 预期结果 | 用例类型 | 优先级 |
|---------|---------|---------|---------|---------|---------|---------|---------|
| TC-501 | 所有需求 | 端到端完整延迟计算流程 | 1\. 完整的测试数据集<br>2. 各种交易和信号场景 | 1\. 运行完整工作流<br>2. 验证所有类型的处理结果 | 所有交易正确计算延迟或标记状态 | E2E测试 | 高 |
| TC-502 | @FR008 | 大批量数据处理测试 | 1000+交易记录需要处理 | 1\. 创建大量测试数据<br>2. 多次运行工作流 | 所有记录被正确处理，无重复 | E2E测试 | 中 |
| TC-503 | @FR010 | 工作流调度测试 | 工作流配置为10秒间隔 | 1\. 启动工作流调度<br>2. 观察多个执行周期 | 每10秒正确执行一次 | E2E测试 | 低 |

### 3.6. 信号抑制检查测试 **\[新增\]**

#### 3.6.1. SignalSuppressionChecker测试

| 用例ID | 关联需求 | 用例标题 | 前置条件 | 测试步骤 | 预期结果 | 用例类型 | 优先级 |
|---------|---------|---------|---------|---------|---------|---------|---------|
| TC-SUP-001 | @FR009 | 首次策略-代币组合检查 | 数据库中无历史延迟记录 | 1\. 传入新的交易记录<br>2. 调用check_signal_suppression | 返回is_suppressed=False，允许延迟计算 | 单元测试 | 高 |
| TC-SUP-002 | @FR009 | 时间间隔大于抑制阈值 | 存在历史记录，KOL交易时间间隔65分钟，same_token_notification_interval=60分钟 | 1\. 准备测试数据<br>2. 检查信号抑制 | 返回is_suppressed=False，time_interval_minutes=65 | 单元测试 | 高 |
| TC-SUP-003 | @FR009 | 时间间隔等于抑制阈值 | 存在历史记录，时间间隔恰好等于same_token_notification_interval | 1\. 准备边界值数据<br>2. 检查信号抑制 | 返回is_suppressed=True，status="suppressed" | 单元测试 | 高 |
| TC-SUP-004 | @FR009 | 时间间隔小于抑制阈值 | 存在历史记录，时间间隔小于same_token_notification_interval | 1\. 准备小于阈值的数据<br>2. 检查信号抑制 | 返回is_suppressed=True，status="suppressed" | 单元测试 | 高 |
| TC-SUP-005 | @FR009 | 策略配置缺失处理 | 无法获取same_token_notification_interval配置 | 1\. 模拟配置缺失<br>2. 检查信号抑制 | 返回is_suppressed=False，status="config_missing" | 单元测试 | 中 |
| TC-SUP-006 | @FR009 | 历史记录KOL时间缺失 | 历史延迟记录存在但kol_last_trade_timestamp为空 | 1\. 创建缺失KOL时间的历史记录<br>2. 检查抑制 | 返回is_suppressed=False，status="no_history" | 单元测试 | 中 |
| TC-SUP-007 | @FR009 | 当前信号KOL时间获取失败 | KOL交易时间解析失败 | 1\. 模拟KOL时间解析失败<br>2. 检查信号抑制 | 返回is_suppressed=False，status="error" | 单元测试 | 中 |
| TC-SUP-008 | @FR009 | 不同策略相同代币 | 相同代币但不同策略的历史记录 | 1\. 准备不同策略数据<br>2. 检查信号抑制 | 返回is_suppressed=False，不受其他策略影响 | 单元测试 | 中 |
| TC-SUP-009 | @FR009 | 相同策略不同代币 | 相同策略但不同代币的历史记录 | 1\. 准备不同代币数据<br>2. 检查信号抑制 | 返回is_suppressed=False，不受其他代币影响 | 单元测试 | 中 |

#### 3.6.2. 延迟计算器信号抑制集成测试

| 用例ID | 关联需求 | 用例标题 | 前置条件 | 测试步骤 | 预期结果 | 用例类型 | 优先级 |
|---------|---------|---------|---------|---------|---------|---------|---------|
| TC-SUP-101 | @FR009 | 被抑制信号的延迟记录创建 | 信号被抑制检查标记为suppressed | 1\. 计算被抑制信号的延迟<br>2. 验证延迟记录状态 | 创建状态为SIGNAL_SUPPRESSED的延迟记录 | 集成测试 | 高 |
| TC-SUP-102 | @FR009 | 未被抑制信号的正常处理 | 信号通过抑制检查 | 1\. 计算未被抑制信号的延迟<br>2. 验证正常延迟计算 | 正常进行延迟计算，状态为CALCULATED | 集成测试 | 高 |
| TC-SUP-103 | @FR009 | 并发抑制检查 | 多个相同策略-代币的信号同时处理 | 1\. 并发提交相同策略代币的交易<br>2. 验证抑制检查结果 | 正确处理并发，避免重复计算 | 集成测试 | 中 |

### 3.7. **[新增]** 交易金额过滤功能测试

#### 3.7.1. KOL交易时间查找金额过滤测试

| 用例ID | 关联需求 | 用例标题 | 前置条件 | 测试步骤 | 预期结果 | 用例类型 | 优先级 |
|---------|---------|---------|---------|---------|---------|---------|---------|
| TC-AMT-001 | @FR003 | 基本金额过滤功能验证 | Signal配置transaction_min_amount=1000，KOL活动cost_usd="1500.50" | 1\. 调用resolve_kol_last_trade_time<br>2. 验证返回的KOL活动 | 返回符合金额条件的KOL活动时间 | 单元测试 | 高 |
| TC-AMT-002 | @FR003 | 金额不足过滤测试 | Signal配置transaction_min_amount=1000，KOL活动cost_usd="800.00" | 1\. 调用KOL时间解析<br>2. 验证过滤结果 | 返回"no_qualified_kol_activities_found"错误 | 单元测试 | 高 |
| TC-AMT-003 | @FR003 | 金额边界值测试（等于） | Signal配置transaction_min_amount=1000，KOL活动cost_usd="1000.00" | 1\. 执行金额过滤查询<br>2. 验证边界值处理 | 返回KOL活动（等于阈值应通过） | 单元测试 | 高 |
| TC-AMT-004 | @FR003 | 金额边界值测试（略大于） | Signal配置transaction_min_amount=1000，KOL活动cost_usd="1000.01" | 1\. 执行金额过滤查询<br>2. 验证边界值处理 | 返回KOL活动（大于阈值应通过） | 单元测试 | 中 |
| TC-AMT-005 | @FR003 | 金额边界值测试（略小于） | Signal配置transaction_min_amount=1000，KOL活动cost_usd="999.99" | 1\. 执行金额过滤查询<br>2. 验证边界值处理 | 过滤掉该活动（小于阈值应被过滤） | 单元测试 | 中 |
| TC-AMT-006 | @FR003 | 缺少transaction_min_amount配置 | Signal的trigger_conditions中无transaction_min_amount字段 | 1\. 调用KOL时间解析<br>2. 验证错误处理 | 返回"no_min_amount_config"错误 | 单元测试 | 高 |
| TC-AMT-007 | @FR003 | cost_usd字符串格式验证 | KOL活动cost_usd为各种格式："1000"、"1000.0"、"1,000.50" | 1\. 测试不同字符串格式<br>2. 验证转换和比较 | 正确转换为数字并比较 | 单元测试 | 中 |
| TC-AMT-008 | @FR003 | 无效cost_usd处理 | KOL活动cost_usd为"invalid"、空字符串、null | 1\. 传入无效金额数据<br>2. 验证异常处理 | 优雅处理异常，跳过无效记录 | 单元测试 | 中 |
| TC-AMT-009 | @FR003 | 多个KOL活动金额混合过滤 | 多个KOL活动，只有部分符合金额条件 | 1\. 准备混合金额数据<br>2. 执行过滤查询<br>3. 验证返回最新的符合条件活动 | 只返回符合金额条件且时间最新的活动 | 单元测试 | 高 |
| TC-AMT-010 | @FR003 | 零金额和负金额处理 | KOL活动cost_usd为"0"、"-100" | 1\. 测试零金额和负金额<br>2. 验证过滤逻辑 | 零金额和负金额被正确过滤 | 单元测试 | 低 |

#### 3.7.2. KOLWalletActivityDAO金额过滤扩展测试

| 用例ID | 关联需求 | 用例标题 | 前置条件 | 测试步骤 | 预期结果 | 用例类型 | 优先级 |
|---------|---------|---------|---------|---------|---------|---------|---------|
| TC-DAO-AMT-001 | @FR003 | find_activities_in_time_range金额过滤 | KOL活动数据包含不同cost_usd值 | 1\. 调用find_activities_in_time_range(min_cost_usd=1000)<br>2. 验证返回结果 | 只返回cost_usd>=1000的活动 | 集成测试 | 高 |
| TC-DAO-AMT-002 | @FR003 | 无min_cost_usd参数测试 | 调用时不传入min_cost_usd参数 | 1\. 调用find_activities_in_time_range(min_cost_usd=None)<br>2. 验证行为 | 返回所有符合其他条件的活动（不进行金额过滤） | 集成测试 | 中 |
| TC-DAO-AMT-003 | @FR003 | 金额过滤与时间过滤组合 | 测试数据同时需要通过时间和金额过滤 | 1\. 设置时间范围和金额限制<br>2. 执行查询<br>3. 验证双重过滤效果 | 返回同时满足时间和金额条件的活动 | 集成测试 | 高 |
| TC-DAO-AMT-004 | @FR003 | 金额过滤数据库查询性能 | 大量KOL活动数据（1000+条） | 1\. 创建大量测试数据<br>2. 执行金额过滤查询<br>3. 测量查询时间 | 查询时间<1秒，结果正确 | 性能测试 | 中 |

#### 3.7.3. 端到端金额过滤集成测试

| 用例ID | 关联需求 | 用例标题 | 前置条件 | 测试步骤 | 预期结果 | 用例类型 | 优先级 |
|---------|---------|---------|---------|---------|---------|---------|---------|
| TC-E2E-AMT-001 | @FR003 | 完整金额过滤延迟计算流程 | 交易记录、信号记录、KOL活动记录完整，部分KOL活动不符合金额条件 | 1\. 运行完整延迟计算工作流<br>2. 验证只使用符合金额条件的KOL活动 | 延迟计算使用正确的KOL活动时间，计算结果准确 | E2E测试 | 高 |
| TC-E2E-AMT-002 | @FR003 | 所有KOL活动都不符合金额条件 | 信号关联的所有KOL活动cost_usd都小于transaction_min_amount | 1\. 运行延迟计算<br>2. 验证错误处理 | 延迟记录状态为KOL_ACTIVITY_MISSING，包含准确的错误信息 | E2E测试 | 高 |
| TC-E2E-AMT-003 | @FR003 | 混合场景批量处理 | 批量交易中有些有金额限制，有些没有，有些KOL活动符合条件 | 1\. 准备多样化测试数据<br>2. 批量运行延迟计算<br>3. 验证各种场景处理 | 每个交易根据其对应信号的金额条件正确处理 | E2E测试 | 中 |

### 3.7. 边界值和异常测试

#### 3.7.1. 边界条件测试

| 用例ID | 关联需求 | 用例标题 | 前置条件 | 测试步骤 | 预期结果 | 用例类型 | 优先级 |
|---------|---------|---------|---------|---------|---------|---------|---------|
| TC-601 | @FR005 | 零延迟测试 | KOL交易时间与实际交易时间相同 | 1\. 准备相同时间戳数据<br>2. 计算延迟 | 延迟为0.0秒，状态为CALCULATED | 单元测试 | 中 |
| TC-602 | @FR006 | 600秒边界值测试 | 延迟恰好为600秒 | 1\. 准备600秒延迟数据<br>2. 计算延迟 | 状态为CALCULATED，不标记为异常 | 单元测试 | 中 |
| TC-603 | @FR006 | 601秒边界值测试 | 延迟为601秒 | 1\. 准备601秒延迟数据<br>2. 计算延迟 | 状态为EXCESSIVE_DELAY | 单元测试 | 中 |
| TC-604 | @FR003 | 最大回溯时间测试 | transaction_lookback_hours为24小时 | 1\. 准备24小时回溯数据<br>2. 查询KOL活动 | 正确查询24小时内的活动 | 单元测试 | 低 |

#### 3.6.2. 异常数据测试

| 用例ID | 关联需求 | 用例标题 | 前置条件 | 测试步骤 | 预期结果 | 用例类型 | 优先级 |
|---------|---------|---------|---------|---------|---------|---------|---------|
| TC-701 | @FR004 | 无效ObjectId处理 | 无 | 1\. 传入格式错误的ObjectId<br>2. 调用查询方法 | 抛出格式错误异常或返回错误状态 | 单元测试 | 中 |
| TC-702 | @FR006 | 极大延迟值测试 | 延迟超过1天(86400秒) | 1\. 准备极大延迟数据<br>2. 计算和保存 | 正确处理并标记为EXCESSIVE_DELAY | 单元测试 | 低 |
| TC-703 | @FR003 | 损坏的时间戳数据 | KOL活动时间戳为0或负数 | 1\. 准备异常时间戳数据<br>2. 解析KOL交易时间 | 优雅处理异常，返回错误状态 | 单元测试 | 中 |
| TC-704 | @FR008 | 数据库连接中断恢复 | 处理过程中数据库连接中断 | 1\. 模拟连接中断<br>2. 测试重连和恢复 | 自动重连或优雅失败 | 集成测试 | 低 |

## 4. 测试数据准备 (Test Data Preparation)

### 4.1. 测试数据分类

#### 4.1.1. 正常场景数据

``` python
# 示例：正常交易记录和信号数据
NORMAL_TRADE_RECORD = {
    "signal_id": ObjectId("..."),
    "strategy_name": "test_strategy",
    "trade_type": "buy",
    "status": "success",
    "created_at": datetime(2025, 6, 18, 10, 30, 0),
    # ... 其他字段
}

NORMAL_SIGNAL = {
    "hit_kol_wallets": ["wallet1", "wallet2"],
    "trigger_timestamp": datetime(2025, 6, 18, 10, 25, 0),
    "trigger_conditions": {
        "transaction_lookback_hours": 2
    }
}

NORMAL_KOL_ACTIVITY = {
    "wallet": "wallet1",
    "timestamp": int(datetime(2025, 6, 18, 10, 20, 0).timestamp()),
    "event_type": "buy"
}
```

#### 4.1.2. 异常场景数据

``` python
# 示例：各种异常情况的测试数据
MISSING_SIGNAL_TRADE = {
    "signal_id": None,  # 无信号
    "created_at": datetime(2025, 6, 18, 10, 30, 0)
}

EMPTY_KOL_WALLETS_SIGNAL = {
    "hit_kol_wallets": [],  # 空KOL钱包列表
    "trigger_timestamp": datetime(2025, 6, 18, 10, 25, 0)
}
```

#### 4.1.3. **[新增]** 交易金额过滤测试数据

``` python
# 示例：交易金额过滤相关测试数据
AMOUNT_FILTER_SIGNAL = {
    "hit_kol_wallets": ["wallet1", "wallet2"],
    "trigger_timestamp": datetime(2025, 6, 18, 10, 25, 0),
    "trigger_conditions": {
        "transaction_lookback_hours": 2,
        "transaction_min_amount": 1000.0  # 最小金额1000 USD
    }
}

# 符合金额条件的KOL活动
QUALIFIED_KOL_ACTIVITY = {
    "wallet": "wallet1",
    "timestamp": int(datetime(2025, 6, 18, 10, 20, 0).timestamp()),
    "event_type": "buy",
    "cost_usd": "1500.50"  # 大于最小金额
}

# 不符合金额条件的KOL活动
UNQUALIFIED_KOL_ACTIVITY = {
    "wallet": "wallet1", 
    "timestamp": int(datetime(2025, 6, 18, 10, 20, 0).timestamp()),
    "event_type": "buy",
    "cost_usd": "800.00"  # 小于最小金额
}

# 金额边界值活动
BOUNDARY_KOL_ACTIVITY = {
    "wallet": "wallet1",
    "timestamp": int(datetime(2025, 6, 18, 10, 20, 0).timestamp()), 
    "event_type": "buy",
    "cost_usd": "1000.00"  # 恰好等于最小金额
}

NEGATIVE_DELAY_DATA = {
    # KOL活动时间晚于交易时间
    "kol_timestamp": int(datetime(2025, 6, 18, 10, 35, 0).timestamp()),
    "trade_timestamp": datetime(2025, 6, 18, 10, 30, 0)
}
```

### 4.2. 测试工具函数

``` python
async def create_test_trade_record(**overrides) -> TradeRecord:
    """创建测试用交易记录"""
    default_data = NORMAL_TRADE_RECORD.copy()
    default_data.update(overrides)
    return TradeRecord(**default_data)

async def create_test_signal(**overrides) -> Signal:
    """创建测试用信号"""
    default_data = NORMAL_SIGNAL.copy()
    default_data.update(overrides)
    return Signal(**default_data)

async def setup_test_scenario(scenario_name: str):
    """根据场景名称设置测试数据"""
    scenarios = {
        "normal_delay": setup_normal_delay_scenario,
        "missing_signal": setup_missing_signal_scenario,
        "excessive_delay": setup_excessive_delay_scenario,
        # ... 其他场景
    }
    return await scenarios[scenario_name]()
```

## 5. 测试执行策略 (Test Execution Strategy)

### 5.1. 测试优先级

**高优先级 (P0)**: - 核心延迟计算逻辑 - 数据库CRUD操作 - 工作流基本流程 - 重复处理防护

**中优先级 (P1)**: - 边界值处理 - 异常状态标记 - 统计查询功能 - 数据验证

**低优先级 (P2)**: - 性能测试 - 工作流调度 - 极端异常场景 - 优化功能

### 5.2. 测试执行顺序

1.  **单元测试**: 按模块独立执行
2.  **集成测试**: 验证模块间协作
3.  **端到端测试**: 完整流程验证
4.  **性能测试**: 最后执行避免影响其他测试

### 5.3. 持续集成

``` yaml
# 示例：CI配置片段
test_stages:
  - name: unit_tests
    command: pytest test/unit/ -v --cov=models,dao,utils
  - name: integration_tests  
    command: pytest test/integration/ -v
  - name: e2e_tests
    command: pytest test/e2e/ -v --timeout=60
```

## 6. 测试覆盖率目标 (Coverage Goals)

### 6.1. 代码覆盖率

-   **整体覆盖率**: ≥ 90%
-   **核心业务逻辑**: ≥ 95%
-   **异常处理路径**: ≥ 80%
-   **边界条件**: ≥ 85%

### 6.2. 功能覆盖率

-   **需求覆盖**: 100% (所有FR需求点有对应测试)
-   **用户故事覆盖**: 100% (所有US有对应场景测试)
-   **异常路径覆盖**: ≥ 90%

## 7. 测试工具和框架 (Testing Tools)

### 7.1. 核心测试框架

``` python
# pytest配置示例
# pytest.ini
[tool:pytest]
testpaths = test
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --strict-markers
    --cov=models
    --cov=dao  
    --cov=utils
    --cov-report=html
    --cov-report=term-missing
asyncio_mode = auto
```

### 7.2. 模拟和测试替身

``` python
# 示例：关键组件的Mock策略
@pytest.fixture
def mock_signal_dao():
    """模拟SignalDAO"""
    with patch('utils.trading_delay_calculator.SignalDAO') as mock:
        yield mock

@pytest.fixture  
def mock_kol_activity_dao():
    """模拟KOLWalletActivityDAO"""
    with patch('utils.trading_delay_calculator.KOLWalletActivityDAO') as mock:
        yield mock

async def test_with_database_isolation():
    """数据库隔离测试示例"""
    # 使用本地开发环境MongoDB (dev-local-mongo)
    # 测试前后进行数据清理，确保测试独立性
    pass
```

## 8. 风险评估 (Risk Assessment)

### 8.1. 测试风险

| 风险项         | 影响程度 | 可能性 | 缓解措施                 |
|----------------|----------|--------|--------------------------|
| 时间戳精度差异 | 高       | 中     | 使用统一时间源和精度标准 |
| 大批量数据性能 | 中       | 高     | 专门的性能测试用例       |
| 并发访问冲突   | 中       | 低     | 数据库事务测试           |
| 测试数据污染   | 低       | 中     | 测试前后数据清理和恢复   |

### 8.2. 测试质量保证

-   **测试用例评审**: 开发团队共同评审
-   **测试数据管理**: 版本化测试数据集
-   **测试环境管理**: 本地开发环境数据库，测试前后数据清理
-   **失败分析**: 详细的失败日志和诊断

------------------------------------------------------------------------

**关联链接 (Related Links):** - **关联需求 (Related Requirement)**: @trading_delay_monitor_requirements_ai.md - **技术实现方案 (Development Plan)**: @trading_delay_monitor_dev_plan_ai.md - **跟踪任务 (Tracked by)**: @trading_delay_monitor_todo_list.md