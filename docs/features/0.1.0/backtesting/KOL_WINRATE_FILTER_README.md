# 回测模块V2 - KOL总胜率过滤功能

**版本**: 0.1.0  
**更新时间**: 2025-06-05  
**功能状态**: ✅ 已完成并测试通过  

## 功能概述

KOL总胜率过滤功能允许在回测过程中根据KOL的历史总胜率过滤交易信号，从而提高信号质量和回测准确性。

### 核心特性

- 🎯 **智能过滤**: 基于gmgn_wallet_stats表中的历史胜率数据过滤KOL
- 📊 **可配置阈值**: 支持0-1范围内的任意胜率阈值设置
- ⚡ **高性能**: 在MongoDB聚合查询中直接过滤，无需额外查询
- 🔄 **向后兼容**: 默认值为0，不影响现有回测逻辑

## 配置参数

### 新增参数

```yaml
kol_min_winrate: 0.5  # KOL最小总胜率阈值(0-1，默认0表示不过滤)
```

### 参数说明

| 参数值 | 含义 | 效果 |
|--------|------|------|
| `0.0` | 不过滤（默认） | 包含所有KOL，保持原有行为 |
| `0.3` | 胜率 > 30% | 只包含总胜率大于30%的KOL |
| `0.5` | 胜率 > 50% | 只包含总胜率大于50%的KOL |
| `0.7` | 胜率 > 70% | 只包含总胜率大于70%的KOL |
| `1.0` | 胜率 = 100% | 只包含总胜率等于100%的KOL（极严格） |

## 使用方法

### 1. 配置文件方式

```yaml
# backtest_config.yaml
backtest_start_time: 1672531200
backtest_end_time: 1675209600
kol_min_winrate: 0.5  # 只包含胜率 > 50% 的KOL
# ... 其他配置参数
```

### 2. 代码方式

```python
from utils.backtest_v2.config_manager import BacktestConfigV2
from utils.backtest_v2.backtest_engine import BacktestEngineV2

# 创建配置
config = BacktestConfigV2(
    backtest_start_time=1672531200,
    backtest_end_time=1675209600,
    kol_min_winrate=0.6,  # 胜率 > 60%
    # ... 其他参数
)

# 运行回测
engine = BacktestEngineV2(config)
result = await engine.run_backtest()
```

### 3. 命令行方式

```bash
# 使用配置文件
python run_backtest_ed.py --mode single_v2 --config examples/backtest_v2_kol_winrate_config.yaml

# 命令行参数覆盖
python run_backtest_ed.py --mode single_v2 --config config.yaml --kol_min_winrate 0.7
```

## 技术实现

### MongoDB聚合管道

新功能通过扩展MongoDB聚合管道实现，主要包含以下阶段：

1. **KOL信息关联**: 通过`$lookup`关联`kol_wallets`表
2. **胜率数据关联**: 通过`$lookup`关联`gmgn_wallet_stats`表
3. **数据充实**: 使用`$map`和`$let`构建完整的KOL信息
4. **胜率过滤**: 使用`$filter`过滤出胜率大于阈值的KOL

### 关键代码片段

```javascript
// MongoDB聚合管道中的胜率过滤逻辑
{
  '$addFields': {
    'kol_wallets': {
      '$filter': {
        'input': '$enriched_kols',
        'as': 'enriched_kol',
        'cond': {
          '$and': [
            {'$ne': ['$$enriched_kol._kol_info_obj', null]},
            {'$ne': ['$$enriched_kol._kol_stat_obj', null]},
            {'$in': ['kol', '$$enriched_kol.tags']},
            {'$gte': ['$$enriched_kol.txs', kol_account_min_txs]},
            {'$lte': ['$$enriched_kol.txs', kol_account_max_txs]},
            {'$gt': ['$$enriched_kol.winrate', kol_min_winrate]}  // 胜率过滤
          ]
        }
      }
    }
  }
}
```

## 数据来源

### gmgn_wallet_stats表

胜率数据来源于`gmgn_wallet_stats`表，具体字段：

- **表名**: `gmgn_wallet_stats`
- **过滤条件**: `period = 'all'`（总体统计数据）
- **胜率字段**: `winrate`（浮点数，范围0-1）

### 数据质量要求

- KOL必须在`kol_wallets`表中存在
- KOL必须在`gmgn_wallet_stats`表中有`period='all'`的记录
- 胜率字段不能为null或无效值

## 性能影响

### 查询性能

- ✅ **优化**: 在单一聚合查询中完成过滤，无额外查询开销
- ✅ **索引**: 利用现有的`wallet_address`和`period`索引
- ✅ **内存**: 过滤在数据库层面完成，减少内存使用

### 预期效果

- 胜率阈值越高，符合条件的KOL越少
- 符合条件的KOL越少，产生的交易信号越少
- 信号质量可能更高（来自高胜率KOL）

## 示例和测试

### 运行示例

```bash
# 运行功能演示脚本
python examples/run_backtest_v2_with_winrate_filter.py
```

### 测试用例

```bash
# 运行配置管理测试
python -m pytest test/utils/backtest_v2/test_config_manager.py::TestBacktestConfigV2::test_kol_winrate_validation -v

# 运行数据查询测试
python -m pytest test/utils/backtest_v2/test_data_query.py::TestDataQuery::test_kol_winrate_filtering_in_pipeline -v
```

## 注意事项

### 配置建议

1. **起始值**: 建议从`0.3`或`0.4`开始测试
2. **数据充足性**: 确保有足够的高胜率KOL数据
3. **阈值平衡**: 过高的阈值可能导致信号过少

### 常见问题

**Q: 设置胜率过滤后没有交易信号？**
A: 可能是胜率阈值设置过高，建议降低阈值或检查KOL胜率数据。

**Q: 胜率数据从哪里来？**
A: 来自`gmgn_wallet_stats`表，由GMGN API定期更新。

**Q: 是否影响现有回测？**
A: 不影响，默认值为0表示不过滤，保持原有行为。

## 更新日志

- **2025-06-05**: 初始版本发布，支持基本的胜率过滤功能
- **2025-06-05**: 添加MongoDB聚合管道集成
- **2025-06-05**: 完成测试用例和文档
