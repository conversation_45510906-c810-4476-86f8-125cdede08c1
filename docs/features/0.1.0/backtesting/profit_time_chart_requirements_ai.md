# 回测V2利润时间分布图表功能详细需求规格

**生成时间**: 2025-06-17T01:43:16+08:00  
**版本**: 0.1.0  
**模块**: backtesting  
**文档类型**: 需求规格  
**生成方式**: AI生成  

## 项目背景

当前memeMonitor项目的回测模块V2已基本完成开发，能够生成完整的交易记录和统计报告。现需要为单次回测结果详情页面添加一个可视化图表，用于展示交易的时间分布和利润情况，帮助用户更直观地分析交易策略的表现。

## 核心需求概述

### 1. 功能定位

**需求描述**: 在现有的单次回测运行详情页面（`generate_single_run_report`生成的HTML报告）中，在交易记录表格下方添加一个ECharts散点图，展示每笔交易的时间与利润分布关系。

**目标用户**: 
- 策略开发者：分析交易策略在不同时间段的表现
- 量化研究员：识别策略的时间敏感性和利润分布模式
- 风险管理人员：监控交易风险的时间分布

### 2. 图表展示要求

**图表类型**: ECharts散点图（Scatter Chart）
**数据维度**:
- X轴：交易时间（buy_timestamp，人类可读时间格式：YYYY-MM-DD HH:mm:ss）
- Y轴：交易盈利率（return_rate，百分比格式）
- 散点大小：可选择性地反映交易规模或其他维度

**视觉设计要求**:
- 盈利交易（盈利率>0）用绿色圆点表示
- 亏损交易（盈利率<0）用红色圆点表示
- 平盈交易（盈利率=0）用灰色圆点表示
- 散点大小可以反映交易的美元金额或持仓时间

## 详细功能需求

### 3.1 数据处理需求

**输入数据源**: 
- 从现有的交易记录数据（`trades`列表）中提取以下字段：
  - `buy_timestamp`: 买入时间戳
  - `sell_timestamp`: 卖出时间戳（用于计算持仓时间）
  - `return_rate`: 交易盈利率（小数格式，如0.25表示25%）
  - `profit_usd`: 交易利润美元金额（用于散点大小映射）
  - `token_symbol` 或 `token_address`: 代币标识
  - `buy_price`: 买入价格
  - `sell_price`: 卖出价格
  - `kol_count`: 触发该交易的KOL数量
  - `holding_hours`: 持仓时间

**数据预处理要求**:
1. 时间格式化：将`buy_timestamp`转换为人类可读的时间格式（YYYY-MM-DD HH:mm:ss）
2. 盈利率数据处理：将`return_rate`转换为百分比格式用于Y轴显示
3. 颜色分类：根据`return_rate`正负值确定散点颜色
4. 大小映射：根据`profit_usd`绝对值或其他维度映射散点大小（可选）

### 3.2 交互功能需求

**鼠标悬停（Tooltip）显示信息**:
- 代币符号/地址
- 买入时间（格式化显示：YYYY-MM-DD HH:mm:ss）
- 卖出时间（格式化显示：YYYY-MM-DD HH:mm:ss）
- 买入价格（USD）
- 卖出价格（USD）
- 交易盈利率（百分比格式，带正负号和颜色）
- 交易利润（USD金额）
- 持仓时间（小时/分钟格式）
- 触发KOL数量

**缩放和平移功能**:
- 支持图表的缩放操作（鼠标滚轮）
- 支持图表的拖拽平移
- 支持双击重置缩放

**图例功能**:
- 显示盈利/亏损/平盈的颜色说明
- 可点击图例隐藏/显示对应类型的散点

### 3.3 布局和样式需求

**图表位置**: 
- 位于交易记录表格的正下方
- 与现有页面样式保持一致
- 图表容器高度：400-500px
- 图表容器宽度：100%（自适应）

**标题和标签**:
- 图表标题：「交易盈利率时间分布图」
- X轴标签：「交易时间」
- Y轴标签：「盈利率 (%)」
- 副标题：可显示交易总数、盈利交易数等基本统计

**样式要求**:
- 与现有HTML报告的CSS样式保持一致
- 支持响应式设计，在不同屏幕尺寸下正常显示
- 图表背景色与页面背景色协调

### 3.4 技术集成需求

**ECharts版本兼容性**:
- 兼容项目当前使用的ECharts版本
- CDN引入方式，保持与现有图表一致

**数据传递方式**:
- 通过Jinja2模板将处理后的数据注入到HTML中
- 数据格式为JSON字符串，直接在JavaScript中使用

**错误处理**:
- 当交易数据为空时，显示友好的空状态提示
- 当数据格式异常时，记录错误日志并显示默认视图
- 网络异常导致ECharts库加载失败时的降级处理

## 配置参数

### 4.1 可选配置项

**散点大小配置**:
- `scatter_size_base`: 基础散点大小（默认：8px）
- `scatter_size_max`: 最大散点大小（默认：20px）
- `scatter_size_factor`: 大小因子字段选择（可选：'holding_hours', 'kol_count', 'fixed'）

**颜色配置**:
- `profit_color`: 盈利散点颜色（默认：'#52c41a'）
- `loss_color`: 亏损散点颜色（默认：'#ff4d4f'）
- `neutral_color`: 平盈散点颜色（默认：'#d9d9d9'）

**显示配置**:
- `show_legend`: 是否显示图例（默认：True）
- `show_grid`: 是否显示网格线（默认：True）
- `chart_height`: 图表高度（默认：450px）

## 性能要求

### 5.1 数据处理性能

**响应时间要求**:
- 1000笔交易以内：图表渲染时间 < 2秒
- 5000笔交易以内：图表渲染时间 < 5秒
- 超过5000笔交易：考虑数据采样或分页加载

**内存使用**:
- 避免在前端存储过大的数据集
- 优化JSON数据结构，减少冗余字段

### 5.2 兼容性要求

**浏览器兼容性**:
- Chrome 80+
- Firefox 80+
- Safari 13+
- Edge 80+

**移动端适配**:
- 在移动设备上能够正常显示和交互
- 触摸操作支持（缩放、平移）

## 验收标准

### 6.1 功能验收

1. **图表正确显示**：能够正确展示交易数据的时间和利润分布
2. **交互功能完整**：悬停、缩放、平移、图例切换功能正常
3. **数据准确性**：图表中的数据与交易记录表格中的数据完全一致
4. **样式一致性**：与现有页面样式保持协调统一

### 6.2 性能验收

1. **渲染性能**：满足上述性能要求的响应时间
2. **内存使用**：正常使用情况下不出现内存泄漏
3. **错误处理**：各种异常情况下能够优雅降级

### 6.3 兼容性验收

1. **浏览器兼容**：在支持的浏览器中功能完整
2. **响应式设计**：在不同屏幕尺寸下显示正常
3. **数据兼容**：与现有数据格式完全兼容

## 技术风险评估

### 7.1 主要风险点

1. **数据量过大**：当交易记录过多时可能影响页面加载性能
2. **时间格式兼容**：不同时区和时间格式的处理
3. **ECharts版本冲突**：与现有图表库的版本兼容性

### 7.2 风险缓解措施

1. **数据采样**：超过阈值时采用智能采样算法
2. **时间标准化**：统一使用UTC时间戳并在前端格式化显示
3. **版本锁定**：明确指定ECharts版本，确保兼容性

---

**关联链接 (Related Links):**
- **跟踪任务 (Tracked by):** @profit_time_chart_todo_list.md
- **技术实现方案 (Development Plan):** @profit_time_chart_dev_plan_ai.md
- **测试用例 (Test Cases):** @profit_time_chart_test_cases_ai.md
- **相关模块 (Related Module):** @回测V2模块文档 