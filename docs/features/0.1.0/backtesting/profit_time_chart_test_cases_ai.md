# 回测V2盈利率时间分布图表功能测试用例设计

**生成时间**: 2025-06-17T01:43:16+08:00  
**版本**: 0.1.0  
**模块**: backtesting  
**文档类型**: 测试用例设计  
**生成方式**: AI生成  

## 测试策略概述

### 1. 测试目标

本测试用例设计旨在确保回测V2盈利率时间分布图表功能的：
- **功能正确性**：图表能正确展示交易数据
- **数据完整性**：图表数据与原始交易记录完全一致
- **交互功能**：所有交互功能正常工作
- **性能表现**：在各种数据量下表现良好
- **兼容性**：在不同浏览器和设备上正常运行

### 2. 测试范围

**包含范围**：
- 数据处理函数的单元测试
- HTML模板渲染测试
- ECharts图表功能测试
- 交互功能测试
- 性能测试
- 错误处理测试

**不包含范围**：
- ECharts库本身的功能测试
- 浏览器渲染引擎测试
- 网络传输相关测试

### 3. 测试环境

**开发环境**：
- Python 3.11+
- pytest 测试框架
- mock库用于模拟数据

**浏览器环境**：
- Chrome 90+（主要测试）
- Firefox 85+
- Safari 14+
- Edge 85+

## 单元测试用例设计

### 4. 数据处理函数测试

#### 4.1 `_prepare_profit_time_chart_data()` 函数测试

**测试类**: `TestPrepareChartData`

```python
class TestPrepareChartData:
    """测试利润时间图表数据准备函数"""
    
    def test_empty_trades_list(self):
        """测试空交易列表的处理"""
        # 输入: 空列表
        # 预期: 返回空的图表数据结构
        
    def test_single_profit_trade(self):
        """测试单笔盈利交易"""
        # 输入: 包含一笔盈利交易的列表 (return_rate > 0)
        # 预期: profit_points包含一个点，其他为空
        
    def test_single_loss_trade(self):
        """测试单笔亏损交易"""
        # 输入: 包含一笔亏损交易的列表 (return_rate < 0)
        # 预期: loss_points包含一个点，其他为空
        
    def test_single_neutral_trade(self):
        """测试单笔平盈交易"""
        # 输入: return_rate为0的交易
        # 预期: neutral_points包含一个点
        
    def test_mixed_trades(self):
        """测试包含盈利、亏损、平盈的混合交易"""
        # 输入: 混合类型的交易列表
        # 预期: 正确分类到不同的点集中
        
    def test_timestamp_formatting(self):
        """测试时间戳格式化正确性"""
        # 输入: Unix时间戳
        # 预期: 转换为人类可读时间格式 (YYYY-MM-DD HH:mm:ss)
        
    def test_tooltip_info_generation(self):
        """测试tooltip信息生成"""
        # 输入: 完整的交易记录
        # 预期: tooltip包含所有必要字段且格式正确
        
    def test_missing_fields_handling(self):
        """测试缺少字段的处理"""
        # 输入: 缺少某些字段的交易记录
        # 预期: 使用默认值，不抛出异常
        
    def test_invalid_data_types(self):
        """测试无效数据类型的处理"""
        # 输入: 字段类型错误的数据
        # 预期: 合理的类型转换或错误提示
```

#### 4.2 辅助函数测试

**测试类**: `TestChartUtilityFunctions`

```python
class TestChartUtilityFunctions:
    """测试图表相关工具函数"""
    
    def test_convert_timestamp_for_chart(self):
        """测试时间戳转换函数"""
        # 输入: [1718582400, 0, -1]
        # 预期: [1718582400000, 0, -1000]
        
    def test_calculate_point_size_normal_range(self):
        """测试正常范围的散点大小计算"""
        # 输入: value=50, min=0, max=100, base=8, max_size=20
        # 预期: 14 (中间值)
        
    def test_calculate_point_size_edge_cases(self):
        """测试边界情况的散点大小计算"""
        # 输入: min_val == max_val
        # 预期: 返回base_size
        
    def test_generate_tooltip_info_complete(self):
        """测试完整交易记录的tooltip生成"""
        # 输入: 包含所有字段的交易记录
        # 预期: 包含所有格式化字段的字典
        
    def test_generate_tooltip_info_partial(self):
        """测试部分字段的tooltip生成"""
        # 输入: 只包含核心字段的交易记录
        # 预期: 缺失字段使用默认值
```

#### 4.3 数据验证函数测试

**测试类**: `TestDataValidation`

```python
class TestDataValidation:
    """测试数据验证相关函数"""
    
    def test_validate_chart_data_valid(self):
        """测试有效数据的验证"""
        # 输入: 正确格式的交易列表
        # 预期: (True, "")
        
    def test_validate_chart_data_empty(self):
        """测试空数据的验证"""
        # 输入: 空列表
        # 预期: (False, "交易数据为空")
        
    def test_validate_chart_data_missing_fields(self):
        """测试缺少必要字段的验证"""
        # 输入: 缺少buy_timestamp字段的数据
        # 预期: (False, "缺少必要字段: buy_timestamp")
        
    def test_optimize_chart_data_small_dataset(self):
        """测试小数据集的优化（不需要采样）"""
        # 输入: 100条交易记录
        # 预期: 返回原始数据
        
    def test_optimize_chart_data_large_dataset(self):
        """测试大数据集的优化（需要采样）"""
        # 输入: 10000条交易记录
        # 预期: 返回采样后的数据，保持关键特征
```

### 5. 集成测试用例设计

#### 5.1 报告生成集成测试

**测试类**: `TestReportGenerationIntegration`

```python
class TestReportGenerationIntegration:
    """测试报告生成集成功能"""
    
    def test_generate_single_run_report_with_chart(self):
        """测试包含图表的单次运行报告生成"""
        # 输入: 包含交易数据的结果JSON
        # 预期: 生成的HTML包含图表部分
        
    def test_html_template_rendering(self):
        """测试HTML模板渲染"""
        # 输入: 模拟的图表数据
        # 预期: 模板正确渲染，包含所有必要的HTML元素
        
    def test_javascript_data_injection(self):
        """测试JavaScript数据注入"""
        # 输入: 图表数据
        # 预期: 数据正确注入到HTML中的JavaScript变量
        
    def test_echarts_cdn_inclusion(self):
        """测试ECharts CDN引入"""
        # 预期: HTML包含正确的ECharts CDN链接
```

#### 5.2 端到端功能测试

**测试类**: `TestEndToEndFunctionality`

```python
class TestEndToEndFunctionality:
    """测试端到端功能"""
    
    def test_complete_workflow_small_dataset(self):
        """测试小数据集的完整工作流"""
        # 输入: 10笔交易的回测结果
        # 预期: 完整生成包含图表的HTML报告
        
    def test_complete_workflow_large_dataset(self):
        """测试大数据集的完整工作流"""
        # 输入: 5000笔交易的回测结果
        # 预期: 完整生成包含采样优化后图表的HTML报告
        
    def test_complete_workflow_edge_cases(self):
        """测试边界情况的完整工作流"""
        # 输入: 只有盈利交易或只有亏损交易
        # 预期: 正常生成报告，图表显示正确
```

## 功能测试用例设计

### 6. 前端交互功能测试

#### 6.1 图表渲染测试

**测试场景**: 
1. **基础渲染测试**
   - 验证图表容器正确创建
   - 验证ECharts实例正确初始化
   - 验证散点正确显示在图表中

2. **数据分类显示测试**
   - 验证盈利交易显示为绿色散点
   - 验证亏损交易显示为红色散点
   - 验证平盈交易显示为灰色散点

3. **坐标轴测试**
   - 验证X轴显示时间格式正确
   - 验证Y轴显示利润数值正确
   - 验证坐标轴标签和单位正确

#### 6.2 交互功能测试

**测试场景**:
1. **Tooltip悬停测试**
   - 鼠标悬停在散点上显示详细信息
   - Tooltip内容包含所有必要字段
   - Tooltip格式化正确（时间、金额、百分比）

2. **缩放功能测试**
   - 鼠标滚轮缩放功能正常
   - 数据缩放器（DataZoom）滑块功能正常
   - 双击重置缩放功能正常

3. **平移功能测试**
   - 图表拖拽平移功能正常
   - 平移后数据显示正确

4. **图例交互测试**
   - 点击图例隐藏/显示对应类型的散点
   - 图例状态与散点显示状态一致

### 7. 错误处理测试

#### 7.1 数据异常测试

**测试场景**:
1. **空数据处理**
   - 无交易数据时显示友好提示
   - 不抛出JavaScript错误

2. **数据格式异常处理**
   - 时间戳格式错误的处理
   - 利润数据为null/undefined的处理
   - 缺少必要字段的处理

3. **数值异常处理**
   - 极大或极小的利润值处理
   - 无效的时间戳处理
   - NaN或Infinity值的处理

#### 7.2 网络和加载异常测试

**测试场景**:
1. **ECharts库加载失败**
   - CDN不可用时的降级处理
   - 显示错误提示信息

2. **图表初始化失败**
   - 容器元素不存在时的处理
   - 浏览器不支持时的降级显示

## 性能测试用例设计

### 8. 数据处理性能测试

#### 8.1 数据量性能测试

**测试用例**: `TestPerformanceWithDataSize`

```python
def test_performance_small_dataset(self):
    """测试小数据集处理性能"""
    # 数据量: 100笔交易
    # 预期: 处理时间 < 100ms
    
def test_performance_medium_dataset(self):
    """测试中等数据集处理性能"""
    # 数据量: 1000笔交易
    # 预期: 处理时间 < 500ms
    
def test_performance_large_dataset(self):
    """测试大数据集处理性能"""
    # 数据量: 5000笔交易
    # 预期: 处理时间 < 2秒
    
def test_memory_usage_large_dataset(self):
    """测试大数据集内存使用"""
    # 数据量: 10000笔交易
    # 预期: 内存增长 < 50MB
```

#### 8.2 前端渲染性能测试

**手动测试场景**:
1. **图表渲染时间测试**
   - 不同数据量下的图表初始化时间
   - 交互操作的响应时间

2. **浏览器兼容性性能测试**
   - 在不同浏览器中的渲染性能对比
   - 移动设备上的性能表现

## 兼容性测试用例设计

### 9. 浏览器兼容性测试  

#### 9.1 主流浏览器测试

**测试矩阵**:

| 浏览器 | 版本 | 操作系统 | 测试重点 |
|--------|------|----------|----------|
| Chrome | 90+ | Windows/Mac/Linux | 完整功能测试 |
| Firefox | 85+ | Windows/Mac/Linux | 渲染和交互测试 |
| Safari | 14+ | Mac | Safari特有问题测试 |
| Edge | 85+ | Windows | 兼容性测试 |

**测试内容**:
- 图表正常显示
- 所有交互功能正常
- 性能表现可接受
- 无JavaScript错误

#### 9.2 响应式设计测试

**测试场景**:
1. **桌面端测试**
   - 1920x1080分辨率
   - 1366x768分辨率

2. **平板端测试**
   - iPad (1024x768)
   - Android平板 (800x1280)

3. **手机端测试**
   - iPhone (375x812)
   - Android手机 (360x640)

## 用户验收测试用例

### 10. 业务场景测试

#### 10.1 典型使用场景

**场景1：策略分析师查看交易分布**
- 打开包含100笔交易的回测报告
- 查看利润时间分布图
- 通过悬停查看具体交易详情
- 使用缩放功能查看特定时间段

**预期结果**:
- 图表清晰显示交易时间分布
- 能够识别盈利和亏损的时间模式
- 交互功能响应流畅

**场景2：研究员分析大数据集**
- 打开包含3000笔交易的回测报告
- 查看图表渲染性能
- 测试缩放和平移功能
- 验证数据采样效果

**预期结果**:
- 图表在合理时间内完成渲染
- 采样保持了关键数据特征
- 交互功能不卡顿

#### 10.2 异常场景测试

**场景1：处理异常数据**
- 包含异常利润值的交易数据（极大正值/负值）
- 包含无效时间戳的数据
- 包含缺失字段的数据

**预期结果**:
- 系统优雅处理异常数据
- 显示合理的错误提示
- 不影响正常数据的显示

## 自动化测试实现

### 11. 测试代码示例

#### 11.1 单元测试示例

```python
# test/utils/backtest_analysis/test_profit_time_chart.py

import pytest
import json
from datetime import datetime, timezone
from utils.backtest_analysis.report_generator import _prepare_profit_time_chart_data

class TestProfitTimeChart:
    """利润时间图表功能测试"""
    
    @pytest.fixture
    def sample_trades(self):
        """测试用的交易数据"""
        return [
            {
                'buy_timestamp': 1718582400,
                'sell_timestamp': 1718586000,
                'profit_usd': 45.67,
                'token_symbol': 'PEPE',
                'token_address': '0x123...',
                'buy_price': 0.000012,
                'sell_price': 0.000015,
                'return_rate': 0.25,
                'holding_hours': 1.0,
                'kol_count': 8
            },
            {
                'buy_timestamp': 1718590000,
                'sell_timestamp': 1718593600,
                'profit_usd': -23.45,
                'token_symbol': 'DOGE',
                'token_address': '0x456...',
                'buy_price': 0.05,
                'sell_price': 0.04,
                'return_rate': -0.2,
                'holding_hours': 1.0,
                'kol_count': 6
            }
        ]
    
    def test_prepare_chart_data_normal(self, sample_trades):
        """测试正常数据的图表准备"""
        result = _prepare_profit_time_chart_data(sample_trades)
        
        assert 'chart_data' in result
        assert 'chart_config' in result
        assert 'statistics' in result
        
        # 检查数据分类
        chart_data = result['chart_data']
        assert len(chart_data['profit_points']) == 1
        assert len(chart_data['loss_points']) == 1
        assert len(chart_data['neutral_points']) == 0
        
        # 检查统计信息
        stats = result['statistics']
        assert stats['total_trades'] == 2
        assert stats['profit_trades'] == 1
        assert stats['loss_trades'] == 1
    
    def test_prepare_chart_data_empty(self):
        """测试空数据的处理"""
        result = _prepare_profit_time_chart_data([])
        
        assert result['statistics']['total_trades'] == 0
        assert len(result['chart_data']['profit_points']) == 0
        assert len(result['chart_data']['loss_points']) == 0
```

#### 11.2 集成测试示例

```python
# test/utils/backtest_analysis/test_report_integration.py

def test_single_run_report_with_chart(tmp_path):
    """测试包含图表的单次运行报告生成"""
    # 准备测试数据
    test_result = {
        'statistics': {'total_trades': 2, 'win_rate': 0.5},
        'trades': [/* 测试交易数据 */],
        'config': {'initial_capital': 10000}
    }
    
    # 创建临时JSON文件
    json_file = tmp_path / "test_result.json"
    with open(json_file, 'w') as f:
        json.dump(test_result, f)
    
    # 生成报告
    html_file = tmp_path / "test_report.html"
    success = generate_single_run_report(str(json_file), str(html_file))
    
    assert success == True
    assert html_file.exists()
    
    # 检查HTML内容
    html_content = html_file.read_text()
    assert 'profit-time-chart' in html_content
    assert 'echarts' in html_content.lower()
    assert '交易利润时间分布图' in html_content
```

---

**关联链接 (Related Links):**
- **依据需求 (Based on Requirements):** @profit_time_chart_requirements_ai.md
- **依据技术方案 (Based on Tech Plan):** @profit_time_chart_dev_plan_ai.md
- **跟踪任务 (Tracked by):** @profit_time_chart_todo_list.md
- **测试执行结果 (Test Results):** 待执行后更新链接 