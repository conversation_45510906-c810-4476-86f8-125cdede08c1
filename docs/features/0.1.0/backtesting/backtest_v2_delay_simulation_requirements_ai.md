# 回测V2延迟模拟功能产品需求文档（简化版）

**生成时间**: 2025-06-20T17:30:00+08:00  
**版本**: 0.1.0  
**模块**: backtesting  
**文档类型**: 产品需求规格  
**生成方式**: AI生成  
**负责人**: 产品经理

## 1. 项目背景 (Project Background)

### 1.1 业务背景

memeMonitor项目已成功实现了交易延迟监控功能，能够实时收集和分析从KOL信号触发到实际交易执行的延迟数据。当前回测引擎V2假设交易能够即时执行，这与真实交易环境存在显著差异。

为了提高回测结果的准确性和可信度，我们需要在回测中引入固定的交易延迟模拟。

### 1.2 目标用户

- **量化分析师**: 需要更准确的回测结果来验证策略有效性
- **策略开发者**: 需要了解延迟对策略收益的真实影响
- **投资决策者**: 需要基于真实延迟的策略表现数据做出投资决策

### 1.3 核心价值主张

通过在回测中引入可配置的固定延迟，使回测结果更接近实际交易表现，为用户提供更可靠的策略评估依据。

## 2. 功能需求概述 (Functional Requirements Overview)

### 2.1 核心功能

**FR001 - 固定延迟模拟**
- 支持配置固定的延迟秒数（如30秒、60秒等）
- 延迟应用于买入信号到实际交易执行的时间差
- 延迟期间的价格变化应反映到最终交易价格中

**FR002 - 延迟配置管理**
- 延迟模拟功能可通过配置开关控制（默认禁用）
- 支持配置固定延迟秒数
- 支持配置最大延迟限制（防止异常配置）

**FR003 - 延迟感知价格查询**
- 能够获取延迟后时间点的准确价格数据
- 计算延迟期间的价格变化影响
- 处理价格数据缺失的情况

**FR004 - 结果分析增强**
- 在回测结果中显示延迟统计信息
- 提供延迟对收益影响的分析
- 支持延迟前后结果对比

**FR005 - 扩展接口预留**
- 设计延迟策略接口，为未来支持统计延迟、分布采样等模式预留扩展点
- 当前只实现固定延迟策略

## 3. 详细功能规格 (Detailed Functional Specifications)

### 3.1 延迟模拟配置

#### 3.1.1 配置参数

**YAML格式示例：**
```yaml
# 回测配置文件扩展 - 延迟模拟配置（JSON对象格式）
delay_simulation:
  enabled: false                    # 默认禁用，确保向后兼容
  strategy: "fixed"                 # 当前只支持固定延迟策略
  fixed_delay_seconds: 30.0         # 固定延迟30秒
  max_delay_seconds: 300.0          # 最大延迟限制5分钟
```

**JSON格式示例：**
```json
{
  "delay_simulation": {
    "enabled": false,
    "strategy": "fixed",
    "fixed_delay_seconds": 30.0,
    "max_delay_seconds": 300.0
  }
}
```

#### 3.1.2 配置验证
- `fixed_delay_seconds` 必须为非负数
- `fixed_delay_seconds` 不能超过 `max_delay_seconds`
- `max_delay_seconds` 默认为300秒（5分钟）

### 3.2 延迟应用逻辑

#### 3.2.1 信号处理流程
```
原始流程:
KOL信号(10:00:00) → 立即查询价格($1.00) → 模拟交易

延迟模拟流程:
KOL信号(10:00:00) → 延迟30秒 → 查询延迟后价格(10:00:30, $1.05) → 模拟交易
```

#### 3.2.2 价格影响计算
- 记录原始信号时间的价格
- 获取延迟后时间点的价格
- 计算价格变化百分比和绝对变化
- 评估价格变化对交易的影响

### 3.3 结果展示

#### 3.3.1 延迟统计信息
```json
{
  "delay_simulation_summary": {
    "enabled": true,
    "strategy": "fixed",
    "delay_seconds": 30.0,
    "total_signals_processed": 150,
    "avg_price_change_percent": 1.2,
    "positive_price_changes": 105,    // 延迟期间价格上涨的信号数量
    "negative_price_changes": 45      // 延迟期间价格下跌的信号数量
  }
}
```

#### 3.3.2 延迟影响分析
```json
{
  "delay_impact_analysis": {
    "total_return_impact_percent": -1.5,     // 延迟对总收益的影响(%)
    "win_rate_impact_percent": -2.3,         // 延迟对胜率的影响(%)
    "avg_trade_impact_percent": -0.8,        // 延迟对单笔交易的平均影响(%)
    "max_price_change_percent": 5.2,         // 延迟期间最大价格变化(%)
    "min_price_change_percent": -3.1         // 延迟期间最小价格变化(%)
  }
}
```

## 4. 非功能性需求 (Non-Functional Requirements)

### 4.1 性能要求

**NFR001 - 执行性能**
- 启用延迟模拟后，回测执行时间增长 < 15%
- 内存使用增长 < 5%

**NFR002 - 价格查询性能**
- 延迟后价格查询时间 < 100ms
- 支持批量价格查询优化

### 4.2 兼容性要求

**NFR003 - 向后兼容**
- 现有回测配置在未启用延迟模拟时行为保持不变
- 现有API接口和命令行参数保持兼容
- 结果文件格式向后兼容

### 4.3 可靠性要求

**NFR004 - 错误处理**
- 价格数据获取失败时的fallback机制
- 详细的错误日志和用户友好的错误提示
- 配置参数验证和错误提示

## 5. 验收标准 (Acceptance Criteria)

### 5.1 功能验收

**AC001 - 固定延迟模拟**
- [ ] 能够配置固定延迟秒数
- [ ] 延迟正确应用到买入信号
- [ ] 延迟后的价格获取准确

**AC002 - 配置管理**
- [ ] 延迟模拟可通过配置开关控制
- [ ] 配置参数验证正确
- [ ] 向后兼容性完整

**AC003 - 结果展示**
- [ ] 回测结果包含延迟统计信息
- [ ] 延迟影响分析数据准确
- [ ] 结果格式符合规范

### 5.2 性能验收

**AC004 - 性能基准**
- [ ] 启用延迟模拟后回测执行时间增长 < 15%
- [ ] 内存使用增长 < 5%
- [ ] 价格查询性能 < 100ms

### 5.3 质量验收

**AC005 - 错误处理**
- [ ] 价格数据不可用时有合理fallback
- [ ] 配置错误有清晰提示
- [ ] 所有异常情况都有适当处理

## 6. 实施优先级 (Implementation Priority)

### 6.1 MVP版本（必需功能）
1. **固定延迟配置** - 基本的延迟配置和开关
2. **延迟应用逻辑** - 在信号处理中应用固定延迟
3. **价格查询** - 获取延迟后时间点的价格
4. **基础结果展示** - 基本的延迟统计信息

### 6.2 完整版本（期望功能）
1. **延迟影响分析** - 详细的影响分析和统计
2. **错误处理完善** - 完整的异常处理机制
3. **性能优化** - 价格查询缓存和批量处理
4. **扩展接口** - 为未来延迟策略预留接口

## 7. 未来扩展规划 (Future Extensions)

### 7.1 延迟策略接口设计
```python
from abc import ABC, abstractmethod

class DelayStrategy(ABC):
    """延迟策略抽象接口"""
    
    @abstractmethod
    async def calculate_delay(self, signal_timestamp: datetime) -> float:
        """计算延迟秒数"""
        pass
    
    @abstractmethod
    def get_strategy_name(self) -> str:
        """获取策略名称"""
        pass

class FixedDelayStrategy(DelayStrategy):
    """固定延迟策略实现"""
    
    def __init__(self, delay_seconds: float):
        self.delay_seconds = delay_seconds
    
    async def calculate_delay(self, signal_timestamp: datetime) -> float:
        return self.delay_seconds
    
    def get_strategy_name(self) -> str:
        return "fixed"

# 未来可扩展的策略：
# class StatisticalDelayStrategy(DelayStrategy): ...
# class DistributionSamplingStrategy(DelayStrategy): ...
```

### 7.2 预留扩展点
- 延迟数据加载接口（为统计延迟预留）
- 延迟策略工厂模式
- 配置参数扩展结构

## 8. 风险评估 (Risk Assessment)

### 8.1 技术风险

**风险1: 价格数据获取延迟**
- **影响**: 回测执行时间增长
- **缓解措施**: 价格数据缓存，批量获取优化
- **应急方案**: 使用现有价格数据，记录时间差异

### 8.2 业务风险

**风险2: 结果差异引起用户困惑**
- **影响**: 用户不理解延迟前后结果差异
- **缓解措施**: 详细的文档说明，清晰的结果展示
- **应急方案**: 提供延迟前后对比功能

## 9. 成功指标 (Success Metrics)

### 9.1 功能指标
- 延迟模拟功能使用率 > 20%
- 配置错误率 < 3%
- 用户满意度评分 > 4.0/5.0

### 9.2 性能指标
- 启用延迟后回测执行时间增长 < 15%
- 价格查询平均时间 < 100ms
- 系统稳定性 > 99.5%

---

**关联链接 (Related Links):**
- **技术实现方案 (Technical Plan)**: @backtest_v2_delay_simulation_dev_plan_ai.md
- **测试用例 (Test Cases)**: @backtest_v2_delay_simulation_test_cases_ai.md
- **跟踪任务 (Tracked by)**: @backtest_v2_delay_simulation_todo_list.md
- **回测V2基础需求**: @backtest_v2_requirements_ai.md 