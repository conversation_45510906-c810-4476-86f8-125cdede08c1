# KOL胜率过滤功能 - 技术实现方案

**创建日期**: 2025-06-06
**功能名称**: `kol_winrate_filter`
**相关模块**: `monitor_kol_activity`

## 1. 概述
本次修改的核心是在 `_execute_kol_buy_strategy` 函数中引入一个逻辑分支。通过检查策略配置中的新字段 `kol_min_winrate`，来决定是执行包含KOL胜率过滤的增强版聚合查询，还是执行原有的聚合查询，从而实现功能的向后兼容和可配置性。

## 2. 文件修改详情

### 2.1. `models/config.py`
-   **目标**: 扩展策略配置模型以支持胜率阈值。
-   **修改点**: 在 `SingleKolStrategyConfig` Pydantic模型类中。
-   **具体操作**:
    -   在 `kol_account_max_txs` 字段定义之后，添加一个新的可选字段：
        ```python
        kol_min_winrate: Optional[float] = Field(default=None, description="KOL账号最低胜率, 范围 0.0-1.0")
        ```

### 2.2. `workflows/monitor_kol_activity/handler.py`
-   **目标**: 实现核心的逻辑切换和执行。
-   **修改点**: `_execute_kol_buy_strategy` 函数。
-   **实现伪代码**:
    ```python
    async def _execute_kol_buy_strategy(strategy_params: SingleKolStrategyConfig):
        # ... (日志记录和时间变量初始化) ...
        # 在日志中增加 kol_min_winrate 的打印，方便调试
        logger.info(f"... kol_min_winrate={strategy_params.kol_min_winrate}")

        # 核心逻辑分支
        if strategy_params.kol_min_winrate and strategy_params.kol_min_winrate > 0:
            # ==> 分支 A: 启用胜率过滤
            logger.info("Applying KOL winrate filter...")
            pipeline = build_winrate_pipeline(strategy_params, current_time, start_time)
        else:
            # ==> 分支 B: 维持原有逻辑
            logger.info("Not using KOL winrate filter.")
            pipeline = build_original_pipeline(strategy_params, current_time, start_time)

        # ... (后续代码，如执行聚合查询、处理结果等，保持不变) ...
    ```
-   **聚合查询实现**:
    -   **分支 A (`build_winrate_pipeline`)**:
        -   此 `pipeline` 将基于用户请求中提供的聚合查询结构。
        -   所有硬编码的值（如时间戳, `500`, `10`, `800`, `0.5`, `6`）都将替换为从 `strategy_params` 对象动态获取的变量，例如：
            -   `'cost_usd': {'$gt': strategy_params.transaction_min_amount}`
            -   `'$gte': ['$$enriched_kol.txs', strategy_params.kol_account_min_txs or 0]` (使用 `or` 提供默认值)
            -   `'$gt': ['$$enriched_kol.winrate', strategy_params.kol_min_winrate]`
            -   `'kol_wallets_count': {'$gte': strategy_params.kol_account_min_count}`
        -   此查询将不包含对 `records` 字段的 `$push` 操作，以提高效率。
    -   **分支 B (`build_original_pipeline`)**:
        -   直接使用函数中现存的 `pipeline` 定义。

## 3. 潜在影响与风险评估
-   **数据兼容性**: 新的聚合查询不返回 `records` 字段。经过代码分析，`_execute_kol_buy_strategy` 函数的下游逻辑并不直接依赖此字段，而是依赖 `_id` (token address) 和 `kol_wallets`。因此，此变更是安全的。
-   **性能**: 新的查询增加了两个 `$lookup` 和相关的处理阶段，可能会比原查询略慢。但由于避免了拉取和传输大量的 `records` 数据，对于有很多买入记录的代币，性能可能反而有所提升。总体影响预计不大，且在可接受范围内。
-   **配置管理**: 存量配置不受影响，符合向后兼容要求。新功能需要用户在数据库中手动更新 `config` 集合中的 `kol_activity` 文档，为其策略添加 `kol_min_winrate` 字段才能启用。 