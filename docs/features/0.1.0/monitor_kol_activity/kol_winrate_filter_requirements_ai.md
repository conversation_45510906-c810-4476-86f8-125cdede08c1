# KOL胜率过滤功能 - 详细需求规格

**创建日期**: 2025-06-06
**功能名称**: `kol_winrate_filter`
**相关模块**: `monitor_kol_activity`

## 1. 需求背景
当前 `monitor_kol_activity` 工作流在筛选买入信号时，主要依据KOL的近期购买行为（如购买KOL数量、交易额等），但未考虑这些KOL的历史盈利能力。为了提高信号的质量，需要引入一个基于KOL历史交易胜率的可选过滤条件。

## 2. 核心需求
在发现买入信号的逻辑中，增加一项可选的过滤功能，允许策略配置指定一个最低KOL胜率。

## 3. 验收标准
1.  **配置模型更新**:
    *   在 `models.config.SingleKolStrategyConfig` Pydantic模型中，必须新增一个名为 `kol_min_winrate` 的字段。
    *   该字段必须是可选的 (`Optional[float]`)，默认值为 `None` 或不设置。
    *   字段描述应清晰说明其用途："KOL账号最低胜率, 范围 0.0-1.0"。

2.  **逻辑向后兼容**:
    *   对于现有的、未包含 `kol_min_winrate` 字段的策略配置，系统的行为必须保持不变。
    *   当 `kol_min_winrate` 字段在配置中为 `None` 或其值小于等于 `0` 时，系统必须沿用现有的、不包含胜率过滤的聚合查询逻辑。

3.  **胜率过滤逻辑**:
    *   当 `kol_min_winrate` 字段被设置为一个大于 `0` 的浮点数（例如 `0.5`）时，系统必须启用新的胜率过滤逻辑。
    *   新的逻辑必须使用一个新的MongoDB聚合查询。
    *   该查询必须能够正确地关联 `kol_wallet_activity` (主表), `kol_wallets` (获取KOL身份), 和 `gmgn_wallet_stats` (获取KOL胜率) 这三个集合。
    *   查询结果中的KOL列表必须被过滤，仅保留那些在 `gmgn_wallet_stats` 集合中 `period: 'all'` 的记录里，`winrate` 字段值 **大于** `kol_min_winrate` 配置值的KOL。

4.  **信号触发条件**:
    *   经过胜率过滤后，对于某个代币，如果满足条件的KOL钱包数量仍然 **大于或等于** `kol_account_min_count` 配置的阈值，该代币才被视为一个有效的买入信号候选者。
    *   如果过滤后KOL数量不足，则不应产生信号。

5.  **查询效率**:
    *   启用胜率过滤的新聚合查询应被优化。在能够满足逻辑的前提下，不应拉取非必需的数据，特别是应避免在聚合管道中收集完整的交易记录列表 (`records`)。 