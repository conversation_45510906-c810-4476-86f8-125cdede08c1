# KOL胜率过滤功能 - 测试用例设计

**创建日期**: 2025-06-06
**功能名称**: `kol_winrate_filter`
**相关模块**: `monitor_kol_activity`
**测试文件**: `test/workflows/monitor_kol_activity/test_handler.py`

## 1. 测试目标
验证 `_execute_kol_buy_strategy` 函数能够根据 `kol_min_winrate` 配置项正确地选择和构建MongoDB聚合查询管道，确保新旧逻辑的正确切换和参数的动态注入。

## 2. 测试策略
我们将采用单元测试的方式，直接对 `_execute_kol_buy_strategy` 函数进行测试。核心是 **Patch** 数据库访问对象 (`KOLWalletActivityDAO`)，并捕获传递给其 `aggregate` 方法的 `pipeline` 参数，然后对该 `pipeline` 的结构和内容进行断言。

## 3. 测试用例详情

| 用例方法 | 用例概述 | 前置条件 | 输入 | 预期输出 |
| :--- | :--- | :--- | :--- | :--- |
| `test_execute_kol_buy_strategy_with_winrate_filter` | 验证当 `kol_min_winrate` 被设置为有效值时，是否构建了新的、带胜率过滤的聚合查询。 | 1. 创建一个 `SingleKolStrategyConfig` 实例，`kol_min_winrate=0.6`, `kol_account_min_txs=10`。 <br> 2. Patch `KOLWalletActivityDAO`。 | 调用 `_execute_kol_buy_strategy` 并传入上述配置对象。 | 1. `KOLWalletActivityDAO.aggregate` 被调用一次。 <br> 2. 传递给 `aggregate` 的 `pipeline` **必须** 包含一个 `$lookup` 到 `gmgn_wallet_stats` 的阶段。 <br> 3. `pipeline` 中过滤胜率的条件必须是 `{'$gt': ['$$enriched_kol.winrate', 0.6]}`。 <br> 4. `pipeline` 中过滤交易次数的条件必须是 `{'$gte': ['$$enriched_kol.txs', 10]}`。 <br> 5. `pipeline` **不应** 包含 `'$group': {'records': {'$push': '$$ROOT'}}` 阶段。 |
| `test_execute_kol_buy_strategy_without_winrate_filter` | 验证当 `kol_min_winrate` 为 `None` (默认) 时，是否回退到原有的、不带胜率过滤的聚合查询。 | 1. 创建一个 `SingleKolStrategyConfig` 实例，`kol_min_winrate` 保持默认 (`None`)。 <br> 2. Patch `KOLWalletActivityDAO`。 | 调用 `_execute_kol_buy_strategy` 并传入上述配置对象。 | 1. `KOLWalletActivityDAO.aggregate` 被调用一次。 <br> 2. 传递给 `aggregate` 的 `pipeline` **不应** 包含对 `gmgn_wallet_stats` 的 `$lookup`。 <br> 3. `pipeline` **必须** 包含 `'$group': {'records': {'$push': '$$ROOT'}}` 阶段。 <br> 4. `pipeline` 的结构应与修改前的旧版 `pipeline` 完全一致。 |
| `test_execute_kol_buy_strategy_with_zero_winrate` | 验证当 `kol_min_winrate` 为 `0` 时，是否仍回退到原有的聚合查询。 | 1. 创建一个 `SingleKolStrategyConfig` 实例，`kol_min_winrate=0`。 <br> 2. Patch `KOLWalletActivityDAO`。 | 调用 `_execute_kol_buy_strategy` 并传入上述配置对象。 | 1. `KOLWalletActivityDAO.aggregate` 被调用一次。 <br> 2. 传递给 `aggregate` 的 `pipeline` **不应** 包含对 `gmgn_wallet_stats` 的 `$lookup`，应为旧版 `pipeline`。 |
| `test_execute_kol_buy_strategy_with_winrate_filter_and_no_min_txs` | 验证当启用胜率过滤，但 `kol_account_min_txs` 为 `None` 时，查询能否正确处理并使用默认值 `0`。 | 1. 创建一个 `SingleKolStrategyConfig` 实例，`kol_min_winrate=0.6`, `kol_account_min_txs=None`。 <br> 2. Patch `KOLWalletActivityDAO`。 | 调用 `_execute_kol_buy_strategy` 并传入上述配置对象。 | 1. `KOLWalletActivityDAO.aggregate` 被调用一次。 <br> 2. 传递给 `aggregate` 的 `pipeline` **必须** 包含胜率过滤逻辑。 <br> 3. `pipeline` 中过滤交易次数的条件必须是 `{'$gte': ['$$enriched_kol.txs', 0]}`。 |

## 4. 测试辅助函数修改
- 需要修改测试文件中的 `_create_mock_strategy_config` 辅助函数，为其增加 `kol_min_winrate` 参数，以便在测试用例中方便地创建不同配置的实例。 