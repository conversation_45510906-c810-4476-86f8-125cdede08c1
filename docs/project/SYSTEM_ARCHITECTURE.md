# memeMonitor 系统架构详解

## 架构概览

memeMonitor 采用**分层微服务架构**，结合**工作流驱动**的设计理念，实现高度模块化和可扩展的加密货币监控与交易系统。

## 整体架构图

```mermaid
graph TB
    %% 外部接入层
    subgraph "外部接入层"
        USER[用户界面]
        THIRD[第三方系统]
        MOBILE[移动端]
    end

    %% API网关层
    subgraph "API网关层"
        NGINX[Nginx负载均衡]
        API[FastAPI服务]
        AUTH[认证授权]
    end

    %% 应用服务层
    subgraph "应用服务层"
        WM[工作流管理器<br/>WorkflowManager]
        ATM[自动交易管理器<br/>AutoTradeManager]
        BE[回测引擎<br/>BacktestEngine]
        SM[信号管理器<br/>SignalManager]
        STAT[统计分析器<br/>StatisticsManager]
    end

    %% 业务逻辑层
    subgraph "业务逻辑层"
        subgraph "数据采集模块"
            SPIDER1[GMGN爬虫]
            SPIDER2[Solana监控]
            SPIDER3[Twitter爬虫]
            SPIDER4[KOL监控]
        end
        
        subgraph "交易执行模块"
            TO[交易编排器<br/>TradeOrchestrator]
            CS[渠道选择器<br/>ChannelSelector]
            SR[滑点重试<br/>SlippageRetry]
            TRM[交易记录管理器<br/>TradeRecordManager]
        end
        
        subgraph "分析处理模块"
            SA[统计分析]
            RA[风险分析]
            KA[KOL分析]
            TA[技术分析]
        end
    end

    %% 数据访问层
    subgraph "数据访问层"
        DAO1[信号DAO]
        DAO2[交易记录DAO]
        DAO3[配置DAO]
        DAO4[其他DAO...]
    end

    %% 基础设施层
    subgraph "基础设施层"
        MONGO[(MongoDB主库)]
        REDIS[(Redis缓存)]
        MQ[消息队列]
        LOG[日志系统]
        MONITOR[监控系统]
    end

    %% 外部服务
    subgraph "外部服务"
        GMGN_API[GMGN API]
        JUPITER[Jupiter聚合器]
        SOLANA[Solana RPC]
        TWITTER_API[Twitter API]
        TG[Telegram]
        FEISHU[飞书]
    end

    %% 连接关系
    USER --> NGINX
    THIRD --> NGINX
    MOBILE --> NGINX
    
    NGINX --> API
    API --> AUTH
    
    API --> WM
    API --> ATM
    API --> BE
    API --> SM
    API --> STAT
    
    WM --> SPIDER1
    WM --> SPIDER2
    WM --> SPIDER3
    WM --> SPIDER4
    
    ATM --> TO
    TO --> CS
    TO --> SR
    TO --> TRM
    
    SM --> SA
    SM --> RA
    SM --> KA
    SM --> TA
    
    SPIDER1 --> DAO1
    SPIDER2 --> DAO2
    SPIDER3 --> DAO3
    SPIDER4 --> DAO4
    
    TO --> DAO2
    TRM --> DAO2
    
    DAO1 --> MONGO
    DAO2 --> MONGO
    DAO3 --> MONGO
    DAO4 --> MONGO
    
    API --> REDIS
    WM --> MQ
    ATM --> MQ
    
    SPIDER1 --> GMGN_API
    SPIDER2 --> SOLANA
    SPIDER3 --> TWITTER_API
    
    TO --> GMGN_API
    TO --> JUPITER
    TO --> SOLANA
    
    ATM --> TG
    STAT --> FEISHU
```

## 核心架构特点

### 1. 分层架构设计

#### 外部接入层
- **职责**: 提供多端访问入口
- **组件**: Web界面、移动端、第三方系统接入
- **特点**: 统一入口，多端适配

#### API网关层  
- **职责**: 请求路由、负载均衡、认证授权
- **组件**: Nginx、FastAPI、认证模块
- **特点**: 高可用、安全防护、性能优化

#### 应用服务层
- **职责**: 核心业务逻辑编排
- **组件**: 各类管理器和引擎
- **特点**: 服务化、可扩展、高内聚

#### 业务逻辑层
- **职责**: 具体业务实现
- **组件**: 按功能领域划分的模块
- **特点**: 低耦合、可复用、易测试

#### 数据访问层
- **职责**: 数据存储抽象
- **组件**: DAO模式实现
- **特点**: 统一接口、事务管理、缓存优化

#### 基础设施层
- **职责**: 提供技术基础设施
- **组件**: 数据库、缓存、消息队列等
- **特点**: 高可靠、可监控、可扩展

### 2. 工作流驱动架构

```mermaid
graph LR
    subgraph "工作流引擎"
        WF1[数据采集工作流]
        WF2[信号处理工作流]
        WF3[交易执行工作流]
        WF4[统计分析工作流]
    end
    
    subgraph "节点组件"
        N1[数据源节点]
        N2[处理节点]
        N3[过滤节点]
        N4[存储节点]
        N5[通知节点]
    end
    
    WF1 --> N1
    WF2 --> N2
    WF2 --> N3
    WF3 --> N4
    WF4 --> N5
```

**特点**:
- **声明式配置**: 通过YAML配置定义工作流
- **节点化处理**: 每个处理步骤都是独立节点
- **动态编排**: 支持运行时调整工作流
- **并行执行**: 支持节点并行处理
- **错误恢复**: 内置重试和failover机制

### 3. 微服务化设计

```mermaid
graph TB
    subgraph "数据采集服务"
        DS1[GMGN数据服务]
        DS2[Solana数据服务]
        DS3[社交数据服务]
    end
    
    subgraph "交易执行服务"
        TS1[GMGN交易服务]
        TS2[Jupiter交易服务]
        TS3[交易管理服务]
    end
    
    subgraph "分析处理服务"
        AS1[信号分析服务]
        AS2[风险评估服务]
        AS3[统计分析服务]
        AS4[交易延迟监控服务]
    end
    
    subgraph "基础服务"
        BS1[配置管理服务]
        BS2[通知服务]
        BS3[监控服务]
    end
```

## 数据流向分析

### 1. 数据采集流

```mermaid
sequenceDiagram
    participant WM as 工作流管理器
    participant SP as 爬虫节点
    participant PROXY as 代理池
    participant API as 外部API
    participant DAO as 数据访问层
    participant DB as 数据库

    WM->>SP: 启动数据采集
    SP->>PROXY: 获取代理IP
    PROXY-->>SP: 返回可用代理
    SP->>API: 通过代理请求数据
    API-->>SP: 返回数据
    SP->>DAO: 存储原始数据
    DAO->>DB: 持久化存储
    SP->>WM: 报告采集结果
```

### 2. 信号生成流

```mermaid
sequenceDiagram
    participant DM as 数据监控
    participant SF as 信号过滤器
    participant SG as 信号生成器
    participant CACHE as 缓存层
    participant NOTIFY as 通知服务

    DM->>SF: 检测到数据变化
    SF->>SF: 应用过滤规则
    SF->>SG: 生成信号候选
    SG->>SG: 信号验证和评分
    SG->>CACHE: 缓存信号数据
    SG->>NOTIFY: 发送信号通知
```

### 3. 交易执行流

```mermaid
sequenceDiagram
    participant ATM as 自动交易管理器
    participant TO as 交易编排器
    participant CS as 渠道选择器
    participant TS as 交易服务
    participant SR as 滑点重试
    participant TRM as 交易记录管理器

    ATM->>TO: 接收交易信号
    TO->>CS: 选择交易渠道
    CS-->>TO: 返回最优渠道
    TO->>TS: 执行交易
    TS-->>TO: 交易结果
    alt 交易失败且需要重试
        TO->>SR: 触发滑点重试
        SR->>TS: 调整参数重试
        TS-->>SR: 重试结果
    end
    TO->>TRM: 记录交易结果
    TRM->>ATM: 返回执行状态
```

### 4. 交易延迟监控流 **新增**

```mermaid
sequenceDiagram
    participant SM as 信号管理器
    participant TDM as 交易延迟监控器
    participant KTR as KOL交易时间解析器
    participant SSC as 信号抑制检查器
    participant TDC as 延迟计算器
    participant DAO as 延迟记录DAO
    participant DB as 数据库

    SM->>TDM: 接收到交易信号
    TDM->>SSC: 检查信号抑制状态
    SSC-->>TDM: 返回抑制检查结果
    alt 信号未被抑制
        TDM->>KTR: 解析KOL最后交易时间
        KTR->>KTR: 应用金额过滤条件
        KTR->>KTR: 在时间窗口内查找交易
        KTR-->>TDM: 返回KOL交易时间
        alt KOL交易时间查找成功
            TDM->>TDC: 计算交易延迟
            TDC-->>TDM: 返回延迟值
            TDM->>DAO: 存储延迟记录(正常状态)
        else KOL交易时间查找失败
            TDM->>DAO: 存储延迟记录(查找失败状态)
        end
    else 信号被抑制
        TDM->>DAO: 存储延迟记录(信号抑制状态)
    end
    DAO->>DB: 持久化延迟记录
    TDM->>SM: 完成延迟监控处理
```

## 技术栈选型

### 后端技术栈

| 层次          | 技术选型           | 使用原因               |
| ----------- | -------------- | ------------------ |
| **编程语言**    | Python 3.11+   | 丰富的数据科学生态、异步支持     |
| **Web框架**   | FastAPI        | 高性能、自动文档生成、类型检查    |
| **数据库**     | MongoDB        | 灵活Schema、水平扩展、文档存储 |
| **ORM**     | Beanie         | 异步MongoDB ODM、类型安全 |
| **缓存**      | Redis          | 高性能内存数据库、丰富数据结构    |
| **任务队列**    | 自研MessageQueue | 轻量级、与工作流深度集成       |
| **HTTP客户端** | aiohttp        | 异步HTTP客户端、代理支持     |
| **数据处理**    | pandas + numpy | 强大的数据分析能力          |

### 基础设施

| 组件       | 技术选型                    | 使用原因             |
| -------- | ----------------------- | ---------------- |
| **容器化**  | Docker + Docker Compose | 标准化部署、环境一致性      |
| **反向代理** | Nginx                   | 高性能、负载均衡、SSL终止   |
| **进程管理** | Supervisor              | 进程监控、自动重启        |
| **依赖管理** | Poetry                  | 现代Python包管理、依赖锁定 |
| **配置管理** | YAML + 环境变量             | 声明式配置、环境分离       |
| **日志系统** | Python logging          | 结构化日志、多级别输出      |

## 关键设计模式

### 1. 工作流模式 (Workflow Pattern)

```python
class Workflow:
    """工作流基类"""
    def __init__(self, name: str):
        self.name = name
        self.nodes = []
        self.connections = {}
    
    async def execute(self):
        """执行工作流"""
        for node in self.nodes:
            await node.process()
```

**优势**:
- 业务逻辑可视化
- 配置驱动的流程定义
- 易于扩展和维护

### 2. DAO模式 (Data Access Object)

```python
class BaseDAO:
    """DAO基类"""
    def __init__(self, model_class):
        self.model_class = model_class
    
    async def create(self, data):
        """创建记录"""
        return await self.model_class.create(data)
    
    async def find_by_id(self, id):
        """根据ID查找"""
        return await self.model_class.get(id)
```

**优势**:
- 数据访问统一化
- 业务逻辑与数据分离
- 便于单元测试

### 3. 策略模式 (Strategy Pattern)

```python
class TradingStrategy:
    """交易策略接口"""
    async def should_buy(self, signal) -> bool:
        raise NotImplementedError
    
    async def should_sell(self, signal) -> bool:
        raise NotImplementedError

class KOLFollowStrategy(TradingStrategy):
    """KOL跟单策略"""
    async def should_buy(self, signal) -> bool:
        return signal.type == "kol_buy"
```

**优势**:
- 策略可插拔
- 运行时策略切换
- 易于回测验证

### 4. 观察者模式 (Observer Pattern)

```python
class SignalEmitter:
    """信号发射器"""
    def __init__(self):
        self.observers = []
    
    def subscribe(self, observer):
        self.observers.append(observer)
    
    async def emit(self, signal):
        for observer in self.observers:
            await observer.handle_signal(signal)
```

**优势**:
- 事件驱动架构
- 松耦合通信
- 支持多订阅者

### 5. 重试策略模式 (Retry Strategy Pattern) **新增**

```python
class RetryStrategy:
    """重试策略抽象基类"""
    async def should_retry(self, attempt: int, exception: Exception) -> bool:
        raise NotImplementedError
    
    async def get_delay(self, attempt: int) -> float:
        raise NotImplementedError

class ExponentialBackoffStrategy(RetryStrategy):
    """指数退避重试策略"""
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
    
    async def should_retry(self, attempt: int, exception: Exception) -> bool:
        return attempt < self.max_retries
    
    async def get_delay(self, attempt: int) -> float:
        return self.base_delay * (2 ** attempt)

class CircuitBreaker:
    """断路器模式"""
    def __init__(self, failure_threshold: int = 5, recovery_timeout: float = 60.0):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        self.failure_count = 0
        self.last_failure_time = None
```

### 6. 代理管理模式 (Proxy Management Pattern)

```python
class ProxyPollBasic:
    """代理池抽象基类"""
    def get_proxy(self) -> ProxyInfo:
        raise NotImplementedError
    
    def mark_proxy_failed(self, proxy: ProxyInfo):
        raise NotImplementedError

class AsyncProxySession:
    """统一代理会话"""
    def __init__(self, proxy_poll: ProxyPollBasic):
        self.proxy_poll = proxy_poll
    
    async def request(self, method, url, **kwargs):
        # 自动代理轮换和重试
        pass
```

**优势**:
- 统一代理管理
- 自动故障转移
- 智能轮换策略
- 全局复用

### 7. 消息发送模式 (Message Sender Pattern)

```python
class MessageSender:
    """消息发送器抽象基类"""
    def send_message(self, message: str, **kwargs) -> bool:
        raise NotImplementedError

class TelegramMessageSender(MessageSender):
    """Telegram消息发送器"""
    def send_message(self, message: str, **kwargs) -> bool:
        # Telegram API 实现
        pass

class FeishuMessageSender(MessageSender):
    """飞书消息发送器"""
    def send_message(self, message: str, **kwargs) -> bool:
        # 飞书 API 实现
        pass
```

**优势**:
- 多渠道消息统一
- 可插拔消息渠道
- 消息格式标准化
- 发送状态追踪

#### 🔄 数据采集服务
*   **爬虫引擎**: `utils/spiders/` - 多源数据采集
*   **代理管理**: `utils/proxy/` + `utils/session.py` - 统一代理池和反反爬虫机制
*   **重试策略**: `utils/retry/` - 网络请求容错

#### 🛠️ 基础服务
*   **数据库连接**: `utils/connectors/` - MongoDB 连接管理
*   **消息发送**: `utils/message_sender/` - 统一消息通知服务
*   **配置管理**: 环境变量和配置文件管理
*   **日志系统**: 结构化日志记录

#### 📊 分析处理服务
*   **回测引擎**: `utils/backtest/` - 策略历史验证
*   **回测引擎V2**: `utils/backtest_v2/` - 新一代模块化回测系统
*   **数据分析**: `utils/backtest_analysis/` - 结果统计分析
*   **可视化工具**: 交易数据图表生成
*   **延迟监控**: `workflows/trading_delay_monitor/` - 实时交易延迟数据收集和分析

**优势**:
- 智能重试机制
- 服务过载保护
- 可配置的重试策略
- 全局复用

### 8. 延迟监控模式 (Delay Monitoring Pattern) **新增**

```python
class KOLTradeTimeResolver:
    """KOL交易时间解析器"""
    def __init__(self, kol_wallet_activity_dao: KOLWalletActivityDAO):
        self.kol_wallet_activity_dao = kol_wallet_activity_dao
    
    async def resolve_kol_last_trade_time(
        self, 
        signal: Signal, 
        time_window_hours: int = 48
    ) -> Optional[datetime]:
        """解析KOL最后交易时间，支持金额过滤"""
        # 获取交易金额过滤条件
        min_cost_usd = None
        if (signal.trigger_conditions and 
            hasattr(signal.trigger_conditions, 'transaction_min_amount')):
            min_cost_usd = signal.trigger_conditions.transaction_min_amount
        
        # 在时间窗口内查找符合条件的KOL活动
        activities = await self.kol_wallet_activity_dao.find_activities_in_time_range(
            wallet_address=signal.kol_wallet_address,
            token_address=signal.token_address,
            start_time=signal.signal_time - timedelta(hours=time_window_hours),
            end_time=signal.signal_time,
            min_cost_usd=min_cost_usd
        )
        
        # 返回最新的活动时间
        return activities[0].time if activities else None

class SignalSuppressionChecker:
    """信号抑制检查器"""
    def __init__(self, message_send_history_dao: TokenMessageSendHistoryDAO):
        self.message_send_history_dao = message_send_history_dao
    
    async def is_signal_suppressed(self, signal: Signal) -> bool:
        """检查信号是否因重复通知而被抑制"""
        # 检查在通知间隔内是否已有相同代币的通知
        recent_messages = await self.message_send_history_dao.find_recent_by_token(
            token_address=signal.token_address,
            since=signal.signal_time - timedelta(
                minutes=signal.same_token_notification_interval or 10
            )
        )
        return len(recent_messages) > 0

class TradingDelayCalculator:
    """交易延迟计算器"""
    def calculate_delay(
        self, 
        signal_time: datetime, 
        kol_trade_time: datetime
    ) -> float:
        """计算交易延迟（分钟）"""
        return (signal_time - kol_trade_time).total_seconds() / 60.0

class TradingDelayMonitor:
    """交易延迟监控器"""
    def __init__(
        self,
        kol_trade_resolver: KOLTradeTimeResolver,
        suppression_checker: SignalSuppressionChecker,
        delay_calculator: TradingDelayCalculator,
        delay_record_dao: TradingDelayRecordDAO
    ):
        self.kol_trade_resolver = kol_trade_resolver
        self.suppression_checker = suppression_checker
        self.delay_calculator = delay_calculator
        self.delay_record_dao = delay_record_dao
    
    async def monitor_signal_delay(self, signal: Signal) -> TradingDelayRecord:
        """监控信号延迟并记录结果"""
        # 检查信号抑制
        if await self.suppression_checker.is_signal_suppressed(signal):
            return await self._create_delay_record(
                signal, 
                DelayStatus.SIGNAL_SUPPRESSED
            )
        
        # 解析KOL交易时间
        kol_trade_time = await self.kol_trade_resolver.resolve_kol_last_trade_time(signal)
        if not kol_trade_time:
            return await self._create_delay_record(
                signal, 
                DelayStatus.KOL_TRADE_TIME_NOT_FOUND
            )
        
        # 计算延迟
        delay_minutes = self.delay_calculator.calculate_delay(
            signal.signal_time, 
            kol_trade_time
        )
        
        return await self._create_delay_record(
            signal, 
            DelayStatus.NORMAL,
            kol_trade_time=kol_trade_time,
            delay_minutes=delay_minutes
        )
```

**优势**:
- **实时延迟数据**: 为回测系统提供真实的延迟分布数据
- **智能时间解析**: 支持金额过滤的KOL交易时间查找算法
- **状态分类管理**: 清晰的延迟状态分类和异常情况处理
- **工作流集成**: 与现有工作流系统无缝集成
- **数据质量保证**: 完整的测试覆盖和数据验证机制

## 性能优化策略

### 1. 数据库优化

- **索引优化**: 为高频查询字段建立复合索引
- **连接池**: 使用连接池复用数据库连接
- **查询优化**: 使用聚合管道优化复杂查询
- **分片策略**: 基于时间的数据分片

### 2. 缓存策略

- **多级缓存**: 应用缓存 + Redis缓存
- **缓存预热**: 系统启动时预加载热点数据
- **缓存失效**: 基于TTL和业务逻辑的缓存失效

### 3. 异步处理

- **异步I/O**: 全面采用async/await模式
- **并发控制**: 使用信号量控制并发度
- **批量处理**: 批量数据库操作减少网络开销

### 4. 资源管理

- **连接复用**: HTTP连接池和数据库连接池
- **内存管理**: 及时释放大对象，避免内存泄漏
- **代理轮换**: 智能代理IP轮换算法

## 可扩展性设计

### 1. 水平扩展

- **无状态设计**: 应用层无状态，便于水平扩展
- **数据分片**: 支持数据库分片扩展
- **负载均衡**: 支持多实例负载均衡

### 2. 垂直扩展

- **模块化架构**: 按业务领域垂直拆分
- **服务分离**: 计算密集型服务独立部署
- **资源隔离**: 不同服务使用独立资源

### 3. 功能扩展

- **插件化**: 支持策略和节点插件化
- **配置驱动**: 通过配置扩展功能
- **API扩展**: RESTful API支持版本管理

## 安全性设计

### 1. 数据安全

- **敏感信息加密**: API密钥等敏感信息加密存储
- **访问控制**: 基于角色的访问控制
- **数据脱敏**: 日志中敏感数据脱敏

### 2. 网络安全

- **HTTPS通信**: 强制使用HTTPS
- **代理隔离**: 通过代理IP隔离真实IP
- **请求限流**: 防止API滥用

### 3. 运行时安全

- **输入验证**: 严格的输入参数验证
- **错误处理**: 安全的错误信息返回
- **日志审计**: 详细的操作日志记录

## 监控与运维

### 1. 系统监控

- **性能指标**: CPU、内存、网络、磁盘监控
- **业务指标**: 交易成功率、信号生成量等
- **错误监控**: 异常捕获和报警

### 2. 日志管理

- **结构化日志**: JSON格式的结构化日志
- **日志聚合**: 集中式日志收集和分析
- **日志轮转**: 基于时间和大小的日志轮转

### 3. 健康检查

- **服务健康检查**: 定期检查服务状态
- **依赖检查**: 检查外部依赖可用性
- **数据质量监控**: 监控数据质量指标

---

**最后更新**: 2025-06-20
**文档版本**: v1.2
**维护人**: 系统架构组
**更新内容**: 新增交易延迟监控模块的架构设计和数据流向说明