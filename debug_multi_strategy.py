#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试多策略验证逻辑
"""

import asyncio
from unittest.mock import Mock, patch
from workflows.daily_backtest_verification.components.verification_runner import (
    DailyBacktestVerificationRunner,
    VerificationResult,
    VerificationStatus,
    StrategyVerificationResult
)
from workflows.daily_backtest_verification.components.strategy_selector import (
    StrategyNotFoundError
)

async def debug_multi_strategy():
    """调试多策略验证"""
    runner = DailyBacktestVerificationRunner()
    
    # 设置配置数据
    config_data = {
        'strategies': {
            'kol_strategy_v1': {'enabled': True},
            'kol_strategy_v2': {'enabled': True},
            'invalid_strategy': {'enabled': True}  # 这个策略不存在，会失败
        }
    }
    
    print("开始调试多策略验证...")
    
    with patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._load_configurations') as mock_load_config, \
         patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._execute_backtest_with_validation') as mock_execute_backtest, \
         patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._read_backtest_result_with_validation') as mock_read_result, \
         patch('workflows.daily_backtest_verification.components.verification_runner.DailyBacktestVerificationRunner._send_verification_notification') as mock_send_notification:
        
        # 设置mock返回值
        mock_load_config.return_value = (config_data, '/tmp/config.yaml', ['123456'])
        
        # 模拟回测执行成功
        mock_backtest_result = Mock()
        mock_backtest_result.success = True
        mock_backtest_result.result_file_path = '/tmp/result.json'
        mock_execute_backtest.return_value = mock_backtest_result
        
        # 模拟回测结果数据 - 完全匹配的情况
        mock_result_data = {
            'total_trades': 100,
            'total_signals': 100,
            'matched_trades': 100
        }
        mock_read_result.return_value = mock_result_data
        
        # 模拟执行时间
        with patch('time.time') as mock_time:
            mock_time.return_value = 1000.0  # 固定时间值
            
            # 模拟策略验证
            with patch.object(runner.strategy_selector, 'validate_strategy_name') as mock_validate_name, \
                 patch.object(runner.strategy_selector, 'validate_strategy_in_config') as mock_validate_config:
                
                # 模拟策略名称验证都通过
                mock_validate_name.return_value = None
                
                # 模拟策略配置验证：前两个成功，第三个失败
                def side_effect_config(strategy_name, config_data):
                    print(f"验证策略: {strategy_name}")
                    if strategy_name == 'invalid_strategy':
                        print(f"策略 {strategy_name} 验证失败")
                        raise StrategyNotFoundError(f"策略 '{strategy_name}' 在配置中不存在")
                    print(f"策略 {strategy_name} 验证成功")
                    return None
                
                mock_validate_config.side_effect = side_effect_config
                
                try:
                    # 执行验证（多策略模式）
                    result = await runner.run_verification()
                    
                    print(f"验证结果状态: {result.status}")
                    print(f"策略结果数量: {len(result.strategy_results)}")
                    
                    for strategy_result in result.strategy_results:
                        print(f"策略: {strategy_result.strategy_name}, 成功: {strategy_result.success}, 错误: {strategy_result.error_message}")
                    
                    print(f"报告文本:\n{result.report_text}")
                    
                except Exception as e:
                    print(f"验证过程中发生异常: {e}")
                    print(f"异常类型: {type(e)}")
                    import traceback
                    traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_multi_strategy())