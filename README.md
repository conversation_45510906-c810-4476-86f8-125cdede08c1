# memeMonitor - 智能加密货币监控与自动交易系统

[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![MongoDB](https://img.shields.io/badge/MongoDB-7.0+-green.svg)](https://www.mongodb.com/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.100+-red.svg)](https://fastapi.tiangolo.com/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

memeMonitor 是一个专业的加密货币监控和自动交易系统，专注于meme币等高波动性资产的智能分析和交易。系统基于工作流驱动的微服务架构，集成了数据采集、信号分析、自动交易、回测验证等完整功能链路。

## ✨ 核心特性

### 🔍 多源数据监控
- **KOL钱包监控**: 实时跟踪知名投资者的交易活动
- **GMGN平台集成**: 获取智能资金流向和交易信号
- **Solana链监控**: 直接监控区块链上的交易活动
- **社交媒体分析**: 集成Twitter数据，分析市场情绪

### 🤖 智能自动交易
- **多渠道交易**: 支持GMGN API、Jupiter聚合器等多种交易方式
- **滑点智能重试**: 交易失败时自动调整滑点参数并重试
- **策略驱动**: 基于配置的策略自动触发买入卖出
- **风险控制**: 多层级配置和实时风险监控

### 📊 专业回测分析
- **双引擎回测**: 传统回测 + 事件驱动回测
- **Kelly准则过滤**: 基于数学模型的投资策略优化
- **可视化报告**: 丰富的图表和统计分析
- **策略验证**: 历史数据验证交易策略有效性

### 🔧 企业级架构
- **工作流引擎**: 基于节点的数据处理流程
- **异步处理**: 高并发处理能力
- **代理IP管理**: 确保数据采集稳定性
- **全面监控**: 系统状态和业务指标监控

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据采集层     │    │   信号处理层     │    │   交易执行层     │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • GMGN爬虫      │    │ • 信号生成       │    │ • 多渠道交易     │
│ • Solana监控    │───▶│ • 策略匹配       │───▶│ • 滑点重试       │
│ • Twitter爬虫   │    │ • 风险评估       │    │ • 结果记录       │
│ • KOL监控       │    │ • 过滤器         │    │ • 状态通知       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────┐
│                    工作流引擎 & 数据存储                      │
├─────────────────────────────────────────────────────────────┤
│ • WorkflowManager  • MongoDB    • Redis   • 配置管理        │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 快速开始

### 环境要求

- Python 3.11+
- MongoDB 6.0+
- Redis (可选，用于缓存)
- Node.js 18+ (用于部分交易脚本)

### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd memeMonitor

# 安装Python依赖 (推荐使用Poetry)
poetry install

# 或使用pip
pip install -r requirements.txt

# 安装Node.js依赖 (如需要)
npm install
```

### 配置环境

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件，填入必要信息
vim .env
```

必要的环境变量配置：
```bash
# 数据库配置
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=meme_monitor

# 代理配置
PROXY_SERVER=your-proxy-server
PROXY_USERNAME=your-username
PROXY_PASSWORD=your-password

# API密钥
GMGN_API_KEY=your-gmgn-api-key
TWITTER_API_KEY=your-twitter-api-key

# 通知配置
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
FEISHU_WEBHOOK_URL=your-feishu-webhook
```

### 初始化数据库

```bash
# 初始化数据库和集合
python -c "from models import init_db; import asyncio; asyncio.run(init_db())"
```

## 📖 使用方法

### 启动API服务

```bash
# 启动REST API服务
python main.py

# 或使用Gunicorn (生产环境)
gunicorn -c gunicorn_conf.py main:app
```

API服务将在 `http://localhost:8000` 启动，可访问 `http://localhost:8000/docs` 查看API文档。

### 运行工作流

```bash
# 运行单个工作流
python run_workflow.py --file workflows/monitor_kol_activity/monitor_kol_activity.yaml

# 运行指定目录下的所有工作流
python run_workflow.py --dir workflows/gmgn_kol_activity

# 运行所有工作流
python run_workflow.py
```

### 执行自动交易

```bash
# 启动KOL活动监控和自动交易
python run_workflow.py --file workflows/monitor_kol_activity/monitor_kol_activity.yaml
```

### 运行回测分析

```bash
# 传统回测
python run_backtest.py --strategy your_strategy --start-date 2024-01-01 --end-date 2024-12-31

# 事件驱动回测
python run_backtest_ed.py --config backtest_config.yaml

# Kelly准则过滤
python filter_by_kelly.py --input trades.csv --output filtered_trades.csv
```

### 运行定时任务

```bash
# 启动定时任务调度器
python run_jobs.py
```

## 🔧 主要组件

### 工作流系统
```python
# 创建自定义工作流
from utils.workflows.workflow import Workflow, WorkflowNode

class CustomWorkflow(Workflow):
    def __init__(self):
        super().__init__("CustomWorkflow")
        # 添加节点和连接
        self.setup_nodes()
        
    def setup_nodes(self):
        # 节点配置逻辑
        pass
```

### 交易系统
```python
# 配置自动交易
from utils.trading import AutoTradeManager

# 创建交易管理器
trade_manager = AutoTradeManager(
    config_path="trading_config.yaml"
)

# 启动自动交易
await trade_manager.start()
```

### 数据分析
```python
# 使用数据访问对象
from dao.signal_dao import SignalDAO
from dao.trade_record_dao import TradeRecordDAO

# 查询信号数据
signal_dao = SignalDAO()
signals = await signal_dao.find_by_type("kol_buy")

# 分析交易记录
trade_dao = TradeRecordDAO()
stats = await trade_dao.get_trading_statistics()
```

## 📊 API接口

### 获取信号列表
```bash
# 获取最近的信号
curl "http://localhost:8000/api/v1/signals?limit=10"

# 按类型过滤
curl "http://localhost:8000/api/v1/signals?signal_type=kol_buy&period=today"

# 分页查询
curl "http://localhost:8000/api/v1/signals?skip=20&limit=10"
```

### 交易统计
```bash
# 获取交易统计
curl "http://localhost:8000/api/v1/trading/statistics"

# 获取特定策略统计
curl "http://localhost:8000/api/v1/trading/statistics?strategy=kol_follow"
```

## 🏭 生产部署

### 使用Docker部署

```bash
# 构建镜像
docker build -t meme-monitor .

# 运行容器
docker run -d \
  --name meme-monitor \
  -p 8000:8000 \
  -v ./logs:/app/logs \
  -v ./config:/app/config \
  --env-file .env \
  meme-monitor
```

### 使用Docker Compose

```bash
# 启动完整服务栈
docker-compose up -d
```

### Nginx配置

参考 `deploy/nginx/` 目录下的配置文件。

### Supervisor配置

参考 `deploy/supervisor/` 目录下的配置文件。

## 🧪 测试

```bash
# 运行所有测试
poetry run pytest

# 运行特定模块测试
poetry run pytest test/api/

# 运行覆盖率测试
poetry run pytest --cov=. --cov-report=html
```

## 📈 监控指标

系统提供以下关键监控指标：

- **数据采集**: 爬虫成功率、数据延迟、错误率
- **信号处理**: 信号生成量、过滤率、处理延迟  
- **交易执行**: 交易成功率、滑点情况、收益率
- **系统性能**: CPU、内存、网络、数据库性能

## 🤝 贡献指南

1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

请确保：
- 遵循代码规范 (PEP 8)
- 添加必要的测试用例
- 更新相关文档

## 📚 文档

详细文档请参考：

- [项目概述](docs/project/PROJECT_OVERVIEW.md)
- [API文档](docs/features/0.1.0/apis/)
- [交易系统文档](docs/features/0.1.0/自动交易/)
- [回测系统文档](docs/features/0.1.0/backtesting/)
- [工作流文档](docs/features/0.1.0/workflows/)

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔗 相关链接

- [项目主页](https://github.com/your-org/memeMonitor)
- [问题报告](https://github.com/your-org/memeMonitor/issues)
- [变更日志](CHANGELOG.md)

## ⚠️ 免责声明

本系统仅供学习和研究使用。加密货币交易存在高风险，可能导致资金损失。使用本系统进行实际交易前，请充分了解相关风险并谨慎决策。作者不对使用本系统造成的任何损失承担责任。

---

**⭐ 如果这个项目对您有帮助，请给我们一个Star！** 