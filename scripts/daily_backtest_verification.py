#!/usr/bin/env python3
"""
每日回测校验 - Crontab 入口脚本

这是专门为定时任务设计的入口脚本，具有完善的错误处理和日志记录功能。
适合配置在crontab中进行定时执行。

使用方式:
    python scripts/daily_backtest_verification.py
    
    # 指定日期运行（可选）
    python scripts/daily_backtest_verification.py --date 2025-01-12

Crontab 配置示例:
    # 每天上午9点执行每日回测校验
    0 9 * * * cd /path/to/meme_monitor && python scripts/daily_backtest_verification.py >> logs/daily_verification_cron.log 2>&1
"""

import asyncio
import os
import sys
import logging
import argparse
from datetime import datetime, date
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv
from models import init_db

# 加载环境变量
load_dotenv()

# 确保日志目录存在
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)

# 配置Crontab专用的日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/daily_backtest_verification_cron.log')
    ]
)

logger = logging.getLogger(__name__)

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='每日回测校验 - Crontab工具')
    parser.add_argument(
        '--date', 
        type=str, 
        help='指定校验日期 (格式: YYYY-MM-DD)，默认为昨天'
    )
    parser.add_argument(
        '--dry-run', 
        action='store_true', 
        help='干运行模式，不发送通知'
    )
    return parser.parse_args()

async def main():
    """主函数"""
    start_time = datetime.now()
    
    try:
        # 解析参数
        args = parse_arguments()
        
        # 解析日期
        target_date = None
        if args.date:
            try:
                target_date = datetime.strptime(args.date, '%Y-%m-%d').date()
            except ValueError:
                logger.error(f"无效的日期格式: {args.date}，请使用 YYYY-MM-DD 格式")
                return 1
        
        logger.info("=" * 80)
        logger.info("🕘 CRONTAB 每日回测校验任务启动")
        logger.info(f"⏰ 启动时间: {start_time}")
        logger.info(f"📅 目标日期: {target_date or '昨天(默认)'}")
        if args.dry_run:
            logger.info("🧪 运行模式: 干运行（不发送通知）")
        logger.info("=" * 80)
        
        # 初始化数据库连接
        logger.info("📊 正在初始化数据库连接...")
        await init_db()
        logger.info("✅ 数据库连接已初始化")
        
        # 导入并执行校验逻辑
        from workflows.daily_backtest_verification.components.verification_runner import DailyBacktestVerificationRunner
        
        # 如果是干运行模式，设置环境变量
        if args.dry_run:
            os.environ['DRY_RUN_MODE'] = 'true'
            logger.info("🧪 干运行模式已启用")
        
        # 执行校验
        logger.info("🔍 正在执行每日回测校验...")
        runner = DailyBacktestVerificationRunner()
        result = await runner.run_verification(target_date)
        
        # 处理结果
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if result.status == 'success':
            logger.info("🎉 每日回测校验成功完成!")
            logger.info(f"📊 校验状态: {result.status}")
            if result.total_trades is not None:
                logger.info(f"📈 回测交易数: {result.total_trades}")
                logger.info(f"📊 实际信号数: {result.total_signals}")
                logger.info(f"✅ 匹配交易数: {result.matched_trades}")
            logger.info(f"⏱️  总执行时间: {duration:.1f}秒")
            return 0
        else:
            logger.error("❌ 每日回测校验失败!")
            logger.error(f"📊 校验状态: {result.status}")
            if result.error_message:
                logger.error(f"❌ 错误信息: {result.error_message}")
            logger.error(f"⏱️  执行时间: {duration:.1f}秒")
            return 1
            
    except ImportError as e:
        logger.error(f"❌ 导入模块失败: {e}")
        logger.error("请确保项目依赖已正确安装: poetry install")
        return 1
    except Exception as e:
        logger.error(f"❌ 每日回测校验执行异常: {e}", exc_info=True)
        return 1
    finally:
        # 清理环境变量
        if 'DRY_RUN_MODE' in os.environ:
            del os.environ['DRY_RUN_MODE']
            
        end_time = datetime.now()
        total_duration = (end_time - start_time).total_seconds()
        
        logger.info("=" * 80)
        logger.info(f"🏁 CRONTAB 每日回测校验任务结束")
        logger.info(f"⏰ 结束时间: {end_time}")
        logger.info(f"⏱️  总用时: {total_duration:.1f}秒")
        logger.info("=" * 80)

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code) 