[tool:pytest]
# pytest 配置文件

# 设置异步模式
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function

# 测试路径
testpaths = test

# 添加标记
markers =
    asyncio: 标记为异步测试
    integration: 标记为集成测试
    unit: 标记为单元测试
    slow: 标记为慢速测试
    skip_if_no_env: 如果缺少环境变量则跳过

# 输出设置
addopts = 
    -v
    --strict-markers
    --tb=short
    --color=yes
    --durations=10

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:beanie.*
    ignore::pytest.PytestUnhandledCoroutineWarning
    ignore::_pytest.warnings.PytestWarning
    ignore::pytest.PytestWarning

# 最小版本要求
minversion = 6.0

# Python路径
python_paths = .
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 日志设置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 覆盖率设置 (如果安装了pytest-cov)
# addopts = --cov=utils --cov-report=html --cov-report=term-missing

# 并行测试设置 (如果安装了pytest-xdist)
# addopts = -n auto