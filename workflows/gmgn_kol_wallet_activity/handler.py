import logging
import traceback
from typing import Any, Dict, List, Optional
from datetime import datetime

from dao.kol_wallet_activity_dao import KOL<PERSON>alletActivityDAO
from dao.kol_wallet_dao import KOLWalletDAO
from models.kol_wallet import KOLWallet
from models.kol_wallet_activity import KOLWalletActivity
from utils.spiders.smart_money.kol_wallet_activity_spider import KOLWalletActivitySpider
from utils.spiders.smart_money.smart_money import SmartMoneySpider


logger = logging.getLogger("GmgnKolWalletActivityHandler")
spider = KOLWalletActivitySpider()
kol_wallet_activity_dao = KOLWalletActivityDAO()
kol_wallet_dao = KOLWalletDAO()

smart_money_spider = SmartMoneySpider()


async def generate_data():
    """生成代币数据，使用异步生成器模式
    
    Yields:
        Dict: 单个KOL钱包数据
    """
    
    kol_wallet_resp = await smart_money_spider.get_smart_money_wallets(tag="renowned", orderby="last_active")
    smart_wallets = kol_wallet_resp.get("data", {}).get("rank", [])
    
    logger.info(f"获取到kol钱包信息列表: {len(smart_wallets)} 个")
    
    # 获取数据库中最后活动的账号时间戳
    last_active_timestamp = await kol_wallet_dao.get_kol_last_active()
    
    logger.info(f"数据库中最新的kol活动时间是: {last_active_timestamp}")
    
    wallet_count = 0
    logger.info(last_active_timestamp)
    for smart_wallet in smart_wallets:
        if smart_wallet["last_active"] >= last_active_timestamp:
            wallet_count += 1
            yield smart_wallet
    
    logger.info(f"成功获取并处理 {wallet_count} 个新活动的KOL钱包")


async def process_kol_wallet_activity(kol_wallet_dict: Dict):
    """处理代币活动数据，使用异步生成器模式
    
    Args:
        kol_wallet_dict: KOL钱包数据
        
    Yields:
        Dict: 处理后的钱包活动数据项
    """
    if not kol_wallet_dict:
        logger.warning("收到空的KOL钱包数据")
        return
    
    # 获取代币地址
    kol_wallet_address = kol_wallet_dict.get('address')
    if not kol_wallet_address:
        logger.warning(f"KOL钱包数据缺少地址字段: {kol_wallet_dict}")
        return
    
    last_activity = await kol_wallet_activity_dao.get_last_activity(kol_wallet_address)
    if isinstance(last_activity, KOLWalletActivity):
        last_timestamp = last_activity.timestamp
    elif isinstance(last_activity, dict):
        last_timestamp = last_activity["timestamp"]
    else:
        last_timestamp = None
    
    # 活动计数
    activity_count = 0
    
    # 直接使用异步生成器处理并yield结果
    if last_timestamp:
        logger.info(f"获取  {kol_wallet_address} 最后一个活动记录: {last_timestamp}")
        # 将时间戳转换为字符串，因为spider期望的是字符串类型
        timestamp_str = str(last_timestamp) if isinstance(last_timestamp, int) else last_timestamp
        async for activity in spider.fetch_wallet_activities(kol_wallet_address, until_trade_timestamp=timestamp_str):
            activity["wallet"] = kol_wallet_address
            activity_count += 1
            yield activity
    else:
        async for activity in spider.fetch_wallet_activities(kol_wallet_address):
            activity["wallet"] = kol_wallet_address
            activity_count += 1
            yield activity
    
    if activity_count == 0:
        logger.warning(f"没有活动记录: {kol_wallet_address}")
    else:
        logger.info(f"获取钱包活动成功: {activity_count} 条")

async def validate(data: Any) -> bool:
    """验证代币活动数据是否有效
    
    Args:
        data: 代币活动数据
        
    Returns:
        bool: 如果数据有效则返回True，否则返回False
    """
    if not data.get("wallet"):
        return False
    if not data.get("timestamp"):
        return False
    return True


async def store_data(data: List[Dict]) -> int:
    """存储代币活动数据到数据库
    
    Args:
        data: 代币活动数据列表
        
    Returns:
        int: 成功存储的记录数
    """
    if not data:
        return 0
    
    processed_dicts_to_store: List[Dict] = []
    for item_dict in data:
        try:
            activity_obj = KOLWalletActivity(**item_dict)
            processed_dict = activity_obj.model_dump(by_alias=True, exclude_none=True)
            
            # 从字典中移除 created_at 和 updated_at, 
            # 因为这些将由 DAO 层的 $setOnInsert 和 $currentDate 精确控制
            processed_dict.pop('created_at', None)
            processed_dict.pop('updated_at', None)
            
            processed_dicts_to_store.append(processed_dict)
        except Exception as e:
            logger.error(f"转换KOL钱包活动数据失败 for item: {item_dict}. Error: {e}\n{traceback.format_exc()}")
            continue

    if not processed_dicts_to_store:
        logger.warning("没有可存储的有效KOL钱包活动数据 after conversion.")
        return 0
        
    try:
        update_count = await kol_wallet_activity_dao.upsert_activities(processed_dicts_to_store)
        logger.info(f"批量更新完成: 更新了 {update_count} 条记录")
        return update_count
    except Exception as e:
        error_msg = f"存储代币活动数据时发生错误: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        return 0