name: "GMGN KOL钱包活动记录"
description: "定期获取链上的KOL钱包活动记录，并将数据存储到数据库"

nodes:
  - name: "KolWalletActivitySchedulerNode3"
    node_type: "input"
    interval: 1
    generate_data: workflows.gmgn_kol_wallet_activity.handler.generate_data
    flow_control:
      max_pending_messages: 20
      check_interval: 1
      enable_flow_control: true

  - name: "KolWalletActivityMonitorNode3"
    node_type: "process"
    depend_ons: ["KolWalletActivitySchedulerNode3"]
    concurrency: 2
    interval: 1
    process_item: workflows.gmgn_kol_wallet_activity.handler.process_kol_wallet_activity

  - name: "KolWalletActivityStoreNode3"
    node_type: "storage"
    depend_ons: ["KolWalletActivityMonitorNode3"]
    batch_size: 1000
    store_data: workflows.gmgn_kol_wallet_activity.handler.store_data
    validate: workflows.gmgn_kol_wallet_activity.handler.validate 
