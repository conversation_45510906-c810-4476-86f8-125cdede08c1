# workflows/trading_delay_monitor/trading_delay_monitor_workflow.yaml

name: "交易延迟监控工作流"
description: "定期计算交易延迟并存储到数据库，为回测系统提供真实延迟数据"

nodes:
  - name: "TradingDelayProcessorNode"
    node_type: "input"
    interval: 10  # 每10秒运行一次
    generate_data: workflows.trading_delay_monitor.handler.process_trading_delays
    flow_control:
      max_pending_messages: 5
      check_interval: 2
      enable_flow_control: true 