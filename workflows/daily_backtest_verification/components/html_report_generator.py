"""
HTML报告生成器

负责将校验结果生成为HTML格式的报告文件。
"""

import os
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
from jinja2 import Environment, FileSystemLoader, TemplateNotFound

from .verification_runner import VerificationResult


class HTMLReportGenerationError(Exception):
    """HTML报告生成异常"""
    pass


class HTMLReportGenerator:
    """
    HTML报告生成器
    
    负责将校验结果转换为HTML格式的报告，并保存到指定目录。
    """
    
    def __init__(self):
        """初始化HTML报告生成器"""
        self.logger = logging.getLogger(__name__)
        
        # 设置模板目录
        template_dir = Path(__file__).parent.parent / "templates"
        if not template_dir.exists():
            raise HTMLReportGenerationError(f"模板目录不存在: {template_dir}")
        
        self.env = Environment(loader=FileSystemLoader(str(template_dir)))
        
    def generate_report(self, verification_result: VerificationResult, 
                       output_dir: str, filename: Optional[str] = None) -> str:
        """
        生成HTML报告
        
        Args:
            verification_result: 校验结果
            output_dir: 输出目录
            filename: 文件名，如果为None则自动生成
            
        Returns:
            str: 生成的HTML文件路径
            
        Raises:
            HTMLReportGenerationError: 报告生成失败
        """
        try:
            # 确保输出目录存在
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # 生成文件名
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"backtest_verification_{verification_result.date}_{timestamp}.html"
            
            # 确保文件名以.html结尾
            if not filename.endswith('.html'):
                filename += '.html'
            
            file_path = output_path / filename
            
            # 准备模板数据
            template_data = self._prepare_template_data(verification_result)
            
            # 渲染模板
            template = self.env.get_template('report.html')
            html_content = template.render(**template_data)
            
            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.logger.info(f"HTML报告生成成功: {file_path}")
            return str(file_path)
            
        except TemplateNotFound as e:
            error_msg = f"模板文件未找到: {e}"
            self.logger.error(error_msg)
            raise HTMLReportGenerationError(error_msg)
        except Exception as e:
            error_msg = f"生成HTML报告失败: {e}"
            self.logger.error(error_msg, exc_info=True)
            raise HTMLReportGenerationError(error_msg)
    
    def _prepare_template_data(self, verification_result: VerificationResult) -> Dict[str, Any]:
        """
        准备模板数据
        
        Args:
            verification_result: 校验结果
            
        Returns:
            Dict[str, Any]: 模板数据
        """
        # 状态映射
        status_mapping = {
            "success": {"class": "success", "icon": "✅", "text": "匹配"},
            "mismatch": {"class": "mismatch", "icon": "⚠️", "text": "不匹配"},
            "error": {"class": "error", "icon": "❌", "text": "错误"}
        }
        
        status_info = status_mapping.get(verification_result.status, 
                                       {"class": "error", "icon": "❓", "text": "未知"})
        
        # 提取不匹配详情
        missing_trades = []
        extra_trades = []
        
        if hasattr(verification_result, 'unmatched_trades_details') and verification_result.unmatched_trades_details:
            for detail in verification_result.unmatched_trades_details:
                if detail.get('type') == 'missing_trades':
                    missing_trades = getattr(verification_result, 'missing_trades', [])
                elif detail.get('type') == 'extra_trades':
                    extra_trades = getattr(verification_result, 'extra_trades', [])
        
        # 如果没有详细信息，尝试从其他属性获取
        if not missing_trades and hasattr(verification_result, 'missing_trades'):
            missing_trades = verification_result.missing_trades
        if not extra_trades and hasattr(verification_result, 'extra_trades'):
            extra_trades = verification_result.extra_trades
        
        template_data = {
            # 基本信息
            'date': verification_result.date,
            'generation_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            
            # 状态信息
            'status_class': status_info['class'],
            'status_icon': status_info['icon'],
            'status_text': status_info['text'],
            
            # 统计数据
            'total_trades': verification_result.total_trades,
            'total_signals': verification_result.total_signals,
            'matched_trades': verification_result.matched_trades,
            'execution_time': f"{verification_result.execution_time:.1f}",
            
            # 策略信息
            'successful_strategies': getattr(verification_result, 'strategy_results', []),
            'failed_strategies': getattr(verification_result, 'failed_strategies', []),
            
            # 不匹配详情
            'mismatch_details': bool(missing_trades or extra_trades),
            'missing_trades': missing_trades,
            'extra_trades': extra_trades,
        }
        
        return template_data
    
    def generate_filename(self, verification_result: VerificationResult, 
                         include_timestamp: bool = True) -> str:
        """
        生成报告文件名
        
        Args:
            verification_result: 校验结果
            include_timestamp: 是否包含时间戳
            
        Returns:
            str: 文件名
        """
        base_name = f"backtest_verification_{verification_result.date}"
        
        if include_timestamp:
            timestamp = datetime.now().strftime("%H%M%S")
            base_name += f"_{timestamp}"
        
        return f"{base_name}.html"
    
    def get_report_url(self, base_url: str, filename: str) -> str:
        """
        生成报告访问URL
        
        Args:
            base_url: 基础URL
            filename: 文件名
            
        Returns:
            str: 完整的访问URL
        """
        # 确保base_url以/结尾
        if not base_url.endswith('/'):
            base_url += '/'
        
        # 只使用文件名，不包含路径
        filename = os.path.basename(filename)
        
        return f"{base_url}{filename}"
