"""
每日回测校验运行器

负责协调整个校验流程：
- 整合所有组件
- 执行完整的校验流程
- 处理错误和异常情况
- 生成校验报告

技术实现方案：@daily_backtest_verification_dev_plan_ai.md
"""

import asyncio
import logging
from datetime import date, timedelta, datetime
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from pathlib import Path

from .config_reader import ConfigReader
from .backtest_executor import BacktestExecutor
from .backtest_result_reader import BacktestResultReader
from .notification_sender import NotificationSender
from .strategy_selector import (
    StrategySelector, 
    MissingStrategyError, 
    StrategyNotFoundError, 
    StrategyDisabledError
)
from dao.signal_dao import SignalDAO


# 常量定义
class VerificationStatus:
    """校验状态常量"""
    SUCCESS = 'success'
    MISMATCH = 'mismatch'
    ERROR = 'error'


class ReportEmojis:
    """报告表情符号常量"""
    SUCCESS = "✅ 通过"
    MISMATCH = "⚠️ 不匹配"
    ERROR = "❌ 失败"
    TITLE = "📊"
    DATE = "📅"
    STATUS = "🔍"
    TRADES = "📈"
    SIGNALS = "📊"
    MATCHED = "✅"
    TIME = "⏱️"
    WARNING = "⚠️"
    ERROR_MSG = "❌"
    STRATEGY = "🎯"


class LogMessages:
    """日志消息模板"""
    VERIFICATION_START = "开始校验流程，目标日期: {date}"
    BACKTEST_EXECUTING = "执行回测..."
    RESULT_READING = "读取回测结果..."
    VERIFICATION_COMPLETE = "校验完成，状态: {status}"
    VERIFICATION_EXCEPTION = "校验流程异常: {error}"
    CONFIG_RELOAD_FAILED = "重新加载管理员配置失败: {error}"
    ERROR_NOTIFICATION_FAILED = "发送错误通知失败: {error}"
    VERIFICATION_NOTIFICATION_FAILED = "发送校验通知失败: {error}"
    ERROR_NOTIFICATION_EXCEPTION = "发送错误通知异常: {error}"
    TEMP_FILE_CLEANED = "清理临时文件: {file_path}"
    TEMP_FILE_CLEANUP_FAILED = "清理临时文件失败: {file_path}, 错误: {error}"


@dataclass
class StrategyVerificationResult:
    """单个策略的验证结果"""
    strategy_name: str
    success: bool
    error_message: Optional[str] = None
    total_trades: Optional[int] = None
    total_signals: Optional[int] = None
    matched_trades: Optional[int] = None
    execution_time: Optional[float] = None


@dataclass
class VerificationResult:
    """校验结果"""
    status: str  # 'success', 'mismatch', 'error'
    date: date
    total_trades: Optional[int] = None
    total_signals: Optional[int] = None
    matched_trades: Optional[int] = None
    execution_time: Optional[float] = None
    report_text: str = ""
    error_message: Optional[str] = None
    # 多策略支持
    strategy_results: Optional[List[StrategyVerificationResult]] = None
    failed_strategies: Optional[List[StrategyVerificationResult]] = None


class VerificationError(Exception):
    """校验异常"""
    pass


class DailyBacktestVerificationRunner:
    """
    每日回测校验运行器
    
    负责协调整个校验流程的执行，整合所有组件。
    """
    
    def __init__(self):
        """初始化校验运行器"""
        self.config_reader = ConfigReader()
        self.executor = BacktestExecutor()
        self.reader = BacktestResultReader()
        self.notification_sender = NotificationSender()
        self.signal_dao = SignalDAO()
        self.strategy_selector = StrategySelector()
        self.logger = logging.getLogger(__name__)
    
    async def run_verification(self, target_date: Optional[date] = None, strategy_name: Optional[str] = None) -> VerificationResult:
        """
        运行校验流程
        
        Args:
            target_date: 目标日期，如果为None则使用昨天
            strategy_name: 策略名称，如果为None则使用多策略模式
            
        Returns:
            VerificationResult: 校验结果
        """
        # 使用默认日期（昨天）
        if target_date is None:
            target_date = date.today() - timedelta(days=1)
        
        # 保存目标日期到实例变量，供后续方法使用
        self._current_target_date = target_date
        
        temp_config_file = None
        admin_chat_ids = []
        strategy_results = []
        
        try:
            self.logger.info(LogMessages.VERIFICATION_START.format(date=target_date))
            
            # 1. 加载配置
            config_data, temp_config_file, admin_chat_ids = await self._load_configurations()
            
            # 2. 验证策略配置
            strategy_results = await self._validate_strategy_configuration(config_data, strategy_name)
            
            # 检查是否所有策略都失败
            successful_strategies = [r for r in strategy_results if r.success]
            if not successful_strategies:
                # 所有策略都失败时，抛出异常
                failed_strategies = [r for r in strategy_results if not r.success]
                error_messages = [f"{r.strategy_name}: {r.error_message}" for r in failed_strategies]
                raise VerificationError(f"所有策略验证都失败: {'; '.join(error_messages)}")
            
            # 3. 执行回测
            backtest_result = await self._execute_backtest_with_validation(temp_config_file, admin_chat_ids)
            
            # 4. 读取回测结果
            result_data = await self._read_backtest_result_with_validation(
                backtest_result.result_file_path, admin_chat_ids
            )
            
            # 5. 生成和发送校验报告
            verification_result = await self._generate_and_send_report(
                result_data, target_date, backtest_result.execution_time, admin_chat_ids, strategy_results
            )
            
            self.logger.info(LogMessages.VERIFICATION_COMPLETE.format(status=verification_result.status))
            return verification_result
            
        except Exception as e:
            return await self._handle_verification_exception(e, target_date, admin_chat_ids, strategy_results)
            
        finally:
            # 清理临时文件
            if temp_config_file:
                self._cleanup_temp_files([temp_config_file])
    
    async def _load_configurations(self) -> tuple[Any, str, List[str]]:
        """
        加载所有必要的配置
        
        Returns:
            tuple: (配置数据, 临时配置文件路径, 管理员Chat ID列表)
        """
        # 读取KOL活动配置
        config_data, temp_config_file = await self.config_reader.read_kol_activity_config()
        
        # 读取管理员Telegram配置
        admin_chat_ids = await self.config_reader.read_admin_telegram_config()
        
        return config_data, temp_config_file, admin_chat_ids
    
    async def _validate_strategy_configuration(self, config_data: Any, strategy_name: Optional[str] = None) -> List[StrategyVerificationResult]:
        """
        验证策略配置
        
        Args:
            config_data: 配置数据
            strategy_name: 策略名称，如果为None则使用多策略模式
            
        Returns:
            List[StrategyVerificationResult]: 策略验证结果列表
            
        Raises:
            MissingStrategyError: 当缺少策略名称且配置中没有启用的策略时
        """
        strategy_results = []
        
        try:
            if strategy_name:
                # 单策略模式
                self.logger.info(f"使用指定的策略名称: {strategy_name}")
                result = await self._validate_single_strategy(config_data, strategy_name)
                strategy_results.append(result)
            else:
                # 多策略模式：验证配置中所有启用的策略
                self.logger.info("进入多策略验证模式")
                enabled_strategies = self.strategy_selector.get_enabled_strategies_from_config(config_data)
                
                if not enabled_strategies:
                    raise MissingStrategyError("配置中没有启用的策略")
                
                self.logger.info(f"发现 {len(enabled_strategies)} 个启用的策略: {enabled_strategies}")
                
                for strategy in enabled_strategies:
                    result = await self._validate_single_strategy(config_data, strategy)
                    strategy_results.append(result)
            
            # 检查是否有成功的策略
            successful_strategies = [r for r in strategy_results if r.success]
            failed_strategies = [r for r in strategy_results if not r.success]
            
            # 记录验证结果
            if failed_strategies:
                self.logger.warning(f"部分策略验证失败: {[r.strategy_name for r in failed_strategies]}")
            
            self.logger.info(f"策略验证完成，成功: {len(successful_strategies)}, 失败: {len(failed_strategies)}")
            return strategy_results
            
        except (MissingStrategyError, VerificationError) as e:
            self.logger.error(f"策略验证失败: {e}")
            raise
        except Exception as e:
            self.logger.error(f"策略验证过程中发生未知错误: {e}")
            raise
    
    async def _validate_single_strategy(self, config_data: Any, strategy_name: str) -> StrategyVerificationResult:
        """
        验证单个策略
        
        Args:
            config_data: 配置数据
            strategy_name: 策略名称
            
        Returns:
            StrategyVerificationResult: 单个策略的验证结果
        """
        try:
            # 基础策略名称验证
            self.strategy_selector.validate_strategy_name(strategy_name)
            
            # 验证策略在配置中的存在性和启用状态
            self.strategy_selector.validate_strategy_in_config(strategy_name, config_data)
            
            # 获取策略配置（用于验证策略是否可用）
            strategy_config = self.strategy_selector.get_strategy_config(strategy_name)
            
            self.logger.info(f"策略验证成功: {strategy_name}")
            self.logger.debug(f"策略配置: {strategy_config}")
            
            return StrategyVerificationResult(
                strategy_name=strategy_name,
                success=True
            )
            
        except (MissingStrategyError, StrategyNotFoundError, StrategyDisabledError) as e:
            self.logger.warning(f"策略 {strategy_name} 验证失败: {e}")
            return StrategyVerificationResult(
                strategy_name=strategy_name,
                success=False,
                error_message=str(e)
            )
        except Exception as e:
            self.logger.error(f"策略 {strategy_name} 验证过程中发生未知错误: {e}")
            return StrategyVerificationResult(
                strategy_name=strategy_name,
                success=False,
                error_message=f"未知错误: {e}"
            )
    
    def _extract_strategy_name_from_config(self, config_data: Any) -> str:
        """
        从配置数据中提取策略名称
        
        Args:
            config_data: 配置数据
            
        Returns:
            str: 策略名称
            
        Raises:
            MissingStrategyError: 当配置中缺少策略名称时
        """
        try:
            # 假设配置数据是字典格式，策略名称在 'strategy' 字段中
            if isinstance(config_data, dict):
                strategy_name = config_data.get('strategy')
                if not strategy_name:
                    raise MissingStrategyError("配置中缺少策略名称")
                return strategy_name
            else:
                # 如果配置数据不是字典，尝试其他方式提取
                # 这里可以根据实际的配置格式进行调整
                raise MissingStrategyError("无法从配置中提取策略名称")
        except Exception as e:
            if isinstance(e, MissingStrategyError):
                raise
            raise MissingStrategyError(f"提取策略名称时发生错误: {e}")
    
    async def _execute_backtest_with_validation(self, temp_config_file: str, admin_chat_ids: List[str]):
        """
        执行回测并验证结果
        
        Args:
            temp_config_file: 临时配置文件路径
            admin_chat_ids: 管理员Chat ID列表
            
        Returns:
            BacktestResult: 回测结果
            
        Raises:
            VerificationError: 回测执行失败时抛出
        """
        self.logger.info(LogMessages.BACKTEST_EXECUTING)
        # 获取目标日期，如果没有则使用昨天
        target_date = getattr(self, '_current_target_date', None)
        backtest_result = await self.executor.execute_backtest(temp_config_file, target_date=target_date)
        
        if not backtest_result.success:
            error_msg = f"回测执行失败: {backtest_result.error}"
            raise VerificationError(error_msg)
        
        return backtest_result
    
    async def _query_actual_signals_count(self, target_date: date) -> int:
        """
        查询指定日期的实际信号数量
        
        Args:
            target_date: 目标日期
            
        Returns:
            int: 实际信号数量
        """
        try:
            # 计算日期范围（UTC时间戳）
            start_datetime = datetime.combine(target_date, datetime.min.time())
            end_datetime = datetime.combine(target_date, datetime.max.time())
            
            # 转换为UTC时间戳
            start_timestamp = int(start_datetime.timestamp())
            end_timestamp = int(end_datetime.timestamp())
            
            # 查询信号数量
            query = {
                "timestamp": {
                    "$gte": start_timestamp,
                    "$lte": end_timestamp
                }
            }
            
            signals = await self.signal_dao.find_signals(query)
            signal_count = len(signals)
            
            self.logger.info(f"查询到 {target_date} 的实际信号数量: {signal_count}")
            return signal_count
            
        except Exception as e:
            self.logger.error(f"查询实际信号数量失败: {str(e)}", exc_info=True)
            return 0
    
    async def _read_backtest_result_with_validation(self, result_file_path: Optional[str], admin_chat_ids: List[str]):
        """
        读取回测结果并验证
        
        Args:
            result_file_path: 结果文件路径
            admin_chat_ids: 管理员Chat ID列表
            
        Returns:
            结果数据
            
        Raises:
            VerificationError: 结果文件路径为空或读取失败时抛出
        """
        self.logger.info(LogMessages.RESULT_READING)
        
        if not result_file_path:
            error_msg = "回测结果文件路径为空"
            raise VerificationError(error_msg)
        
        return await self.reader.read_result_file(result_file_path)
    
    async def _generate_and_send_report(
        self, 
        result_data: Any, 
        target_date: date, 
        execution_time: float, 
        admin_chat_ids: List[str],
        strategy_results: List[StrategyVerificationResult]
    ) -> VerificationResult:
        """
        生成校验报告并发送通知
        
        Args:
            result_data: 回测结果数据
            target_date: 目标日期
            execution_time: 执行时间
            admin_chat_ids: 管理员Chat ID列表
            strategy_results: 策略验证结果列表
            
        Returns:
            VerificationResult: 校验结果
        """
        # 生成校验报告
        verification_result = await self._generate_verification_report(
            result_data, target_date, execution_time, strategy_results
        )
        
        # 发送通知
        await self._send_verification_notification(verification_result, admin_chat_ids)
        
        return verification_result
    
    async def _handle_verification_exception(
        self, 
        exception: Exception, 
        target_date: date, 
        admin_chat_ids: List[str],
        strategy_results: Optional[List[StrategyVerificationResult]] = None
    ) -> VerificationResult:
        """
        处理校验过程中的异常
        
        Args:
            exception: 异常对象
            target_date: 目标日期
            admin_chat_ids: 管理员Chat ID列表
            strategy_results: 策略验证结果列表
            
        Returns:
            VerificationResult: 错误结果
        """
        error_msg = LogMessages.VERIFICATION_EXCEPTION.format(error=str(exception))
        self.logger.error(error_msg, exc_info=True)
        
        # 确保有管理员配置用于发送通知
        final_admin_chat_ids = await self._ensure_admin_config(admin_chat_ids)
        
        # 发送错误通知
        await self._send_error_notification(error_msg, final_admin_chat_ids)
        
        return self._create_error_result(target_date, error_msg, strategy_results)
    
    async def _ensure_admin_config(self, current_admin_chat_ids: List[str]) -> List[str]:
        """
        确保有可用的管理员配置
        
        Args:
            current_admin_chat_ids: 当前的管理员Chat ID列表
            
        Returns:
            List[str]: 可用的管理员Chat ID列表
        """
        if current_admin_chat_ids:
            return current_admin_chat_ids
        
        try:
            return await self.config_reader.read_admin_telegram_config()
        except Exception as config_error:
            self.logger.error(LogMessages.CONFIG_RELOAD_FAILED.format(error=config_error))
            return []
    
    async def _generate_verification_report(
        self, 
        backtest_result: Any, 
        target_date: date, 
        execution_time: float,
        strategy_results: List[StrategyVerificationResult]
    ) -> VerificationResult:
        """
        生成校验报告
        
        Args:
            backtest_result: 回测结果数据
            target_date: 目标日期
            execution_time: 执行时间
            strategy_results: 策略验证结果列表
            
        Returns:
            VerificationResult: 校验结果
        """
        # 查询实际信号数量
        actual_signals_count = await self._query_actual_signals_count(target_date)
        
        # 提取结果数据
        total_trades, total_signals, matched_trades = self._extract_result_data(backtest_result, actual_signals_count)
        
        # 判断校验状态
        status = self._determine_verification_status(total_trades, total_signals, matched_trades, strategy_results)
        
        # 分离成功和失败的策略
        successful_strategies = [r for r in strategy_results if r.success]
        failed_strategies = [r for r in strategy_results if not r.success]
        
        # 创建校验结果
        result = VerificationResult(
            status=status,
            date=target_date,
            total_trades=total_trades,
            total_signals=total_signals,
            matched_trades=matched_trades,
            execution_time=execution_time,
            strategy_results=strategy_results,
            failed_strategies=failed_strategies if failed_strategies else None
        )
        
        # 生成报告文本
        result.report_text = self._format_report_text(result)
        
        return result
    
    def _extract_result_data(self, backtest_result: Any, actual_signals_count: int) -> tuple[int, int, int]:
        """
        从回测结果中提取数据
        
        Args:
            backtest_result: 回测结果数据
            actual_signals_count: 实际信号数量
            
        Returns:
            tuple: (总交易数, 总信号数, 匹配交易数)
        """
        # 处理BacktestResultData对象
        if hasattr(backtest_result, 'total_trades'):
            total_trades = backtest_result.total_trades
            # 使用实际查询到的信号数量
            total_signals = actual_signals_count
            matched_trades = getattr(backtest_result, 'matched_trades', total_trades)
        else:
            # 处理字典格式
            total_trades = backtest_result.get('total_trades', 0)
            # 使用实际查询到的信号数量
            total_signals = actual_signals_count
            matched_trades = backtest_result.get('matched_trades', 0)
        
        return total_trades, total_signals, matched_trades
    
    def _determine_verification_status(self, total_trades: int, total_signals: int, matched_trades: int, strategy_results: List = None) -> str:
        """
        判断校验状态
        
        Args:
            total_trades: 总交易数
            total_signals: 总信号数
            matched_trades: 匹配交易数
            strategy_results: 策略验证结果列表
            
        Returns:
            str: 校验状态
        """
        # 如果有策略验证结果，需要考虑策略验证情况
        if strategy_results:
            # 检查是否有成功的策略
            has_successful_strategy = any(r.success for r in strategy_results)
            # 如果没有任何策略成功，返回错误状态
            if not has_successful_strategy:
                return VerificationStatus.ERROR
        
        # 基于数据匹配情况判断状态
        if total_trades == total_signals == matched_trades:
            return VerificationStatus.SUCCESS
        else:
            return VerificationStatus.MISMATCH
    
    def _format_report_text(self, result: VerificationResult) -> str:
        """
        格式化报告文本
        
        Args:
            result: 校验结果
            
        Returns:
            str: 格式化的报告文本
        """
        # 状态表情符号映射
        status_emoji_map = {
            VerificationStatus.SUCCESS: ReportEmojis.SUCCESS,
            VerificationStatus.MISMATCH: ReportEmojis.MISMATCH,
            VerificationStatus.ERROR: ReportEmojis.ERROR
        }
        
        status_emoji = status_emoji_map.get(result.status, ReportEmojis.ERROR)
        
        # 基础报告行
        report_lines = [
            f"{ReportEmojis.TITLE} 每日回测校验报告",
            "",
            f"{ReportEmojis.DATE} 日期: {result.date}",
            f"{ReportEmojis.STATUS} 状态: {status_emoji}",
            ""
        ]
        
        # 添加数据详情
        if result.total_trades is not None:
            report_lines.extend([
                f"{ReportEmojis.TRADES} 回测交易数: {result.total_trades}",
                f"{ReportEmojis.SIGNALS} 实际信号数: {result.total_signals}",
                f"{ReportEmojis.MATCHED} 匹配交易数: {result.matched_trades}",
            ])
        
        # 添加执行时间
        if result.execution_time is not None:
            try:
                execution_time_str = f"{result.execution_time:.1f}秒"
            except (TypeError, ValueError):
                # 处理Mock对象或其他无法格式化的情况
                execution_time_str = str(result.execution_time)
            report_lines.append(f"{ReportEmojis.TIME} 执行时间: {execution_time_str}")
        
        # 添加不匹配详情
        if result.status == VerificationStatus.MISMATCH and result.total_signals is not None and result.matched_trades is not None:
            unmatched_signals = result.total_signals - result.matched_trades
            if unmatched_signals > 0:
                report_lines.extend([
                    "",
                    f"{ReportEmojis.WARNING} 匹配差异: {unmatched_signals}个信号未生成交易"
                ])
        
        # 添加策略信息（多策略模式）
        if result.strategy_results and len(result.strategy_results) > 1:
            successful_strategies = [r for r in result.strategy_results if r.success]
            failed_strategies = [r for r in result.strategy_results if not r.success]
            
            report_lines.extend([
                "",
                f"{ReportEmojis.STRATEGY} 策略执行情况:"
            ])
            
            # 成功的策略
            if successful_strategies:
                report_lines.append(f"  ✅ 成功策略 ({len(successful_strategies)}):")  
                for strategy in successful_strategies:
                    report_lines.append(f"    • {strategy.strategy_name}")
            
            # 失败的策略
            if failed_strategies:
                report_lines.append(f"  ❌ 失败策略 ({len(failed_strategies)}):")
                for strategy in failed_strategies:
                    report_lines.append(f"    • {strategy.strategy_name}: {strategy.error_message}")
        
        elif result.strategy_results and len(result.strategy_results) == 1:
            # 单策略模式
            strategy = result.strategy_results[0]
            report_lines.extend([
                "",
                f"{ReportEmojis.STRATEGY} 策略: {strategy.strategy_name}"
            ])
            if not strategy.success:
                report_lines.append(f"  ❌ 策略验证失败: {strategy.error_message}")
        
        # 添加错误信息
        if result.error_message:
            report_lines.extend([
                "",
                f"{ReportEmojis.ERROR_MSG} 错误信息: {result.error_message}"
            ])
        
        return "\n".join(report_lines)
    
    def _create_error_result(
        self, 
        target_date: date, 
        error_message: str,
        strategy_results: Optional[List[StrategyVerificationResult]] = None
    ) -> VerificationResult:
        """
        创建错误结果
        
        Args:
            target_date: 目标日期
            error_message: 错误信息
            strategy_results: 策略验证结果列表
            
        Returns:
            VerificationResult: 错误结果
        """
        failed_strategies = []
        if strategy_results:
            failed_strategies = [r.strategy_name for r in strategy_results if not r.success]
        
        result = VerificationResult(
            status=VerificationStatus.ERROR,
            date=target_date,
            error_message=error_message,
            strategy_results=strategy_results or [],
            failed_strategies=failed_strategies
        )
        
        result.report_text = self._format_report_text(result)
        
        return result
    
    async def _send_verification_notification(
        self, 
        result: VerificationResult, 
        admin_chat_ids: List[str]
    ) -> None:
        """
        发送校验通知
        
        Args:
            result: 校验结果
            admin_chat_ids: 管理员Chat ID列表
        """
        try:
            await self.notification_sender.send_verification_report(result.report_text, admin_chat_ids)
        except Exception as e:
            self.logger.error(LogMessages.VERIFICATION_NOTIFICATION_FAILED.format(error=e))
            # 不抛出异常，避免影响主要校验流程
    
    async def _send_error_notification(self, error_message: str, admin_chat_ids: List[str]) -> None:
        """
        发送错误通知
        
        Args:
            error_message: 错误信息
            admin_chat_ids: 管理员Chat ID列表
        """
        try:
            await self.notification_sender.send_error_notification(error_message, admin_chat_ids)
        except Exception as e:
            self.logger.error(LogMessages.ERROR_NOTIFICATION_EXCEPTION.format(error=e))
            # 静默处理，不抛出异常
    
    def _cleanup_temp_files(self, file_paths: List[str]) -> None:
        """
        清理临时文件
        
        Args:
            file_paths: 文件路径列表
        """
        for file_path in file_paths:
            try:
                if file_path and Path(file_path).exists():
                    Path(file_path).unlink()
                    self.logger.debug(LogMessages.TEMP_FILE_CLEANED.format(file_path=file_path))
            except Exception as e:
                self.logger.warning(LogMessages.TEMP_FILE_CLEANUP_FAILED.format(file_path=file_path, error=e))