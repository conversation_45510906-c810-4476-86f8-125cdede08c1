"""
每日回测校验运行器

负责协调整个校验流程：
- 整合所有组件
- 执行完整的校验流程
- 处理错误和异常情况
- 生成校验报告

技术实现方案：@daily_backtest_verification_dev_plan_ai.md
"""

import asyncio
import logging
from datetime import date, timedelta, datetime
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from pathlib import Path

from .config_reader import ConfigReader
from .backtest_executor import BacktestExecutor
from .backtest_result_reader import BacktestResultReader
from .notification_sender import NotificationSender
from .strategy_selector import (
    StrategySelector, 
    MissingStrategyError, 
    StrategyNotFoundError, 
    StrategyDisabledError
)
from dao.signal_dao import SignalDAO


# 常量定义
class VerificationStatus:
    """校验状态常量"""
    SUCCESS = 'success'
    MISMATCH = 'mismatch'
    ERROR = 'error'


class ReportEmojis:
    """报告表情符号常量"""
    SUCCESS = "✅ 通过"
    MISMATCH = "⚠️ 不匹配"
    ERROR = "❌ 失败"
    TITLE = "📊"
    DATE = "📅"
    STATUS = "🔍"
    TRADES = "📈"
    SIGNALS = "📊"
    MATCHED = "✅"
    TIME = "⏱️"
    WARNING = "⚠️"
    ERROR_MSG = "❌"
    STRATEGY = "🎯"


class LogMessages:
    """日志消息模板"""
    VERIFICATION_START = "开始校验流程，目标日期: {date}"
    BACKTEST_EXECUTING = "执行回测..."
    RESULT_READING = "读取回测结果..."
    VERIFICATION_COMPLETE = "校验完成，状态: {status}"
    VERIFICATION_EXCEPTION = "校验流程异常: {error}"
    CONFIG_RELOAD_FAILED = "重新加载管理员配置失败: {error}"
    ERROR_NOTIFICATION_FAILED = "发送错误通知失败: {error}"
    VERIFICATION_NOTIFICATION_FAILED = "发送校验通知失败: {error}"
    ERROR_NOTIFICATION_EXCEPTION = "发送错误通知异常: {error}"
    TEMP_FILE_CLEANED = "清理临时文件: {file_path}"
    TEMP_FILE_CLEANUP_FAILED = "清理临时文件失败: {file_path}, 错误: {error}"


@dataclass
class StrategyVerificationResult:
    """单个策略的验证结果"""
    strategy_name: str
    success: bool
    error_message: Optional[str] = None
    total_trades: Optional[int] = None
    total_signals: Optional[int] = None
    matched_trades: Optional[int] = None
    execution_time: Optional[float] = None


@dataclass
class VerificationResult:
    """校验结果"""
    status: str  # 'success', 'mismatch', 'error'
    date: date
    total_trades: Optional[int] = None
    total_signals: Optional[int] = None
    matched_trades: Optional[int] = None
    execution_time: Optional[float] = None
    report_text: str = ""
    error_message: Optional[str] = None
    # 多策略支持
    strategy_results: Optional[List[StrategyVerificationResult]] = None
    failed_strategies: Optional[List[StrategyVerificationResult]] = None
    # 不匹配交易详情
    unmatched_trades_details: Optional[List[Dict[str, Any]]] = None
    missing_signals_count: Optional[int] = None


class VerificationError(Exception):
    """校验异常"""
    pass


class DailyBacktestVerificationRunner:
    """
    每日回测校验运行器
    
    负责协调整个校验流程的执行，整合所有组件。
    """
    
    def __init__(self):
        """初始化校验运行器"""
        self.config_reader = ConfigReader()
        self.executor = BacktestExecutor()
        self.reader = BacktestResultReader()
        self.notification_sender = NotificationSender()
        self.signal_dao = SignalDAO()
        self.strategy_selector = StrategySelector()
        self.logger = logging.getLogger(__name__)
    
    async def run_verification(self, target_date: Optional[date] = None, strategies: Optional[List[str] | str] = None) -> VerificationResult:
        """
        运行校验流程

        Args:
            target_date: 目标日期，如果为None则使用昨天
            strategies: 策略列表或"all"表示所有策略，如果为None则使用多策略模式

        Returns:
            VerificationResult: 校验结果
        """
        # 使用默认日期（昨天）
        if target_date is None:
            target_date = date.today() - timedelta(days=1)
        
        # 保存目标日期到实例变量，供后续方法使用
        self._current_target_date = target_date
        
        temp_config_file = None
        admin_chat_ids = []
        strategy_results = []
        
        try:
            self.logger.info(LogMessages.VERIFICATION_START.format(date=target_date))

            # 确定要执行的策略列表
            target_strategies = await self._determine_target_strategies(strategies)

            # 根据策略数量选择执行模式
            if len(target_strategies) == 1:
                # 单策略模式 - 使用原有逻辑
                config_data, temp_config_file, admin_chat_ids = await self._load_single_strategy_configuration(target_strategies[0], target_date)
                strategy_results = await self._validate_strategy_configuration(config_data, target_strategies)

                # 检查策略验证结果
                if not strategy_results or not strategy_results[0].success:
                    error_msg = strategy_results[0].error_message if strategy_results else "策略验证失败"
                    raise VerificationError(f"策略验证失败: {error_msg}")

                # 执行回测
                backtest_result = await self._execute_backtest_with_validation(temp_config_file, admin_chat_ids)
                result_data = await self._read_backtest_result_with_validation(backtest_result.result_file_path, admin_chat_ids)

                # 生成和发送校验报告
                verification_result = await self._generate_and_send_report(
                    result_data, target_date, backtest_result.execution_time, admin_chat_ids, strategy_results
                )
            else:
                # 多策略模式 - 分别执行每个策略的回测
                verification_result = await self._run_multi_strategy_verification(target_strategies, target_date)

                # 发送通知
                admin_chat_ids = await self.config_reader.read_admin_telegram_config()
                await self._send_notification(verification_result, admin_chat_ids)

            self.logger.info(LogMessages.VERIFICATION_COMPLETE.format(status=verification_result.status))
            return verification_result
            
        except Exception as e:
            return await self._handle_verification_exception(e, target_date, admin_chat_ids, strategy_results)
            
        finally:
            # 清理临时文件
            if temp_config_file:
                self._cleanup_temp_files([temp_config_file])

    async def _determine_target_strategies(self, strategies: Optional[List[str] | str]) -> List[str]:
        """
        确定要执行的策略列表

        Args:
            strategies: 策略列表或"all"表示所有策略，如果为None则使用多策略模式

        Returns:
            List[str]: 目标策略列表
        """
        if strategies == "all":
            # 获取所有已启用的策略
            return await self.config_reader.get_all_enabled_strategies()
        elif isinstance(strategies, list):
            # 使用指定的策略列表
            return strategies
        elif isinstance(strategies, str):
            # 单个策略
            return [strategies]
        else:
            # 默认获取所有已启用的策略
            return await self.config_reader.get_all_enabled_strategies()

    async def _load_single_strategy_configuration(self, strategy_name: str, target_date: date) -> tuple[Any, str, List[str]]:
        """
        加载单个策略的配置

        Args:
            strategy_name: 策略名称
            target_date: 目标日期

        Returns:
            tuple: (配置数据, 临时配置文件路径, 管理员Chat ID列表)
        """
        target_date_str = target_date.strftime('%Y-%m-%d')

        # 读取单个策略配置
        config_data, temp_config_file = await self.config_reader.read_strategy_config(strategy_name, target_date_str)

        # 读取管理员Telegram配置
        admin_chat_ids = await self.config_reader.read_admin_telegram_config()

        return config_data, temp_config_file, admin_chat_ids

    async def _run_multi_strategy_verification(self, target_strategies: List[str], target_date: date) -> VerificationResult:
        """
        执行多策略回测校验

        Args:
            target_strategies: 目标策略列表
            target_date: 目标日期

        Returns:
            VerificationResult: 汇总的校验结果
        """
        all_strategy_results = []
        all_trades = []
        total_execution_time = 0.0
        temp_files = []

        try:
            self.logger.info(f"开始多策略回测校验，策略数量: {len(target_strategies)}")

            # 为每个策略分别执行回测
            for i, strategy_name in enumerate(target_strategies, 1):
                self.logger.info(f"执行策略 {i}/{len(target_strategies)}: {strategy_name}")

                try:
                    # 1. 加载单个策略配置
                    config_data, temp_config_file, admin_chat_ids = await self._load_single_strategy_configuration(strategy_name, target_date)
                    temp_files.append(temp_config_file)

                    # 2. 验证策略配置
                    strategy_results = await self._validate_strategy_configuration(config_data, [strategy_name])

                    if not strategy_results or not strategy_results[0].success:
                        error_msg = strategy_results[0].error_message if strategy_results else "策略验证失败"
                        self.logger.error(f"策略 {strategy_name} 验证失败: {error_msg}")
                        all_strategy_results.extend(strategy_results)
                        continue

                    # 3. 执行回测
                    backtest_result = await self._execute_backtest_with_validation(temp_config_file, admin_chat_ids)
                    total_execution_time += backtest_result.execution_time

                    # 4. 读取回测结果
                    result_data = await self._read_backtest_result_with_validation(backtest_result.result_file_path, admin_chat_ids)

                    # 5. 收集交易数据
                    all_trades.extend(result_data.trades)
                    all_strategy_results.extend(strategy_results)

                    self.logger.info(f"策略 {strategy_name} 回测完成，交易数: {len(result_data.trades)}")

                except Exception as e:
                    self.logger.error(f"策略 {strategy_name} 执行失败: {e}")
                    # 创建失败的策略结果
                    failed_result = StrategyVerificationResult(strategy_name=strategy_name, success=False, error_message=str(e))
                    all_strategy_results.append(failed_result)

            # 6. 查询实际信号详情
            actual_signals = await self._query_actual_signals_details(target_date, target_strategies)
            actual_signals_count = len(actual_signals)

            # 7. 分析不匹配详情
            mismatch_details = await self._analyze_mismatch_details(all_trades, actual_signals, target_strategies)

            # 8. 生成汇总报告
            verification_result = await self._generate_multi_strategy_report(
                target_date, all_trades, actual_signals_count, total_execution_time, all_strategy_results, mismatch_details
            )

            self.logger.info(f"多策略回测校验完成，总交易数: {len(all_trades)}, 总执行时间: {total_execution_time:.1f}秒")
            return verification_result

        finally:
            # 清理临时文件
            if temp_files:
                self._cleanup_temp_files(temp_files)

    async def _generate_multi_strategy_report(self, target_date: date, all_trades: List[Any], actual_signals_count: int,
                                            total_execution_time: float, all_strategy_results: List[StrategyVerificationResult],
                                            mismatch_details: Dict[str, Any]) -> VerificationResult:
        """
        生成多策略汇总报告

        Args:
            target_date: 目标日期
            all_trades: 所有策略的交易列表
            actual_signals_count: 实际信号数量
            total_execution_time: 总执行时间
            all_strategy_results: 所有策略的验证结果
            mismatch_details: 不匹配详情分析结果

        Returns:
            VerificationResult: 汇总的校验结果
        """
        # 计算汇总统计
        total_trades = len(all_trades) // 2  # 因为每个完整交易包含买入和卖出两条记录
        matched_trades = total_trades  # 回测生成的交易都算作匹配的

        # 确定整体状态
        if total_trades == actual_signals_count:
            status = "success"
        elif total_trades > 0:
            status = "mismatch"
        else:
            status = "error"

        # 分离成功和失败的策略
        successful_strategies = [r for r in all_strategy_results if r.success]
        failed_strategies = [r for r in all_strategy_results if not r.success]

        # 使用详细的不匹配分析结果
        missing_signals_count = mismatch_details.get("missing_trades_count", 0)
        extra_trades_count = mismatch_details.get("extra_trades_count", 0)

        unmatched_trades_details = []
        if missing_signals_count > 0:
            unmatched_trades_details.append({
                "type": "missing_trades",
                "count": missing_signals_count,
                "description": f"{missing_signals_count}个信号未生成交易"
            })
        if extra_trades_count > 0:
            unmatched_trades_details.append({
                "type": "extra_trades",
                "count": extra_trades_count,
                "description": f"{extra_trades_count}个回测交易无对应信号"
            })

        # 生成报告文本
        report_text = self._format_multi_strategy_report_text(
            target_date, status, total_trades, actual_signals_count, matched_trades,
            total_execution_time, successful_strategies, failed_strategies, mismatch_details
        )

        return VerificationResult(
            status=status,
            date=target_date,
            total_trades=total_trades,
            total_signals=actual_signals_count,
            matched_trades=matched_trades,
            execution_time=total_execution_time,
            report_text=report_text,
            strategy_results=successful_strategies,
            failed_strategies=failed_strategies,
            unmatched_trades_details=unmatched_trades_details,
            missing_signals_count=missing_signals_count
        )

    def _format_multi_strategy_report_text(self, target_date: date, status: str, total_trades: int,
                                         actual_signals_count: int, matched_trades: int, execution_time: float,
                                         successful_strategies: List[StrategyVerificationResult],
                                         failed_strategies: List[StrategyVerificationResult],
                                         mismatch_details: Dict[str, Any]) -> str:
        """
        格式化多策略报告文本
        """
        # 状态图标
        status_icon = "✅" if status == "success" else "⚠️" if status == "mismatch" else "❌"
        status_text = "匹配" if status == "success" else "不匹配" if status == "mismatch" else "错误"

        # 基础报告
        report_lines = [
            "📊 每日回测校验报告",
            "",
            f"📅 日期: {target_date}",
            f"🔍 状态: {status_icon} {status_text}",
            "",
            f"📈 回测交易数: {total_trades}",
            f"📊 实际信号数: {actual_signals_count}",
            f"✅ 匹配交易数: {matched_trades}",
            f"⏱️ 执行时间: {execution_time:.1f}秒"
        ]

        # 添加详细的不匹配信息
        missing_trades_count = mismatch_details.get("missing_trades_count", 0)
        extra_trades_count = mismatch_details.get("extra_trades_count", 0)
        missing_trades = mismatch_details.get("missing_trades", [])
        extra_trades = mismatch_details.get("extra_trades", [])

        if missing_trades_count > 0 or extra_trades_count > 0:
            report_lines.extend([
                "",
                "⚠️ 不匹配详情:"
            ])

            if missing_trades_count > 0:
                report_lines.append(f"  📉 {missing_trades_count}个信号未生成交易:")
                for trade in missing_trades:  # 显示所有，不省略
                    token_display = trade.get("token_display", "unknown")
                    trigger_time = trade.get("trigger_time", "unknown")
                    strategy_name = trade.get("strategy_name", "unknown")
                    report_lines.append(f"    • {token_display} ({trigger_time}, {strategy_name})")

            if extra_trades_count > 0:
                report_lines.append(f"  📈 {extra_trades_count}个回测交易无对应信号:")
                for trade in extra_trades:  # 显示所有，不省略
                    token_display = trade.get("token_display", "unknown")
                    buy_time = trade.get("buy_time", "unknown")
                    report_lines.append(f"    • {token_display} ({buy_time})")

        # 添加策略执行情况
        report_lines.extend([
            "",
            "🎯 策略执行情况:"
        ])

        if successful_strategies:
            report_lines.append(f"  ✅ 成功策略 ({len(successful_strategies)}):")
            for strategy in successful_strategies:
                report_lines.append(f"    • {strategy.strategy_name}")

        if failed_strategies:
            report_lines.append(f"  ❌ 失败策略 ({len(failed_strategies)}):")
            for strategy in failed_strategies:
                report_lines.append(f"    • {strategy.strategy_name}: {strategy.error_message}")

        return "\n".join(report_lines)

    async def _load_configurations(self, strategies: Optional[List[str] | str], target_date: date) -> tuple[Any, str, List[str]]:
        """
        加载所有必要的配置

        Args:
            strategies: 策略列表或"all"表示所有策略，如果为None则使用多策略模式
            target_date: 目标日期

        Returns:
            tuple: (配置数据, 临时配置文件路径, 管理员Chat ID列表)
        """
        target_date_str = target_date.strftime('%Y-%m-%d')

        # 确定要使用的策略列表
        if strategies == "all":
            # 获取所有已启用的策略
            strategy_list = await self.config_reader.get_all_enabled_strategies()
        elif isinstance(strategies, list):
            # 使用指定的策略列表
            strategy_list = strategies
        elif isinstance(strategies, str):
            # 单个策略
            strategy_list = [strategies]
        else:
            # 默认获取所有已启用的策略
            strategy_list = await self.config_reader.get_all_enabled_strategies()

        # 读取策略配置
        if len(strategy_list) == 1:
            # 单策略模式
            config_data, temp_config_file = await self.config_reader.read_strategy_config(strategy_list[0], target_date_str)
        else:
            # 多策略模式
            config_data, temp_config_file = await self.config_reader.read_multi_strategy_config(strategy_list, target_date_str)

        # 读取管理员Telegram配置
        admin_chat_ids = await self.config_reader.read_admin_telegram_config()

        return config_data, temp_config_file, admin_chat_ids
    
    async def _validate_strategy_configuration(self, config_data: Any, strategies: Optional[List[str] | str] = None) -> List[StrategyVerificationResult]:
        """
        验证策略配置

        Args:
            config_data: 配置数据
            strategies: 策略列表或"all"表示所有策略，如果为None则使用多策略模式

        Returns:
            List[StrategyVerificationResult]: 策略验证结果列表

        Raises:
            MissingStrategyError: 当缺少策略名称且配置中没有启用的策略时
        """
        try:
            # 获取目标策略列表
            if 'target_strategies' in config_data:
                # 多策略模式
                target_strategies = config_data['target_strategies']
                self.logger.info(f"多策略模式，目标策略: {target_strategies}")
            elif isinstance(strategies, list) and len(strategies) == 1:
                # 单策略模式
                target_strategies = strategies
                self.logger.info(f"单策略模式，目标策略: {target_strategies[0]}")
            elif isinstance(strategies, str):
                # 单策略模式
                target_strategies = [strategies]
                self.logger.info(f"单策略模式，目标策略: {strategies}")
            else:
                # 默认情况，从配置中推断
                target_strategies = ["unknown"]
                self.logger.info("使用默认策略验证")

            # 验证配置完整性
            required_fields = [
                'transaction_lookback_hours', 'transaction_min_amount', 'kol_account_min_count',
                'sell_strategy_hours', 'sell_kol_ratio', 'backtest_start_time', 'backtest_end_time'
            ]

            missing_fields = [field for field in required_fields if field not in config_data]
            if missing_fields:
                error_msg = f"策略配置缺少必要字段: {missing_fields}"
                self.logger.error(error_msg)
                # 为所有目标策略返回失败结果
                return [StrategyVerificationResult(strategy_name=strategy, success=False, error_message=error_msg)
                       for strategy in target_strategies]

            # 验证成功 - 为所有目标策略返回成功结果
            results = []
            for strategy in target_strategies:
                result = StrategyVerificationResult(strategy_name=strategy, success=True)
                results.append(result)
                self.logger.info(f"策略验证成功: {strategy}")

            self.logger.info(f"策略验证完成，成功: {len(results)}, 失败: 0")
            return results

        except Exception as e:
            self.logger.error(f"策略配置验证失败: {e}")
            # 为所有目标策略返回失败结果
            if 'target_strategies' in config_data:
                target_strategies = config_data['target_strategies']
            elif isinstance(strategies, list):
                target_strategies = strategies
            elif isinstance(strategies, str):
                target_strategies = [strategies]
            else:
                target_strategies = ["unknown"]

            return [StrategyVerificationResult(strategy_name=strategy, success=False, error_message=str(e))
                   for strategy in target_strategies]
    
    async def _validate_single_strategy(self, config_data: Any, strategy_name: str) -> StrategyVerificationResult:
        """
        验证单个策略
        
        Args:
            config_data: 配置数据
            strategy_name: 策略名称
            
        Returns:
            StrategyVerificationResult: 单个策略的验证结果
        """
        try:
            # 基础策略名称验证
            self.strategy_selector.validate_strategy_name(strategy_name)
            
            # 验证策略在配置中的存在性和启用状态
            self.strategy_selector.validate_strategy_in_config(strategy_name, config_data)
            
            # 获取策略配置（用于验证策略是否可用）
            strategy_config = self.strategy_selector.get_strategy_config(strategy_name)
            
            self.logger.info(f"策略验证成功: {strategy_name}")
            self.logger.debug(f"策略配置: {strategy_config}")
            
            return StrategyVerificationResult(
                strategy_name=strategy_name,
                success=True
            )
            
        except (MissingStrategyError, StrategyNotFoundError, StrategyDisabledError) as e:
            self.logger.warning(f"策略 {strategy_name} 验证失败: {e}")
            return StrategyVerificationResult(
                strategy_name=strategy_name,
                success=False,
                error_message=str(e)
            )
        except Exception as e:
            self.logger.error(f"策略 {strategy_name} 验证过程中发生未知错误: {e}")
            return StrategyVerificationResult(
                strategy_name=strategy_name,
                success=False,
                error_message=f"未知错误: {e}"
            )
    
    def _extract_strategy_name_from_config(self, config_data: Any) -> str:
        """
        从配置数据中提取策略名称
        
        Args:
            config_data: 配置数据
            
        Returns:
            str: 策略名称
            
        Raises:
            MissingStrategyError: 当配置中缺少策略名称时
        """
        try:
            # 假设配置数据是字典格式，策略名称在 'strategy' 字段中
            if isinstance(config_data, dict):
                strategy_name = config_data.get('strategy')
                if not strategy_name:
                    raise MissingStrategyError("配置中缺少策略名称")
                return strategy_name
            else:
                # 如果配置数据不是字典，尝试其他方式提取
                # 这里可以根据实际的配置格式进行调整
                raise MissingStrategyError("无法从配置中提取策略名称")
        except Exception as e:
            if isinstance(e, MissingStrategyError):
                raise
            raise MissingStrategyError(f"提取策略名称时发生错误: {e}")
    
    async def _execute_backtest_with_validation(self, temp_config_file: str, admin_chat_ids: List[str]):
        """
        执行回测并验证结果
        
        Args:
            temp_config_file: 临时配置文件路径
            admin_chat_ids: 管理员Chat ID列表
            
        Returns:
            BacktestResult: 回测结果
            
        Raises:
            VerificationError: 回测执行失败时抛出
        """
        self.logger.info(LogMessages.BACKTEST_EXECUTING)
        # 获取目标日期，如果没有则使用昨天
        target_date = getattr(self, '_current_target_date', None)
        backtest_result = await self.executor.execute_backtest(temp_config_file, target_date=target_date)
        
        if not backtest_result.success:
            error_msg = f"回测执行失败: {backtest_result.error}"
            raise VerificationError(error_msg)
        
        return backtest_result
    
    async def _query_actual_signals_count(self, target_date: date, strategy_names: Optional[List[str]] = None) -> int:
        """
        查询指定日期的实际信号数量

        Args:
            target_date: 目标日期
            strategy_names: 策略名称列表，如果为None则查询所有策略

        Returns:
            int: 实际信号数量
        """
        try:
            # 计算日期范围（使用datetime对象而不是时间戳）
            start_datetime = datetime.combine(target_date, datetime.min.time())
            end_datetime = datetime.combine(target_date, datetime.max.time())

            # 构建查询条件
            query = {
                "created_at": {
                    "$gte": start_datetime,
                    "$lte": end_datetime
                }
            }

            # 如果指定了策略名称，添加策略过滤条件
            if strategy_names:
                query["trigger_conditions.strategy_name"] = {"$in": strategy_names}

            signals = await self.signal_dao.find_signals(query)
            signal_count = len(signals)

            strategy_info = f" (策略: {', '.join(strategy_names)})" if strategy_names else " (所有策略)"
            self.logger.info(f"查询到 {target_date} 的实际信号数量: {signal_count}{strategy_info}")
            return signal_count

        except Exception as e:
            self.logger.error(f"查询实际信号数量失败: {str(e)}", exc_info=True)
            return 0

    async def _query_actual_signals_details(self, target_date: date, strategy_names: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        查询指定日期的实际信号详情

        Args:
            target_date: 目标日期
            strategy_names: 策略名称列表，如果为None则查询所有策略

        Returns:
            List[Dict]: 实际信号详情列表
        """
        try:
            # 计算日期范围（使用datetime对象而不是时间戳）
            start_datetime = datetime.combine(target_date, datetime.min.time())
            end_datetime = datetime.combine(target_date, datetime.max.time())

            # 构建查询条件
            query = {
                "created_at": {
                    "$gte": start_datetime,
                    "$lte": end_datetime
                }
            }

            # 如果指定了策略名称，添加策略过滤条件
            if strategy_names:
                query["trigger_conditions.strategy_name"] = {"$in": strategy_names}

            signals = await self.signal_dao.find_signals(query)

            # 转换为字典格式，便于后续处理
            signal_details = []
            for signal in signals:
                signal_dict = {
                    "id": str(signal.id),
                    "token_address": signal.token_address,
                    "token_name": signal.token_name,
                    "token_symbol": signal.token_symbol,
                    "signal_type": signal.signal_type,
                    "trigger_timestamp": signal.trigger_timestamp,
                    "trigger_conditions": signal.trigger_conditions,
                    "hit_kol_wallets": signal.hit_kol_wallets,
                    "created_at": signal.created_at,
                    "strategy_name": signal.trigger_conditions.get("strategy_name", "unknown") if signal.trigger_conditions else "unknown"
                }
                signal_details.append(signal_dict)

            strategy_info = f" (策略: {', '.join(strategy_names)})" if strategy_names else " (所有策略)"
            self.logger.info(f"查询到 {target_date} 的实际信号详情: {len(signal_details)}个{strategy_info}")
            return signal_details

        except Exception as e:
            self.logger.error(f"查询实际信号详情失败: {str(e)}", exc_info=True)
            return []

    async def _analyze_mismatch_details(self, all_trades: List[Any], actual_signals: List[Dict[str, Any]],
                                      target_strategies: List[str]) -> Dict[str, Any]:
        """
        分析不匹配详情

        Args:
            all_trades: 所有回测交易
            actual_signals: 实际信号详情
            target_strategies: 目标策略列表

        Returns:
            Dict: 不匹配详情分析结果
        """
        try:
            # 提取回测交易的详细信息（每个交易记录都是完整的交易）
            backtest_details = []
            backtest_buy_trades = []

            for trade in all_trades:
                if hasattr(trade, 'token_address') and hasattr(trade, 'buy_timestamp'):
                    token_address = trade.token_address
                    buy_timestamp = trade.buy_timestamp

                    backtest_buy_trades.append(trade)
                    backtest_details.append({
                        "token_address": token_address,
                        "buy_timestamp": buy_timestamp,
                        "buy_time_str": datetime.fromtimestamp(buy_timestamp).strftime("%H:%M:%S") if buy_timestamp else "unknown",
                        "token_display": f"{token_address[:8]}...{token_address[-4:]}"
                    })

            # 提取实际信号的详细信息
            signal_details = []
            buy_signals = []

            for signal in actual_signals:
                # 只处理买入信号
                if signal.get("signal_type") == "kol_buy":
                    token_address = signal.get("token_address")
                    trigger_timestamp = signal.get("trigger_timestamp")
                    strategy_name = signal.get("strategy_name", "unknown")

                    if token_address:
                        buy_signals.append(signal)
                        signal_details.append({
                            "token_address": token_address,
                            "trigger_timestamp": trigger_timestamp,
                            "trigger_time_str": trigger_timestamp.strftime("%H:%M:%S") if trigger_timestamp else "unknown",
                            "strategy_name": strategy_name,
                            "token_name": signal.get("token_name", ""),
                            "token_symbol": signal.get("token_symbol", ""),
                            "token_display": f"{token_address[:8]}...{token_address[-4:]}"  # 显示地址前8位和后4位
                        })

            # 基于数量的不匹配分析
            total_backtest_trades = len(backtest_buy_trades)
            total_signals = len(buy_signals)

            self.logger.info(f"不匹配分析: 回测交易数={total_backtest_trades}, 实际信号数={total_signals}")

            # 计算不匹配数量
            if total_backtest_trades > total_signals:
                # 回测交易多于信号：有额外的回测交易
                missing_trades_count = 0
                extra_trades_count = total_backtest_trades - total_signals
                self.logger.info(f"检测到 {extra_trades_count} 个额外的回测交易")
            elif total_signals > total_backtest_trades:
                # 信号多于回测交易：有信号未生成交易
                missing_trades_count = total_signals - total_backtest_trades
                extra_trades_count = 0
                self.logger.info(f"检测到 {missing_trades_count} 个信号未生成交易")
            else:
                # 数量相等：可能完全匹配或者有交叉不匹配
                missing_trades_count = 0
                extra_trades_count = 0
                self.logger.info("信号数量与回测交易数量相等")

            # 生成详细的不匹配信息
            mismatch_details = {
                "missing_trades_count": missing_trades_count,
                "extra_trades_count": extra_trades_count,
                "missing_trades": [],
                "extra_trades": [],
                "signal_summary": {},
                "backtest_summary": {}
            }

            # 按策略统计信号
            for strategy in target_strategies:
                strategy_signals = [s for s in signal_details if s["strategy_name"] == strategy]
                mismatch_details["signal_summary"][strategy] = len(strategy_signals)

            # 统计回测交易
            mismatch_details["backtest_summary"]["total_trades"] = len(backtest_details)

            # 生成详细的不匹配信息（显示所有，不省略）
            if missing_trades_count > 0:
                # 显示所有未生成交易的信号
                for signal_info in signal_details:
                    mismatch_details["missing_trades"].append({
                        "token_address": signal_info["token_address"],
                        "token_display": signal_info["token_display"],
                        "trigger_time": signal_info["trigger_time_str"],
                        "strategy_name": signal_info["strategy_name"]
                    })

            if extra_trades_count > 0:
                # 显示所有额外的回测交易
                for backtest_info in backtest_details:
                    mismatch_details["extra_trades"].append({
                        "token_address": backtest_info["token_address"],
                        "token_display": backtest_info["token_display"],
                        "buy_time": backtest_info["buy_time_str"]
                    })

            return mismatch_details

        except Exception as e:
            self.logger.error(f"分析不匹配详情失败: {str(e)}", exc_info=True)
            return {
                "missing_trades_count": 0,
                "extra_trades_count": 0,
                "missing_trades": [],
                "extra_trades": [],
                "signal_summary": {},
                "backtest_summary": {}
            }

    async def _read_backtest_result_with_validation(self, result_file_path: Optional[str], admin_chat_ids: List[str]):
        """
        读取回测结果并验证
        
        Args:
            result_file_path: 结果文件路径
            admin_chat_ids: 管理员Chat ID列表
            
        Returns:
            结果数据
            
        Raises:
            VerificationError: 结果文件路径为空或读取失败时抛出
        """
        self.logger.info(LogMessages.RESULT_READING)
        
        if not result_file_path:
            error_msg = "回测结果文件路径为空"
            raise VerificationError(error_msg)
        
        return await self.reader.read_result_file(result_file_path)
    
    async def _generate_and_send_report(
        self, 
        result_data: Any, 
        target_date: date, 
        execution_time: float, 
        admin_chat_ids: List[str],
        strategy_results: List[StrategyVerificationResult]
    ) -> VerificationResult:
        """
        生成校验报告并发送通知
        
        Args:
            result_data: 回测结果数据
            target_date: 目标日期
            execution_time: 执行时间
            admin_chat_ids: 管理员Chat ID列表
            strategy_results: 策略验证结果列表
            
        Returns:
            VerificationResult: 校验结果
        """
        # 生成校验报告
        verification_result = await self._generate_verification_report(
            result_data, target_date, execution_time, strategy_results
        )
        
        # 发送通知
        await self._send_verification_notification(verification_result, admin_chat_ids)
        
        return verification_result
    
    async def _handle_verification_exception(
        self, 
        exception: Exception, 
        target_date: date, 
        admin_chat_ids: List[str],
        strategy_results: Optional[List[StrategyVerificationResult]] = None
    ) -> VerificationResult:
        """
        处理校验过程中的异常
        
        Args:
            exception: 异常对象
            target_date: 目标日期
            admin_chat_ids: 管理员Chat ID列表
            strategy_results: 策略验证结果列表
            
        Returns:
            VerificationResult: 错误结果
        """
        error_msg = LogMessages.VERIFICATION_EXCEPTION.format(error=str(exception))
        self.logger.error(error_msg, exc_info=True)
        
        # 确保有管理员配置用于发送通知
        final_admin_chat_ids = await self._ensure_admin_config(admin_chat_ids)
        
        # 发送错误通知
        await self._send_error_notification(error_msg, final_admin_chat_ids)
        
        return self._create_error_result(target_date, error_msg, strategy_results)
    
    async def _ensure_admin_config(self, current_admin_chat_ids: List[str]) -> List[str]:
        """
        确保有可用的管理员配置
        
        Args:
            current_admin_chat_ids: 当前的管理员Chat ID列表
            
        Returns:
            List[str]: 可用的管理员Chat ID列表
        """
        if current_admin_chat_ids:
            return current_admin_chat_ids
        
        try:
            return await self.config_reader.read_admin_telegram_config()
        except Exception as config_error:
            self.logger.error(LogMessages.CONFIG_RELOAD_FAILED.format(error=config_error))
            return []
    
    async def _generate_verification_report(
        self, 
        backtest_result: Any, 
        target_date: date, 
        execution_time: float,
        strategy_results: List[StrategyVerificationResult]
    ) -> VerificationResult:
        """
        生成校验报告
        
        Args:
            backtest_result: 回测结果数据
            target_date: 目标日期
            execution_time: 执行时间
            strategy_results: 策略验证结果列表
            
        Returns:
            VerificationResult: 校验结果
        """
        # 查询实际信号数量 - 只查询成功验证的策略
        successful_strategies = [r for r in strategy_results if r.success]
        strategy_names = [s.strategy_name for s in successful_strategies] if successful_strategies else None
        actual_signals_count = await self._query_actual_signals_count(target_date, strategy_names)
        
        # 提取结果数据
        total_trades, total_signals, matched_trades = self._extract_result_data(backtest_result, actual_signals_count)
        
        # 判断校验状态
        status = self._determine_verification_status(total_trades, total_signals, matched_trades, strategy_results)

        # 分离成功和失败的策略
        failed_strategies = [r for r in strategy_results if not r.success]
        
        # 创建校验结果
        result = VerificationResult(
            status=status,
            date=target_date,
            total_trades=total_trades,
            total_signals=total_signals,
            matched_trades=matched_trades,
            execution_time=execution_time,
            strategy_results=strategy_results,
            failed_strategies=failed_strategies if failed_strategies else None
        )
        
        # 生成报告文本
        result.report_text = self._format_report_text(result)
        
        return result
    
    def _extract_result_data(self, backtest_result: Any, actual_signals_count: int) -> tuple[int, int, int]:
        """
        从回测结果中提取数据
        
        Args:
            backtest_result: 回测结果数据
            actual_signals_count: 实际信号数量
            
        Returns:
            tuple: (总交易数, 总信号数, 匹配交易数)
        """
        # 处理BacktestResultData对象
        if hasattr(backtest_result, 'total_trades'):
            total_trades = backtest_result.total_trades
            # 使用实际查询到的信号数量
            total_signals = actual_signals_count
            matched_trades = getattr(backtest_result, 'matched_trades', total_trades)
        else:
            # 处理字典格式
            total_trades = backtest_result.get('total_trades', 0)
            # 使用实际查询到的信号数量
            total_signals = actual_signals_count
            matched_trades = backtest_result.get('matched_trades', 0)
        
        return total_trades, total_signals, matched_trades
    
    def _determine_verification_status(self, total_trades: int, total_signals: int, matched_trades: int, strategy_results: List = None) -> str:
        """
        判断校验状态
        
        Args:
            total_trades: 总交易数
            total_signals: 总信号数
            matched_trades: 匹配交易数
            strategy_results: 策略验证结果列表
            
        Returns:
            str: 校验状态
        """
        # 如果有策略验证结果，需要考虑策略验证情况
        if strategy_results:
            # 检查是否有成功的策略
            has_successful_strategy = any(r.success for r in strategy_results)
            # 如果没有任何策略成功，返回错误状态
            if not has_successful_strategy:
                return VerificationStatus.ERROR
        
        # 基于数据匹配情况判断状态
        if total_trades == total_signals == matched_trades:
            return VerificationStatus.SUCCESS
        else:
            return VerificationStatus.MISMATCH
    
    def _format_report_text(self, result: VerificationResult) -> str:
        """
        格式化报告文本
        
        Args:
            result: 校验结果
            
        Returns:
            str: 格式化的报告文本
        """
        # 状态表情符号映射
        status_emoji_map = {
            VerificationStatus.SUCCESS: ReportEmojis.SUCCESS,
            VerificationStatus.MISMATCH: ReportEmojis.MISMATCH,
            VerificationStatus.ERROR: ReportEmojis.ERROR
        }
        
        status_emoji = status_emoji_map.get(result.status, ReportEmojis.ERROR)
        
        # 基础报告行
        report_lines = [
            f"{ReportEmojis.TITLE} 每日回测校验报告",
            "",
            f"{ReportEmojis.DATE} 日期: {result.date}",
            f"{ReportEmojis.STATUS} 状态: {status_emoji}",
            ""
        ]
        
        # 添加数据详情
        if result.total_trades is not None:
            report_lines.extend([
                f"{ReportEmojis.TRADES} 回测交易数: {result.total_trades}",
                f"{ReportEmojis.SIGNALS} 实际信号数: {result.total_signals}",
                f"{ReportEmojis.MATCHED} 匹配交易数: {result.matched_trades}",
            ])
        
        # 添加执行时间
        if result.execution_time is not None:
            try:
                execution_time_str = f"{result.execution_time:.1f}秒"
            except (TypeError, ValueError):
                # 处理Mock对象或其他无法格式化的情况
                execution_time_str = str(result.execution_time)
            report_lines.append(f"{ReportEmojis.TIME} 执行时间: {execution_time_str}")
        
        # 添加不匹配详情
        if result.status == VerificationStatus.MISMATCH and result.total_signals is not None and result.matched_trades is not None:
            unmatched_signals = result.total_signals - result.matched_trades
            if unmatched_signals > 0:
                report_lines.extend([
                    "",
                    f"{ReportEmojis.WARNING} 匹配差异: {unmatched_signals}个信号未生成交易"
                ])
        
        # 添加策略信息（多策略模式）
        if result.strategy_results and len(result.strategy_results) > 1:
            successful_strategies = [r for r in result.strategy_results if r.success]
            failed_strategies = [r for r in result.strategy_results if not r.success]
            
            report_lines.extend([
                "",
                f"{ReportEmojis.STRATEGY} 策略执行情况:"
            ])
            
            # 成功的策略
            if successful_strategies:
                report_lines.append(f"  ✅ 成功策略 ({len(successful_strategies)}):")  
                for strategy in successful_strategies:
                    report_lines.append(f"    • {strategy.strategy_name}")
            
            # 失败的策略
            if failed_strategies:
                report_lines.append(f"  ❌ 失败策略 ({len(failed_strategies)}):")
                for strategy in failed_strategies:
                    report_lines.append(f"    • {strategy.strategy_name}: {strategy.error_message}")
        
        elif result.strategy_results and len(result.strategy_results) == 1:
            # 单策略模式
            strategy = result.strategy_results[0]
            report_lines.extend([
                "",
                f"{ReportEmojis.STRATEGY} 策略: {strategy.strategy_name}"
            ])
            if not strategy.success:
                report_lines.append(f"  ❌ 策略验证失败: {strategy.error_message}")
        
        # 添加错误信息
        if result.error_message:
            report_lines.extend([
                "",
                f"{ReportEmojis.ERROR_MSG} 错误信息: {result.error_message}"
            ])
        
        return "\n".join(report_lines)
    
    def _create_error_result(
        self, 
        target_date: date, 
        error_message: str,
        strategy_results: Optional[List[StrategyVerificationResult]] = None
    ) -> VerificationResult:
        """
        创建错误结果
        
        Args:
            target_date: 目标日期
            error_message: 错误信息
            strategy_results: 策略验证结果列表
            
        Returns:
            VerificationResult: 错误结果
        """
        failed_strategies = []
        if strategy_results:
            failed_strategies = [r.strategy_name for r in strategy_results if not r.success]
        
        result = VerificationResult(
            status=VerificationStatus.ERROR,
            date=target_date,
            error_message=error_message,
            strategy_results=strategy_results or [],
            failed_strategies=failed_strategies
        )
        
        result.report_text = self._format_report_text(result)
        
        return result
    
    async def _send_verification_notification(
        self, 
        result: VerificationResult, 
        admin_chat_ids: List[str]
    ) -> None:
        """
        发送校验通知
        
        Args:
            result: 校验结果
            admin_chat_ids: 管理员Chat ID列表
        """
        try:
            await self.notification_sender.send_verification_report(result.report_text, admin_chat_ids)
        except Exception as e:
            self.logger.error(LogMessages.VERIFICATION_NOTIFICATION_FAILED.format(error=e))
            # 不抛出异常，避免影响主要校验流程

    async def _send_notification(self, verification_result: VerificationResult, admin_chat_ids: List[str]) -> None:
        """
        发送校验通知

        Args:
            verification_result: 校验结果
            admin_chat_ids: 管理员Chat ID列表
        """
        await self._send_verification_notification(verification_result, admin_chat_ids)

    async def _send_error_notification(self, error_message: str, admin_chat_ids: List[str]) -> None:
        """
        发送错误通知
        
        Args:
            error_message: 错误信息
            admin_chat_ids: 管理员Chat ID列表
        """
        try:
            await self.notification_sender.send_error_notification(error_message, admin_chat_ids)
        except Exception as e:
            self.logger.error(LogMessages.ERROR_NOTIFICATION_EXCEPTION.format(error=e))
            # 静默处理，不抛出异常
    
    def _cleanup_temp_files(self, file_paths: List[str]) -> None:
        """
        清理临时文件
        
        Args:
            file_paths: 文件路径列表
        """
        for file_path in file_paths:
            try:
                if file_path and Path(file_path).exists():
                    Path(file_path).unlink()
                    self.logger.debug(LogMessages.TEMP_FILE_CLEANED.format(file_path=file_path))
            except Exception as e:
                self.logger.warning(LogMessages.TEMP_FILE_CLEANUP_FAILED.format(file_path=file_path, error=e))