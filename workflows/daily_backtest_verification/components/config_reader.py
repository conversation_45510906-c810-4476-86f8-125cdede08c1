"""
配置读取器组件

负责从数据库读取配置信息并生成临时文件：
- 读取 kol_activity 配置
- 读取管理员 Telegram 配置
- 创建和管理临时配置文件

技术实现方案：@daily_backtest_verification_dev_plan_ai.md
"""

import json
import tempfile
import os
import logging
from typing import Dict, Any, List, Tuple, Optional

from dao.config_dao import ConfigDAO


class ConfigNotFoundError(Exception):
    """配置不存在异常"""
    pass


class ConfigInvalidError(Exception):
    """配置格式无效异常"""
    pass


class ConfigReader:
    """
    配置读取器
    
    负责从数据库读取各种配置信息，并生成用于回测的临时配置文件。
    """
    
    def __init__(self):
        """初始化配置读取器"""
        self.config_dao = ConfigDAO()
        self.logger = logging.getLogger(__name__)
    
    async def read_strategy_config(self, strategy_name: str, target_date: str) -> <PERSON><PERSON>[Dict[str, Any], str]:
        """
        读取指定策略配置并生成临时文件

        Args:
            strategy_name: 策略名称
            target_date: 目标日期 (YYYY-MM-DD格式)

        Returns:
            Tuple[Dict[str, Any], str]: (配置数据, 临时文件路径)

        Raises:
            ConfigNotFoundError: 配置不存在
            ConfigInvalidError: 配置格式无效
        """
        try:
            # 从数据库读取配置
            config_record = await self.config_dao.get_config("kol_activity")

            if config_record is None:
                raise ConfigNotFoundError("kol_activity 配置不存在")

            # 验证配置格式
            if not hasattr(config_record, 'data'):
                raise ConfigInvalidError("配置格式无效：缺少 data 字段")

            # 检查 data 字段是否为字符串（无效格式）
            if isinstance(config_record.data, str):
                raise ConfigInvalidError("配置格式无效：data 字段不能是字符串")

            # 将配置数据转换为字典格式
            config_data = self._convert_to_dict(config_record.data)

            # 查找指定策略
            buy_strategies = config_data.get('buy_strategies', [])
            target_strategy = None

            for strategy in buy_strategies:
                if strategy.get('strategy_name') == strategy_name:
                    target_strategy = strategy
                    break

            if target_strategy is None:
                raise ConfigNotFoundError(f"策略 '{strategy_name}' 不存在")

            # 生成回测配置
            backtest_config = self._generate_backtest_config(target_strategy, target_date)

            # 创建临时文件
            temp_file_path = self.create_temp_config_file(backtest_config)

            self.logger.info(f"成功读取策略 '{strategy_name}' 配置，临时文件: {temp_file_path}")
            return backtest_config, temp_file_path

        except (ConfigNotFoundError, ConfigInvalidError):
            raise
        except Exception as e:
            self.logger.error(f"读取策略 '{strategy_name}' 配置失败: {e}")
            raise

    async def read_multi_strategy_config(self, strategies: List[str], target_date: str) -> Tuple[Dict[str, Any], str]:
        """
        读取多个策略配置并生成合并的临时文件

        Args:
            strategies: 策略名称列表
            target_date: 目标日期 (YYYY-MM-DD格式)

        Returns:
            Tuple[Dict[str, Any], str]: (合并的配置数据, 临时文件路径)

        Raises:
            ConfigNotFoundError: 配置不存在
            ConfigInvalidError: 配置格式无效
        """
        try:
            # 从数据库读取配置
            config_record = await self.config_dao.get_config("kol_activity")

            if config_record is None:
                raise ConfigNotFoundError("kol_activity 配置不存在")

            # 验证配置格式
            if not hasattr(config_record, 'data'):
                raise ConfigInvalidError("配置格式无效：缺少 data 字段")

            if isinstance(config_record.data, str):
                raise ConfigInvalidError("配置格式无效：data 字段不能是字符串")

            # 将配置数据转换为字典格式
            config_data = self._convert_to_dict(config_record.data)

            # 查找指定策略
            buy_strategies = config_data.get('buy_strategies', [])
            found_strategies = []
            missing_strategies = []

            for strategy_name in strategies:
                target_strategy = None
                for strategy in buy_strategies:
                    if strategy.get('strategy_name') == strategy_name:
                        target_strategy = strategy
                        break

                if target_strategy is None:
                    missing_strategies.append(strategy_name)
                else:
                    found_strategies.append(target_strategy)

            if missing_strategies:
                raise ConfigNotFoundError(f"策略不存在: {', '.join(missing_strategies)}")

            # 生成合并的回测配置（使用第一个策略的配置作为基础）
            if found_strategies:
                base_strategy = found_strategies[0]
                backtest_config = self._generate_backtest_config(base_strategy, target_date)

                # 添加策略列表信息
                backtest_config['target_strategies'] = strategies
            else:
                raise ConfigNotFoundError("没有找到任何有效策略")

            # 创建临时文件
            temp_file_path = self.create_temp_config_file(backtest_config)

            self.logger.info(f"成功读取多策略配置 {strategies}，临时文件: {temp_file_path}")
            return backtest_config, temp_file_path

        except (ConfigNotFoundError, ConfigInvalidError):
            raise
        except Exception as e:
            self.logger.error(f"读取多策略配置失败: {e}")
            raise ConfigInvalidError(f"读取多策略配置时发生错误: {e}")

    async def get_all_enabled_strategies(self) -> List[str]:
        """
        获取所有已启用的策略名称

        Returns:
            List[str]: 已启用的策略名称列表

        Raises:
            ConfigNotFoundError: 配置不存在
            ConfigInvalidError: 配置格式无效
        """
        try:
            # 从数据库读取配置
            config_record = await self.config_dao.get_config("kol_activity")

            if config_record is None:
                raise ConfigNotFoundError("kol_activity 配置不存在")

            # 验证配置格式
            if not hasattr(config_record, 'data'):
                raise ConfigInvalidError("配置格式无效：缺少 data 字段")

            if isinstance(config_record.data, str):
                raise ConfigInvalidError("配置格式无效：data 字段不能是字符串")

            # 将配置数据转换为字典格式
            config_data = self._convert_to_dict(config_record.data)

            # 获取所有策略名称
            buy_strategies = config_data.get('buy_strategies', [])
            enabled_strategies = []

            for strategy in buy_strategies:
                strategy_name = strategy.get('strategy_name')
                if strategy_name:
                    enabled_strategies.append(strategy_name)

            self.logger.info(f"找到 {len(enabled_strategies)} 个已启用策略: {enabled_strategies}")
            return enabled_strategies

        except (ConfigNotFoundError, ConfigInvalidError):
            raise
        except Exception as e:
            self.logger.error(f"获取已启用策略失败: {e}")
            raise ConfigInvalidError(f"获取已启用策略时发生错误: {e}")

    async def read_admin_telegram_config(self) -> List[str]:
        """
        读取管理员 Telegram 配置
        
        Returns:
            List[str]: 管理员 Chat ID 列表
        """
        try:
            config_record = await self.config_dao.get_config("application_config")
            
            if config_record is None:
                self.logger.warning("application_config 配置不存在，返回空列表")
                return []
            
            # 转换配置数据为字典
            config_data = self._convert_to_dict(config_record.data)
            chat_ids = config_data.get("admin_telegram_chat_ids", [])
            
            self.logger.info(f"成功读取管理员 Telegram 配置，Chat ID 数量: {len(chat_ids)}")
            return chat_ids
            
        except Exception as e:
            self.logger.error(f"读取管理员 Telegram 配置失败: {e}")
            return []
    
    def _convert_to_dict(self, data: Any) -> Dict[str, Any]:
        """
        将配置数据转换为字典格式
        
        Args:
            data: 配置数据对象
            
        Returns:
            Dict[str, Any]: 字典格式的配置数据
            
        Raises:
            ConfigInvalidError: 配置格式无效
        """
        try:
            # 如果是 Pydantic 模型，使用 model_dump
            if hasattr(data, 'model_dump'):
                return data.model_dump()
            # 如果是旧版 Pydantic 模型，使用 dict()
            elif hasattr(data, 'dict'):
                return data.dict()
            # 如果已经是字典类型，直接返回
            elif isinstance(data, dict):
                return data
            # 其他情况抛出异常
            else:
                raise ConfigInvalidError(f"配置格式无效：无法转换为字典格式 - 数据类型: {type(data)}")
        except Exception as e:
            raise ConfigInvalidError(f"配置格式无效：无法转换为字典格式 - {e}")
    
    def create_temp_config_file(self, config_data: Dict[str, Any]) -> str:
        """
        创建临时配置文件
        
        Args:
            config_data: 配置数据
            
        Returns:
            str: 临时文件路径
            
        Raises:
            Exception: 文件创建失败
        """
        try:
            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(
                mode='w',
                suffix='.json',
                prefix='backtest_config_',
                delete=False,
                encoding='utf-8'
            )
            
            # 写入配置数据
            json.dump(config_data, temp_file, ensure_ascii=False, indent=2)
            temp_file.flush()
            temp_file.close()
            
            self.logger.debug(f"创建临时配置文件: {temp_file.name}")
            return temp_file.name
            
        except Exception as e:
            self.logger.error(f"创建临时配置文件失败: {e}")
            raise Exception(f"临时文件创建失败: {e}")
    
    def cleanup_temp_file(self, file_path: str) -> None:
        """
        清理临时文件

        Args:
            file_path: 文件路径
        """
        try:
            if file_path and os.path.exists(file_path):
                os.unlink(file_path)
                self.logger.debug(f"清理临时文件: {file_path}")
        except Exception as e:
            self.logger.warning(f"清理临时文件失败: {file_path}, 错误: {e}")
            # 静默处理，不抛出异常

    def _generate_backtest_config(self, strategy_config: Dict[str, Any], target_date: str) -> Dict[str, Any]:
        """
        根据策略配置生成回测配置

        Args:
            strategy_config: 策略配置
            target_date: 目标日期 (YYYY-MM-DD格式)

        Returns:
            Dict[str, Any]: 回测配置
        """
        from datetime import datetime, timedelta

        # 解析目标日期
        target_datetime = datetime.strptime(target_date, '%Y-%m-%d')
        start_timestamp = int(target_datetime.timestamp())
        end_timestamp = int((target_datetime + timedelta(days=1) - timedelta(seconds=1)).timestamp())

        # 生成回测配置
        backtest_config = {
            "transaction_lookback_hours": strategy_config.get("transaction_lookback_hours", 6),
            "transaction_min_amount": strategy_config.get("transaction_min_amount", 500),
            "kol_account_min_count": strategy_config.get("kol_account_min_count", 6),
            "token_mint_lookback_hours": strategy_config.get("token_mint_lookback_hours", 72),
            "kol_account_min_txs": strategy_config.get("kol_account_min_txs", 10),
            "kol_account_max_txs": strategy_config.get("kol_account_max_txs", 500),
            "backtest_start_time": start_timestamp,
            "backtest_end_time": end_timestamp,
            "sell_strategy_hours": strategy_config.get("sell_strategy_hours", 24),
            "sell_kol_ratio": strategy_config.get("sell_kol_ratio", 0.5),
            "same_token_notification_interval_minutes": strategy_config.get("same_token_notification_interval", 60),
            "processing_interval": 60
        }

        self.logger.info(f"生成回测配置: 时间范围 {start_timestamp} - {end_timestamp}, "
                        f"卖出策略: {backtest_config['sell_strategy_hours']}小时")

        return backtest_config