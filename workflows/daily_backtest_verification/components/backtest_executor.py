"""
回测命令执行器组件

负责执行回测命令并处理结果：
- 构建和执行回测命令
- 监控执行过程和超时控制
- 解析执行结果和错误处理
- 支持异步执行和并发控制

技术实现方案：@daily_backtest_verification_dev_plan_ai.md
"""

import asyncio
import subprocess
import time
import os
import re
import logging
from datetime import datetime, date
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime


@dataclass
class BacktestResult:
    """回测执行结果"""
    success: bool
    output: str
    error: str
    execution_time: float
    result_file_path: Optional[str] = None


class BacktestExecutionError(Exception):
    """回测执行异常"""
    pass


class BacktestTimeoutError(Exception):
    """回测执行超时异常"""
    pass


class BacktestExecutor:
    """
    回测命令执行器
    
    负责执行回测命令并处理执行结果。
    支持超时控制、错误处理和结果解析。
    """
    
    def __init__(self, default_timeout: float = 600.0):
        """
        初始化回测执行器
        
        Args:
            default_timeout: 默认超时时间（秒），默认10分钟
        """
        self.default_timeout = default_timeout
        self.logger = logging.getLogger(__name__)
    
    async def execute_backtest(self, config_file_path: str, timeout: Optional[float] = None, target_date: Optional[date] = None) -> BacktestResult:
        """
        执行回测命令
        
        Args:
            config_file_path: 配置文件路径
            timeout: 超时时间（秒），如果为None则使用默认超时时间
            
        Returns:
            BacktestResult: 执行结果
            
        Raises:
            BacktestExecutionError: 执行异常
            BacktestTimeoutError: 执行超时异常
        """
        # 验证配置文件存在
        if not os.path.exists(config_file_path):
            raise BacktestExecutionError(f"配置文件不存在: {config_file_path}")
        
        # 使用指定超时时间或默认超时时间
        actual_timeout = timeout if timeout is not None else self.default_timeout
        
        # 构建命令
        command = self._build_command(config_file_path, target_date)
        
        # 记录开始时间
        start_time = time.time()
        
        try:
            # 执行命令
            process_result = await self._execute_command(command, actual_timeout)
            
            # 计算执行时间
            execution_time = time.time() - start_time
            
            # 解析结果
            return self._parse_result(process_result, execution_time)
            
        except subprocess.TimeoutExpired as e:
            self.logger.error(f"回测执行超时: {e}")
            raise BacktestTimeoutError(f"回测执行超时: {actual_timeout}秒")
            
        except PermissionError as e:
            self.logger.error(f"权限错误: {e}")
            raise BacktestExecutionError(f"权限错误: {str(e)}")
            
        except subprocess.CalledProcessError as e:
            # 直接从subprocess.run抛出的CalledProcessError应该转换为BacktestExecutionError
            self.logger.error(f"回测命令执行失败: {e}")
            raise BacktestExecutionError(f"回测命令执行失败: {str(e)}")
            
        except Exception as e:
            self.logger.error(f"回测执行异常: {e}")
            raise BacktestExecutionError(f"回测执行异常: {str(e)}")
    
    def _build_command(self, config_file_path: str, target_date: Optional[date] = None) -> List[str]:
        """
        构建回测命令
        
        Args:
            config_file_path: 配置文件路径
            target_date: 目标日期，如果提供则设置为回测时间范围
            
        Returns:
            List[str]: 命令参数列表
        """
        command = [
            "python",
            "run_backtest_ed.py",
            "--mode",
            "single_v2",
            "--config",
            config_file_path
        ]
        
        # 如果提供了目标日期，计算回测时间范围
        if target_date:
            # 将目标日期转换为UTC时间戳
            # 回测时间范围：目标日期00:00:00 到 23:59:59
            start_datetime = datetime.combine(target_date, datetime.min.time())
            end_datetime = datetime.combine(target_date, datetime.max.time())
            
            # 转换为UTC时间戳
            start_timestamp = int(start_datetime.timestamp())
            end_timestamp = int(end_datetime.timestamp())
            
            command.extend([
                "--start_time", str(start_timestamp),
                "--end_time", str(end_timestamp)
            ])
            
            self.logger.info(f"设置回测时间范围: {start_datetime} 到 {end_datetime} (时间戳: {start_timestamp} - {end_timestamp})")
        
        return command
    
    async def _execute_command(self, command: List[str], timeout: float) -> subprocess.CompletedProcess:
        """
        异步执行命令
        
        Args:
            command: 命令参数列表
            timeout: 超时时间（秒）
            
        Returns:
            subprocess.CompletedProcess: 执行结果
            
        Raises:
            subprocess.TimeoutExpired: 执行超时
            subprocess.CalledProcessError: 执行失败（由subprocess.run直接抛出）
        """
        # 在事件循环中运行同步的subprocess.run
        loop = asyncio.get_event_loop()
        
        # 使用run_in_executor在线程池中执行
        def run_subprocess():
            return subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=timeout,
                check=False  # 不使用check=True，让调用者处理返回码
            )
        
        # 执行命令，让任何直接抛出的异常传播上去
        result = await loop.run_in_executor(None, run_subprocess)
        return result
    
    def _parse_result(self, process_result: subprocess.CompletedProcess, execution_time: float) -> BacktestResult:
        """
        解析执行结果
        
        Args:
            process_result: 进程执行结果
            execution_time: 执行时间
            
        Returns:
            BacktestResult: 解析后的结果
        """
        # 检查执行是否成功
        success = process_result.returncode == 0
        
        # 获取输出和错误信息
        output = process_result.stdout or ""
        error = process_result.stderr or ""
        
        # 解析结果文件路径（仅在成功时）
        result_file_path = None
        if success:
            # 使用执行开始时间查找结果文件
            execution_start_time = time.time() - execution_time
            result_file_path = self._find_result_file_path(execution_start_time)
        
        return BacktestResult(
            success=success,
            output=output,
            error=error,
            execution_time=execution_time,
            result_file_path=result_file_path
        )
    
    def _find_result_file_path(self, execution_start_time: float) -> Optional[str]:
        """
        基于约定和时间戳查找结果文件路径
        
        这种方法比解析日志输出更可靠，因为它基于回测脚本的固定文件命名约定。
        
        Args:
            execution_start_time: 回测开始执行的时间戳
            
        Returns:
            Optional[str]: 结果文件路径，如果未找到则返回None
        """
        # 回测结果目录
        result_base_dir = "backtest_result"
        
        if not os.path.exists(result_base_dir):
            self.logger.warning(f"回测结果基础目录不存在: {result_base_dir}")
            return None
        
        # 根据回测脚本逻辑，V2回测会创建形如 "v2_backtest_results_YYYYMMDD_HHMMSS" 的目录
        # 我们查找在执行时间附近创建的目录
        
        execution_time = datetime.fromtimestamp(execution_start_time)
        
        # 查找所有可能的结果目录（允许一定的时间误差）
        potential_dirs = []
        
        try:
            for item in os.listdir(result_base_dir):
                item_path = os.path.join(result_base_dir, item)
                if os.path.isdir(item_path) and item.startswith("v2_backtest_results_"):
                    # 提取时间戳部分
                    timestamp_part = item.replace("v2_backtest_results_", "")
                    try:
                        # 解析目录名中的时间戳
                        dir_time = datetime.strptime(timestamp_part, "%Y%m%d_%H%M%S")
                        
                        # 检查时间差是否在合理范围内（5分钟内）
                        time_diff = abs((dir_time - execution_time).total_seconds())
                        if time_diff <= 300:  # 5分钟内
                            potential_dirs.append((item_path, time_diff))
                    except ValueError:
                        # 时间戳格式不匹配，跳过
                        continue
            
            # 按时间差排序，选择最接近的
            if potential_dirs:
                potential_dirs.sort(key=lambda x: x[1])
                best_dir = potential_dirs[0][0]
                
                # 检查results.json文件是否存在
                result_file = os.path.join(best_dir, "results.json")
                if os.path.exists(result_file):
                    self.logger.info(f"找到结果文件: {result_file}")
                    return result_file
                else:
                    self.logger.warning(f"结果目录存在但results.json文件不存在: {best_dir}")
            
        except Exception as e:
            self.logger.error(f"查找结果文件时发生异常: {e}")
        
        # 如果基于时间戳的查找失败，尝试查找最新的结果目录
        return self._find_latest_result_file(result_base_dir)
    
    def _find_latest_result_file(self, result_base_dir: str) -> Optional[str]:
        """
        查找最新的回测结果文件（备用方案）
        
        Args:
            result_base_dir: 结果基础目录
            
        Returns:
            Optional[str]: 最新的结果文件路径
        """
        try:
            latest_dir = None
            latest_time = 0
            
            for item in os.listdir(result_base_dir):
                item_path = os.path.join(result_base_dir, item)
                if os.path.isdir(item_path) and item.startswith("v2_backtest_results_"):
                    # 获取目录的修改时间
                    mtime = os.path.getmtime(item_path)
                    if mtime > latest_time:
                        latest_time = mtime
                        latest_dir = item_path
            
            if latest_dir:
                result_file = os.path.join(latest_dir, "results.json")
                if os.path.exists(result_file):
                    self.logger.info(f"找到最新的结果文件: {result_file}")
                    return result_file
            
        except Exception as e:
            self.logger.error(f"查找最新结果文件时发生异常: {e}")
        
        return None