#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略选择器 (Strategy Selector)

负责策略名称验证和策略配置管理：
- 验证策略名称的有效性
- 提供策略配置信息
- 强制单策略模式下指定策略名称
- 移除默认策略选项，提升系统安全性

技术实现方案：@daily_backtest_verification_dev_plan_ai.md
"""

import logging
from typing import Dict, Any, Optional


class InvalidStrategyError(Exception):
    """无效策略异常"""
    pass


class MissingStrategyError(Exception):
    """缺失策略异常"""
    pass


class StrategyNotFoundError(Exception):
    """策略在配置中不存在异常"""
    pass


class StrategyDisabledError(Exception):
    """策略未启用异常"""
    pass


class StrategySelector:
    """
    策略选择器
    
    提供策略名称验证和配置管理功能。
    移除了默认策略选项，强制用户在单策略模式下明确指定策略名称。
    支持检查策略在配置中的存在性和启用状态。
    """
    
    # 支持的策略列表
    VALID_STRATEGIES = {
        'kol_strategy_v1': {
            'description': 'KOL策略版本1 - 基础版本',
            'parameters': {
                'min_score': 0.6,
                'max_positions': 10,
                'risk_level': 'medium'
            }
        },
        'kol_strategy_v2': {
            'description': 'KOL策略版本2 - 优化版本',
            'parameters': {
                'min_score': 0.7,
                'max_positions': 8,
                'risk_level': 'medium',
                'use_dynamic_scoring': True
            }
        },
        'kol_strategy_v3': {
            'description': 'KOL策略版本3 - 高级版本',
            'parameters': {
                'min_score': 0.75,
                'max_positions': 6,
                'risk_level': 'low',
                'use_dynamic_scoring': True,
                'enable_risk_management': True
            }
        },
        'kol_strategy_v4': {
            'description': 'KOL策略版本4 - 实验版本',
            'parameters': {
                'min_score': 0.8,
                'max_positions': 5,
                'risk_level': 'low',
                'use_dynamic_scoring': True,
                'enable_risk_management': True,
                'experimental_features': True
            }
        },
        'kol_strategy_v5': {
            'description': 'KOL策略版本5 - 最新版本',
            'parameters': {
                'min_score': 0.85,
                'max_positions': 4,
                'risk_level': 'very_low',
                'use_dynamic_scoring': True,
                'enable_risk_management': True,
                'advanced_analytics': True
            }
        }
    }
    
    def __init__(self):
        """初始化策略选择器"""
        self.logger = logging.getLogger(__name__)
        self.logger.info("策略选择器已初始化，支持策略: %s", list(self.VALID_STRATEGIES.keys()))
    
    def validate_strategy_name(self, strategy_name: Optional[str]) -> None:
        """
        验证策略名称的有效性
        
        Args:
            strategy_name: 策略名称
            
        Raises:
            MissingStrategyError: 策略名称为空或None
        """
        # 检查是否为None
        if strategy_name is None:
            raise MissingStrategyError("策略名称不能为空，请指定有效的策略名称")
        
        # 检查是否为仅包含空白字符
        if strategy_name and not strategy_name.strip():
            raise MissingStrategyError("策略名称不能为空，请指定有效的策略名称")
        
        # 检查是否为空字符串（作为无效策略处理）
        if strategy_name == "":
            raise MissingStrategyError("策略名称不能为空，请指定有效的策略名称")
        
        # 移除硬编码的策略名称限制，允许任何非空的策略名称
        # 这样可以支持动态策略名称，不再局限于预定义的策略列表
        
        self.logger.debug("策略名称验证通过: %s", strategy_name)
    
    def validate_strategy_in_config(self, strategy_name: str, config_data: Dict[str, Any]) -> None:
        """
        验证策略在配置中的存在性和启用状态
        
        Args:
            strategy_name: 策略名称
            config_data: 配置数据
            
        Raises:
            StrategyNotFoundError: 策略在配置中不存在
            StrategyDisabledError: 策略存在但未启用
        """
        # 支持两种配置格式：
        # 1. 旧格式：strategies 字典
        # 2. 新格式：buy_strategies 数组
        
        # 检查新格式：buy_strategies 数组
        buy_strategies = config_data.get('buy_strategies', [])
        if buy_strategies:
            # 在 buy_strategies 数组中查找策略
            strategy_found = False
            strategy_enabled = False
            
            for strategy_config in buy_strategies:
                if strategy_config.get('strategy_name') == strategy_name:
                    strategy_found = True
                    strategy_enabled = strategy_config.get('is_active', False)
                    break
            
            if not strategy_found:
                raise StrategyNotFoundError(f"策略 '{strategy_name}' 在配置中不存在")
            
            if not strategy_enabled:
                raise StrategyDisabledError(f"策略 '{strategy_name}' 存在但未启用")
        else:
            # 检查旧格式：strategies 字典
            strategies_config = config_data.get('strategies', {})
            if strategy_name not in strategies_config:
                raise StrategyNotFoundError(f"策略 '{strategy_name}' 在配置中不存在")
            
            # 检查策略是否启用
            strategy_config = strategies_config[strategy_name]
            if not strategy_config.get('enabled', False):
                raise StrategyDisabledError(f"策略 '{strategy_name}' 存在但未启用")
        
        self.logger.debug(f"策略配置验证通过: {strategy_name}")
    
    def get_enabled_strategies_from_config(self, config_data: Dict[str, Any]) -> list[str]:
        """
        从配置中获取所有启用的策略名称
        
        Args:
            config_data: 配置数据
            
        Returns:
            list[str]: 启用的策略名称列表
        """
        enabled_strategies = []
        
        # 支持两种配置格式：
        # 1. 旧格式：strategies 字典
        # 2. 新格式：buy_strategies 数组
        
        # 检查新格式：buy_strategies 数组
        buy_strategies = config_data.get('buy_strategies', [])
        if buy_strategies:
            for strategy_config in buy_strategies:
                strategy_name = strategy_config.get('strategy_name')
                is_active = strategy_config.get('is_active', False)
                if strategy_name and is_active:
                    enabled_strategies.append(strategy_name)
        else:
            # 检查旧格式：strategies 字典
            strategies_config = config_data.get('strategies', {})
            for strategy_name, strategy_config in strategies_config.items():
                if strategy_config.get('enabled', False):
                    enabled_strategies.append(strategy_name)
        
        return enabled_strategies
    
    def get_strategy_config(self, strategy_name: Optional[str]) -> Dict[str, Any]:
        """
        获取策略配置信息
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            Dict[str, Any]: 策略配置字典
            
        Raises:
            MissingStrategyError: 策略名称为空或None
        """
        # 首先验证策略名称
        self.validate_strategy_name(strategy_name)
        
        # 获取策略配置
        if strategy_name in self.VALID_STRATEGIES:
            # 如果是预定义策略，使用预定义配置
            config = self.VALID_STRATEGIES[strategy_name].copy()
        else:
            # 如果是自定义策略名称，返回默认配置结构
            config = {
                'description': f'自定义策略: {strategy_name}',
                'parameters': {
                    'min_score': 0.6,
                    'max_positions': 10,
                    'risk_level': 'medium'
                }
            }
        
        config['strategy_name'] = strategy_name
        
        self.logger.debug("获取策略配置: %s", strategy_name)
        return config
    
    def get_available_strategies(self) -> list[str]:
        """
        获取所有可用的策略名称列表
        
        Returns:
            list[str]: 可用策略名称列表
        """
        return list(self.VALID_STRATEGIES.keys())
    
    def is_strategy_valid(self, strategy_name: Optional[str]) -> bool:
        """
        检查策略名称是否有效（不抛出异常的版本）
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            bool: 策略名称是否有效
        """
        try:
            self.validate_strategy_name(strategy_name)
            return True
        except MissingStrategyError:
            return False