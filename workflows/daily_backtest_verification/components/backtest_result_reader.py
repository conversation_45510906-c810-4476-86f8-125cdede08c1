"""
回测结果读取器

读取和解析回测结果文件，提取交易信息和统计数据。
支持JSON格式的回测结果文件解析，提供高性能和健壮的数据处理能力。

实现文档参考：
- @daily_backtest_verification_dev_plan_ai.md
- @daily_backtest_verification_test_cases_ai.md

主要功能：
1. 读取JSON格式的回测结果文件
2. 验证数据格式和完整性  
3. 解析交易信息和统计数据
4. 支持多种时间戳格式
5. 提供详细的错误信息和异常处理
"""

import json
import os
from datetime import datetime, timezone
from dataclasses import dataclass
from typing import List, Dict, Any, Optional, Union
import logging
from pathlib import Path

# 设置日志
logger = logging.getLogger(__name__)


@dataclass
class BacktestTradeInfo:
    """
    回测交易信息数据类
    
    Attributes:
        symbol: 交易符号，如 "BTC/USDT"
        timestamp: 交易时间戳
        action: 交易动作，"buy" 或 "sell"
        amount: 交易数量
        price: 交易价格
    """
    symbol: str
    timestamp: datetime
    action: str
    amount: float
    price: float

    def __post_init__(self):
        """数据验证"""
        if self.action not in ["buy", "sell"]:
            raise ValueError(f"无效的交易动作: {self.action}")
        if self.amount <= 0:
            raise ValueError(f"交易数量必须大于0: {self.amount}")
        if self.price <= 0:
            raise ValueError(f"交易价格必须大于0: {self.price}")


@dataclass
class BacktestResultData:
    """
    回测结果数据类
    
    Attributes:
        trades: 交易信息列表
        total_trades: 总交易数量
        start_time: 回测开始时间
        end_time: 回测结束时间
    """
    trades: List[BacktestTradeInfo]
    total_trades: int
    start_time: datetime
    end_time: datetime

    def __post_init__(self):
        """数据一致性验证"""
        if len(self.trades) != self.total_trades:
            logger.warning(f"交易列表长度({len(self.trades)})与总数({self.total_trades})不一致")
        
        if self.start_time and self.end_time and self.start_time > self.end_time:
            raise ValueError("开始时间不能晚于结束时间")


class ResultFileNotFoundError(Exception):
    """结果文件不存在异常"""
    pass


class ResultFileInvalidError(Exception):
    """结果文件格式无效异常"""
    pass


class BacktestResultReader:
    """
    回测结果读取器
    
    负责读取和解析回测结果文件，提取交易信息和统计数据。
    支持JSON格式的回测结果文件，提供高性能的数据处理能力。
    
    特性：
    - 支持多种时间戳格式自动识别
    - 完整的数据验证和错误处理
    - 性能优化的大文件处理
    - 详细的日志记录和错误信息
    """
    
    # 支持的时间戳格式列表，按使用频率排序
    TIMESTAMP_FORMATS = [
        "%Y-%m-%dT%H:%M:%SZ",           # 最常用的ISO格式
        "%Y-%m-%dT%H:%M:%S.%fZ",        # 带毫秒的ISO格式
        "%Y-%m-%dT%H:%M:%S%z",          # 带时区的ISO格式
        "%Y-%m-%dT%H:%M:%S.%f%z",       # 带毫秒和时区的ISO格式
        "%Y-%m-%dT%H:%M:%S",            # 简化的ISO格式
        "%Y-%m-%dT%H:%M:%S.%f"          # 带毫秒的简化格式
    ]
    
    def __init__(self, max_file_size_mb: int = 100):
        """
        初始化回测结果读取器
        
        Args:
            max_file_size_mb: 最大文件大小限制（MB），默认100MB
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.max_file_size = max_file_size_mb * 1024 * 1024  # 转换为字节
    
    async def read_result_file(self, file_path: str) -> BacktestResultData:
        """
        读取回测结果文件
        
        Args:
            file_path: 结果文件路径
            
        Returns:
            BacktestResultData: 解析后的回测结果数据
            
        Raises:
            ResultFileNotFoundError: 文件不存在
            ResultFileInvalidError: 文件格式无效或文件过大
        """
        file_path_obj = Path(file_path)
        
        # 检查文件是否存在
        if not file_path_obj.exists():
            raise ResultFileNotFoundError(f"回测结果文件不存在: {file_path}")
        
        # 检查文件大小
        file_size = file_path_obj.stat().st_size
        if file_size > self.max_file_size:
            raise ResultFileInvalidError(
                f"文件大小({file_size / 1024 / 1024:.2f}MB)超过限制"
                f"({self.max_file_size / 1024 / 1024}MB)"
            )
        
        self.logger.info(f"开始读取回测结果文件: {file_path} (大小: {file_size / 1024:.2f}KB)")
        
        try:
            # 读取JSON文件
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 验证必要字段
            self._validate_result_data(data)
            
            # 解析交易数据
            trades = self._extract_trades_info(data.get("trades", []))
            
            # 提取统计数据 - 适配实际的results.json格式
            statistics = data.get("statistics", {})
            total_trades = statistics.get("total_trades", len(trades))
            start_time = self._parse_timestamp(statistics.get("backtest_start_time"))
            end_time = self._parse_timestamp(statistics.get("backtest_end_time"))
            
            result = BacktestResultData(
                trades=trades,
                total_trades=total_trades,
                start_time=start_time,
                end_time=end_time
            )
            
            self.logger.info(f"成功解析回测结果: {len(trades)}个交易记录")
            return result
            
        except json.JSONDecodeError as e:
            error_msg = f"回测结果文件格式无效: JSON解析错误 - {str(e)}"
            self.logger.error(error_msg)
            raise ResultFileInvalidError(error_msg)
        except (KeyError, ValueError, TypeError) as e:
            error_msg = f"回测结果文件格式无效: {str(e)}"
            self.logger.error(error_msg)
            raise ResultFileInvalidError(error_msg)
        except Exception as e:
            error_msg = f"读取回测结果文件时发生未知错误: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            raise ResultFileInvalidError(error_msg)
    
    def _validate_result_data(self, data: Dict[str, Any]) -> None:
        """
        验证结果数据格式
        
        Args:
            data: 结果数据字典
            
        Raises:
            ResultFileInvalidError: 数据格式无效
        """
        # 检查顶层结构
        if not isinstance(data, dict):
            raise ResultFileInvalidError("回测结果文件必须是JSON对象")
        
        # 检查必要字段 - 适配实际的results.json格式
        required_fields = ["trades", "statistics"]
        for field in required_fields:
            if field not in data:
                raise ResultFileInvalidError(f"缺少必要字段: {field}")
        
        # 验证trades字段
        if not isinstance(data["trades"], list):
            raise ResultFileInvalidError("trades字段必须是数组")
        
        # 验证statistics字段（实际文件使用statistics而不是summary）
        if not isinstance(data["statistics"], dict):
            raise ResultFileInvalidError("statistics字段必须是对象")
        
        # 批量验证交易数据
        trades = data["trades"]
        if trades:  # 只在有交易数据时进行验证
            self._batch_validate_trades(trades)
    
    def _batch_validate_trades(self, trades: List[Dict[str, Any]]) -> None:
        """
        批量验证交易数据，提高性能
        
        Args:
            trades: 交易数据列表
            
        Raises:
            ResultFileInvalidError: 交易数据格式无效
        """
        # 适配实际的交易数据字段：使用'token_symbol'而不是'symbol'，'type'而不是'action'
        required_fields = {"token_symbol", "timestamp", "type", "amount", "price"}
        
        for i, trade in enumerate(trades):
            try:
                # 检查字段完整性
                if not isinstance(trade, dict):
                    raise ValueError("交易记录必须是对象")
                
                missing_fields = required_fields - trade.keys()
                if missing_fields:
                    raise ValueError(f"缺少必要字段: {', '.join(missing_fields)}")
                
                # 验证数据类型和值
                self._validate_trade_values(trade)
                
            except Exception as e:
                raise ResultFileInvalidError(f"交易数据格式无效: 第{i+1}个交易记录 - {str(e)}")
    
    def _validate_trade_values(self, trade: Dict[str, Any]) -> None:
        """
        验证单个交易的数值和格式
        
        Args:
            trade: 交易数据字典
            
        Raises:
            ValueError: 数据格式无效
        """
        # 验证交易动作 - 适配实际的'type'字段和'BUY'/'SELL'值
        if trade["type"] not in ["BUY", "SELL"]:
            raise ValueError(f"交易类型必须是'BUY'或'SELL': {trade['type']}")
        
        # 验证时间戳格式
        try:
            self._parse_timestamp(trade["timestamp"])
        except Exception:
            raise ValueError(f"时间戳格式无效: {trade['timestamp']}")
        
        # 验证数值字段
        try:
            amount = float(trade["amount"])
            price = float(trade["price"])
            
            if amount <= 0:
                raise ValueError(f"交易数量必须大于0: {amount}")
            if price <= 0:
                raise ValueError(f"交易价格必须大于0: {price}")
                
        except (ValueError, TypeError) as e:
            raise ValueError(f"数值字段格式无效: {str(e)}")
    
    def _extract_trades_info(self, trades_data: List[Dict[str, Any]]) -> List[BacktestTradeInfo]:
        """
        提取交易信息，使用优化的批处理方式
        
        Args:
            trades_data: 交易数据列表
            
        Returns:
            List[BacktestTradeInfo]: 交易信息列表
        """
        if not trades_data:
            return []
        
        trades = []
        for trade_data in trades_data:
            trade = self._parse_trade_data(trade_data)
            trades.append(trade)
        
        return trades
    
    def _parse_trade_data(self, trade_data: Dict[str, Any]) -> BacktestTradeInfo:
        """
        解析单个交易数据
        
        Args:
            trade_data: 交易数据字典
            
        Returns:
            BacktestTradeInfo: 交易信息对象
        """
        # 将'BUY'/'SELL'转换为'buy'/'sell'
        action = str(trade_data["type"]).lower().strip()
        
        # 调试信息：打印timestamp字段的值和类型
        timestamp_value = trade_data.get("timestamp")
        logger.debug(f"解析交易数据 - timestamp值: {timestamp_value}, 类型: {type(timestamp_value)}, 交易数据: {trade_data}")
        
        return BacktestTradeInfo(
            symbol=str(trade_data["token_symbol"]).strip(),
            timestamp=self._parse_timestamp(timestamp_value),
            action=action,
            amount=float(trade_data["amount"]),
            price=float(trade_data["price"])
        )
    
    def _parse_timestamp(self, timestamp_value: Union[str, int, float, None]) -> datetime:
        """
        解析时间戳，支持多种格式
        
        Args:
            timestamp_value: 时间戳值，可以是字符串、整数或浮点数
            
        Returns:
            datetime: 解析后的时间对象
            
        Raises:
            ValueError: 时间戳格式无效
        """
        if timestamp_value is None:
            raise ValueError("时间戳不能为空")
        
        # 如果是数字类型（Unix时间戳）
        if isinstance(timestamp_value, (int, float)):
            try:
                return datetime.fromtimestamp(timestamp_value, tz=timezone.utc)
            except (ValueError, OSError) as e:
                raise ValueError(f"无法解析Unix时间戳: {timestamp_value}, 错误: {e}")
        
        # 如果是字符串类型
        if isinstance(timestamp_value, str):
            timestamp_str = timestamp_value.strip()
            if not timestamp_str:
                raise ValueError("时间戳不能为空")
            
            # 尝试解析多种字符串格式
            for fmt in self.TIMESTAMP_FORMATS:
                try:
                    return datetime.strptime(timestamp_str, fmt)
                except ValueError:
                    continue
            
            # 如果所有格式都失败，提供详细的错误信息
            raise ValueError(
                f"无法解析时间戳格式: {timestamp_str}. "
                f"支持的格式示例: '2025-01-27T10:00:00Z', '2025-01-27T10:00:00.123Z'"
            )
        
        # 不支持的类型
        raise ValueError(f"不支持的时间戳类型: {type(timestamp_value)}, 值: {timestamp_value}")