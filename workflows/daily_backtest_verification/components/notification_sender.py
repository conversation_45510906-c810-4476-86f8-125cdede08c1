import logging
from typing import List, Tuple
from utils.message_sender.message_sender import TelegramMessageSender


class NotificationSender:
    """
    通知发送器
    
    负责向管理员发送校验报告和错误通知。
    支持多个管理员Chat ID的批量发送。
    
    实现方案:
    - @daily_backtest_verification_dev_plan_ai.md
    """
    
    # 常量定义
    ERROR_MESSAGE_PREFIX = "🚨 系统错误"
    EMPTY_LIST_MESSAGE = "Chat ID列表为空，跳过发送"
    SUCCESS_LOG_TEMPLATE = "{message_type}发送成功到Chat ID: {chat_id}"
    FAILURE_LOG_TEMPLATE = "{message_type}发送失败到Chat ID: {chat_id}"
    EXCEPTION_LOG_TEMPLATE = "发送{message_type}异常到Chat ID {chat_id}: {exception}"
    
    def __init__(self):
        """初始化通知发送器"""
        self.telegram_sender = TelegramMessageSender()
        self.logger = logging.getLogger(__name__)
    
    async def send_verification_report(self, 
                                     report_text: str, 
                                     admin_chat_ids: List[str]) -> bool:
        """
        发送校验报告
        
        Args:
            report_text: 报告文本
            admin_chat_ids: 管理员Chat ID列表
            
        Returns:
            bool: 是否全部发送成功
            
        Raises:
            无：内部异常已被捕获并记录
        """
        return await self._send_message_to_admins(
            message=report_text,
            admin_chat_ids=admin_chat_ids,
            message_type="校验报告"
        )
    
    async def send_error_notification(self, 
                                    error_message: str, 
                                    admin_chat_ids: List[str]) -> bool:
        """
        发送错误通知
        
        Args:
            error_message: 错误信息
            admin_chat_ids: 管理员Chat ID列表
            
        Returns:
            bool: 是否全部发送成功
            
        Raises:
            无：内部异常已被捕获并记录
        """
        # 格式化错误消息
        formatted_message = self._format_error_message(error_message)
        
        return await self._send_message_to_admins(
            message=formatted_message,
            admin_chat_ids=admin_chat_ids,
            message_type="错误通知"
        )
    
    def _format_error_message(self, error_message: str) -> str:
        """
        格式化错误消息
        
        Args:
            error_message: 原始错误信息
            
        Returns:
            str: 格式化后的错误消息
        """
        return f"{self.ERROR_MESSAGE_PREFIX}\n\n{error_message}"
    
    async def _send_message_to_admins(self, 
                              message: str, 
                              admin_chat_ids: List[str],
                              message_type: str) -> bool:
        """
        向管理员发送消息的通用方法
        
        Args:
            message: 要发送的消息内容
            admin_chat_ids: 管理员Chat ID列表
            message_type: 消息类型（用于日志）
            
        Returns:
            bool: 是否全部发送成功
        """
        if not admin_chat_ids:
            self.logger.info(f"{message_type} - {self.EMPTY_LIST_MESSAGE}")
            return True
        
        success_count, total_count = await self._batch_send_messages(
            message, admin_chat_ids, message_type
        )
        
        return self._log_and_return_result(
            success_count, total_count, message_type
        )
    
    async def _batch_send_messages(self, 
                                 message: str, 
                                 admin_chat_ids: List[str],
                                 message_type: str) -> Tuple[int, int]:
        """
        批量发送消息
        
        Args:
            message: 要发送的消息内容
            admin_chat_ids: 管理员Chat ID列表
            message_type: 消息类型（用于日志）
            
        Returns:
            Tuple[int, int]: (成功发送数量, 总发送数量)
        """
        success_count = 0
        total_count = len(admin_chat_ids)
        
        for chat_id in admin_chat_ids:
            try:
                result = await self._send_single_message(message, chat_id)
                if result:
                    success_count += 1
                    self.logger.info(self.SUCCESS_LOG_TEMPLATE.format(
                        message_type=message_type, chat_id=chat_id
                    ))
                else:
                    self.logger.warning(self.FAILURE_LOG_TEMPLATE.format(
                        message_type=message_type, chat_id=chat_id
                    ))
            except Exception as e:
                self.logger.error(self.EXCEPTION_LOG_TEMPLATE.format(
                    message_type=message_type, chat_id=chat_id, exception=e
                ))
                # 遇到异常立即返回失败
                return 0, total_count
        
        return success_count, total_count
    
    async def _send_single_message(self, message: str, chat_id: str) -> bool:
        """
        发送单条消息
        
        Args:
            message: 消息内容
            chat_id: Chat ID
            
        Returns:
            bool: 是否发送成功
            
        Raises:
            Exception: Telegram API异常
        """
        return await self.telegram_sender.send_message_to_user(
            message=message,
            user_id=chat_id
        )
    
    def _log_and_return_result(self, 
                             success_count: int, 
                             total_count: int,
                             message_type: str) -> bool:
        """
        记录发送结果并返回成功状态
        
        Args:
            success_count: 成功发送数量
            total_count: 总发送数量
            message_type: 消息类型（用于日志）
            
        Returns:
            bool: 是否全部发送成功
        """
        all_success = success_count == total_count
        
        if all_success:
            self.logger.info(f"{message_type}发送完成，成功发送到{success_count}个管理员")
        else:
            self.logger.warning(f"{message_type}发送部分失败，成功: {success_count}/{total_count}")
        
        return all_success 