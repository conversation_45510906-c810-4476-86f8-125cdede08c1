# 策略选择器 (Strategy Selector)

策略选择器是日常回测验证工作流中的核心组件，负责验证和管理交易策略配置。

## 功能概述

策略选择器提供以下主要功能：

1. **策略名称验证** - 验证用户输入的策略名称是否有效
2. **策略配置管理** - 获取和管理预定义的策略配置
3. **可用策略查询** - 列出所有可用的策略选项
4. **策略有效性检查** - 快速检查策略名称是否有效

## 核心类和异常

### StrategySelector 类

主要的策略选择器类，提供所有策略相关的操作。

#### 方法

- `validate_strategy_name(strategy_name: str)` - 验证策略名称
- `get_strategy_config(strategy_name: str) -> dict` - 获取策略配置
- `get_available_strategies() -> List[str]` - 获取可用策略列表
- `is_strategy_valid(strategy_name: str) -> bool` - 检查策略是否有效

### 异常类

- `InvalidStrategyError` - 当策略名称无效时抛出
- `MissingStrategyError` - 当策略名称缺失或为空时抛出

## 支持的策略

当前支持以下 KOL 策略：

1. **kol_strategy_v1** - 基础版本
   - 最小评分: 0.6
   - 最大持仓: 10
   - 风险等级: medium

2. **kol_strategy_v2** - 优化版本
   - 最小评分: 0.7
   - 最大持仓: 8
   - 风险等级: medium
   - 动态评分: 启用

3. **kol_strategy_v3** - 高级版本
   - 最小评分: 0.8
   - 最大持仓: 6
   - 风险等级: high
   - 动态评分: 启用
   - 风险管理: 启用

4. **kol_strategy_v4** - 专业版本
   - 最小评分: 0.75
   - 最大持仓: 12
   - 风险等级: medium
   - 动态评分: 启用
   - 风险管理: 启用
   - 机器学习: 启用

5. **kol_strategy_v5** - 实验版本
   - 最小评分: 0.85
   - 最大持仓: 5
   - 风险等级: high
   - 动态评分: 启用
   - 风险管理: 启用
   - 机器学习: 启用
   - 实验特性: 启用

## 使用示例

### 基本使用

```python
from workflows.daily_backtest_verification.components.strategy_selector import (
    StrategySelector,
    InvalidStrategyError,
    MissingStrategyError
)

# 创建策略选择器实例
selector = StrategySelector()

# 获取所有可用策略
available_strategies = selector.get_available_strategies()
print(f"可用策略: {available_strategies}")

# 验证策略名称
try:
    selector.validate_strategy_name("kol_strategy_v1")
    print("策略名称有效")
except InvalidStrategyError as e:
    print(f"无效策略: {e}")
except MissingStrategyError as e:
    print(f"策略名称缺失: {e}")

# 获取策略配置
config = selector.get_strategy_config("kol_strategy_v1")
print(f"策略配置: {config}")

# 检查策略有效性
is_valid = selector.is_strategy_valid("kol_strategy_v1")
print(f"策略有效: {is_valid}")
```

### 在验证流程中的集成

策略选择器已集成到 `DailyBacktestVerificationRunner` 中：

```python
from workflows.daily_backtest_verification.verification_runner import (
    DailyBacktestVerificationRunner
)

# 创建验证运行器（自动包含策略选择器）
runner = DailyBacktestVerificationRunner()

# 运行验证（包含策略验证步骤）
result = await runner.run_verification()
```

## 错误处理

策略选择器提供详细的错误信息：

### InvalidStrategyError

当提供的策略名称不在支持的策略列表中时抛出：

```
无效的策略名称: 'invalid_strategy'。有效的策略包括: kol_strategy_v1, kol_strategy_v2, kol_strategy_v3, kol_strategy_v4, kol_strategy_v5
```

### MissingStrategyError

当策略名称为空、None 或仅包含空白字符时抛出：

```
策略名称不能为空，请指定有效的策略名称
```

## 测试

策略选择器包含完整的测试套件：

### 单元测试

```bash
# 运行策略选择器单元测试
python3 -m pytest test/workflows/daily_backtest_verification/components/test_strategy_selector.py -v
```

### 集成测试

```bash
# 运行策略选择器集成测试
python3 -m pytest test/workflows/daily_backtest_verification/components/test_strategy_selector_integration.py -v
```

### 运行示例

```bash
# 运行使用示例
python3 workflows/daily_backtest_verification/examples/strategy_selector_usage.py
```

## 扩展策略

要添加新的策略，请在 `strategy_selector.py` 中的 `VALID_STRATEGIES` 字典中添加新条目：

```python
VALID_STRATEGIES = {
    # 现有策略...
    "new_strategy_v1": {
        "description": "新策略版本1 - 描述",
        "parameters": {
            "min_score": 0.6,
            "max_positions": 10,
            "risk_level": "medium"
        },
        "strategy_name": "new_strategy_v1"
    }
}
```

## 文件结构

```
workflows/daily_backtest_verification/components/
├── strategy_selector.py              # 策略选择器主文件
├── README_strategy_selector.md       # 本文档
└── examples/
    └── strategy_selector_usage.py    # 使用示例

test/workflows/daily_backtest_verification/components/
├── test_strategy_selector.py         # 单元测试
└── test_strategy_selector_integration.py  # 集成测试
```

## 注意事项

1. 策略名称区分大小写
2. 策略配置是只读的，不应在运行时修改
3. 所有策略验证都会在回测执行前进行
4. 无效的策略配置会导致整个验证流程失败
5. 建议在生产环境中使用经过充分测试的策略版本