#!/usr/bin/env python3
"""
每日回测校验功能 - 主入口脚本

这是每日回测校验功能的主要入口点，负责协调整个校验流程的执行。
该脚本整合了所有组件，提供了完整的校验功能。

使用方式:
    # 直接运行（使用昨天作为目标日期）
    python workflows/daily_backtest_verification/main.py
    
    # 指定日期运行
    python workflows/daily_backtest_verification/main.py --date 2025-01-12
    
    # 调试模式运行
    python workflows/daily_backtest_verification/main.py --debug

实现文档参考：
- @daily_backtest_verification_requirements_ai.md
- @daily_backtest_verification_dev_plan_ai.md
- @daily_backtest_verification_test_cases_ai.md
"""

import asyncio
import argparse
import logging
import sys
from datetime import datetime, date, timedelta
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入项目依赖
from dotenv import load_dotenv
from models import init_db
from workflows.daily_backtest_verification.components.verification_runner import DailyBacktestVerificationRunner

# 加载环境变量
load_dotenv()

def setup_logging(debug: bool = False) -> None:
    """
    设置日志配置
    
    Args:
        debug: 是否启用调试模式
    """
    log_level = logging.DEBUG if debug else logging.INFO
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 确保logs目录存在
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 配置日志记录器
    logging.basicConfig(
        level=log_level,
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('logs/daily_backtest_verification.log')
        ]
    )

def parse_arguments() -> argparse.Namespace:
    """
    解析命令行参数
    
    Returns:
        argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(
        description='每日回测校验工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""示例:
  %(prog)s                                    # 使用昨天作为目标日期（多策略模式）
  %(prog)s --yesterday                        # 明确使用昨天作为目标日期
  %(prog)s --date 2025-01-12                 # 校验指定日期（多策略模式）
  %(prog)s --strategy 胜率高                  # 校验单个策略
  %(prog)s --strategy "胜率高,策略2"           # 校验多个策略
  %(prog)s --all-strategies                  # 校验所有已启用策略
  %(prog)s --debug                           # 启用调试模式
  %(prog)s --yesterday --debug               # 校验昨天并启用调试
        """
    )
    
    parser.add_argument(
        '--date',
        type=str,
        help='指定校验日期 (格式: YYYY-MM-DD)，默认为昨天'
    )

    parser.add_argument(
        '--yesterday',
        action='store_true',
        help='使用昨天作为校验日期（等同于不指定--date参数）'
    )

    parser.add_argument(
        '--strategy',
        type=str,
        help='指定要校验的策略名称，支持单个策略或多个策略（用逗号分隔）'
    )

    parser.add_argument(
        '--all-strategies',
        action='store_true',
        help='校验所有已启用的策略'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='启用调试模式，输出详细日志'
    )

    parser.add_argument(
        '--html-output-dir',
        type=str,
        help='HTML报告输出目录路径'
    )

    parser.add_argument(
        '--report-base-url',
        type=str,
        help='报告访问的基础URL前缀，例如: https://meme-monitor.api.gaojbindev.xyz/report/'
    )

    return parser.parse_args()

def parse_date(date_str: str) -> date:
    """
    解析日期字符串
    
    Args:
        date_str: 日期字符串 (YYYY-MM-DD格式)
        
    Returns:
        date: 解析后的日期对象
        
    Raises:
        ValueError: 日期格式无效
    """
    try:
        return datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        raise ValueError(f"无效的日期格式: {date_str}，请使用 YYYY-MM-DD 格式")

async def main() -> int:
    """
    主函数
    
    Returns:
        int: 退出代码 (0: 成功, 1: 失败)
    """
    logger = None
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        # 设置日志
        setup_logging(args.debug)
        logger = logging.getLogger(__name__)
        
        # 解析目标日期
        target_date = None
        if args.date and args.yesterday:
            raise ValueError("不能同时指定 --date 和 --yesterday 参数")
        elif args.date:
            target_date = parse_date(args.date)
        elif args.yesterday:
            target_date = date.today() - timedelta(days=1)
        else:
            # 默认使用昨天
            target_date = date.today() - timedelta(days=1)

        logger.info("=" * 60)
        logger.info("🚀 每日回测校验开始")
        logger.info(f"⏰ 启动时间: {datetime.now()}")
        logger.info(f"📅 目标日期: {target_date}")
        if args.debug:
            logger.info("🐛 调试模式: 启用")
        logger.info("=" * 60)
        
        # 初始化数据库连接
        logger.info("📊 正在初始化数据库连接...")
        await init_db()
        logger.info("✅ 数据库连接初始化完成")
        
        # 解析策略参数
        strategies = None
        if args.all_strategies:
            strategies = "all"
            logger.info("🎯 策略模式: 所有已启用策略")
        elif args.strategy:
            strategies = [s.strip() for s in args.strategy.split(',')]
            if len(strategies) == 1:
                logger.info(f"🎯 策略模式: 单策略 ({strategies[0]})")
            else:
                logger.info(f"🎯 策略模式: 多策略 ({', '.join(strategies)})")
        else:
            logger.info("🎯 策略模式: 多策略（默认）")

        # 创建并运行校验器
        logger.info("🔍 正在启动校验流程...")
        runner = DailyBacktestVerificationRunner()
        result = await runner.run_verification(target_date, strategies=strategies)
        
        # 处理结果
        if result.status == 'success':
            logger.info("🎉 每日回测校验成功完成!")
            logger.info(f"📊 校验结果: {result.status}")
            if result.total_trades is not None:
                logger.info(f"📈 回测交易数: {result.total_trades}")
                logger.info(f"📊 实际信号数: {result.total_signals}")
                logger.info(f"✅ 匹配交易数: {result.matched_trades}")
            if result.execution_time is not None:
                logger.info(f"⏱️  执行时间: {result.execution_time:.1f}秒")
            return 0
        else:
            logger.error("❌ 每日回测校验失败!")
            logger.error(f"📊 校验状态: {result.status}")
            if result.error_message:
                logger.error(f"❌ 错误信息: {result.error_message}")
            return 1
            
    except ValueError as e:
        if logger:
            logger.error(f"❌ 参数错误: {e}")
        else:
            print(f"❌ 参数错误: {e}")
        return 1
    except ImportError as e:
        if logger:
            logger.error(f"❌ 导入模块失败: {e}")
            logger.error("请确保项目依赖已正确安装: poetry install")
        else:
            print(f"❌ 导入模块失败: {e}")
            print("请确保项目依赖已正确安装: poetry install")
        return 1
    except Exception as e:
        if logger:
            logger.error(f"❌ 校验过程发生未知错误: {e}", exc_info=True)
        else:
            print(f"❌ 校验过程发生未知错误: {e}")
        return 1
    finally:
        if logger:
            logger.info("=" * 60)
            logger.info(f"🏁 每日回测校验结束 - {datetime.now()}")
            logger.info("=" * 60)

def cli_entry_point():
    """
    命令行入口点，供外部调用
    """
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

if __name__ == "__main__":
    cli_entry_point()