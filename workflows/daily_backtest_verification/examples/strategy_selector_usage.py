#!/usr/bin/env python3
"""
策略选择器使用示例

本文件展示了如何使用 StrategySelector 类来验证和管理交易策略配置。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from workflows.daily_backtest_verification.components.strategy_selector import (
    StrategySelector,
    # InvalidStrategyError, # 已移除，不再需要
    MissingStrategyError
)


def main():
    """策略选择器使用示例主函数"""
    print("=== 策略选择器使用示例 ===")
    
    # 创建策略选择器实例
    selector = StrategySelector()
    
    # 1. 获取所有可用策略
    print("\n1. 可用策略列表:")
    available_strategies = selector.get_available_strategies()
    for strategy in available_strategies:
        print(f"  - {strategy}")
    
    # 2. 验证有效策略名称
    print("\n2. 策略名称验证:")
    valid_strategies = ["kol_strategy_v1", "kol_strategy_v2", "kol_strategy_v3"]
    for strategy in valid_strategies:
        try:
            selector.validate_strategy_name(strategy)
            print(f"  ✓ '{strategy}' - 有效")
        except MissingStrategyError as e:
            print(f"  ✗ '{strategy}' - 无效: {e}")
    
    # 3. 验证无效策略名称
    print("\n3. 无效策略名称测试:")
    invalid_strategies = ["invalid_strategy", "", "   ", None]
    for strategy in invalid_strategies:
        try:
            selector.validate_strategy_name(strategy)
            print(f"  ✓ '{strategy}' - 有效")
        except MissingStrategyError as e:
            print(f"  ✗ '{strategy}' - 无效: {e}")
    
    # 4. 获取策略配置
    print("\n4. 策略配置获取:")
    try:
        config = selector.get_strategy_config("kol_strategy_v1")
        print(f"  kol_strategy_v1 配置: {config}")
    except MissingStrategyError as e:
        print(f"  获取配置失败: {e}")
    
    # 5. 检查策略是否有效
    print("\n5. 策略有效性检查:")
    test_strategies = ["kol_strategy_v1", "kol_strategy_v2", "invalid_strategy"]
    for strategy in test_strategies:
        is_valid = selector.is_strategy_valid(strategy)
        print(f"  '{strategy}': {'有效' if is_valid else '无效'}")
    
    # 6. 实际使用场景示例
    print("\n6. 实际使用场景示例:")
    user_strategy = "kol_strategy_v2"  # 假设这是用户输入的策略名称
    
    try:
        # 验证策略名称
        selector.validate_strategy_name(user_strategy)
        
        # 获取策略配置
        config = selector.get_strategy_config(user_strategy)
        
        print(f"  使用策略: {user_strategy}")
        print(f"  策略配置: {config}")
        print("  ✓ 策略验证成功，可以继续执行回测")
        
    except Exception as e:  # 现在任何策略名称都是有效的，除了空值
        print(f"  ✗ 策略验证失败: {e}")
        print("  建议使用以下有效策略之一:")
        for strategy in selector.get_available_strategies():
            print(f"    - {strategy}")
    except MissingStrategyError as e:
        print(f"  ✗ 策略名称缺失: {e}")
        print("  请提供有效的策略名称")


if __name__ == "__main__":
    main()