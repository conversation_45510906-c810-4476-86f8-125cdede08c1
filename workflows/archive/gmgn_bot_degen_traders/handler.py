import logging
import traceback
from typing import Any, Dict, List, Optional

from dao.gmgn_bot_degen_traders_dao import GmgnBotDegenTradersDAO
from utils.spiders.smart_money.gmgn_top_traders_spider import GmgnTopTradersSpider


logger = logging.getLogger("GmgnBotDegenTradersHandler")
spider = GmgnTopTradersSpider()
gmgn_bot_degen_traders_dao = GmgnBotDegenTradersDAO()


async def process_bot_degen_traders(token_dict: Dict) -> Optional[List[Dict]]:
    """处理代币数据
    
    Args:
        token_dict: 代币数据
        
    Returns:
        Optional[List[Dict]]: 处理后的代币数据，如果处理失败则返回None
    """
    if not token_dict:
        logger.warning("收到空的代币数据")
        return None
    
    # 获取代币地址
    wallet_address = token_dict.get('address')
    if not wallet_address:
        logger.warning(f"代币数据缺少地址字段: {token_dict}")
        return None
    
    chain = "sol"
    
    # 使用dex_bot标签获取Bot Degen交易者数据
    traders = await spider.get_top_traders(chain, wallet_address, tag="dex_bot")
    if not traders:
        logger.warning(f"没有Bot Degen交易者买过这个币: {wallet_address}")
        return None
    
    return traders

async def validate(data: Any) -> bool:
    """验证代币链接数据是否有效
    
    Args:
        data: 代币链接数据
        
    Returns:
        bool: 如果数据有效则返回True，否则返回False
    """
    return True


async def store_data(data: List[Dict]) -> int:
    """存储Bot Degen交易者数据到数据库
    
    Args:
        data: Bot Degen交易者数据列表
        
    Returns:
        int: 成功存储的记录数
    """
    if not data:
        return 0
    
    traders = data if isinstance(data, list) else [data]
        
    try:
        chain = "sol"
            
        update_count = await gmgn_bot_degen_traders_dao.upsert_traders_many(
            chain=chain,
            data_list=traders
        )
        logger.info(f"批量更新完成: 更新了 {update_count} 条Bot Degen交易者记录")
        return update_count
    except Exception as e:
        error_msg = f"存储Bot Degen交易者数据时发生错误: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        return False