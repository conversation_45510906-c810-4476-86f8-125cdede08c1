name: "GMGN 代币Bot Degen交易者记录"
description: "定期获取链上的Bot Degen交易者记录，并将数据存储到数据库"

nodes:
  - name: "BotDegenTradersSchedulerNode"
    node_type: "input"
    interval: 10
    generate_data: workflows.gmgn_token_link.handler.generate_data
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "BotDegenTradersMonitorNode"
    node_type: "process"
    depend_ons: ["BotDegenTradersSchedulerNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_bot_degen_traders.handler.process_bot_degen_traders

  - name: "BotDegenTradersStoreNode"
    node_type: "storage"
    depend_ons: ["BotDegenTradersMonitorNode"]
    batch_size: 1000  # 每批处理1000条数据
    store_data: workflows.gmgn_bot_degen_traders.handler.store_data
    validate: workflows.gmgn_bot_degen_traders.handler.validate 
