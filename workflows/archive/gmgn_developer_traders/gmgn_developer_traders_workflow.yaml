name: "GMGN 代币聪明钱购买持仓记录"
description: "定期获取链上的聪明钱购买持仓记录，并将数据存储到数据库"

nodes:
  - name: "TopTradersSchedulerNode"
    node_type: "input"
    interval: 10
    generate_data: workflows.gmgn_token_link.handler.generate_data
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "DeveloperTradersMonitorNode"
    node_type: "process"
    depend_ons: ["TopTradersSchedulerNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_developer_traders.handler.process_developer_traders

  - name: "DeveloperTradersStoreNode"
    node_type: "storage"
    depend_ons: ["DeveloperTradersMonitorNode"]
    batch_size: 1000  # 每批处理1000条数据
    store_data: workflows.gmgn_developer_traders.handler.store_data
    validate: workflows.gmgn_developer_traders.handler.validate
