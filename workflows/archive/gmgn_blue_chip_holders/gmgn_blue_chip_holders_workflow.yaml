name: "GMGN 代币蓝筹持有者记录"
description: "定期获取链上的蓝筹持有者记录，并将数据存储到数据库"

nodes:
  - name: "BlueChipHoldersSchedulerNode"
    node_type: "input"
    interval: 10
    generate_data: workflows.gmgn_token_link.handler.generate_data
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "BlueChipHoldersMonitorNode"
    node_type: "process"
    depend_ons: ["BlueChipHoldersSchedulerNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_blue_chip_holders.handler.process_blue_chip_holders

  - name: "BlueChipHoldersStoreNode"
    node_type: "storage"
    depend_ons: ["BlueChipHoldersMonitorNode"]
    batch_size: 1000  # 每批处理1000条数据
    store_data: workflows.gmgn_blue_chip_holders.handler.store_data
    validate: workflows.gmgn_blue_chip_holders.handler.validate 
