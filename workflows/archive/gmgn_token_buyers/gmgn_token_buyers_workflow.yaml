name: 'GMGN代币买家监控工作流'
description: '定期获取代币的买家信息，并将数据存储到数据库'

nodes:
  - name: "TokenBuyersSchedulerNode"
    node_type: "input"
    interval: 1
    generate_data: workflows.gmgn_token_buyers.handler.generate_token_list
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "TokenBuyersMonitorNode"
    node_type: "process"
    depend_ons: ["TokenBuyersSchedulerNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_token_buyers.handler.process_token_buyers

  - name: "TokenBuyersStorageNode"
    node_type: "storage"
    depend_ons: ["TokenBuyersMonitorNode"]
    batch_size: 100  # 每批处理100条数据
    store_data: workflows.gmgn_token_buyers.handler.store_token_buyers_data
    validate: workflows.gmgn_token_buyers.handler.validate_token_buyers_data 