"""
GMGN代币价格同步工作流处理函数模块

提供YAML配置中引用的处理函数
"""

import logging
import traceback
from typing import List, Dict, Any, Optional
from datetime import datetime
from pymongo import UpdateOne

from dao.gmgn_token_dao import GmgnTokenDAO
from models.token import Token
from models.gmgn_token import (
    GmgnToken1m,
    GmgnToken5m,
    GmgnToken1h,
    GmgnToken6h,
    GmgnToken24h
)

# 创建日志记录器
logger = logging.getLogger("GmgnTokenSyncHandler")

# 创建DAO实例
token_dao = GmgnTokenDAO()

# 时间范围与模型的映射
TIME_RANGE_MODELS = {
    "1m": GmgnToken1m,
    "5m": GmgnToken5m,
    "1h": GmgnToken1h,
    "6h": GmgnToken6h,
    "24h": GmgnToken24h
}

# 时间范围与Token模型字段的映射
TIME_RANGE_FIELDS = {
    "1m": "GmgnToken1m",
    "5m": "GmgnToken5m",
    "1h": "GmgnToken1h",
    "6h": "GmgnToken6h",
    "24h": "GmgnToken24h"
}

async def generate_sync_task() -> Optional[List[Dict]]:
    """
    生成同步任务
    
    Returns:
        List[Dict]: 包含同步任务的列表
    """
    try:
        # 为每个时间范围生成一个同步任务
        tasks = [{"time_range": time_range} for time_range in TIME_RANGE_MODELS.keys()]
        logger.info(f"生成了 {len(tasks)} 个同步任务")
        return tasks
    except Exception as e:
        logger.error(f"生成同步任务时发生错误: {str(e)}\n{traceback.format_exc()}")
        return None

async def process_sync_task(task: Dict) -> Optional[List[Dict]]:
    """
    处理同步任务
    
    Args:
        task: 包含时间范围的任务字典
        
    Returns:
        List[Dict]: 处理后的同步结果列表
    """
    try:
        time_range = task.get("time_range")
        if not time_range or time_range not in TIME_RANGE_MODELS:
            logger.error(f"无效的时间范围: {time_range}")
            return None
            
        logger.info(f"开始处理 {time_range} 时间范围的同步任务")
        
        # 获取该时间范围的所有代币数据
        model_class = TIME_RANGE_MODELS[time_range]
        tokens = await model_class.find_all().to_list()
        
        if not tokens:
            logger.warning(f"没有找到 {time_range} 时间范围的代币数据")
            return None
            
        logger.info(f"找到 {len(tokens)} 个 {time_range} 时间范围的代币数据")
        
        # 准备同步结果
        sync_results = []
        
        for gmgn_token in tokens:
            try:
                # 构建同步数据
                sync_data = {
                    "address": gmgn_token.address,
                    "time_range": time_range,
                    "price": gmgn_token.price,
                    "field_name": TIME_RANGE_FIELDS[time_range]
                }
                
                sync_results.append(sync_data)
                
            except Exception as e:
                logger.error(f"处理代币 {gmgn_token.address} 的同步数据时发生错误: {str(e)}\n{traceback.format_exc()}")
                continue
                
        logger.info(f"已处理 {len(sync_results)} 个 {time_range} 时间范围的同步数据")
        return sync_results
        
    except Exception as e:
        logger.error(f"处理同步任务时发生错误: {str(e)}\n{traceback.format_exc()}")
        return None

async def validate_sync_result(data: Dict) -> bool:
    """
    验证同步结果的有效性
    
    Args:
        data: 要验证的数据
        
    Returns:
        bool: 数据是否有效
    """
    try:
        # 检查每个同步结果是否包含必要字段
        required_fields = ["address", "time_range", "price", "field_name"]
        
        if not all(field in data for field in required_fields):
            missing = [field for field in required_fields if field not in data]
            logger.error(f"同步数据缺少必要字段: {missing}")
            return False
                
        return True
        
    except Exception as e:
        logger.error(f"验证同步结果时发生错误: {str(e)}\n{traceback.format_exc()}")
        return False

async def store_sync_result(data_list: List[Dict]) -> int:
    """
    将同步结果存储到Token模型 - 使用批量更新操作
    
    Args:
        data_list: 同步结果列表
        
    Returns:
        int: 成功更新的数量
    """
    try:
        if not data_list:
            logger.warning("没有数据需要存储")
            return 0
        
        # 按地址分组
        address_data = {}
        for item in data_list:
            address = item.get("address")
            if not address:
                logger.warning(f"同步数据缺少地址: {item}")
                continue
                
            if address not in address_data:
                address_data[address] = {}
                
            field_name = item.get("field_name")
            price = item.get("price")
            
            if field_name and price is not None:
                address_data[address][field_name] = price
        
        if not address_data:
            logger.warning("没有有效的数据需要更新")
            return 0
            
        # 准备批量更新操作
        operations = []
        current_time = datetime.utcnow()
        
        for address, fields in address_data.items():
            try:
                # 构建更新文档
                update_doc = {
                    "$set": {
                        **fields,
                        "updated_at": current_time
                    },
                    "$setOnInsert": {
                        "address": address
                    }
                }
                
                # 添加到批量操作列表
                operations.append(
                    UpdateOne(
                        {"address": address},
                        update_doc,
                        upsert=True
                    )
                )
                
            except Exception as e:
                logger.error(f"准备更新代币 {address} 的操作时发生错误: {str(e)}\n{traceback.format_exc()}")
                continue
        
        if not operations:
            logger.warning("没有有效的更新操作")
            return 0
            
        logger.info(f"准备执行 {len(operations)} 个批量更新操作")
        
        # 执行批量更新
        try:
            # 获取Token模型的集合
            collection = Token.get_motor_collection()
            
            # 执行批量写入
            result = await collection.bulk_write(operations, ordered=False)
            modified_count = result.modified_count + result.upserted_count
            
            logger.info(f"批量更新完成: 修改 {result.modified_count} 条，插入 {result.upserted_count} 条")
            return modified_count
            
        except Exception as e:
            logger.error(f"执行批量更新操作时发生错误: {str(e)}\n{traceback.format_exc()}")
            return 0
        
    except Exception as e:
        logger.error(f"存储同步结果时发生错误: {str(e)}\n{traceback.format_exc()}")
        return 0 