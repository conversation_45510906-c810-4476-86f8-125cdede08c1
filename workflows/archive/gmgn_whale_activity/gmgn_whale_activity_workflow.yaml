name: "GMGN 代币聪明钱活动记录"
description: "定期获取链上的聪明钱活动记录，并将数据存储到数据库"

nodes:
  - name: "WhaleActivitySchedulerNode"
    node_type: "input"
    interval: 10
    generate_data: workflows.gmgn_token_link.handler.generate_data
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "WhaleActivityMonitorNode"
    node_type: "process"
    depend_ons: ["WhaleActivitySchedulerNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_whale_activity.handler.process_whale_activity

  - name: "WhaleActivityStoreNode"
    node_type: "storage"
    depend_ons: ["WhaleActivityMonitorNode"]
    batch_size: 1000  # 每批处理1000条数据
    store_data: workflows.gmgn_whale_activity.handler.store_data
    validate: workflows.gmgn_whale_activity.handler.validate 