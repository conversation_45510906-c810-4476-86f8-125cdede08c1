"""
GMGN钱包持仓信息监控工作流处理函数模块

提供YAML配置中引用的处理函数
"""

import logging
import traceback
from typing import List, Dict, Any, Optional
from datetime import datetime

from utils.spiders.smart_money.gmgn_wallet_holdings_spider import GmgnWalletHoldingsSpider
from dao.smart_money_wallet_dao import SmartMoneyWalletDAO
from dao.gmgn_wallet_holdings_dao import GmgnWalletHoldingsDAO

# 创建日志记录器
logger = logging.getLogger("GmgnWalletHoldingsHandler")

# 创建爬虫和DAO实例
spider = GmgnWalletHoldingsSpider()
wallet_dao = SmartMoneyWalletDAO()
holdings_dao = GmgnWalletHoldingsDAO()

async def generate_wallet_list() -> Optional[List[Dict]]:
    """
    生成需要获取持仓信息的钱包列表
    
    Returns:
        List[Dict]: 钱包列表
    """
    try:
        # 从数据库获取聪明钱钱包列表
        wallets = await wallet_dao.find_all_wallets()
        
        if not wallets:
            logger.warning("没有找到需要监控的钱包")
            return None
            
        # 转换为字典列表
        wallet_list = []
        for wallet in wallets:
            wallet_dict = {
                "chain": wallet.chain,
                "address": wallet.address,
            }
            wallet_list.append(wallet_dict)
            
        logger.info(f"生成了 {len(wallet_list)} 个钱包的监控列表")
        return wallet_list
        
    except Exception as e:
        logger.error(f"生成钱包列表时发生错误: {str(e)}\n{traceback.format_exc()}")
        return None

async def process_wallet_holdings(wallet_data: Dict) -> Optional[Dict]:
    """
    处理单个钱包的持仓信息
    
    Args:
        wallet_data: 钱包数据
        
    Returns:
        Dict: 处理后的钱包持仓信息
    """
    try:
        chain = wallet_data.get("chain", "solana")
        address = wallet_data.get("address")
        
        if not address:
            logger.error("缺少钱包地址")
            return None
            
        logger.info(f"开始获取钱包 {address} 的持仓信息...")
        
        # 调用爬虫获取数据
        holdings = await spider.get_wallet_holdings("sol", address)
        
        if not holdings:
            logger.warning(f"获取钱包 {address} 的持仓信息失败")
            return None
        
        # 构建结果数据
        result = {
            "chain": chain,
            "wallet_address": address,
            "holdings": holdings,
        }
        
        logger.info(f"已获取钱包 {address} 的持仓信息，共 {len(holdings)} 个代币")
        return result
        
    except Exception as e:
        logger.error(f"处理钱包持仓信息时发生错误: {str(e)}\n{traceback.format_exc()}")
        return None

async def validate_wallet_holdings_data(data: Any) -> bool:
    """
    验证钱包持仓信息的有效性
    
    Args:
        data: 要验证的数据
        
    Returns:
        bool: 数据是否有效
    """
    try:
        if not data:
            logger.warning("数据为空")
            return False
            
        # 检查是否为列表
        if isinstance(data, list):
            if not data:
                logger.warning("数据列表为空")
                return False
                
            # 检查每个项目是否包含必要字段
            for item in data:
                if not all(field in item for field in ["chain", "wallet_address", "holdings"]):
                    logger.error(f"数据缺少必要字段: {item}")
                    return False
        # 检查单个项目
        elif isinstance(data, dict):
            if not all(field in data for field in ["chain", "wallet_address", "holdings"]):
                logger.error(f"数据缺少必要字段: {data}")
                return False
        else:
            logger.error(f"数据类型错误: {type(data)}")
            return False
            
        return True
        
    except Exception as e:
        logger.error(f"验证钱包持仓信息时发生错误: {str(e)}\n{traceback.format_exc()}")
        return False

async def store_wallet_holdings_data(data_list: List[Dict]) -> int:
    """
    存储钱包持仓信息到数据库
    
    Args:
        data_list: 钱包持仓信息列表
        
    Returns:
        int: 成功存储的数据数量
    """
    try:
        if not data_list:
            logger.warning("没有数据需要存储")
            return 0
            
        success_count = 0
        
        # 处理每个钱包的持仓信息
        for data in data_list:
            if not data:
                continue
                
            chain = data.get("chain")
            wallet_address = data.get("wallet_address")
            holdings = data.get("holdings")
            
            if not all([chain, wallet_address, holdings]):
                logger.error(f"数据格式无效: {data}")
                continue
            
            logger.debug(f"准备处理钱包 {wallet_address} 的持仓信息")
            
            # 更新数据库
            success = await holdings_dao.upsert_holdings(chain, wallet_address, holdings)
            if success:
                logger.info(f"成功更新钱包 {wallet_address} 的持仓信息")
                success_count += 1
            else:
                logger.warning(f"更新钱包 {wallet_address} 的持仓信息失败")
        
        return success_count
        
    except Exception as e:
        logger.error(f"存储钱包持仓信息时发生错误: {str(e)}\n{traceback.format_exc()}")
        return False