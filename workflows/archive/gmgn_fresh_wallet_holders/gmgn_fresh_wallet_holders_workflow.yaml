name: "GMGN 代币新钱包持有者记录"
description: "定期获取链上的新钱包持有者记录，并将数据存储到数据库"

nodes:
  - name: "FreshWalletHoldersSchedulerNode"
    node_type: "input"
    interval: 10
    generate_data: workflows.gmgn_token_link.handler.generate_data
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "FreshWalletHoldersMonitorNode"
    node_type: "process"
    depend_ons: ["FreshWalletHoldersSchedulerNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_fresh_wallet_holders.handler.process_fresh_wallet_holders

  - name: "FreshWalletHoldersStoreNode"
    node_type: "storage"
    depend_ons: ["FreshWalletHoldersMonitorNode"]
    batch_size: 10  # 每批处理10条数据
    store_data: workflows.gmgn_fresh_wallet_holders.handler.store_data
    validate: workflows.gmgn_fresh_wallet_holders.handler.validate 
