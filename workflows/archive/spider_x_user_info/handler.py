"""
X用户信息爬取工作流处理函数模块

提供YAML配置中引用的处理函数
"""

import logging
import traceback
from typing import List, Dict, Any, Optional
from datetime import datetime
import asyncio

from models.tweet_monitor_user import TweetMonitorUser
from utils.spiders.x.utools import UtoolsSpider
from dao.tweet_dao import TweetDAO
from dao.x_user_dao import XUserDAO

# 创建日志记录器
logger = logging.getLogger("SpiderXUserInfoHandler")

# 创建爬虫实例
spider = UtoolsSpider()

# 创建DAO实例
tweet_dao = TweetDAO()
x_user_dao = XUserDAO()

async def generate_user_list() -> Optional[List[Dict]]:
    """生成需要监控的用户列表
    
    从数据库获取需要监控的用户列表
    
    Returns:
        Optional[List[Dict]]: 用户列表，如果获取失败则返回None
    """
    try:
        # 获取所有监控用户
        users = await TweetMonitorUser.find_all().to_list()
        
        if not users:
            logger.info("没有需要监控的用户")
            return []
        
        # 转换为字典列表
        user_dicts = []
        for user in users:
            user_dict = {
                "username": user.username,
                "description": user.description
            }
            user_dicts.append(user_dict)
        
        logger.info(f"成功获取 {len(users)} 个监控用户")
        return user_dicts
    except Exception as e:
        logger.error(f"获取监控用户列表时发生错误: {str(e)}\n{traceback.format_exc()}")
        return []

async def process_user_info(user_data: Dict) -> Optional[Dict]:
    """处理单个用户，获取其信息和推文
    
    Args:
        user_data: 用户数据字典
        
    Returns:
        Optional[Dict]: 处理后的用户信息和推文，如果获取失败则返回None
    """
    # 验证用户数据
    if not user_data:
        logger.warning("收到空的用户数据")
        return None
    
    # 获取用户名
    username = user_data.get('username')
    if not username:
        logger.warning(f"用户数据缺少username字段: {user_data}")
        return None
    
    logger.info(f"处理用户: {username}")
    
    try:
        # 使用spider抓取用户信息
        user_info = spider.scrape_user_info(username)
        if not user_info:
            logger.error(f"获取用户信息失败: {username}")
            return None
        
        # 等待1秒后抓取用户推文
        await asyncio.sleep(1)
        
        # 抓取用户推文
        tweets = spider.scrape_user_tweets(username)
        if not tweets:
            logger.warning(f"未获取到用户 {username} 的推文")
            tweets = []  # 设置为空列表，继续处理
        
        # 构建处理后的数据
        processed_data = {
            "username": username,
            "user_info": user_info,
            "description": user_data.get("description"),
            "tweets": tweets,
            "tweets_count": len(tweets),
            "fetch_time": datetime.utcnow().isoformat()
        }
        
        logger.info(f"成功获取用户信息和推文: {username} (获取了 {len(tweets)} 条推文)")
        return processed_data
    except Exception as e:
        error_msg = f"处理用户 {username} 失败: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        return None

async def validate_user_data(data: Any) -> bool:
    """验证用户数据是否有效
    
    Args:
        data: 用户数据
        
    Returns:
        bool: 数据是否有效
    """
    # 检查必要字段
    if not isinstance(data, dict):
        logger.warning(f"数据不是字典类型: {type(data)}")
        return False
    
    # 检查用户名字段
    username = data.get('username')
    if not username:
        logger.warning("数据缺少username字段")
        return False
    
    # 检查用户信息字段
    user_info = data.get('user_info')
    if not user_info:
        logger.warning(f"数据缺少user_info字段: {username}")
        return False
    
    return True

async def store_user_data(data_list: List[Dict]) -> int:
    """存储用户信息和推文到数据库
    
    Args:
        data_list: 用户数据列表
        
    Returns:
        int: 成功存储的记录数
    """
    if not data_list:
        return 0
    
    success_count = 0
    
    for data in data_list:
        try:
            username = data.get('username')
            user_info = data.get('user_info')
            tweets = data.get('tweets', [])
            
            if not username or not user_info:
                logger.warning(f"数据缺少必要字段: {data}")
                continue
            
            # 1. 保存用户信息
            user_saved = await save_user(username, user_info)
            # 这里不一定是保存失败，可能只是用户信息完全相同，没更新
            if not user_saved:
                logger.warning(f"未保存用户信息: {username}")
            
            # 2. 保存推文信息
            saved_tweets = await tweet_dao.save_many_tweets(tweets)
            
            logger.info(f"用户信息和推文已存储: {username} (保存了 {len(saved_tweets)} 条推文)")
            success_count += 1
        except Exception as e:
            logger.error(f"保存用户数据失败: {str(e)}\n{traceback.format_exc()}")
    
    return success_count

async def save_user(username: str, user_info: dict) -> bool:
    """保存用户信息
    
    Args:
        username: 用户名
        user_info: 用户信息字典
        
    Returns:
        bool: 是否保存成功
    """
    try:
        # 准备用户数据
        user_data = {
            "username": username,
            "display_name": user_info.get("display_name"),
            "description": user_info.get("description"),
            "followers_count": user_info.get("followers_count", 0),
            "following_count": user_info.get("following_count", 0),
            "tweets_count": user_info.get("tweets_count", 0),
            "profile_image_url": user_info.get("profile_image_url"),
            "verified": user_info.get("verified", False),
            "location": user_info.get("location"),
            "created_at": user_info.get("created_at"),
            "rest_id": user_info.get("rest_id")  # 确保rest_id不为None
        }
        
        # 使用DAO更新或插入用户数据
        success = await x_user_dao.upsert_user(user_data)
        if success:
            logger.info(f"已保存用户信息: {username}")
        else:
            logger.warning(f"未保存用户信息: {username}")
        return success
            
    except Exception as e:
        logger.error(f"保存用户信息时发生错误: {str(e)}\n{traceback.format_exc()}")
        return False 