name: "GMGN 代币KOL持仓记录"
description: "定期获取链上的KOL持仓记录，并将数据存储到数据库"

nodes:
  - name: "KolHoldingsSchedulerNode"
    node_type: "input"
    interval: 10
    generate_data: workflows.gmgn_token_link.handler.generate_data
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "KolHoldingsMonitorNode"
    node_type: "process"
    depend_ons: ["KolHoldingsSchedulerNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_kol_holdings.handler.process_kol_holdings

  - name: "KolHoldingsStoreNode"
    node_type: "storage"
    depend_ons: ["KolHoldingsMonitorNode"]
    batch_size: 1000  # 每批处理1000条数据
    store_data: workflows.gmgn_kol_holdings.handler.store_data
    validate: workflows.gmgn_kol_holdings.handler.validate
