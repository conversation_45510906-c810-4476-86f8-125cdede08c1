import logging
import traceback
from typing import Any, Dict, List, Optional

from dao.gmgn_rat_trader_holders_dao import GmgnRatTraderHoldersDAO
from utils.spiders.smart_money.gmgn_top_holders_spider import GmgnTopHoldersSpider


logger = logging.getLogger("GmgnRatTraderHoldingsHandler")
spider = GmgnTopHoldersSpider()
gmgn_rat_trader_holders_dao = GmgnRatTraderHoldersDAO()


async def process_rat_trader_holdings(token_dict: Dict) -> Optional[List[Dict]]:
    """处理代币数据
    
    Args:
        token_dict: 代币数据
        
    Returns:
        Optional[List[Dict]]: 处理后的代币数据，如果处理失败则返回None
    """
    if not token_dict:
        logger.warning("收到空的代币数据")
        return None
    
    # 获取代币地址
    token_address = token_dict.get('address')
    if not token_address:
        logger.warning(f"代币数据缺少地址字段: {token_dict}")
        return None
    
    chain = "sol"
    
    # 使用renowned标签获取KOL持仓数据
    holders = await spider.get_top_holders(
        chain=chain, 
        address=token_address, 
        tag="rat_trader",
        orderby="amount_percentage",
        direction="desc",
        limit=100,
        cost=20
    )
    
    if not holders:
        logger.warning(f"没有钓鱼钱包持有这个币: {token_address}")
        return None
    
    return holders

async def validate(data: Any) -> bool:
    """验证钓鱼钱包持仓数据是否有效
    
    Args:
        data: 钓鱼钱包持仓数据
        
    Returns:
        bool: 如果数据有效则返回True，否则返回False
    """
    return True


async def store_data(data: List[Dict]) -> int:
    """存储钓鱼钱包持仓数据到数据库
    
    Args:
        data: 钓鱼钱包持仓数据列表
        
    Returns:
        int: 成功存储的记录数
    """
    if not data:
        return 0
    
    holders = data if isinstance(data, list) else [data]
        
    try:
        chain = "sol"
        update_count = await gmgn_rat_trader_holders_dao.upsert_holders_many(
            chain=chain,
            data_list=holders
        )
        logger.info(f"批量更新完成: 更新了 {update_count} 条钓鱼钱包持仓记录")
        return update_count
    except Exception as e:
        error_msg = f"存储钓鱼钱包持仓数据时发生错误: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        return False