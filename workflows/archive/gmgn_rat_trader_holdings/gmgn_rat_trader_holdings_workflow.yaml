name: "GMGN 代币钓鱼钱包持仓记录"
description: "定期获取链上的钓鱼钱包持仓记录，并将数据存储到数据库"

nodes:
  - name: "RatTraderHoldingsSchedulerNode"
    node_type: "input"
    interval: 10
    generate_data: workflows.gmgn_token_link.handler.generate_data
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "RatTraderHoldingsMonitorNode"
    node_type: "process"
    depend_ons: ["RatTraderHoldingsSchedulerNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_rat_trader_holdings.handler.process_rat_trader_holdings

  - name: "RatTraderHoldingsStoreNode"
    node_type: "storage"
    depend_ons: ["RatTraderHoldingsMonitorNode"]
    batch_size: 1000  # 每批处理1000条数据
    store_data: workflows.gmgn_rat_trader_holdings.handler.store_data
    validate: workflows.gmgn_rat_trader_holdings.handler.validate
