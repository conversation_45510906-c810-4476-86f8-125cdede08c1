"""
智能钱包监控工作流处理函数模块

提供YAML配置中引用的处理函数
"""

import logging
import traceback
from typing import List, Dict, Any, Optional, Set
from datetime import datetime

from utils.spiders.smart_money.smart_money import SmartMoneySpider
from dao.smart_money_wallet_dao import SmartMoneyWalletDAO
from dao.solana_monitor_address_dao import SolanaMonitorAddressDAO
from models.smart_money_wallet import DailyProfit, RiskMetrics

# 创建日志记录器
logger = logging.getLogger("SmartMoneyHandler")

# 创建爬虫和DAO实例
spider = SmartMoneySpider()
wallet_dao = SmartMoneyWalletDAO()
monitor_address_dao = SolanaMonitorAddressDAO()

def safe_float(value, default=0.0):
    """安全地转换值为浮点数"""
    try:
        if value is None:
            return default
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_int(value, default=0):
    """安全地转换值为整数"""
    try:
        if value is None:
            return default
        return int(value)
    except (ValueError, TypeError):
        return default

def safe_timestamp_to_datetime(timestamp, default=None):
    """安全地转换时间戳为datetime"""
    try:
        if timestamp is None:
            return default or datetime.utcnow()
        return datetime.fromtimestamp(timestamp)
    except (ValueError, TypeError):
        return default or datetime.utcnow()

async def fetch_smart_money_wallets() -> Optional[List[Dict]]:
    """
    获取智能钱包数据
    
    Returns:
        List[Dict]: 智能钱包数据列表
    """
    try:
        logger.info("开始获取智能钱包数据")
        
        # 调用爬虫获取数据
        result = await spider.get_smart_money_wallets()
        
        if not result or result.get("code") != 0:
            logger.warning(f"获取数据失败或数据无效: {result}")
            return None
        
        # 处理返回的数据
        wallets = result.get("data", {}).get("rank", [])
        
        if not wallets:
            logger.warning("没有找到钱包数据")
            return None
        
        # 处理每个钱包的数据
        processed_wallets = []
        for wallet in wallets:
            try:
                # 构建标准化的钱包数据
                wallet_data = {
                    # 基本信息
                    "wallet_address": wallet.get("wallet_address", ""),
                    "address": wallet.get("address", wallet.get("wallet_address", "")),
                    "chain": "solana",
                    
                    # 用户信息
                    "twitter_username": wallet.get("twitter_username"),
                    "twitter_name": wallet.get("twitter_name"),
                    "twitter_description": wallet.get("twitter_description"),
                    "followers_count": safe_int(wallet.get("followers_count")),
                    "is_blue_verified": wallet.get("is_blue_verified", False),
                    "avatar": wallet.get("avatar"),
                    "ens": wallet.get("ens"),
                    "nickname": wallet.get("nickname"),
                    "name": wallet.get("name"),
                    "tag": wallet.get("tag"),
                    "tags": wallet.get("tags", []),
                    "tag_rank": wallet.get("tag_rank", {}),
                    
                    # 余额信息
                    "balance": safe_float(wallet.get("balance")),
                    "eth_balance": safe_float(wallet.get("eth_balance")),
                    "sol_balance": safe_float(wallet.get("sol_balance")),
                    "trx_balance": safe_float(wallet.get("trx_balance")),
                    
                    # 收益指标
                    "realized_profit": safe_float(wallet.get("realized_profit")),
                    "pnl_1d": safe_float(wallet.get("pnl_1d")),
                    "pnl_7d": safe_float(wallet.get("pnl_7d")),
                    "pnl_30d": safe_float(wallet.get("pnl_30d")),
                    "realized_profit_1d": safe_float(wallet.get("realized_profit_1d")),
                    "realized_profit_7d": safe_float(wallet.get("realized_profit_7d")),
                    "realized_profit_30d": safe_float(wallet.get("realized_profit_30d")),
                    
                    # 交易指标
                    "txs": safe_int(wallet.get("txs")),
                    "txs_30d": safe_int(wallet.get("txs_30d")),
                    "buy": safe_int(wallet.get("buy")),
                    "sell": safe_int(wallet.get("sell")),
                    "buy_30d": safe_int(wallet.get("buy_30d")),
                    "sell_30d": safe_int(wallet.get("sell_30d")),
                    "avg_hold_time": safe_float(wallet.get("avg_hold_time")),
                    "token_num_7d": safe_int(wallet.get("token_num_7d")),
                    "avg_holding_period_7d": safe_float(wallet.get("avg_holding_period_7d")),
                    
                    # 胜率和成本
                    "winrate_7d": safe_float(wallet.get("winrate_7d")),
                    "avg_cost_7d": safe_float(wallet.get("avg_cost_7d")),
                    
                    # 收益分布
                    "pnl_lt_minus_dot5_num_7d": safe_int(wallet.get("pnl_lt_minus_dot5_num_7d")),
                    "pnl_minus_dot5_0x_num_7d": safe_int(wallet.get("pnl_minus_dot5_0x_num_7d")),
                    "pnl_lt_2x_num_7d": safe_int(wallet.get("pnl_lt_2x_num_7d")),
                    "pnl_2x_5x_num_7d": safe_int(wallet.get("pnl_2x_5x_num_7d")),
                    "pnl_gt_5x_num_7d": safe_int(wallet.get("pnl_gt_5x_num_7d")),
                    
                    # 收益分布比率
                    "pnl_lt_minus_dot5_num_7d_ratio": safe_float(wallet.get("pnl_lt_minus_dot5_num_7d_ratio")),
                    "pnl_minus_dot5_0x_num_7d_ratio": safe_float(wallet.get("pnl_minus_dot5_0x_num_7d_ratio")),
                    "pnl_lt_2x_num_7d_ratio": safe_float(wallet.get("pnl_lt_2x_num_7d_ratio")),
                    "pnl_2x_5x_num_7d_ratio": safe_float(wallet.get("pnl_2x_5x_num_7d_ratio")),
                    "pnl_gt_5x_num_7d_ratio": safe_float(wallet.get("pnl_gt_5x_num_7d_ratio")),
                    
                    # 最近购买的代币
                    "recent_buy_tokens": wallet.get("recent_buy_tokens", []),
                    
                    # 每日收益
                    "daily_profit_7d": wallet.get("daily_profit_7d", []),
                    
                    # 风险指标
                    "risk": wallet.get("risk", {}),
                    
                    # 时间信息
                    "last_active": wallet.get("last_active"),
                    "timestamp": datetime.now().timestamp()
                }
                
                processed_wallets.append(wallet_data)
                logger.debug(f"已处理钱包数据: {wallet_data['wallet_address']}")
                
            except Exception as e:
                error_msg = f"处理钱包数据时发生错误: {str(e)}\n{traceback.format_exc()}"
                logger.error(error_msg)
                continue
        
        logger.info(f"已获取 {len(processed_wallets)} 个智能钱包数据")
        return processed_wallets
        
    except Exception as e:
        logger.error(f"获取智能钱包数据时发生错误: {str(e)}\n{traceback.format_exc()}")
        return None

async def validate_smart_money_data(data: Any) -> bool:
    """
    验证智能钱包数据的有效性
    
    Args:
        data: 要验证的数据
        
    Returns:
        bool: 数据是否有效
    """
    try:
        if not data:
            logger.warning("数据为空")
            return False
            
        # 检查是否为列表
        if isinstance(data, list):
            if not data:
                logger.warning("数据列表为空")
                return False
                
            # 检查每个项目是否包含必要字段
            for item in data:
                if not all(field in item for field in ["wallet_address", "address", "chain"]):
                    logger.error(f"数据缺少必要字段: {item}")
                    return False
        # 检查单个项目
        elif isinstance(data, dict):
            if not all(field in data for field in ["wallet_address", "address", "chain"]):
                logger.error(f"数据缺少必要字段: {data}")
                return False
        else:
            logger.error(f"数据类型错误: {type(data)}")
            return False
            
        return True
        
    except Exception as e:
        logger.error(f"验证智能钱包数据时发生错误: {str(e)}\n{traceback.format_exc()}")
        return False

async def store_smart_money_data(data_list: List[Dict]) -> int:
    """
    存储智能钱包数据到数据库
    
    Args:
        data_list: 智能钱包数据列表
        
    Returns:
        int: 成功存储的数据数量
    """
    try:
        if not data_list:
            logger.warning("没有数据需要存储")
            return 0
            
        valid_data = []
        
        for data in data_list:
            try:
                # 构建每日收益数据
                daily_profits = []
                try:
                    for profit_data in data.get("daily_profit_7d", []):
                        if isinstance(profit_data, dict):
                            daily_profits.append(
                                DailyProfit(
                                    timestamp=int(profit_data.get("timestamp", 0)),
                                    profit=float(profit_data.get("profit", 0.0))
                                ).dict()
                            )
                except Exception as e:
                    logger.error(f"处理每日收益数据失败: {str(e)}, 数据: {data.get('daily_profit_7d')}")
                    daily_profits = []
                
                # 构建风险指标数据
                risk_data = data.get("risk", {})
                try:
                    risk_metrics = RiskMetrics(
                        token_active=str(risk_data.get("token_active", "0")),
                        token_honeypot=str(risk_data.get("token_honeypot", "0")),
                        token_honeypot_ratio=float(risk_data.get("token_honeypot_ratio", 0.0)),
                        no_buy_hold=str(risk_data.get("no_buy_hold", "0")),
                        no_buy_hold_ratio=float(risk_data.get("no_buy_hold_ratio", 0.0)),
                        sell_pass_buy=str(risk_data.get("sell_pass_buy", "0")),
                        sell_pass_buy_ratio=float(risk_data.get("sell_pass_buy_ratio", 0.0)),
                        fast_tx=str(risk_data.get("fast_tx", "0")),
                        fast_tx_ratio=float(risk_data.get("fast_tx_ratio", 0.0))
                    ).dict()
                except Exception as e:
                    logger.error(f"处理风险指标数据失败: {str(e)}, 数据: {risk_data}")
                    risk_metrics = RiskMetrics().dict()
                
                wallet_data = {
                    # 基本信息
                    "wallet_address": data["wallet_address"],
                    "address": data.get("address", data["wallet_address"]),
                    "chain": data.get("chain", "solana"),
                    
                    # 用户信息
                    "twitter_username": data.get("twitter_username"),
                    "twitter_name": data.get("twitter_name"),
                    "twitter_description": data.get("twitter_description"),
                    "followers_count": int(data.get("followers_count", 0)),
                    "is_blue_verified": bool(data.get("is_blue_verified", False)),
                    "avatar": data.get("avatar"),
                    "ens": data.get("ens"),
                    "nickname": data.get("nickname"),
                    "name": data.get("name"),
                    "tag": data.get("tag"),
                    "tags": list(data.get("tags", [])),
                    "tag_rank": dict(data.get("tag_rank", {})),
                    
                    # 余额信息
                    "balance": float(data.get("balance", 0.0)),
                    "eth_balance": float(data.get("eth_balance", 0.0)),
                    "sol_balance": float(data.get("sol_balance", 0.0)),
                    "trx_balance": float(data.get("trx_balance", 0.0)),
                    
                    # 收益指标
                    "realized_profit": float(data.get("realized_profit", 0.0)),
                    "pnl_1d": float(data.get("pnl_1d", 0.0)),
                    "pnl_7d": float(data.get("pnl_7d", 0.0)),
                    "pnl_30d": float(data.get("pnl_30d", 0.0)),
                    "realized_profit_1d": float(data.get("realized_profit_1d", 0.0)),
                    "realized_profit_7d": float(data.get("realized_profit_7d", 0.0)),
                    "realized_profit_30d": float(data.get("realized_profit_30d", 0.0)),
                    
                    # 交易指标
                    "txs": int(data.get("txs", 0)),
                    "txs_30d": int(data.get("txs_30d", 0)),
                    "buy": int(data.get("buy", 0)),
                    "sell": int(data.get("sell", 0)),
                    "buy_30d": int(data.get("buy_30d", 0)),
                    "sell_30d": int(data.get("sell_30d", 0)),
                    "avg_hold_time": float(data.get("avg_hold_time", 0.0)),
                    "token_num_7d": int(data.get("token_num_7d", 0)),
                    "avg_holding_period_7d": float(data.get("avg_holding_period_7d", 0.0)),
                    
                    # 胜率和成本
                    "winrate_7d": float(data.get("winrate_7d", 0.0)),
                    "avg_cost_7d": float(data.get("avg_cost_7d", 0.0)),
                    
                    # 收益分布
                    "pnl_lt_minus_dot5_num_7d": int(data.get("pnl_lt_minus_dot5_num_7d", 0)),
                    "pnl_minus_dot5_0x_num_7d": int(data.get("pnl_minus_dot5_0x_num_7d", 0)),
                    "pnl_lt_2x_num_7d": int(data.get("pnl_lt_2x_num_7d", 0)),
                    "pnl_2x_5x_num_7d": int(data.get("pnl_2x_5x_num_7d", 0)),
                    "pnl_gt_5x_num_7d": int(data.get("pnl_gt_5x_num_7d", 0)),
                    
                    # 收益分布比率
                    "pnl_lt_minus_dot5_num_7d_ratio": float(data.get("pnl_lt_minus_dot5_num_7d_ratio", 0.0)),
                    "pnl_minus_dot5_0x_num_7d_ratio": float(data.get("pnl_minus_dot5_0x_num_7d_ratio", 0.0)),
                    "pnl_lt_2x_num_7d_ratio": float(data.get("pnl_lt_2x_num_7d_ratio", 0.0)),
                    "pnl_2x_5x_num_7d_ratio": float(data.get("pnl_2x_5x_num_7d_ratio", 0.0)),
                    "pnl_gt_5x_num_7d_ratio": float(data.get("pnl_gt_5x_num_7d_ratio", 0.0)),
                    
                    # 最近购买的代币
                    "recent_buy_tokens": list(data.get("recent_buy_tokens", [])),
                    
                    # 每日收益和风险指标
                    "daily_profit_7d": daily_profits,
                    "risk": risk_metrics,
                    
                    # 时间信息
                    "last_active": safe_timestamp_to_datetime(data.get("last_active")),
                    "last_updated_at": datetime.utcnow()
                }
                valid_data.append(wallet_data)
                
            except Exception as e:
                logger.error(f"构建钱包数据失败: {str(e)}\n{traceback.format_exc()}")
                continue
        
        if valid_data:
            try:
                # 使用 DAO 批量更新钱包数据
                update_count = await wallet_dao.upsert_wallets(valid_data)
                logger.info(f"成功更新 {update_count} 个钱包数据")
                return update_count
            except Exception as e:
                logger.error(f"批量更新钱包数据失败: {str(e)}\n{traceback.format_exc()}")
                return 0
        else:
            logger.warning("没有有效的钱包数据需要存储")
            return 0
        
    except Exception as e:
        logger.error(f"存储智能钱包数据时发生错误: {str(e)}\n{traceback.format_exc()}")
        return 0

async def sync_smart_money_addresses(data_list: List[Dict]) -> int:
    """
    同步智能钱包地址到监控地址表
    
    Args:
        data_list: 智能钱包数据列表
        
    Returns:
        int: 成功同步的地址数量
    """
    try:
        if not data_list:
            logger.warning("没有数据需要同步")
            return 0
            
        # 提取地址集合
        addresses = {data.get("address") for data in data_list if data.get("address")}
        logger.info(f"找到 {len(addresses)} 个智能钱包地址")
        
        # 查找已存在的监控地址
        existing = await monitor_address_dao.find_addresses_in_list(addresses)
        existing_addresses = {addr.address for addr in existing}
        
        # 找出需要新增的地址
        new_addresses = addresses - existing_addresses
        if not new_addresses:
            logger.info("没有需要新增的监控地址")
            return 0
            
        logger.info(f"需要新增 {len(new_addresses)} 个监控地址")
        
        # 批量添加新地址
        try:
            added = await monitor_address_dao.batch_add_monitor_addresses(
                list(new_addresses),
                name_prefix="smart_money",
                description="智能钱包地址"
            )
            logger.info(f"成功添加 {len(added)} 个监控地址")
            return len(added)
            
        except Exception as e:
            logger.error(f"添加监控地址失败: {str(e)}\n{traceback.format_exc()}")
            return 0
        
    except Exception as e:
        logger.error(f"同步智能钱包地址时发生错误: {str(e)}\n{traceback.format_exc()}")
        return 0 