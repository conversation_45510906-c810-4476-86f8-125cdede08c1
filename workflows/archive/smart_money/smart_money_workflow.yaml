name: '智能钱包数据存储工作流'
description: '定期获取智能钱包数据，并将数据存储到数据库'

nodes:
  - name: "SmartMoneyMonitorNode"
    node_type: "input"
    interval: 60  # 每1分钟运行一次
    generate_data: workflows.smart_money.handler.fetch_smart_money_wallets
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "SmartMoneyStorageNode"
    node_type: "storage"
    depend_ons: ["SmartMoneyMonitorNode"]
    batch_size: 100  # 每批处理100条数据
    store_data: workflows.smart_money.handler.store_smart_money_data
    validate: workflows.smart_money.handler.validate_smart_money_data 