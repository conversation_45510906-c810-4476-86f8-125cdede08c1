name: "GMGN 代币持币大户活动记录"
description: "定期获取链上的持币大户活动记录，并将数据存储到数据库"

nodes:
  - name: "TopHoldersActivitySchedulerNode"
    node_type: "input"
    interval: 10
    generate_data: workflows.gmgn_token_link.handler.generate_data
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "TopHoldersActivityMonitorNode"
    node_type: "process"
    depend_ons: ["TopHoldersActivitySchedulerNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_top_holders_activity.handler.process_top_holders_activity

  - name: "TopHoldersActivityStoreNode"
    node_type: "storage"
    depend_ons: ["TopHoldersActivityMonitorNode"]
    batch_size: 1000  # 每批处理1000条数据
    store_data: workflows.gmgn_top_holders_activity.handler.store_data
    validate: workflows.gmgn_top_holders_activity.handler.validate 