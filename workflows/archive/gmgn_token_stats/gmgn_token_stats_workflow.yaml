name: 'GMGN代币统计信息监控工作流'
description: '定期获取代币的统计信息，并将数据存储到数据库'

nodes:
  - name: "TokenStatsSchedulerNode"
    node_type: "input"
    interval: 1  # 每60秒运行一次
    generate_data: workflows.gmgn_token_stats.handler.generate_token_list
    flow_control:
      max_pending_messages: 10
      check_interval: 0.1
      enable_flow_control: true

  - name: "TokenStatsMonitorNode"
    node_type: "process"
    depend_ons: ["TokenStatsSchedulerNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_token_stats.handler.process_token_stats

  - name: "TokenStatsStorageNode"
    node_type: "storage"
    depend_ons: ["TokenStatsMonitorNode"]
    batch_size: 10  # 每批处理10条数据
    store_data: workflows.gmgn_token_stats.handler.store_token_stats_data
    validate: workflows.gmgn_token_stats.handler.validate_token_stats_data 