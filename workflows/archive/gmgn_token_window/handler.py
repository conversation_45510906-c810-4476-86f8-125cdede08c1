"""
GMGN代币多窗口信息监控工作流处理函数模块

提供YAML配置中引用的处理函数
"""

import logging
import traceback
from typing import List, Dict, Any, Optional
from datetime import datetime

from models.token import Token
from utils.spiders.smart_money.gmgn_token_window_spider import GmgnTokenWindowSpider
from dao.gmgn_token_window_dao import GmgnTokenWindowDAO

# 创建日志记录器
logger = logging.getLogger("GmgnTokenWindowHandler")

# 创建爬虫和DAO实例
spider = GmgnTokenWindowSpider()
token_window_dao = GmgnTokenWindowDAO()

async def generate_token_list() -> Optional[List[Dict]]:
    """
    生成需要获取多窗口信息的代币列表
    
    Returns:
        List[Dict]: 代币列表
    """
    try:
        # 从数据库获取所有代币
        tokens = await Token.find_all().to_list()
        
        if not tokens:
            logger.warning("没有找到需要监控的代币")
            return None
            
        # 转换为字典列表
        token_list = []
        for token in tokens:
            token_dict = {
                "chain": "sol",  # 默认为Solana链
                "address": token.address,
                "symbol": token.symbol,
                "id": token.id if hasattr(token, 'id') else None
            }
            token_list.append(token_dict)
            
        logger.info(f"生成了 {len(token_list)} 个代币的监控列表")
        return token_list
        
    except Exception as e:
        logger.error(f"生成代币列表时发生错误: {str(e)}\n{traceback.format_exc()}")
        return None

async def process_token_window(token_data: Dict) -> Optional[Dict]:
    """
    处理单个代币的多窗口信息
    
    Args:
        token_data: 代币数据
        
    Returns:
        Dict: 处理后的代币多窗口信息
    """
    try:
        chain = token_data.get("chain", "sol")
        address = token_data.get("address")
        symbol = token_data.get("symbol", "")
        
        if not address:
            logger.error("缺少代币地址")
            return None
            
        logger.info(f"开始获取代币 {symbol} ({address}) 的多窗口信息...")
        
        # 调用爬虫获取数据
        result = await spider.get_token_window_info(
            chain=chain,
            addresses=[address]
        )
        
        if not result:
            logger.warning(f"获取代币 {symbol} ({address}) 的多窗口信息失败")
            return None
        
        # 获取第一个结果（因为我们只查询了一个地址）
        data = result[0] if result else None
        if not data:
            logger.warning(f"代币 {symbol} ({address}) 的多窗口信息为空")
            return None
        
        # 添加基本信息
        data.update({
            'chain': chain,
            'address': address,
            'symbol': symbol,
        })
        
        logger.info(f"已获取代币 {symbol} ({address}) 的多窗口信息")
        return data
        
    except Exception as e:
        logger.error(f"处理代币多窗口信息时发生错误: {str(e)}\n{traceback.format_exc()}")
        return None

async def validate_token_window_data(data: Any) -> bool:
    """
    验证代币多窗口信息的有效性
    
    Args:
        data: 要验证的数据
        
    Returns:
        bool: 数据是否有效
    """
    try:
        if not data:
            logger.warning("数据为空")
            return False
            
        # 检查是否为列表
        if isinstance(data, list):
            if not data:
                logger.warning("数据列表为空")
                return False
                
            # 检查每个项目是否包含必要字段
            for item in data:
                if not all(field in item for field in ["chain", "address"]):
                    logger.error(f"数据缺少必要字段: {item}")
                    return False
        # 检查单个项目
        elif isinstance(data, dict):
            if not all(field in data for field in ["chain", "address"]):
                logger.error(f"数据缺少必要字段: {data}")
                return False
        else:
            logger.error(f"数据类型错误: {type(data)}")
            return False
            
        return True
        
    except Exception as e:
        logger.error(f"验证代币多窗口信息时发生错误: {str(e)}\n{traceback.format_exc()}")
        return False

async def store_token_window_data(data_list: List[Dict]) -> int:
    """
    存储代币多窗口信息到数据库
    
    Args:
        data_list: 代币多窗口信息列表
        
    Returns:
        int: 成功存储的数据数量
    """
    try:
        if not data_list:
            logger.warning("没有数据需要存储")
            return 0
            
        # 按链分组
        data_by_chain = {}
        for data in data_list:
            if not data:
                continue
                
            chain = data.get("chain", "sol")
            if chain not in data_by_chain:
                data_by_chain[chain] = []
                
            data_by_chain[chain].append(data)
        
        total_updated = 0
        
        # 按链批量更新数据
        for chain, chain_data_list in data_by_chain.items():
            if chain_data_list:
                try:
                    logger.info(f"准备更新链 {chain} 的 {len(chain_data_list)} 条数据")
                    token_windows = await token_window_dao.upsert_token_windows(chain, chain_data_list)
                    logger.info(f"链 {chain} 批量更新完成: 更新了 {len(token_windows)} 条记录")
                    total_updated += len(token_windows)
                except Exception as e:
                    logger.error(f"批量更新链 {chain} 的数据时发生错误: {str(e)}\n{traceback.format_exc()}")
                    continue
        
        return total_updated
        
    except Exception as e:
        logger.error(f"存储代币多窗口信息时发生错误: {str(e)}\n{traceback.format_exc()}")
        return 0 