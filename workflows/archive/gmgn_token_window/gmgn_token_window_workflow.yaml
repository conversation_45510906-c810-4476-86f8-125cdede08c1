name: 'GMGN代币多窗口信息监控工作流'
description: '定期获取代币的多窗口信息，并将数据存储到数据库'

nodes:
  - name: "TokenWindowSchedulerNode"
    node_type: "input"
    interval: 1  # 每60秒运行一次
    generate_data: workflows.gmgn_token_window.handler.generate_token_list
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "TokenWindowMonitorNode"
    node_type: "process"
    depend_ons: ["TokenWindowSchedulerNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_token_window.handler.process_token_window

  - name: "TokenWindowStorageNode"
    node_type: "storage"
    depend_ons: ["TokenWindowMonitorNode"]
    batch_size: 10  # 每批处理10条数据
    store_data: workflows.gmgn_token_window.handler.store_token_window_data
    validate: workflows.gmgn_token_window.handler.validate_token_window_data 