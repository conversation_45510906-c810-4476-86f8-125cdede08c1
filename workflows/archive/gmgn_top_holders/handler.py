import logging
import traceback
from typing import Any, Dict, List, Optional

from dao.gmgn_top_holders_dao import GmgnTopHoldersDAO
from utils.spiders.smart_money.gmgn_top_holders_spider import GmgnTopHoldersSpider


logger = logging.getLogger("GmgnTopHoldersHandler")
spider = GmgnTopHoldersSpider()
gmgn_top_holders_dao = GmgnTopHoldersDAO()


async def process_top_holders(token_dict: Dict) -> Optional[List[Dict]]:
    """处理代币数据
    
    Args:
        token_dict: 代币数据
        
    Returns:
        Optional[List[Dict]]: 处理后的代币数据，如果处理失败则返回None
    """
    if not token_dict:
        logger.warning("收到空的代币数据")
        return None
    
    # 获取代币地址
    token_address = token_dict.get('address')
    if not token_address:
        logger.warning(f"代币数据缺少地址字段: {token_dict}")
        return None
    
    chain = "sol"
    
    # 使用smart_degen标签获取顶级持有者数据
    holders = await spider.get_top_holders(
        chain=chain, 
        address=token_address, 
        tag="smart_degen",
        orderby="amount_percentage",
        direction="desc",
        limit=1000,
        cost=20
    )
    
    if not holders:
        logger.warning(f"没有顶级持有者持有这个币: {token_address}")
        return None
    
    return holders

async def validate(data: Any) -> bool:
    """验证代币链接数据是否有效
    
    Args:
        data: 代币链接数据
        
    Returns:
        bool: 如果数据有效则返回True，否则返回False
    """
    return True


async def store_data(data: List[Dict]) -> int:
    """存储顶级持有者数据到数据库
    
    Args:
        data: 顶级持有者数据列表
        
    Returns:
        int: 成功存储的记录数
    """
    if not data:
        return 0
    
    holders = data if isinstance(data, list) else [data]
        
    try:
        chain = "sol"
        update_count = await gmgn_top_holders_dao.upsert_holders_many(
            chain=chain,
            data_list=holders
        )
        logger.info(f"批量更新完成: 更新了 {update_count} 条顶级持有者记录")
        return update_count
    except Exception as e:
        error_msg = f"存储顶级持有者数据时发生错误: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        return False