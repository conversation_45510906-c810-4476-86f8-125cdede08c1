name: "GMGN 代币顶级持有者记录"
description: "定期获取链上的顶级持有者记录，并将数据存储到数据库"

nodes:
  - name: "TopHoldersSchedulerNode"
    node_type: "input"
    interval: 10
    generate_data: workflows.gmgn_token_link.handler.generate_data
    flow_control:
      max_pending_messages: 10
      check_interval: 1
      enable_flow_control: true

  - name: "TopHoldersMonitorNode"
    node_type: "process"
    depend_ons: ["TopHoldersSchedulerNode"]
    concurrency: 1
    interval: 1
    process_item: workflows.gmgn_top_holders.handler.process_top_holders

  - name: "TopHoldersStoreNode"
    node_type: "storage"
    depend_ons: ["TopHoldersMonitorNode"]
    batch_size: 1000  # 每批处理1000条数据
    store_data: workflows.gmgn_top_holders.handler.store_data
    validate: workflows.gmgn_top_holders.handler.validate 