[tool.poetry]
name = "meme-monitor"
version = "0.1.0"
description = "A meme monitor"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.11"
requests = "^2.32.3"
tweepy = "^4.15.0"
python-dotenv = "^1.0.1"
beanie = "^1.29.0"
motor = "^3.7.0"
solders = ">=0.23.0,<0.24.0"
solana = "^0.36.3"
httpx = {extras = ["socks"], version = "^0.27.0"}
pydantic = "^2.10.6"
redis = "^5.2.1"
aiohttp = "^3.11.12"
curl-cffi = "^0.9.0"
selenium = "^4.29.0"
webdriver-manager = "^4.0.2"
pyyaml = "^6.0.2"
aiokafka = "^0.12.0"
kafka-python = "^2.0.6"
jinja2 = "^3.1.6"
sortedcontainers = "^2.4.0"
fake-useragent = "^2.1.0"
pympler = "^1.1"
matplotlib = "^3.10.1"
numpy = "^2.2.4"
pandas = "^2.2.3"
gunicorn = "^23.0.0"
uvicorn = "^0.34.2"
fastapi = "^0.115.12"
seaborn = "^0.13.2"
requests-html = "^0.10.0"
cloudscraper = "^1.2.71"
base58 = "^2.1.1"
plotly = "^6.1.2"
aiofiles = "^24.1.0"

[tool.poetry.group.dev.dependencies]
snakeviz = "^2.2.2"
pytest = "^8.3.5"
pytest-asyncio = "^1.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
