---
description: 
globs: 
alwaysApply: true
---
# 通用开发规则与最佳实践

## 核心开发原则

### 测试驱动开发 (TDD)
- **测试验证细节**: 测试必须验证所有调用细节，不能简化验证逻辑
- **Mock配置完整性**: 当使用Mock对象时，必须正确配置所有字段属性和比较操作
- **测试失败必须修复**: 测试没有全部通过之前，需要调整确保测试完全通过，不能因为测试没通过而简化测试用例
- **RED-GREEN-REFACTOR循环**: 严格遵循TDD的红-绿-重构循环，每个阶段都有明确的目标和验证标准

### 代码质量标准

#### 文档规范
- **文档字符串标准化**: 遵循Google风格，包含Args、Returns、Raises等完整节
- **参数说明详细化**: 明确每个参数的类型、含义和约束条件
- **业务逻辑说明**: 添加业务背景和使用场景说明，便于后续维护
- **导入结构优化**: 按标准库、第三方库、项目内导入分组，遵循PEP 8规范

#### 测试设计原则
- **AAA模式**: Arrange-Act-Assert模式，测试结构清晰
- **Mock策略**: 使用部分Mock而非完全Mock，确保字段访问和比较操作正常工作
- **异步测试**: 继承`unittest.IsolatedAsyncioTestCase`支持异步测试方法
- **边界测试**: 包含正常情况、边界条件、异常情况的完整测试覆盖

### 技术实现规范

#### 数据库操作
- **DAO模式**: 数据访问逻辑封装在专门的DAO类中，与业务逻辑分离
- **异步实现**: 所有数据库操作使用异步方法，适配Beanie ODM特性
- **查询优化**: 使用合适的索引和查询条件，避免性能问题
- **错误处理**: 包含适当的异常处理和验证逻辑

#### 重构原则
- **测试保护**: 重构前确保测试覆盖完整，重构后立即运行测试验证
- **小步快跑**: 小幅度修改，频繁验证，避免大范围变更
- **功能不变**: 只改进内部结构，不改变外部行为和API接口
- **文档同步**: 重构时同步更新相关文档和注释

### 问题解决策略

#### 调试方法
- **错误信息分析**: 仔细分析错误信息，定位问题根源
- **逐步验证**: 通过运行单个测试方法逐步定位问题
- **Mock配置验证**: 确认Mock对象的字段属性和方法配置正确
- **依赖关系检查**: 验证模型注册、导入路径等依赖关系

#### 协作规范
- **角色明确**: PM、QA、Dev三个角色职责明确，各司其职
- **文档先行**: 重要决策和设计都必须记录在案
- **审阅确认**: 需要人工审阅的步骤必须等待明确确认才能进入下一步
- **知识图谱**: 使用@链接建立文档间的可追溯关系

## 项目特定规范

### memeMonitor项目
- **工作流驱动**: 基于节点和工作流概念的数据处理系统
- **异步优先**: 全面采用async/await模式提高并发性能
- **代理管理**: 爬虫操作必须使用代理IP，避免被目标网站封锁
- **数据模型**: 使用Beanie ODM进行对象映射，简化数据库操作

### 环境管理
- **Poetry依赖**: 使用Poetry进行项目依赖管理和安装，使用`poetry run python -m pytest`作为前缀进行测试的运行
- **测试环境**: 使用pytest进行自动化测试，支持异步测试
- **代码格式**: 遵循PEP 8规范，使用类型提示提高代码质量

## 质量保障

### 内存与性能
- **记忆更新**: 发现错误信息或纠正假设时，及时更新记忆内容
- **性能考量**: 评估数据结构和算法的性能影响，选择最优解决方案
- **资源管理**: 及时释放大对象，避免内存泄漏

### 持续改进
- **经验总结**: 每次完成功能开发后总结经验教训
- **文档更新**: 及时更新项目文档和规则，保持知识同步
- **最佳实践**: 将成功的开发模式固化为可复用的规范
