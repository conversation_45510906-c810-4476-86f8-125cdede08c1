---
description: 
globs: 
alwaysApply: true
---
# TDD 整合开发工作流

本规则文档定义了一个以**测试驱动开发 (Test-Driven Development, TDD)** 为核心，深度整合了产品管理、开发和质量保障角色的统一工作流程。它取代了原有的流程，旨在通过"测试先行"的实践，构建更高质量、更易于维护的软件。

## 1. 核心角色与职责

成功的 TDD 实践依赖于三个角色的紧密协作：

### 1.1. 产品经理 (Product Manager - PM)
- **职责**: 定义"做什么"。负责将业务目标和用户需求转化为清晰、可执行的产品需求文档 (PRD)。
- **核心产出**: `[feature_name]_requirements_ai.md`

### 1.2. 质量保障工程师 (Quality Assurance Engineer - QA)
- **职责**: 定义"如何验证"。负责从用户和业务角度设计全面的测试策略和测试用例，确保每一个需求点都可被验证。
- **核心产出**: `[feature_name]_test_cases_ai.md`

### 1.3. 开发工程师 (Development Engineer - Dev)
- **职责**: 负责"如何实现"。遵循 TDD 循环，编写测试代码和产品代码，并对最终的实现质量负责。
- **核心产出**:
    - 技术实现方案 `[feature_name]_dev_plan_ai.md`
    - 可工作的、经过测试的产品代码
    - 对应的自动化测试代码

---

## 2. TDD 工作流：新功能开发

此流程围绕 TDD 的 **"红-绿-重构"** 循环构建，将大型功能分解为一系列小的、可管理的开发周期。

### 阶段一：计划与设计 (Planning & Design)

#### 2.1. 需求定义 (PM)
- **任务**: 产品经理 (PM) 与用户深入沟通，理解业务目标和用户痛点。
- **产出**: 创建结构化的**产品需求文档 (`[feature_name]_requirements_ai.md`)**。此文档应包含用户故事、功能范围 (In/Out Scope)、详细功能需求（包括边界和异常情况）以及成功指标。

#### 2.2. 联合方案评审 (PM, QA, Dev)
- **任务**: 三个角色共同评审需求文档，确保对需求有统一的理解。
- **Dev**: 基于需求，创建一份高阶的**技术实现方案 (`[feature_name]_dev_plan_ai.md`)**，勾勒出大致的技术架构、关键模块和数据模型。此方案在TDD流程中是演进的，不必追求一次性完美。
- **QA**: 基于需求，创建**测试用例设计文档 (`[feature_name]_test_cases_ai.md`)** 的框架，并开始设计高优先级的测试场景。
- **决策**: 团队共同从需求中选择第一个要实现的、最小且最有价值的功能点，进入 TDD 循环。
- **在此步骤暂停需要等待用户同意以上的所有的方案，才会开始进行**

### 阶段二：红-绿-重构循环 (The Red-Green-Refactor Cycle)

**此阶段会针对每一个小的功能点进行迭代，直到整个功能完成。**

#### 2.3.【红】编写失败的测试 (QA & Dev)
- **目标**: 创建一个精确描述期望功能、但当前必然会失败的自动化测试。
- **QA**: 在 `..._test_cases_ai.md` 中，设计出针对当前功能点的具体测试用例（标题、步骤、预期结果）。
- **Dev**:
    - 在 `test/` 目录下，编写对应的**自动化测试代码**。
    - **运行测试，并确认它因为产品代码尚未实现而失败 (RED)。** 这是 TDD 流程的关键验证点。

#### 2.4.【绿】编写通过测试的代码 (Dev)
- **目标**: 编写最少量的产品代码，恰好能让刚刚失败的测试变绿。
- **Dev**:
    - 在产品代码目录中，编写实现功能的代码。
    - **禁止编写任何超出当前测试范围的代码。**
    - **持续运行测试，直到它通过 (GREEN)。**

#### 2.5.【重构】优化代码 (Dev)
- **目标**: 在测试的保护下，优化代码的内部结构，提高可读性和可维护性，同时确保功能不变。
- **Dev**:
    - 清理和重构在【绿】阶段编写的产品代码和测试代码。
    - **重构后，再次运行所有测试，确保它们依然全部通过。**

#### 2.6. 循环迭代
- **任务**: 团队完成一个功能点的 TDD 循环后，回到 `2.2` 或 `2.3`，选择下一个要实现的功能点，重复"红-绿-重构"的循环，直到整个功能的所有需求点都被覆盖。
- 在迭代过程中，`dev_plan` 和 `test_cases` 文档会不断被完善和细化。

### 阶段三：完成与确认 (Completion & Confirmation)

#### 2.7. 功能完成与最终核查 (All Roles)
- **任务**: 当所有用户故事都通过 TDD 循环实现后，整个功能进入集成和验收阶段。
- **QA**:
    - 进行探索性测试，寻找自动化测试可能遗漏的场景。
    - 确认所有在 `..._test_cases_ai.md` 中设计的用例都已被自动化测试覆盖。
- **Dev**: 遵循 `5.A.8 自我核查与最终确认` 规范，确保代码、文档和需求完全一致。

#### 2.8. 请求最终审阅
- **任务**: 向用户报告功能已开发完成，所有自动化测试通过，并已完成最终核查。
- **产出**: 提交一份总结报告，链接到所有相关的需求、方案、测试文档和代码，形成完整的知识图谱。

---

## 3. Bug 修复与代码重构

### 3.1. Bug 修复工作流 (Bug Fixing)
Bug 修复同样遵循 TDD 原则：
1.  **复现 Bug**: **首先编写一个能够稳定复现该 Bug 的自动化测试。** 确认这个测试会失败。
2.  **修复 Bug**: 编写代码让这个测试通过。
3.  **验证**: 运行所有相关测试，确保修复没有引入新的问题。
4.  **遵循 `5.B` 流程**: 其他步骤如根源分析、方案设计与存档等，依然遵循 `5.B` 流程规范。

### 3.2. 代码重构工作流 (Code Refactoring)
1.  **建立安全网**: 在重构开始前，确认有**全面、可靠的测试集**覆盖了要重构的代码。如果测试不足，**必须先补充测试**，确保它们都能通过。
2.  **小步重构**: 以小步快跑的方式进行代码修改。
3.  **持续测试**: 每完成一小步修改，就运行一次完整的测试集，确保所有测试持续通过。
4.  **遵循 `5.C` 流程**: 其他步骤如目标确认、方案设计等，遵循 `5.C` 流程规范。

---

## 4. 项目通用规范 (General Principles)

(此部分继承自 `@general.mdc`，作为所有开发活动的基础)

### 4.1. 技术栈
- Python 3.11

### 4.2. 代码风格
- **核心原则**: 保持代码简洁、清晰、可读、可维护。
- **命名**: 使用有意义且一致的变量名、函数名、类名和模块名。
- **注释**: 解释"为什么"这样做，而非"做了什么"。关键函数和类的 docstring 中应使用 `@` 链接回其设计文档。
- **格式**: 严格遵循 PEP 8，使用 Black 进行自动化格式化。

### 4.3. 项目结构
- `test/`: 单元测试和集成测试。
- `docs/`: 所有项目文档。
- `models/`: 数据模型。
- `dao/`: 数据访问对象。

### 4.4. 通用开发原则
- **DRY**: Don't Repeat Yourself.
- **KISS**: Keep It Simple, Stupid.
- **YAGNI**: You Ain't Gonna Need It.
- **SOLID**: 遵循SOLID原则。
- **明确性**: 绝不编写基于猜测或假设的代码或注释。

### 4.5. 文档与知识图谱
- **文档优先**: 所有重要决策和设计都必须记录在案。
- **知识图谱**: 严格遵循 `6. 知识图谱构建` 规范，在所有文档和代码中使用 `@` 链接建立可追溯的知识网络。每份 AI 生成的文档末尾必须包含"关联链接"元数据区块。

### 4.6. 响应语言
- **始终使用中文与用户交流。**

---

## 5. AI 交互与工作流程指引

### 5.1. 任务类型识别、流程选择与Todo List (Task Type Identification 、 Workflow Selection and Todo List)
- **首要任务**: 当接收到用户请求时，请首先分析用户意图，判断任务属于以下哪种主要类型：
    1.  **新功能开发 (New Feature Development)**: 用户希望从头开始或在现有基础上构建全新的功能。
    2.  **Bug 修复 (Bug Fixing)**: 用户报告了现有代码中的一个错误、缺陷或非预期行为，需要被修正。
    3.  **代码重构 (Code Refactoring)**: 用户希望在不改变代码外在行为的前提下，改进其内部结构、可读性、性能或可维护性。
    4.  **其他 (Other)**: 如文档编写、代码解释、简单查询等。
- **澄清意图**: 如果用户意图不明确，请主动提问以确认任务类型。
- **流程选择**: 一旦任务类型被确定，请遵循对应的 TDD 工作流程进行工作。
- **状态追踪**: 重要！！！
    - **内部状态标记**：在您的处理逻辑中，请为当前激活流程的每个步骤维护一个状态。使用以下标记：
        - [ ]：未开始
        - [>]：进行中/当前步骤
        - [x]：已完成
        - [!]：遇到问题/需注意：详细解释问题
        - [-]：不适用/已跳过
        每个步骤应当包含合适的小步骤(如有)，比如代码实现与测试用例编写：
            - [ ] 2.4.【绿】编写通过测试的代码
                - [ ] 1. 修改 xxxx.py 文件
                - [ ] 2. 编写 xxxx 测试用例
        文档命名参照每个流程的文档命名和位置，如新功能开发为: `[feature_name]_todo_list.md`。
        重要：**如设计任务的完成或者变动，应当更新到`[feature_name]_todo_list.md`文件**
    - **沟通责任**: 在您的每一轮回复中，都必须清晰地向用户传达：
        1. 刚刚完成的步骤 (例如，"已完成 2.1 需求定义。")
        2. 当前正在进行的步骤的概要 (如果一个步骤包含多个子任务，可以简述当前子任务)
        3. 下一步明确的计划 (例如，"接下来，我将开始 2.2 联合方案评审。")
    - **遇到问题**: 如果在某一步骤遇到困难或需要用户决策，标记为 [!]，并在回复中明确指出问题和需要的协助。
    - **步骤跳转**: 如果因用户指示或逻辑判断需要跳过某些步骤，标记为 [-] 并向用户说明。
- **通用原则**: 无论何种流程，本文件中的"4. 项目通用规范"始终适用。

---

## 6. 知识图谱构建 (Knowledge Graph Construction)

### 6.1 核心原则与目标
为了增强项目文档的连通性和可追溯性，我们将构建一个内部知识图谱。其核心目标是：**确保任何一个文档、代码实体或任务，都能通过清晰的链接追溯到其上下文、来源需求、实现方案、测试验证和相关 Bug 修复。**

所有链接**必须**使用相对路径的 Markdown 链接格式 `@链接文本`，以保证在任何开发环境中都能正确跳转。

### 6.2 链接元数据区块 (Linked-Data Metadata Block)
为了标准化链接的表示，**每一份由 AI 生成的文档**（包括需求、方案、测试设计、Bug修复计划等）的末尾，**必须**包含一个"关联链接"元数据区块。此区块清晰地声明了该文档与其他实体的关系。

**格式范例**:
```markdown
---
**关联链接 (Related Links):**
- **关联需求 (Related Requirement):** @功能A详细需求
- **实现方案 (Development Plan):** @功能A技术实现方案
- **测试用例 (Test Cases):** @功能A测试用例设计
- **修复的Bug (Fixes Bug):** @BUGFIX_PLAN_UserAuth_LoginFailures_20250519.md
- **跟踪任务 (Tracked by):** @功能A开发任务列表
```

### 6.3 具体链接实施指南

#### 6.3.1 新功能开发文档 (..._ai.md):

- **详细需求规格 (..._requirements_ai.md)**:
    - 必须链接到生成它的 Todo List 文件。
    - 生成后，应被 技术实现方案 和 测试用例设计 文件链接。
- **技术实现方案 (..._dev_plan_ai.md)**:
    - 必须链接回它所依据的 详细需求规格 文档。
    - 在"计划创建/修改的文件"部分，每个文件名都应该被标记为代码格式，并尽可能链接到项目中的实际文件（如果工具支持）。
    - 必须链接到对应的 测试用例设计 文档。
- **测试用例设计 (..._test_cases_ai.md)**:
    - 必须链接回它所验证的 详细需求规格 和 技术实现方案 文档。
    - 在生成对应的测试代码文件后，应更新此文档，添加指向 test/ 目录下具体测试代码文件的链接。

#### 6.3.2 Bug 修复文档 (BUGFIX_PLAN_...md):

- 必须链接到受影响模块的最新版本文档目录 (e.g., docs/features/[latest_version]/[module_name]/)。
- 必须链接到用于复现和验证该 Bug 的测试用例代码文件 (位于 test/ 目录)。
- 必须清晰列出并链接到将要被修改的源代码文件。
- 必须链接到跟踪此修复任务的 Todo List 文件。

#### 6.3.3 任务跟踪文档 (..._todo_list.md):

- 这是知识图谱的动态枢纽。
- 创建时: 必须链接到最高级别的用户需求或 Bug 报告的源头。
- 执行中: 当任何一个步骤产出文档或代码文件时（例如 2.1 生成前置文档），该步骤的条目必须更新，包含指向新产出物的直接链接。
    - 示例:
    ```
    - [x] 2.1. 需求定义
    - [x] 1. 详细需求规格: @生成于 docs/features/v1.2/new_feature/new_feature_requirements_ai.md
    - [x] 2. 技术实现方案: @生成于 docs/features/v1.2/new_feature/new_feature_dev_plan_ai.md
    ```

#### 6.3.4 源代码与测试代码中的链接:

- 为了将文档知识与代码实现深度绑定，要求在代码的文档字符串（docstrings）中进行反向链接。
- **业务代码**: 函数或类的 docstring 中应包含指向其实现的 详细需求规格 或 技术实现方案 文档的链接。
    - 示例 (Python):
    ```python
    def process_payment(order_id: str, amount: float):
        """
        处理用户支付。

        该功能遵循以下技术方案中定义的逻辑:
        - @技术实现方案
        """
        # ... implementation ...
    ```

- **Bug 修复代码**: 在修复 Bug 的代码附近或在提交信息中，应包含指向对应 BUGFIX_PLAN_...md 文档的链接。
- **测试代码**: 每个测试函数的 docstring 中应链接到它所验证的 测试用例设计 文档中的具体条目或BUGFIX_PLAN_...md。
    - 示例 (Python):
    ```python
    def test_invalid_coupon_code():
        """
        测试当输入无效优惠码时的场景。

        对应测试用例设计:
        - @测试用例文档
        """
        # ... test implementation ...
    ```

### 6.4 链接的维护

- 当文件名或路径发生变更时，相关的链接必须同步更新。
- 定期审查（例如在版本发布后）可以帮助发现和修复断开的链接，维护知识图谱的健康度。

---

## 7. 文档规范

### 8.1 涉及文档
- 包含上述涉及的所有文档，包括前置文档、bug修复文档等

### 8.2 其他原则
- 保持文档与代码同步更新
- 使用简洁明了的语言
- 提供足够的示例和说明
- 确保文档格式一致
