# 回测模块V2配置示例 - 包含KOL总胜率过滤功能
# 此配置演示如何使用新增的KOL总胜率过滤功能

# 回测时间参数
backtest_start_time: **********  # 2023-01-01 00:00:00 UTC
backtest_end_time: **********    # 2023-02-01 00:00:00 UTC

# 数据筛选参数
transaction_min_amount: 500.0    # 最小交易金额(USD)
kol_account_min_txs: 10          # KOL账号最小交易数
kol_account_max_txs: 500         # KOL账号最大交易数
kol_account_min_count: 6         # 最小KOL账号数量
kol_min_winrate: 0.5             # 🆕 KOL最小总胜率阈值(0-1，0表示不过滤)
token_mint_lookback_hours: 48    # 代币创建时间回溯小时数

# 策略参数
transaction_lookback_hours: 24   # 交易回溯时间窗口(小时)
sell_strategy_hours: 24          # 卖出策略时间窗口(小时)
sell_kol_ratio: 0.5              # 卖出KOL比例阈值

# 回测控制参数
fixed_trade_amount: 100.0        # 固定买入金额(USD)
commission_pct: 0.003            # 手续费百分比
slippage_pct: 0.002              # 滑点百分比
same_token_notification_interval_minutes: 60  # 相同代币通知最小间隔(分钟)

# KOL总胜率过滤功能说明:
# - kol_min_winrate: 设置KOL的最小总胜率阈值
# - 0.0: 不过滤，包含所有KOL（默认值）
# - 0.3: 只包含总胜率 > 30% 的KOL
# - 0.5: 只包含总胜率 > 50% 的KOL
# - 0.7: 只包含总胜率 > 70% 的KOL
# - 1.0: 只包含总胜率 = 100% 的KOL（极严格）
#
# 胜率数据来源: gmgn_wallet_stats表中period='all'的winrate字段
# 过滤逻辑: 在MongoDB聚合查询中通过$lookup关联gmgn_wallet_stats表，
#          然后使用$filter过滤出胜率大于阈值的KOL
